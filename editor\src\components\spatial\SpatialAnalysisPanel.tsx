/**
 * 空间分析面板组件
 * 提供各种空间分析工具和功能
 */
import React, { useState, useCallback } from 'react';
import { 
  Card, 
  Radio, 
  Space, 
  Button, 
  Form, 
  InputNumber, 
  Select, 
  List, 
  Progress, 
  message,
  Divider,
  Collapse,
  Tag,
  Tooltip,
  Alert
} from 'antd';
import {
  RadiusSettingOutlined,
  NodeIndexOutlined,
  MergeCellsOutlined,
  DiffOutlined,
  LineOutlined,
  PlayCircleOutlined,
  DeleteOutlined,
  DownloadOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import './SpatialAnalysisPanel.scss';

const { Option } = Select;
const { Panel } = Collapse;

/**
 * 分析任务接口
 */
export interface AnalysisTask {
  id: string;
  name: string;
  type: 'buffer' | 'intersection' | 'union' | 'difference' | 'distance' | 'area';
  parameters: Record<string, any>;
  inputLayers: string[];
  outputLayer?: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  progress?: number;
  result?: any;
  error?: string;
  createdAt: Date;
  completedAt?: Date;
}

/**
 * 空间分析面板属性接口
 */
export interface SpatialAnalysisPanelProps {
  availableLayers?: Array<{ id: string; name: string; type: string }>;
  onAnalysisRun?: (task: AnalysisTask) => Promise<any>;
  onTaskDelete?: (taskId: string) => void;
  onResultExport?: (task: AnalysisTask) => void;
  className?: string;
}

/**
 * 空间分析面板组件
 */
export const SpatialAnalysisPanel: React.FC<SpatialAnalysisPanelProps> = ({
  availableLayers = [],
  onAnalysisRun,
  onTaskDelete,
  onResultExport,
  className
}) => {
  const [selectedAnalysis, setSelectedAnalysis] = useState<string>('buffer');
  const [analysisTasks, setAnalysisTasks] = useState<AnalysisTask[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [form] = Form.useForm();

  /**
   * 分析类型配置
   */
  const analysisTypes = [
    { 
      key: 'buffer', 
      label: '缓冲区分析', 
      icon: <RadiusSettingOutlined />,
      description: '为几何对象创建指定距离的缓冲区',
      color: '#1890ff'
    },
    {
      key: 'intersection',
      label: '相交分析',
      icon: <NodeIndexOutlined />,
      description: '计算两个几何对象的相交部分',
      color: '#52c41a'
    },
    {
      key: 'union',
      label: '联合分析',
      icon: <MergeCellsOutlined />,
      description: '合并多个几何对象为一个',
      color: '#fa8c16'
    },
    { 
      key: 'difference', 
      label: '差异分析', 
      icon: <DiffOutlined />,
      description: '计算两个几何对象的差异部分',
      color: '#eb2f96'
    },
    { 
      key: 'distance', 
      label: '距离分析', 
      icon: <LineOutlined />,
      description: '计算几何对象之间的距离',
      color: '#722ed1'
    }
  ];

  /**
   * 生成任务ID
   */
  const generateTaskId = () => `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

  /**
   * 运行分析
   */
  const handleRunAnalysis = useCallback(async () => {
    try {
      const values = await form.validateFields();
      setIsRunning(true);

      const analysisType = analysisTypes.find(t => t.key === selectedAnalysis);
      const task: AnalysisTask = {
        id: generateTaskId(),
        name: `${analysisType?.label}_${new Date().toLocaleTimeString()}`,
        type: selectedAnalysis as any,
        parameters: values,
        inputLayers: values.inputLayers || [],
        status: 'running',
        progress: 0,
        createdAt: new Date()
      };

      setAnalysisTasks(prev => [task, ...prev]);

      // 模拟分析进度
      const progressInterval = setInterval(() => {
        setAnalysisTasks(prev => prev.map(t => 
          t.id === task.id 
            ? { ...t, progress: Math.min((t.progress || 0) + 10, 90) }
            : t
        ));
      }, 200);

      try {
        // 调用分析函数
        const result = await onAnalysisRun?.(task);
        
        clearInterval(progressInterval);
        
        setAnalysisTasks(prev => prev.map(t => 
          t.id === task.id 
            ? { 
                ...t, 
                status: 'completed', 
                progress: 100, 
                result,
                completedAt: new Date()
              }
            : t
        ));

        message.success('分析完成');
      } catch (error) {
        clearInterval(progressInterval);
        
        setAnalysisTasks(prev => prev.map(t => 
          t.id === task.id 
            ? { 
                ...t, 
                status: 'error', 
                error: error instanceof Error ? error.message : '分析失败'
              }
            : t
        ));

        message.error('分析失败');
      }
    } catch (error) {
      console.error('参数验证失败:', error);
      message.error('请检查输入参数');
    } finally {
      setIsRunning(false);
    }
  }, [form, selectedAnalysis, analysisTypes, onAnalysisRun]);

  /**
   * 删除任务
   */
  const handleDeleteTask = useCallback((taskId: string) => {
    setAnalysisTasks(prev => prev.filter(t => t.id !== taskId));
    onTaskDelete?.(taskId);
    message.success('任务已删除');
  }, [onTaskDelete]);

  /**
   * 导出结果
   */
  const handleExportResult = useCallback((task: AnalysisTask) => {
    onResultExport?.(task);
    message.success('结果已导出');
  }, [onResultExport]);

  /**
   * 渲染分析参数表单
   */
  const renderAnalysisForm = () => {
    switch (selectedAnalysis) {
      case 'buffer':
        return (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Form.Item
              name="inputLayer"
              label="输入图层"
              rules={[{ required: true, message: '请选择输入图层' }]}
            >
              <Select placeholder="选择图层">
                {availableLayers.map(layer => (
                  <Option key={layer.id} value={layer.id}>
                    {layer.name} ({layer.type})
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="distance"
              label="缓冲距离"
              rules={[{ required: true, message: '请输入缓冲距离' }]}
            >
              <InputNumber
                placeholder="输入距离"
                min={0}
                style={{ width: '100%' }}
                addonAfter={
                  <Form.Item name="unit" noStyle initialValue="meters">
                    <Select style={{ width: 80 }}>
                      <Option value="meters">米</Option>
                      <Option value="kilometers">公里</Option>
                      <Option value="degrees">度</Option>
                    </Select>
                  </Form.Item>
                }
              />
            </Form.Item>

            <Form.Item
              name="segments"
              label="分段数"
              initialValue={32}
            >
              <InputNumber
                min={8}
                max={64}
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Space>
        );

      case 'intersection':
      case 'union':
      case 'difference':
        return (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Form.Item
              name="inputLayer1"
              label="输入图层1"
              rules={[{ required: true, message: '请选择第一个图层' }]}
            >
              <Select placeholder="选择第一个图层">
                {availableLayers.map(layer => (
                  <Option key={layer.id} value={layer.id}>
                    {layer.name} ({layer.type})
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="inputLayer2"
              label="输入图层2"
              rules={[{ required: true, message: '请选择第二个图层' }]}
            >
              <Select placeholder="选择第二个图层">
                {availableLayers.map(layer => (
                  <Option key={layer.id} value={layer.id}>
                    {layer.name} ({layer.type})
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Space>
        );

      case 'distance':
        return (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Form.Item
              name="inputLayers"
              label="输入图层"
              rules={[{ required: true, message: '请选择图层' }]}
            >
              <Select 
                mode="multiple"
                placeholder="选择一个或多个图层"
                maxTagCount={2}
              >
                {availableLayers.map(layer => (
                  <Option key={layer.id} value={layer.id}>
                    {layer.name} ({layer.type})
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="maxDistance"
              label="最大距离"
            >
              <InputNumber
                placeholder="留空表示无限制"
                min={0}
                style={{ width: '100%' }}
                addonAfter="米"
              />
            </Form.Item>
          </Space>
        );

      default:
        return null;
    }
  };

  /**
   * 获取状态颜色
   */
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'default';
      case 'running': return 'processing';
      case 'completed': return 'success';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  /**
   * 获取状态文本
   */
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return '等待中';
      case 'running': return '运行中';
      case 'completed': return '已完成';
      case 'error': return '失败';
      default: return '未知';
    }
  };

  return (
    <div className={`spatial-analysis-panel ${className || ''}`}>
      <Card title="空间分析工具" size="small">
        <Collapse defaultActiveKey={['analysis', 'history']} ghost>
          {/* 分析工具选择 */}
          <Panel header="分析工具" key="analysis">
            <div className="analysis-types">
              <Radio.Group 
                value={selectedAnalysis}
                onChange={(e) => setSelectedAnalysis(e.target.value)}
                style={{ width: '100%' }}
              >
                <Space direction="vertical" style={{ width: '100%' }}>
                  {analysisTypes.map(type => (
                    <Radio key={type.key} value={type.key}>
                      <Space>
                        <span style={{ color: type.color }}>{type.icon}</span>
                        <span>{type.label}</span>
                        <Tooltip title={type.description}>
                          <InfoCircleOutlined style={{ color: '#999' }} />
                        </Tooltip>
                      </Space>
                    </Radio>
                  ))}
                </Space>
              </Radio.Group>
            </div>

            <Divider />

            {/* 分析参数 */}
            <div className="analysis-parameters">
              <Form form={form} layout="vertical" size="small">
                {renderAnalysisForm()}
                
                <Form.Item style={{ marginTop: 16, marginBottom: 0 }}>
                  <Button 
                    type="primary" 
                    icon={<PlayCircleOutlined />}
                    onClick={handleRunAnalysis}
                    loading={isRunning}
                    disabled={availableLayers.length === 0}
                    block
                  >
                    运行分析
                  </Button>
                </Form.Item>
              </Form>

              {availableLayers.length === 0 && (
                <Alert
                  message="没有可用的图层"
                  description="请先添加一些地理数据图层"
                  type="info"
                  showIcon
                  style={{ marginTop: 12 }}
                />
              )}
            </div>
          </Panel>

          {/* 分析历史 */}
          <Panel 
            header={`分析历史 (${analysisTasks.length})`} 
            key="history"
          >
            <div className="analysis-history">
              <List
                size="small"
                dataSource={analysisTasks}
                locale={{ emptyText: '暂无分析任务' }}
                renderItem={(task) => (
                  <List.Item
                    actions={[
                      task.status === 'completed' && (
                        <Tooltip title="导出结果">
                          <Button 
                            size="small" 
                            type="text"
                            icon={<DownloadOutlined />}
                            onClick={() => handleExportResult(task)}
                          />
                        </Tooltip>
                      ),
                      <Tooltip title="删除任务">
                        <Button 
                          size="small" 
                          type="text" 
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => handleDeleteTask(task.id)}
                        />
                      </Tooltip>
                    ].filter(Boolean)}
                  >
                    <List.Item.Meta
                      title={
                        <Space>
                          {task.name}
                          <Tag color={getStatusColor(task.status)}>
                            {getStatusText(task.status)}
                          </Tag>
                        </Space>
                      }
                      description={
                        <div>
                          <div>类型: {analysisTypes.find(t => t.key === task.type)?.label}</div>
                          <div>创建时间: {task.createdAt.toLocaleTimeString()}</div>
                          {task.completedAt && (
                            <div>完成时间: {task.completedAt.toLocaleTimeString()}</div>
                          )}
                          {task.error && (
                            <div style={{ color: '#ff4d4f' }}>错误: {task.error}</div>
                          )}
                          {task.status === 'running' && task.progress !== undefined && (
                            <Progress 
                              percent={task.progress} 
                              size="small" 
                              style={{ marginTop: 4 }}
                            />
                          )}
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </div>
          </Panel>
        </Collapse>
      </Card>
    </div>
  );
};
