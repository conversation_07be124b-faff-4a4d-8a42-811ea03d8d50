.service-status-monitor {
  .status-summary {
    margin-bottom: 16px;
    padding: 12px;
    background: #fafafa;
    border-radius: 6px;
    
    .status-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
    }
  }
  
  .config-info {
    margin-bottom: 16px;
    padding: 12px;
    background: #f6f8fa;
    border-radius: 6px;
    
    h4 {
      margin: 0 0 8px 0;
      color: #262626;
    }
    
    .config-item {
      margin-bottom: 4px;
      font-size: 13px;
      
      strong {
        color: #595959;
        margin-right: 8px;
      }
    }
  }
  
  .test-report {
    .report-summary {
      margin-bottom: 16px;
      padding: 12px;
      background: #f0f2f5;
      border-radius: 6px;
      font-weight: 500;
    }
  }
}
