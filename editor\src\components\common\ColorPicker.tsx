/**
 * 颜色选择器组件
 * 用于选择颜色
 */
import React, { useState, useEffect } from 'react';
import { Button, Popover, Input, Space } from 'antd';

/**
 * 颜色选择器组件属性
 */
interface ColorPickerProps {
  /** 值 */
  value?: string;
  /** 变更回调 */
  onChange?: (value: string) => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 预设颜色 */
  presetColors?: string[];
}

/**
 * 颜色选择器组件
 */
const ColorPicker: React.FC<ColorPickerProps> = ({
  value = '#000000',
  onChange,
  disabled = false,
  presetColors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff', '#000000', '#ffffff']
}) => {
  const [color, setColor] = useState<string>(value);
  const [popoverVisible, setPopoverVisible] = useState<boolean>(false);

  // 当值变化时更新内部状态
  useEffect(() => {
    setColor(value);
  }, [value]);

  // 处理输入框变更
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const colorValue = e.target.value;
    setColor(colorValue);

    if (onChange) {
      onChange(colorValue);
    }
  };

  // 处理预设颜色点击
  const handlePresetColorClick = (presetColor: string) => {
    setColor(presetColor);
    if (onChange) {
      onChange(presetColor);
    }
    setPopoverVisible(false);
  };

  // 渲染颜色选择器内容
  const colorPickerContent = (
    <div style={{ padding: '8px' }}>
      <div style={{ marginBottom: '8px' }}>
        <input
          type="color"
          value={color}
          onChange={(e) => {
            const newColor = e.target.value;
            setColor(newColor);
            if (onChange) {
              onChange(newColor);
            }
          }}
          style={{ width: '100%', height: '32px', border: 'none', cursor: 'pointer' }}
        />
      </div>
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: '4px' }}>
        {presetColors.map((presetColor) => (
          <div
            key={presetColor}
            style={{
              width: '24px',
              height: '24px',
              backgroundColor: presetColor,
              border: '1px solid #d9d9d9',
              cursor: 'pointer',
              borderRadius: '2px'
            }}
            onClick={() => handlePresetColorClick(presetColor)}
          />
        ))}
      </div>
    </div>
  );

  return (
    <Space>
      <Popover
        content={colorPickerContent}
        trigger="click"
        open={popoverVisible && !disabled}
        onOpenChange={setPopoverVisible}
      >
        <Button
          style={{
            backgroundColor: color,
            width: 32,
            height: 32,
            minWidth: 32,
            border: '1px solid #d9d9d9',
            cursor: disabled ? 'not-allowed' : 'pointer',
            opacity: disabled ? 0.5 : 1
          }}
          disabled={disabled}
        />
      </Popover>
      <Input
        value={color}
        onChange={handleInputChange}
        disabled={disabled}
        style={{ width: 120 }}
        placeholder="#000000"
      />
    </Space>
  );
};

export default ColorPicker;
