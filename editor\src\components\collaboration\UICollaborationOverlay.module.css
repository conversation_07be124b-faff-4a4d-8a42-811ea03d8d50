/**
 * UICollaborationOverlay.module.css
 * 
 * UI协作编辑覆盖层样式
 */

.ui-collaboration-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9000;
  overflow: hidden;
}

/* 在线用户列表容器 */
.online-users-container {
  position: absolute;
  top: 16px;
  right: 16px;
  pointer-events: auto;
  z-index: 10001;
}

.online-users-list {
  background: var(--background-color, #ffffff);
  border: 1px solid var(--border-color, #d9d9d9);
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 200px;
}

.users-header {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-color-secondary, #8c8c8c);
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color-light, #f0f0f0);
}

.users-avatars {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.users-avatars :global(.ant-badge) {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.users-avatars :global(.ant-badge:hover) {
  transform: scale(1.1);
}

/* 用户光标 */
.user-cursor {
  animation: cursorFadeIn 0.3s ease-out;
  transition: all 0.1s ease-out;
}

.cursor-pointer {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  animation: cursorPulse 2s infinite;
}

.cursor-label {
  animation: labelSlideIn 0.3s ease-out;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 用户选择框 */
.user-selection {
  animation: selectionFadeIn 0.2s ease-out;
  transition: all 0.1s ease-out;
}

.selection-label {
  animation: labelSlideIn 0.3s ease-out;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 组件锁定指示器 */
.component-lock-indicator {
  animation: lockFadeIn 0.3s ease-out;
}

.component-lock-indicator::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid currentColor;
  border-radius: 6px;
  opacity: 0.3;
  animation: lockPulse 1.5s infinite;
}

/* 动画定义 */
@keyframes cursorFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes cursorPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes labelSlideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes selectionFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes lockFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes lockPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.02);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .online-users-container {
    top: 8px;
    right: 8px;
  }
  
  .online-users-list {
    padding: 8px;
    min-width: 150px;
  }
  
  .users-header {
    font-size: 11px;
    margin-bottom: 6px;
    padding-bottom: 6px;
  }
  
  .users-avatars {
    gap: 4px;
  }
  
  .cursor-label,
  .selection-label {
    font-size: 10px;
    padding: 1px 4px;
    max-width: 80px;
  }
}

/* 暗色主题 */
[data-theme="dark"] .ui-collaboration-overlay {
  --background-color: #141414;
  --border-color: #434343;
  --border-color-light: #303030;
  --text-color-secondary: #a6a6a6;
}

[data-theme="dark"] .online-users-list {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .cursor-pointer {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.4));
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .user-selection {
    border-width: 3px;
  }
  
  .component-lock-indicator {
    border-width: 3px;
  }
  
  .cursor-label,
  .selection-label {
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .user-cursor,
  .user-selection,
  .component-lock-indicator {
    animation: none;
    transition: none;
  }
  
  .cursor-pointer {
    animation: none;
  }
  
  .component-lock-indicator::before {
    animation: none;
  }
  
  .users-avatars :global(.ant-badge) {
    transition: none;
  }
}

/* 工具提示样式 */
.ui-collaboration-overlay :global(.ant-tooltip) {
  z-index: 10002;
}

.ui-collaboration-overlay :global(.ant-tooltip-inner) {
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 4px;
}

/* 徽章样式 */
.ui-collaboration-overlay :global(.ant-badge-dot) {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* 头像样式 */
.ui-collaboration-overlay :global(.ant-avatar) {
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.ui-collaboration-overlay :global(.ant-avatar:hover) {
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 性能优化 */
.ui-collaboration-overlay {
  contain: layout style paint;
  will-change: transform;
}

.user-cursor,
.user-selection,
.component-lock-indicator {
  contain: layout style paint;
  will-change: transform, opacity;
}

/* 打印样式 */
@media print {
  .ui-collaboration-overlay {
    display: none;
  }
}

/* 焦点状态 */
.online-users-list:focus-within {
  outline: 2px solid var(--primary-color, #1890ff);
  outline-offset: 2px;
}

/* 加载状态 */
.ui-collaboration-overlay.loading {
  opacity: 0.5;
  pointer-events: none;
}

.ui-collaboration-overlay.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32px;
  height: 32px;
  margin: -16px 0 0 -16px;
  border: 3px solid var(--border-color, #d9d9d9);
  border-top-color: var(--primary-color, #1890ff);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 错误状态 */
.ui-collaboration-overlay.error {
  opacity: 0.3;
}

.ui-collaboration-overlay.error::before {
  content: '协作连接已断开';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--error-color, #ff4d4f);
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10003;
}

/* 自定义滚动条 */
.online-users-list::-webkit-scrollbar {
  width: 4px;
}

.online-users-list::-webkit-scrollbar-track {
  background: var(--scrollbar-track-color, #f1f1f1);
  border-radius: 2px;
}

.online-users-list::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-color, #c1c1c1);
  border-radius: 2px;
}

.online-users-list::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-color, #a8a8a8);
}
