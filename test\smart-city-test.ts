/**
 * 智慧城市节点测试文件
 * 验证所有节点是否正常工作
 */

import {
  IoTDeviceConnectNode,
  IoTDataProcessingNode,
  TrafficFlowMonitorNode,
  SmartTrafficLightNode,
  AirQualityMonitorNode,
  SmartStreetLightNode,
  EmergencyResponseNode
} from '../engine/src/visualscript/nodes/smartcity/SmartCityNodes';

import { registerSmartCityNodes } from '../engine/src/visualscript/nodes/smartcity/SmartCityNodeRegistry';
import { SmartCityApplication } from '../examples/smart-city-demo/SmartCityApplication';

/**
 * 测试所有智慧城市节点
 */
async function testSmartCityNodes(): Promise<void> {
  console.log('🚀 开始测试智慧城市节点...\n');

  // 测试节点注册
  console.log('📋 测试节点注册...');
  const nodeRegistry = registerSmartCityNodes();
  console.log(`✅ 成功注册 ${nodeRegistry.size} 个节点\n`);

  // 测试IoT设备连接节点
  console.log('🔌 测试IoT设备连接节点...');
  const iotConnectNode = new IoTDeviceConnectNode();
  
  // 设置输入值
  iotConnectNode['inputs'].get('deviceId').value = 'test_sensor_001';
  iotConnectNode['inputs'].get('protocol').value = 'MQTT';
  iotConnectNode['inputs'].get('endpoint').value = 'mqtt://test.local:1883';
  iotConnectNode['inputs'].get('config').value = { type: 'temperature_sensor' };
  
  await iotConnectNode.execute();
  console.log('✅ IoT设备连接节点测试完成\n');

  // 测试IoT数据处理节点
  console.log('📊 测试IoT数据处理节点...');
  const iotProcessingNode = new IoTDataProcessingNode();
  
  iotProcessingNode['inputs'].get('rawData').value = { temperature: 35, humidity: 80 };
  iotProcessingNode['inputs'].get('processingType').value = 'anomaly_detect';
  iotProcessingNode['inputs'].get('threshold').value = {
    temperature: { min: 0, max: 30 },
    humidity: { min: 0, max: 70 }
  };
  
  iotProcessingNode.execute();
  console.log('✅ IoT数据处理节点测试完成\n');

  // 测试交通流量监测节点
  console.log('🚗 测试交通流量监测节点...');
  const trafficMonitorNode = new TrafficFlowMonitorNode();
  
  trafficMonitorNode['inputs'].get('roadId').value = 'test_road_001';
  trafficMonitorNode['inputs'].get('analysisInterval').value = 60;
  
  trafficMonitorNode.execute();
  console.log('✅ 交通流量监测节点测试完成\n');

  // 测试智能信号灯控制节点
  console.log('🚦 测试智能信号灯控制节点...');
  const trafficLightNode = new SmartTrafficLightNode();
  
  trafficLightNode['inputs'].get('intersectionId').value = 'test_intersection_001';
  trafficLightNode['inputs'].get('currentPhase').value = 'north_south';
  trafficLightNode['inputs'].get('trafficData').value = { congestionLevel: 'heavy' };
  
  trafficLightNode.execute();
  console.log('✅ 智能信号灯控制节点测试完成\n');

  // 测试空气质量监测节点
  console.log('🌬️ 测试空气质量监测节点...');
  const airQualityNode = new AirQualityMonitorNode();
  
  airQualityNode['inputs'].get('sensorData').value = { pm25: 75, pm10: 120 };
  airQualityNode['inputs'].get('location').value = { lat: 39.9042, lng: 116.4074 };
  
  airQualityNode.execute();
  console.log('✅ 空气质量监测节点测试完成\n');

  // 测试智慧路灯管理节点
  console.log('💡 测试智慧路灯管理节点...');
  const streetLightNode = new SmartStreetLightNode();
  
  streetLightNode['inputs'].get('lightId').value = 'test_light_001';
  streetLightNode['inputs'].get('ambientLight').value = 20;
  streetLightNode['inputs'].get('motionDetected').value = true;
  streetLightNode['inputs'].get('timeOfDay').value = 'night';
  streetLightNode['inputs'].get('energyMode').value = 'eco';
  
  streetLightNode.execute();
  console.log('✅ 智慧路灯管理节点测试完成\n');

  // 测试应急响应管理节点
  console.log('🚨 测试应急响应管理节点...');
  const emergencyNode = new EmergencyResponseNode();
  
  emergencyNode['inputs'].get('eventType').value = 'fire';
  emergencyNode['inputs'].get('location').value = { lat: 39.9042, lng: 116.4074 };
  emergencyNode['inputs'].get('severity').value = 'high';
  emergencyNode['inputs'].get('description').value = '建筑物火灾';
  emergencyNode['inputs'].get('availableResources').value = [
    { type: 'fire_truck', count: 2 },
    { type: 'ambulance', count: 1 }
  ];
  
  emergencyNode.execute();
  console.log('✅ 应急响应管理节点测试完成\n');

  console.log('🎉 所有智慧城市节点测试完成！');
}

/**
 * 测试智慧城市应用
 */
async function testSmartCityApplication(): Promise<void> {
  console.log('\n🏙️ 测试智慧城市应用...');
  
  try {
    const app = new SmartCityApplication();
    await app.start();
    
    // 等待一段时间让模拟数据生成
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    const stats = app.getCityStats();
    console.log('📈 城市统计信息:', stats);
    
    console.log('✅ 智慧城市应用测试完成\n');
  } catch (error) {
    console.error('❌ 智慧城市应用测试失败:', error);
  }
}

/**
 * 运行所有测试
 */
async function runAllTests(): Promise<void> {
  console.log('🧪 智慧城市系统测试开始\n');
  console.log('=' .repeat(50));
  
  try {
    await testSmartCityNodes();
    await testSmartCityApplication();
    
    console.log('=' .repeat(50));
    console.log('🎊 所有测试完成！智慧城市系统运行正常！');
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

export {
  testSmartCityNodes,
  testSmartCityApplication,
  runAllTests
};
