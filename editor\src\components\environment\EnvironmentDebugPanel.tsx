/**
 * 环境调试面板
 * 
 * 该面板提供环境感知和响应系统的调试功能，包括日志、状态检查等。
 */
import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Tabs,
  Button,
  Space,
  List,
  Tag,
  Tooltip,
  Switch,
  Select,
  Typography,
  Badge,
  Collapse,
  Input,
  message
} from 'antd';
import {
  BugOutlined,
  ClearOutlined,
  DownloadOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

// 本地类型定义
export enum EnvironmentType {
  INDOOR = 'indoor',
  OUTDOOR = 'outdoor',
  UNDERWATER = 'underwater',
  SPACE = 'space',
  CAVE = 'cave',
  FOREST = 'forest',
  DESERT = 'desert',
  SNOW = 'snow',
  URBAN = 'urban',
  CUSTOM = 'custom'
}

export enum WeatherType {
  CLEAR = 'clear',
  CLOUDY = 'cloudy',
  RAINY = 'rainy',
  STORMY = 'stormy',
  SNOWY = 'snowy',
  FOGGY = 'foggy',
  CUSTOM = 'custom'
}

export enum TerrainType {
  FLAT = 'flat',
  HILLS = 'hills',
  MOUNTAINS = 'mountains',
  WATER = 'water',
  URBAN = 'urban',
  CUSTOM = 'custom'
}

export interface EnvironmentAwarenessData {
  environmentType: EnvironmentType;
  weatherType: WeatherType;
  terrainType: TerrainType;
  lightIntensity: number;
  temperature: number;
  humidity: number;
  windSpeed: number;
  windDirection: { x: number; y: number; z: number };
  noiseLevel: number;
  airQuality: number;
  waterLevel: number;
  visibility: number;
  timeOfDay: number;
  customParameters: Map<string, any>;
  lastEnvironmentChangeTime: number;
  awarenessRange: number;
}

const { TabPane } = Tabs;
const { Option } = Select;
const { Text } = Typography;
const { Panel } = Collapse;
const { Search } = Input;

/**
 * 日志级别枚举
 */
enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error'
}

/**
 * 日志项接口
 */
interface LogItem {
  id: string;
  timestamp: number;
  level: LogLevel;
  message: string;
  details?: any;
}

/**
 * 环境调试面板属性接口
 */
interface EnvironmentDebugPanelProps {
  entityId?: string;
  environmentData?: EnvironmentAwarenessData;
  activeResponses?: Map<string, any[]>;
  logs?: LogItem[];
  onClearLogs?: () => void;
  onRefresh?: () => void;
  onToggleDebug?: (enabled: boolean) => void;
  debugEnabled?: boolean;
}

/**
 * 环境调试面板组件
 */
const EnvironmentDebugPanel: React.FC<EnvironmentDebugPanelProps> = ({
  entityId,
  environmentData,
  activeResponses = new Map(),
  logs = [],
  onClearLogs,
  onRefresh,
  onToggleDebug,
  debugEnabled = false
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<string>('logs');
  const [filteredLogs, setFilteredLogs] = useState<LogItem[]>(logs);
  const [logLevelFilter, setLogLevelFilter] = useState<string[]>(['debug', 'info', 'warning', 'error']);
  const [searchValue, setSearchValue] = useState<string>('');
  const [autoScroll, setAutoScroll] = useState<boolean>(true);
  const logsEndRef = useRef<HTMLDivElement>(null);

  // 翻译辅助函数
  const translate = (key: string, defaultValue: string = ''): string => {
    const result = t(key);
    return typeof result === 'string' ? result : defaultValue;
  };

  // 过滤日志
  useEffect(() => {
    filterLogs(logs, logLevelFilter, searchValue);
  }, [logs, logLevelFilter, searchValue]);

  // 自动滚动到底部
  useEffect(() => {
    if (autoScroll && logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [filteredLogs, autoScroll]);

  /**
   * 过滤日志
   * @param logs 日志
   * @param levels 级别
   * @param search 搜索
   */
  const filterLogs = (logs: LogItem[], levels: string[], search: string) => {
    let filtered = logs.filter(log => levels.includes(log.level));
    
    if (search) {
      const lowerSearch = search.toLowerCase();
      filtered = filtered.filter(log => 
        log.message.toLowerCase().includes(lowerSearch) || 
        (log.details && JSON.stringify(log.details).toLowerCase().includes(lowerSearch))
      );
    }
    
    setFilteredLogs(filtered);
  };

  /**
   * 处理日志级别过滤变更
   * @param levels 级别
   */
  const handleLogLevelFilterChange = (levels: string[]) => {
    setLogLevelFilter(levels);
  };

  /**
   * 处理搜索
   * @param value 搜索值
   */
  const handleSearch = (value: string) => {
    setSearchValue(value);
  };

  /**
   * 处理清除日志
   */
  const handleClearLogs = () => {
    if (onClearLogs) {
      onClearLogs();
      message.success(translate('environment.logsCleared', '日志已清除'));
    }
  };

  /**
   * 处理刷新
   */
  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
      message.success(translate('environment.dataRefreshed', '数据已刷新'));
    }
  };

  /**
   * 处理切换调试
   */
  const handleToggleDebug = () => {
    if (onToggleDebug) {
      onToggleDebug(!debugEnabled);
    }
  };

  /**
   * 处理下载日志
   */
  const handleDownloadLogs = () => {
    try {
      // 创建要下载的数据
      const dataToDownload = {
        logs: logs,
        timestamp: new Date().toISOString(),
        entityId
      };

      // 转换为JSON字符串
      const jsonString = JSON.stringify(dataToDownload, null, 2);
      
      // 创建Blob对象
      const blob = new Blob([jsonString], { type: 'application/json' });
      
      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `environment_logs_${new Date().toISOString()}.json`;
      
      // 触发下载
      document.body.appendChild(a);
      a.click();
      
      // 清理
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      message.success(translate('environment.logsDownloaded', '日志已下载'));
    } catch (error) {
      console.error('下载日志失败:', error);
      message.error(translate('environment.downloadFailed', '下载失败'));
    }
  };

  /**
   * 获取日志级别标签
   * @param level 级别
   * @returns 标签
   */
  const getLogLevelTag = (level: LogLevel) => {
    switch (level) {
      case LogLevel.DEBUG:
        return <Tag color="blue">{translate('environment.debug', '调试')}</Tag>;
      case LogLevel.INFO:
        return <Tag color="green">{translate('environment.info', '信息')}</Tag>;
      case LogLevel.WARNING:
        return <Tag color="orange">{translate('environment.warning', '警告')}</Tag>;
      case LogLevel.ERROR:
        return <Tag color="red">{translate('environment.error', '错误')}</Tag>;
      default:
        return <Tag>{level}</Tag>;
    }
  };

  /**
   * 格式化时间戳
   * @param timestamp 时间戳
   * @returns 格式化的时间
   */
  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString() + '.' + date.getMilliseconds().toString().padStart(3, '0');
  };

  /**
   * 渲染日志标签页
   */
  const renderLogsTab = () => {
    return (
      <div className="logs-tab">
        <div className="logs-controls">
          <Space style={{ marginBottom: 16 }}>
            <Select
              mode="multiple"
              placeholder={translate('environment.selectLogLevels', '选择日志级别')}
              value={logLevelFilter}
              onChange={handleLogLevelFilterChange}
              style={{ minWidth: 200 }}
            >
              <Option value="debug">{translate('environment.debug', '调试')}</Option>
              <Option value="info">{translate('environment.info', '信息')}</Option>
              <Option value="warning">{translate('environment.warning', '警告')}</Option>
              <Option value="error">{translate('environment.error', '错误')}</Option>
            </Select>

            <Search
              placeholder={translate('environment.searchLogs', '搜索日志')}
              allowClear
              onSearch={handleSearch}
              style={{ width: 200 }}
            />

            <Switch
              checkedChildren={translate('environment.autoScroll', '自动滚动')}
              unCheckedChildren={translate('environment.manualScroll', '手动滚动')}
              checked={autoScroll}
              onChange={setAutoScroll}
            />

            <Button
              icon={<ClearOutlined />}
              onClick={handleClearLogs}
            >
              {translate('environment.clearLogs', '清除日志')}
            </Button>

            <Button
              icon={<DownloadOutlined />}
              onClick={handleDownloadLogs}
            >
              {translate('environment.downloadLogs', '下载日志')}
            </Button>
          </Space>
        </div>
        
        <div className="logs-container" style={{ maxHeight: 400, overflow: 'auto' }}>
          {filteredLogs.length > 0 ? (
            <List
              itemLayout="horizontal"
              dataSource={filteredLogs}
              renderItem={log => (
                <List.Item key={log.id}>
                  <Space>
                    <Text type="secondary">[{formatTimestamp(log.timestamp)}]</Text>
                    {getLogLevelTag(log.level)}
                    <Text>{log.message}</Text>
                    {log.details && (
                      <Tooltip title={JSON.stringify(log.details, null, 2)}>
                        <InfoCircleOutlined />
                      </Tooltip>
                    )}
                  </Space>
                </List.Item>
              )}
            />
          ) : (
            <div className="empty-logs">
              <p>{translate('environment.noLogs', '暂无日志')}</p>
            </div>
          )}
          <div ref={logsEndRef} />
        </div>
      </div>
    );
  };

  /**
   * 渲染活动响应标签页
   */
  const renderActiveResponsesTab = () => {
    const responseEntries = Array.from(activeResponses.entries());
    
    return (
      <div className="active-responses-tab">
        <div className="responses-header">
          <Space style={{ marginBottom: 16 }}>
            <Badge count={responseEntries.length} overflowCount={99} style={{ backgroundColor: '#52c41a' }}>
              <Tag>{translate('environment.activeResponses', '活动响应')}</Tag>
            </Badge>

            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
            >
              {translate('environment.refresh', '刷新')}
            </Button>
          </Space>
        </div>
        
        <div className="responses-container">
          {responseEntries.length > 0 ? (
            <Collapse defaultActiveKey={responseEntries.map(([id]) => id)}>
              {responseEntries.map(([ruleId, actions]) => (
                <Panel 
                  key={ruleId} 
                  header={
                    <Space>
                      <Text strong>{ruleId}</Text>
                      <Badge count={actions.length} overflowCount={99} style={{ backgroundColor: '#108ee9' }}>
                        <Tag>{translate('environment.actions', '动作')}</Tag>
                      </Badge>
                    </Space>
                  }
                >
                  <List
                    itemLayout="horizontal"
                    dataSource={actions}
                    renderItem={(action: any, index) => (
                      <List.Item key={index}>
                        <Space>
                          <Tag color="blue">{action.type || '未知类型'}</Tag>
                          <Text>{JSON.stringify(action.params || {})}</Text>
                        </Space>
                      </List.Item>
                    )}
                  />
                </Panel>
              ))}
            </Collapse>
          ) : (
            <div className="empty-responses">
              <p>{translate('environment.noActiveResponses', '暂无活动响应')}</p>
            </div>
          )}
        </div>
      </div>
    );
  };

  /**
   * 渲染环境状态标签页
   */
  const renderEnvironmentStateTab = () => {
    if (!environmentData) {
      return (
        <div className="no-data">
          <p>{translate('environment.noData', '暂无环境数据')}</p>
          <Button type="primary" onClick={handleRefresh}>
            {translate('environment.refresh', '刷新')}
          </Button>
        </div>
      );
    }

    return (
      <div className="environment-state-tab">
        <div className="state-header">
          <Space style={{ marginBottom: 16 }}>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
            >
              {translate('environment.refresh', '刷新')}
            </Button>
          </Space>
        </div>

        <Collapse defaultActiveKey={['basic', 'conditions', 'other']}>
          <Panel header={translate('environment.basicInfo', '基本信息')} key="basic">
            <List>
              <List.Item>
                <Text strong>{translate('environment.environmentType', '环境类型')}:</Text>
                <Text>{environmentData.environmentType}</Text>
              </List.Item>
              <List.Item>
                <Text strong>{translate('environment.weatherType', '天气类型')}:</Text>
                <Text>{environmentData.weatherType}</Text>
              </List.Item>
              <List.Item>
                <Text strong>{translate('environment.terrainType', '地形类型')}:</Text>
                <Text>{environmentData.terrainType}</Text>
              </List.Item>
              <List.Item>
                <Text strong>{translate('environment.timeOfDay', '时间')}:</Text>
                <Text>{Math.floor(environmentData.timeOfDay)}:{(environmentData.timeOfDay % 1 * 60).toFixed(0).padStart(2, '0')}</Text>
              </List.Item>
              <List.Item>
                <Text strong>{translate('environment.lastChangeTime', '最后变更时间')}:</Text>
                <Text>{new Date(environmentData.lastEnvironmentChangeTime).toLocaleString()}</Text>
              </List.Item>
            </List>
          </Panel>
          
          <Panel header={translate('environment.conditions', '环境条件')} key="conditions">
            <List>
              <List.Item>
                <Text strong>{translate('environment.temperature', '温度')}:</Text>
                <Text>{environmentData.temperature.toFixed(1)}°C</Text>
              </List.Item>
              <List.Item>
                <Text strong>{translate('environment.humidity', '湿度')}:</Text>
                <Text>{(environmentData.humidity * 100).toFixed(0)}%</Text>
              </List.Item>
              <List.Item>
                <Text strong>{translate('environment.windSpeed', '风速')}:</Text>
                <Text>{environmentData.windSpeed.toFixed(1)} m/s</Text>
              </List.Item>
              <List.Item>
                <Text strong>{translate('environment.windDirection', '风向')}:</Text>
                <Text>X: {environmentData.windDirection.x.toFixed(2)}, Y: {environmentData.windDirection.y.toFixed(2)}, Z: {environmentData.windDirection.z.toFixed(2)}</Text>
              </List.Item>
              <List.Item>
                <Text strong>{translate('environment.visibility', '能见度')}:</Text>
                <Text>{environmentData.visibility.toFixed(0)} m</Text>
              </List.Item>
            </List>
          </Panel>
          
          <Panel header={translate('environment.other', '其他')} key="other">
            <List>
              <List.Item>
                <Text strong>{translate('environment.lightIntensity', '光照强度')}:</Text>
                <Text>{(environmentData.lightIntensity * 100).toFixed(0)}%</Text>
              </List.Item>
              <List.Item>
                <Text strong>{translate('environment.noiseLevel', '噪音等级')}:</Text>
                <Text>{(environmentData.noiseLevel * 100).toFixed(0)}%</Text>
              </List.Item>
              <List.Item>
                <Text strong>{translate('environment.airQuality', '空气质量')}:</Text>
                <Text>{(environmentData.airQuality * 100).toFixed(0)}%</Text>
              </List.Item>
              <List.Item>
                <Text strong>{translate('environment.waterLevel', '水位')}:</Text>
                <Text>{environmentData.waterLevel.toFixed(1)} m</Text>
              </List.Item>
              <List.Item>
                <Text strong>{translate('environment.awarenessRange', '感知范围')}:</Text>
                <Text>{environmentData.awarenessRange.toFixed(0)} m</Text>
              </List.Item>
            </List>
          </Panel>

          <Panel header={translate('environment.customParameters', '自定义参数')} key="custom">
            {environmentData.customParameters.size > 0 ? (
              <List>
                {Array.from(environmentData.customParameters.entries()).map(([key, value]) => (
                  <List.Item key={key}>
                    <Text strong>{key}:</Text>
                    <Text>{JSON.stringify(value)}</Text>
                  </List.Item>
                ))}
              </List>
            ) : (
              <p>{translate('environment.noCustomParameters', '暂无自定义参数')}</p>
            )}
          </Panel>
        </Collapse>
      </div>
    );
  };

  return (
    <div className="environment-debug-panel">
      <Card
        title={
          <Space>
            <BugOutlined />
            <span>{translate('environment.debugPanel', '环境调试面板')}</span>
          </Space>
        }
        extra={
          <Space>
            <Tooltip title={debugEnabled ? translate('environment.disableDebug', '禁用调试') : translate('environment.enableDebug', '启用调试')}>
              <Switch
                checkedChildren={<BugOutlined />}
                unCheckedChildren={<BugOutlined />}
                checked={debugEnabled}
                onChange={handleToggleDebug}
              />
            </Tooltip>
            <Tooltip title={translate('environment.refresh', '刷新')}>
              <Button
                type="text"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              />
            </Tooltip>
          </Space>
        }
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <span>
                <InfoCircleOutlined />
                {translate('environment.logs', '日志')}
              </span>
            }
            key="logs"
          >
            {renderLogsTab()}
          </TabPane>
          <TabPane
            tab={
              <span>
                <CheckCircleOutlined />
                {translate('environment.activeResponses', '活动响应')}
              </span>
            }
            key="activeResponses"
          >
            {renderActiveResponsesTab()}
          </TabPane>
          <TabPane
            tab={
              <span>
                <SettingOutlined />
                {translate('environment.environmentState', '环境状态')}
              </span>
            }
            key="environmentState"
          >
            {renderEnvironmentStateTab()}
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default EnvironmentDebugPanel;
