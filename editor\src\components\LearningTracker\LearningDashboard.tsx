import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Progress, Tag, Timeline, Spin, Alert } from 'antd';
import { 
  UserOutlined, 
  BookOutlined, 
  TrophyOutlined, 
  ClockCircleOutlined,
  HeartOutlined,
  BulbOutlined,
  RocketOutlined
} from '@ant-design/icons';
import { useLearningData } from './LearningDataProvider';
import { LearningAnalyticsService } from '../../services/LearningAnalyticsService';

/**
 * 学习分析仪表板组件
 * 展示用户的学习画像、进度和推荐
 */
export const LearningDashboard: React.FC<{ userId: string }> = ({ userId }) => {
  const { stats, realtimeState } = useLearningData();
  const [analyticsData, setAnalyticsData] = useState<any>(null);
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const analyticsService = new LearningAnalyticsService();

  /**
   * 加载学习分析数据
   */
  useEffect(() => {
    loadAnalyticsData();
  }, [userId]);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 获取学习分析报告
      const analytics = await analyticsService.getLearningAnalytics(userId);
      setAnalyticsData(analytics);

      // 获取个性化推荐
      const recs = await analyticsService.getRecommendations(userId, { limit: 5 });
      setRecommendations(recs);

    } catch (err: any) {
      setError(err.message || '加载学习数据失败');
      console.error('加载学习分析数据失败:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>正在加载学习数据...</p>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="加载失败"
        description={error}
        type="error"
        showIcon
        action={
          <button onClick={loadAnalyticsData} style={{ border: 'none', background: 'none', color: '#1890ff', cursor: 'pointer' }}>
            重试
          </button>
        }
      />
    );
  }

  const profile = analyticsData?.profile;
  const progressSummary = analyticsData?.progressSummary;
  const insights = analyticsData?.insights || [];

  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      <h1 style={{ marginBottom: 24 }}>学习分析仪表板</h1>

      {/* 实时状态卡片 */}
      {realtimeState && (
        <Card title="实时学习状态" style={{ marginBottom: 24 }}>
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="学习状态"
                value={realtimeState.isActive ? '进行中' : '已暂停'}
                prefix={<UserOutlined />}
                valueStyle={{ color: realtimeState.isActive ? '#3f8600' : '#cf1322' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="注意力水平"
                value={Math.round(realtimeState.attentionLevel * 100)}
                suffix="%"
                prefix={<BulbOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="参与度"
                value={Math.round(realtimeState.engagementLevel * 100)}
                suffix="%"
                prefix={<HeartOutlined />}
              />
            </Col>
            <Col span={6}>
              <div>
                <p style={{ margin: 0, fontSize: 14, color: '#666' }}>当前难度</p>
                <Tag color={
                  realtimeState.difficultyLevel === 'easy' ? 'green' :
                  realtimeState.difficultyLevel === 'medium' ? 'orange' : 'red'
                }>
                  {realtimeState.difficultyLevel === 'easy' ? '简单' :
                   realtimeState.difficultyLevel === 'medium' ? '中等' : '困难'}
                </Tag>
              </div>
            </Col>
          </Row>
        </Card>
      )}

      {/* 学习概览 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总学习活动"
              value={stats?.totalEvents || 0}
              prefix={<BookOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="知识领域"
              value={progressSummary?.totalKnowledgeAreas || 0}
              prefix={<BulbOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="掌握领域"
              value={progressSummary?.masteredAreas || 0}
              prefix={<TrophyOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均置信度"
              value={Math.round((progressSummary?.averageConfidence || 0) * 100)}
              suffix="%"
              prefix={<RocketOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        {/* 学习进度 */}
        <Col span={12}>
          <Card title="学习进度" style={{ marginBottom: 24 }}>
            <div style={{ marginBottom: 16 }}>
              <p>整体进度</p>
              <Progress 
                percent={Math.round((progressSummary?.overallProgress || 0) * 100)}
                status="active"
              />
            </div>
            
            {profile?.knowledgeAreas && Object.entries(profile.knowledgeAreas).map(([area, knowledge]: [string, any]) => (
              <div key={area} style={{ marginBottom: 12 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                  <span>{area}</span>
                  <span>{Math.round(knowledge.confidence * 100)}%</span>
                </div>
                <Progress 
                  percent={Math.round(knowledge.confidence * 100)}
                  size="small"
                  status={knowledge.confidence > 0.8 ? 'success' : knowledge.confidence > 0.5 ? 'active' : 'exception'}
                />
              </div>
            ))}
          </Card>
        </Col>

        {/* 学习偏好 */}
        <Col span={12}>
          <Card title="学习特征" style={{ marginBottom: 24 }}>
            {profile?.learningPreferences && (
              <div>
                <div style={{ marginBottom: 16 }}>
                  <p><strong>学习节奏:</strong> 
                    <Tag color="blue" style={{ marginLeft: 8 }}>
                      {profile.learningPreferences.learningPace === 'fast' ? '快速' :
                       profile.learningPreferences.learningPace === 'medium' ? '中等' : '缓慢'}
                    </Tag>
                  </p>
                </div>
                
                <div style={{ marginBottom: 16 }}>
                  <p><strong>交互风格:</strong>
                    <Tag color="green" style={{ marginLeft: 8 }}>
                      {profile.learningPreferences.interactionStyle === 'active' ? '主动型' :
                       profile.learningPreferences.interactionStyle === 'exploratory' ? '探索型' : '被动型'}
                    </Tag>
                  </p>
                </div>

                <div style={{ marginBottom: 16 }}>
                  <p><strong>偏好难度:</strong>
                    <Tag color="orange" style={{ marginLeft: 8 }}>
                      {profile.learningPreferences.preferredDifficulty === 'hard' ? '困难' :
                       profile.learningPreferences.preferredDifficulty === 'medium' ? '中等' : '简单'}
                    </Tag>
                  </p>
                </div>

                <div style={{ marginBottom: 16 }}>
                  <p><strong>平均会话时长:</strong> {Math.round(profile.learningPreferences.sessionDuration)}分钟</p>
                </div>

                <div>
                  <p><strong>偏好内容类型:</strong></p>
                  {profile.learningPreferences.preferredContentTypes?.map((type: string) => (
                    <Tag key={type} style={{ marginBottom: 4 }}>
                      {type === 'video' ? '视频' :
                       type === 'article' ? '文章' :
                       type === 'interactive' ? '互动' :
                       type === 'exercise' ? '练习' : type}
                    </Tag>
                  ))}
                </div>
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 个性化推荐 */}
      <Card title="个性化推荐" style={{ marginBottom: 24 }}>
        <Row gutter={16}>
          {recommendations.map((rec, index) => (
            <Col span={8} key={rec.id || index}>
              <Card 
                size="small" 
                title={rec.title}
                extra={
                  <Tag color={
                    rec.difficulty === 'easy' ? 'green' :
                    rec.difficulty === 'medium' ? 'orange' : 'red'
                  }>
                    {rec.difficulty === 'easy' ? '简单' :
                     rec.difficulty === 'medium' ? '中等' : '困难'}
                  </Tag>
                }
                style={{ marginBottom: 16 }}
              >
                <p style={{ fontSize: 12, color: '#666', marginBottom: 8 }}>
                  {rec.description}
                </p>
                <p style={{ fontSize: 12, marginBottom: 8 }}>
                  <ClockCircleOutlined /> {rec.estimatedDuration}分钟
                </p>
                <p style={{ fontSize: 12, color: '#1890ff', marginBottom: 0 }}>
                  推荐理由: {rec.reason}
                </p>
                <div style={{ marginTop: 8 }}>
                  <Progress 
                    percent={Math.round(rec.relevanceScore * 100)}
                    size="small"
                    format={() => `相关度 ${Math.round(rec.relevanceScore * 100)}%`}
                  />
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 学习洞察 */}
      {insights.length > 0 && (
        <Card title="学习洞察">
          <Timeline>
            {insights.map((insight: string, index: number) => (
              <Timeline.Item key={index} dot={<BulbOutlined />}>
                {insight}
              </Timeline.Item>
            ))}
          </Timeline>
        </Card>
      )}
    </div>
  );
};
