#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 递归查找所有 .tsx 和 .ts 文件
function findFiles(dir, extensions = ['.tsx', '.ts']) {
  let results = [];
  try {
    const list = fs.readdirSync(dir);
    
    list.forEach(file => {
      const filePath = path.join(dir, file);
      try {
        const stat = fs.statSync(filePath);
        
        if (stat && stat.isDirectory()) {
          // 跳过不需要的目录
          if (!['node_modules', '.git', 'dist', 'build', 'coverage'].includes(file)) {
            results = results.concat(findFiles(filePath, extensions));
          }
        } else {
          const ext = path.extname(file);
          if (extensions.includes(ext)) {
            results.push(filePath);
          }
        }
      } catch (err) {
        // 忽略无法访问的文件
      }
    });
  } catch (err) {
    // 忽略无法访问的目录
  }
  
  return results;
}

// 修复单个文件
function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 修复从 ../../../engine/src/ 的导入
    if (content.includes('../../../engine/src/')) {
      content = content.replace(/from\s+['"]\.\.\/\.\.\/\.\.\/engine\/src\/[^'"]*['"];?/g, "from '../../libs';");
      modified = true;
    }
    
    // 修复从 ../../engine/src/ 的导入
    if (content.includes('../../engine/src/')) {
      content = content.replace(/from\s+['"]\.\.\/\.\.\/engine\/src\/[^'"]*['"];?/g, "from '../libs';");
      modified = true;
    }
    
    // 修复从 ../engine/src/ 的导入
    if (content.includes('../engine/src/')) {
      content = content.replace(/from\s+['"]\.\.\/engine\/src\/[^'"]*['"];?/g, "from './libs';");
      modified = true;
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`错误处理文件 ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src 目录不存在');
    process.exit(1);
  }
  
  console.log('开始修复引擎导入问题...');
  
  const files = findFiles(srcDir);
  let fixedCount = 0;
  
  console.log(`找到 ${files.length} 个文件`);
  
  files.forEach(file => {
    if (fixFile(file)) {
      console.log(`✓ 修复了 ${path.relative(__dirname, file)}`);
      fixedCount++;
    }
  });
  
  console.log(`\n修复完成！共修复了 ${fixedCount} 个文件。`);
}

main();
