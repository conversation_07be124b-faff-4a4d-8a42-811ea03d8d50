// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC721/IERC721.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Counters.sol";
import "@openzeppelin/contracts/interfaces/IERC2981.sol";

/**
 * @title DLEngineMarketplace
 * @dev DL引擎NFT市场合约
 * 支持固定价格销售、拍卖、版税分配等功能
 */
contract DLEngineMarketplace is ReentrancyGuard, Pausable, Ownable {
    using Counters for Counters.Counter;

    // 计数器
    Counters.Counter private _listingIdCounter;
    Counters.Counter private _auctionIdCounter;

    // 结构体
    struct Listing {
        uint256 listingId;
        address seller;
        address nftContract;
        uint256 tokenId;
        uint256 price;
        address paymentToken; // address(0) for ETH
        bool active;
        uint256 listedAt;
        uint256 expiresAt;
    }

    struct Auction {
        uint256 auctionId;
        address seller;
        address nftContract;
        uint256 tokenId;
        uint256 startingPrice;
        uint256 reservePrice;
        uint256 currentBid;
        address currentBidder;
        address paymentToken;
        uint256 startTime;
        uint256 endTime;
        bool active;
        bool settled;
    }

    struct Bid {
        address bidder;
        uint256 amount;
        uint256 timestamp;
    }

    // 映射
    mapping(uint256 => Listing) public listings;
    mapping(uint256 => Auction) public auctions;
    mapping(uint256 => Bid[]) public auctionBids;
    mapping(address => bool) public approvedNFTContracts;
    mapping(address => bool) public approvedPaymentTokens;
    mapping(address => uint256) public pendingWithdrawals;

    // 费用设置
    uint256 public marketplaceFee = 250; // 2.5% (基点)
    uint256 public constant MAX_FEE = 1000; // 10%
    address public feeRecipient;

    // 拍卖设置
    uint256 public minAuctionDuration = 1 hours;
    uint256 public maxAuctionDuration = 30 days;
    uint256 public bidExtensionTime = 15 minutes;
    uint256 public minBidIncrement = 500; // 5% (基点)

    // 事件
    event ItemListed(
        uint256 indexed listingId,
        address indexed seller,
        address indexed nftContract,
        uint256 tokenId,
        uint256 price,
        address paymentToken
    );

    event ItemSold(
        uint256 indexed listingId,
        address indexed buyer,
        address indexed seller,
        uint256 price,
        address paymentToken
    );

    event ListingCancelled(uint256 indexed listingId);

    event AuctionCreated(
        uint256 indexed auctionId,
        address indexed seller,
        address indexed nftContract,
        uint256 tokenId,
        uint256 startingPrice,
        uint256 reservePrice,
        uint256 endTime
    );

    event BidPlaced(
        uint256 indexed auctionId,
        address indexed bidder,
        uint256 amount,
        uint256 endTime
    );

    event AuctionSettled(
        uint256 indexed auctionId,
        address indexed winner,
        uint256 winningBid
    );

    event AuctionCancelled(uint256 indexed auctionId);

    constructor(address _feeRecipient) {
        require(_feeRecipient != address(0), "Invalid fee recipient");
        feeRecipient = _feeRecipient;
        
        // 默认支持ETH支付
        approvedPaymentTokens[address(0)] = true;
    }

    /**
     * @dev 上架NFT
     */
    function listItem(
        address nftContract,
        uint256 tokenId,
        uint256 price,
        address paymentToken,
        uint256 duration
    ) external nonReentrant whenNotPaused {
        require(approvedNFTContracts[nftContract], "NFT contract not approved");
        require(approvedPaymentTokens[paymentToken], "Payment token not approved");
        require(price > 0, "Price must be greater than 0");
        require(duration > 0, "Duration must be greater than 0");

        IERC721 nft = IERC721(nftContract);
        require(nft.ownerOf(tokenId) == msg.sender, "Not the owner");
        require(
            nft.isApprovedForAll(msg.sender, address(this)) || 
            nft.getApproved(tokenId) == address(this),
            "Marketplace not approved"
        );

        uint256 listingId = _listingIdCounter.current();
        _listingIdCounter.increment();

        uint256 expiresAt = block.timestamp + duration;

        listings[listingId] = Listing({
            listingId: listingId,
            seller: msg.sender,
            nftContract: nftContract,
            tokenId: tokenId,
            price: price,
            paymentToken: paymentToken,
            active: true,
            listedAt: block.timestamp,
            expiresAt: expiresAt
        });

        emit ItemListed(listingId, msg.sender, nftContract, tokenId, price, paymentToken);
    }

    /**
     * @dev 购买NFT
     */
    function buyItem(uint256 listingId) external payable nonReentrant whenNotPaused {
        Listing storage listing = listings[listingId];
        require(listing.active, "Listing not active");
        require(block.timestamp <= listing.expiresAt, "Listing expired");
        require(msg.sender != listing.seller, "Cannot buy your own item");

        IERC721 nft = IERC721(listing.nftContract);
        require(nft.ownerOf(listing.tokenId) == listing.seller, "Seller no longer owns the token");

        uint256 totalPrice = listing.price;
        
        // 处理支付
        if (listing.paymentToken == address(0)) {
            // ETH支付
            require(msg.value >= totalPrice, "Insufficient payment");
        } else {
            // ERC20代币支付
            require(msg.value == 0, "ETH not accepted for this listing");
            IERC20 paymentToken = IERC20(listing.paymentToken);
            require(
                paymentToken.transferFrom(msg.sender, address(this), totalPrice),
                "Payment transfer failed"
            );
        }

        // 停用listing
        listing.active = false;

        // 转移NFT
        nft.safeTransferFrom(listing.seller, msg.sender, listing.tokenId);

        // 分配资金
        _distributeFunds(
            listing.nftContract,
            listing.tokenId,
            listing.seller,
            totalPrice,
            listing.paymentToken
        );

        // 退还多余的ETH
        if (listing.paymentToken == address(0) && msg.value > totalPrice) {
            payable(msg.sender).transfer(msg.value - totalPrice);
        }

        emit ItemSold(listingId, msg.sender, listing.seller, totalPrice, listing.paymentToken);
    }

    /**
     * @dev 取消上架
     */
    function cancelListing(uint256 listingId) external nonReentrant {
        Listing storage listing = listings[listingId];
        require(listing.active, "Listing not active");
        require(listing.seller == msg.sender, "Not the seller");

        listing.active = false;
        emit ListingCancelled(listingId);
    }

    /**
     * @dev 创建拍卖
     */
    function createAuction(
        address nftContract,
        uint256 tokenId,
        uint256 startingPrice,
        uint256 reservePrice,
        address paymentToken,
        uint256 duration
    ) external nonReentrant whenNotPaused {
        require(approvedNFTContracts[nftContract], "NFT contract not approved");
        require(approvedPaymentTokens[paymentToken], "Payment token not approved");
        require(startingPrice > 0, "Starting price must be greater than 0");
        require(reservePrice >= startingPrice, "Reserve price too low");
        require(duration >= minAuctionDuration, "Duration too short");
        require(duration <= maxAuctionDuration, "Duration too long");

        IERC721 nft = IERC721(nftContract);
        require(nft.ownerOf(tokenId) == msg.sender, "Not the owner");
        require(
            nft.isApprovedForAll(msg.sender, address(this)) || 
            nft.getApproved(tokenId) == address(this),
            "Marketplace not approved"
        );

        uint256 auctionId = _auctionIdCounter.current();
        _auctionIdCounter.increment();

        uint256 endTime = block.timestamp + duration;

        auctions[auctionId] = Auction({
            auctionId: auctionId,
            seller: msg.sender,
            nftContract: nftContract,
            tokenId: tokenId,
            startingPrice: startingPrice,
            reservePrice: reservePrice,
            currentBid: 0,
            currentBidder: address(0),
            paymentToken: paymentToken,
            startTime: block.timestamp,
            endTime: endTime,
            active: true,
            settled: false
        });

        emit AuctionCreated(
            auctionId,
            msg.sender,
            nftContract,
            tokenId,
            startingPrice,
            reservePrice,
            endTime
        );
    }

    /**
     * @dev 出价
     */
    function placeBid(uint256 auctionId) external payable nonReentrant whenNotPaused {
        Auction storage auction = auctions[auctionId];
        require(auction.active, "Auction not active");
        require(block.timestamp < auction.endTime, "Auction ended");
        require(msg.sender != auction.seller, "Cannot bid on your own auction");

        uint256 bidAmount;
        if (auction.paymentToken == address(0)) {
            bidAmount = msg.value;
        } else {
            require(msg.value == 0, "ETH not accepted for this auction");
            // 对于ERC20代币，需要先approve，这里只记录金额
            bidAmount = msg.value; // 这里需要从前端传入金额
        }

        uint256 minBid = auction.currentBid == 0 
            ? auction.startingPrice 
            : auction.currentBid + (auction.currentBid * minBidIncrement / 10000);
        
        require(bidAmount >= minBid, "Bid too low");

        // 退还前一个出价者的资金
        if (auction.currentBidder != address(0)) {
            if (auction.paymentToken == address(0)) {
                pendingWithdrawals[auction.currentBidder] += auction.currentBid;
            } else {
                IERC20 paymentToken = IERC20(auction.paymentToken);
                require(
                    paymentToken.transfer(auction.currentBidder, auction.currentBid),
                    "Refund failed"
                );
            }
        }

        // 处理新出价的支付
        if (auction.paymentToken != address(0)) {
            IERC20 paymentToken = IERC20(auction.paymentToken);
            require(
                paymentToken.transferFrom(msg.sender, address(this), bidAmount),
                "Payment transfer failed"
            );
        }

        // 更新拍卖状态
        auction.currentBid = bidAmount;
        auction.currentBidder = msg.sender;

        // 记录出价历史
        auctionBids[auctionId].push(Bid({
            bidder: msg.sender,
            amount: bidAmount,
            timestamp: block.timestamp
        }));

        // 如果接近结束时间，延长拍卖
        if (auction.endTime - block.timestamp < bidExtensionTime) {
            auction.endTime = block.timestamp + bidExtensionTime;
        }

        emit BidPlaced(auctionId, msg.sender, bidAmount, auction.endTime);
    }

    /**
     * @dev 结算拍卖
     */
    function settleAuction(uint256 auctionId) external nonReentrant {
        Auction storage auction = auctions[auctionId];
        require(auction.active, "Auction not active");
        require(block.timestamp >= auction.endTime, "Auction not ended");
        require(!auction.settled, "Auction already settled");

        auction.active = false;
        auction.settled = true;

        IERC721 nft = IERC721(auction.nftContract);
        
        if (auction.currentBid >= auction.reservePrice && auction.currentBidder != address(0)) {
            // 拍卖成功
            nft.safeTransferFrom(auction.seller, auction.currentBidder, auction.tokenId);
            
            // 分配资金
            _distributeFunds(
                auction.nftContract,
                auction.tokenId,
                auction.seller,
                auction.currentBid,
                auction.paymentToken
            );

            emit AuctionSettled(auctionId, auction.currentBidder, auction.currentBid);
        } else {
            // 拍卖失败，退还出价
            if (auction.currentBidder != address(0)) {
                if (auction.paymentToken == address(0)) {
                    pendingWithdrawals[auction.currentBidder] += auction.currentBid;
                } else {
                    IERC20 paymentToken = IERC20(auction.paymentToken);
                    require(
                        paymentToken.transfer(auction.currentBidder, auction.currentBid),
                        "Refund failed"
                    );
                }
            }
            
            emit AuctionCancelled(auctionId);
        }
    }

    /**
     * @dev 分配资金（包括版税和平台费用）
     */
    function _distributeFunds(
        address nftContract,
        uint256 tokenId,
        address seller,
        uint256 totalAmount,
        address paymentToken
    ) internal {
        uint256 remaining = totalAmount;

        // 计算并分配版税
        if (IERC165(nftContract).supportsInterface(type(IERC2981).interfaceId)) {
            (address royaltyRecipient, uint256 royaltyAmount) = 
                IERC2981(nftContract).royaltyInfo(tokenId, totalAmount);
            
            if (royaltyAmount > 0 && royaltyRecipient != address(0)) {
                _transferFunds(royaltyRecipient, royaltyAmount, paymentToken);
                remaining -= royaltyAmount;
            }
        }

        // 计算并分配平台费用
        uint256 feeAmount = (totalAmount * marketplaceFee) / 10000;
        if (feeAmount > 0) {
            _transferFunds(feeRecipient, feeAmount, paymentToken);
            remaining -= feeAmount;
        }

        // 剩余资金给卖家
        if (remaining > 0) {
            _transferFunds(seller, remaining, paymentToken);
        }
    }

    /**
     * @dev 转移资金
     */
    function _transferFunds(address to, uint256 amount, address paymentToken) internal {
        if (paymentToken == address(0)) {
            // ETH转账
            (bool success, ) = payable(to).call{value: amount}("");
            if (!success) {
                pendingWithdrawals[to] += amount;
            }
        } else {
            // ERC20代币转账
            IERC20 token = IERC20(paymentToken);
            require(token.transfer(to, amount), "Transfer failed");
        }
    }

    /**
     * @dev 提取待提取资金
     */
    function withdraw() external nonReentrant {
        uint256 amount = pendingWithdrawals[msg.sender];
        require(amount > 0, "No funds to withdraw");

        pendingWithdrawals[msg.sender] = 0;
        payable(msg.sender).transfer(amount);
    }

    /**
     * @dev 批准NFT合约
     */
    function approveNFTContract(address nftContract) external onlyOwner {
        approvedNFTContracts[nftContract] = true;
    }

    /**
     * @dev 移除NFT合约批准
     */
    function removeNFTContract(address nftContract) external onlyOwner {
        approvedNFTContracts[nftContract] = false;
    }

    /**
     * @dev 批准支付代币
     */
    function approvePaymentToken(address paymentToken) external onlyOwner {
        approvedPaymentTokens[paymentToken] = true;
    }

    /**
     * @dev 移除支付代币批准
     */
    function removePaymentToken(address paymentToken) external onlyOwner {
        approvedPaymentTokens[paymentToken] = false;
    }

    /**
     * @dev 设置平台费用
     */
    function setMarketplaceFee(uint256 _fee) external onlyOwner {
        require(_fee <= MAX_FEE, "Fee too high");
        marketplaceFee = _fee;
    }

    /**
     * @dev 设置费用接收者
     */
    function setFeeRecipient(address _feeRecipient) external onlyOwner {
        require(_feeRecipient != address(0), "Invalid fee recipient");
        feeRecipient = _feeRecipient;
    }

    /**
     * @dev 暂停合约
     */
    function pause() external onlyOwner {
        _pause();
    }

    /**
     * @dev 恢复合约
     */
    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev 获取拍卖出价历史
     */
    function getAuctionBids(uint256 auctionId) external view returns (Bid[] memory) {
        return auctionBids[auctionId];
    }

    /**
     * @dev 获取活跃的listing数量
     */
    function getActiveListingCount() external view returns (uint256) {
        uint256 count = 0;
        uint256 totalListings = _listingIdCounter.current();
        
        for (uint256 i = 0; i < totalListings; i++) {
            if (listings[i].active && block.timestamp <= listings[i].expiresAt) {
                count++;
            }
        }
        
        return count;
    }

    /**
     * @dev 获取活跃的拍卖数量
     */
    function getActiveAuctionCount() external view returns (uint256) {
        uint256 count = 0;
        uint256 totalAuctions = _auctionIdCounter.current();
        
        for (uint256 i = 0; i < totalAuctions; i++) {
            if (auctions[i].active && block.timestamp < auctions[i].endTime) {
                count++;
            }
        }
        
        return count;
    }
}
