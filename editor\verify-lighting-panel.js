/**
 * 验证 LightingPanel.tsx 文件的修复
 */
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const filePath = path.join(__dirname, 'src/components/rendering/LightingPanel.tsx');

try {
  const content = fs.readFileSync(filePath, 'utf8');
  
  console.log('✅ LightingPanel.tsx 文件读取成功');
  
  // 检查是否包含修复的内容
  const checks = [
    {
      name: 'ColorPicker 修复',
      test: content.includes('color.toRgb()'),
      description: '检查 ColorPicker 的 onChange 处理是否正确'
    },
    {
      name: 'Input 导入',
      test: content.includes('Input') && content.includes('from \'antd\''),
      description: '检查 Input 组件是否正确导入'
    },
    {
      name: 'Switch 修复',
      test: content.includes('<Switch />') && !content.includes('<Switch>{'),
      description: '检查 Switch 组件是否正确使用'
    },
    {
      name: '未使用导入清理',
      test: !content.includes('EyeOutlined') && !content.includes('SettingOutlined'),
      description: '检查未使用的导入是否已清理'
    },
    {
      name: 'Tag size 属性修复',
      test: !content.includes('<Tag key={tag} size="small">'),
      description: '检查 Tag 组件的 size 属性是否已移除'
    }
  ];
  
  let allPassed = true;
  
  checks.forEach(check => {
    if (check.test) {
      console.log(`✅ ${check.name}: 通过 - ${check.description}`);
    } else {
      console.log(`❌ ${check.name}: 失败 - ${check.description}`);
      allPassed = false;
    }
  });
  
  if (allPassed) {
    console.log('\n🎉 所有检查都通过了！LightingPanel.tsx 文件已成功修复。');
  } else {
    console.log('\n⚠️ 部分检查未通过，可能需要进一步修复。');
  }
  
} catch (error) {
  console.error('❌ 验证过程中出现错误:', error.message);
}
