/**
 * 示例插件
 * 演示如何创建一个面板插件
 */
import React, { useState, useEffect } from 'react';
import { Card, Button, Space, Input, List, message, Typography } from 'antd';
import { PlusOutlined, DeleteOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { PanelPlugin, PluginAPI } from '../services/PanelPluginManager';

const { Text, Title } = Typography;

interface ExamplePluginProps {
  pluginAPI: PluginAPI;
  pluginConfig: any;
}

interface TodoItem {
  id: string;
  text: string;
  completed: boolean;
  createdAt: number;
}

const ExamplePluginComponent: React.FC<ExamplePluginProps> = ({ pluginAPI, pluginConfig }) => {
  const [todos, setTodos] = useState<TodoItem[]>([]);
  const [newTodoText, setNewTodoText] = useState('');

  useEffect(() => {
    // 监听编辑器事件
    pluginAPI.on('entitySelected', handleEntitySelected);
    pluginAPI.on('sceneChanged', handleSceneChanged);

    return () => {
      pluginAPI.off('entitySelected', handleEntitySelected);
      pluginAPI.off('sceneChanged', handleSceneChanged);
    };
  }, [pluginAPI]);

  const handleEntitySelected = (data: any) => {
    console.log('Entity selected:', data);
    pluginAPI.showNotification('Entity selected in Example Plugin', 'info');
  };

  const handleSceneChanged = (data: any) => {
    console.log('Scene changed:', data);
  };

  const addTodo = () => {
    if (newTodoText.trim()) {
      const newTodo: TodoItem = {
        id: Date.now().toString(),
        text: newTodoText.trim(),
        completed: false,
        createdAt: Date.now()
      };
      setTodos([...todos, newTodo]);
      setNewTodoText('');
      pluginAPI.showNotification('Todo added successfully', 'success');
    }
  };

  const deleteTodo = (id: string) => {
    setTodos(todos.filter(todo => todo.id !== id));
    pluginAPI.showNotification('Todo deleted', 'info');
  };

  const toggleTodo = (id: string) => {
    setTodos(todos.map(todo => 
      todo.id === id ? { ...todo, completed: !todo.completed } : todo
    ));
  };

  const showPluginInfo = () => {
    message.info('This is an example plugin demonstrating the plugin system capabilities.');
  };

  return (
    <div style={{ padding: '16px', height: '100%', overflow: 'auto' }}>
      <Card
        title={
          <Space>
            <InfoCircleOutlined />
            <span>Example Plugin - Todo List</span>
          </Space>
        }
        size="small"
        extra={
          <Button type="text" icon={<InfoCircleOutlined />} onClick={showPluginInfo}>
            Info
          </Button>
        }
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Title level={5}>Add New Todo</Title>
            <Space.Compact style={{ width: '100%' }}>
              <Input
                placeholder="Enter todo text..."
                value={newTodoText}
                onChange={(e) => setNewTodoText(e.target.value)}
                onPressEnter={addTodo}
              />
              <Button type="primary" icon={<PlusOutlined />} onClick={addTodo}>
                Add
              </Button>
            </Space.Compact>
          </div>

          <div>
            <Title level={5}>Todo List ({todos.length})</Title>
            <List
              size="small"
              dataSource={todos}
              renderItem={(todo) => (
                <List.Item
                  actions={[
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => deleteTodo(todo.id)}
                    />
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <Text
                        delete={todo.completed}
                        style={{ 
                          textDecoration: todo.completed ? 'line-through' : 'none',
                          color: todo.completed ? '#999' : 'inherit'
                        }}
                        onClick={() => toggleTodo(todo.id)}
                      >
                        {todo.text}
                      </Text>
                    }
                    description={
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        Created: {new Date(todo.createdAt).toLocaleString()}
                      </Text>
                    }
                  />
                </List.Item>
              )}
              locale={{ emptyText: 'No todos yet. Add one above!' }}
            />
          </div>

          <div>
            <Title level={5}>Plugin API Demo</Title>
            <Space wrap>
              <Button 
                size="small" 
                onClick={() => pluginAPI.showNotification('Hello from plugin!', 'success')}
              >
                Show Notification
              </Button>
              <Button 
                size="small" 
                onClick={() => console.log('Selected entity:', pluginAPI.getSelectedEntity())}
              >
                Get Selected Entity
              </Button>
              <Button 
                size="small" 
                onClick={() => console.log('Scene:', pluginAPI.getScene())}
              >
                Get Scene
              </Button>
              <Button 
                size="small" 
                onClick={() => pluginAPI.emit('customEvent', { message: 'Hello from plugin!' })}
              >
                Emit Custom Event
              </Button>
            </Space>
          </div>

          <div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              Plugin Config: {JSON.stringify(pluginConfig, null, 2)}
            </Text>
          </div>
        </Space>
      </Card>
    </div>
  );
};

// 插件定义
export const ExamplePlugin: PanelPlugin = {
  manifest: {
    id: 'example-plugin',
    name: 'Example Plugin',
    version: '1.0.0',
    description: 'A simple example plugin demonstrating the plugin system capabilities',
    author: 'DL Engine Team',
    category: 'Utilities',
    tags: ['example', 'demo', 'todo', 'utility'],
    dependencies: [],
    permissions: ['notifications', 'scene-access'],
    minEditorVersion: '1.0.0'
  },
  component: ExamplePluginComponent,
  config: {
    enabled: false,
    settings: {
      maxTodos: 50,
      autoSave: true,
      showTimestamps: true
    },
    position: 'right',
    size: 300,
    closable: true,
    resizable: true
  },
  onActivate: () => {
    console.log('Example Plugin activated');
  },
  onDeactivate: () => {
    console.log('Example Plugin deactivated');
  },
  onSettingsChange: (settings) => {
    console.log('Example Plugin settings changed:', settings);
  }
};

export default ExamplePlugin;
