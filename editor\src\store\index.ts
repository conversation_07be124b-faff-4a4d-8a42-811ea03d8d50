/**
 * Redux存储配置
 */
import { configureStore } from '@reduxjs/toolkit';
import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';

// 导入状态切片
import authReducer from './auth/authSlice';
import projectReducer from './project/projectSlice';
import editorReducer from './editor/editorSlice';
import assetReducer from './asset/assetSlice';
import sceneReducer from './scene/sceneSlice';
import uiReducer from './ui/uiSlice';
import layoutReducer from './ui/layoutSlice';
import materialsReducer from './materials/materialsSlice';
import animationsReducer from './animations/animationsSlice';
import stateMachineReducer from './animations/stateMachineSlice';
import blendSpaceReducer from './animations/blendSpaceSlice';
import particlesReducer from './particles/particlesSlice';
import physicsReducer from './physics/physicsSlice';
import waterReducer from './physics/waterSlice';
import waterMaterialReducer from './rendering/waterMaterialSlice';
import undergroundLightingReducer from './rendering/undergroundLightingSlice';
import collaborationReducer from './collaboration/collaborationSlice';
import connectionReducer from './collaboration/connectionSlice';
import conflictReducer from './collaboration/conflictSlice';
import conflictPredictionReducer from './collaboration/conflictPredictionSlice';
import aiResolverReducer from './collaboration/aiResolverSlice';
import versionReducer from './collaboration/versionSlice';
import permissionReducer from './collaboration/permissionSlice';
import conflictVisualizationReducer from './collaboration/conflictVisualizationSlice';
import editingZonesReducer from './collaboration/editingZonesSlice';
import lockReducer from './collaboration/lockSlice';
import userTestingReducer from './testing/userTestingSlice';
import debugReducer from './debug/debugSlice';
import achievementsReducer from './achievements/achievementsSlice';
import resourceVersionReducer from './resources/resourceVersionSlice';
import resourceHotUpdateReducer from './resources/resourceHotUpdateSlice';
import gitReducer from './git/gitSlice';
import terrainReducer from './terrain/terrainSlice';
import optimizedEditorReducer from './optimized/OptimizedEditorSlice';

// 创建Redux存储
export const store = configureStore({
  reducer: {
    auth: authReducer,
    project: projectReducer,
    editor: editorReducer,
    asset: assetReducer,
    scene: sceneReducer,
    ui: uiReducer,
    layout: layoutReducer,
    materials: materialsReducer,
    animations: animationsReducer,
    stateMachine: stateMachineReducer,
    blendSpace: blendSpaceReducer,
    particles: particlesReducer,
    physics: physicsReducer,
    water: waterReducer,
    waterMaterial: waterMaterialReducer,
    undergroundLighting: undergroundLightingReducer,
    collaboration: collaborationReducer,
    connection: connectionReducer,
    conflict: conflictReducer,
    conflictPrediction: conflictPredictionReducer,
    aiResolver: aiResolverReducer,
    version: versionReducer,
    permission: permissionReducer,
    conflictVisualization: conflictVisualizationReducer,
    editingZones: editingZonesReducer,
    lock: lockReducer,
    userTesting: userTestingReducer,
    debug: debugReducer,
    achievements: achievementsReducer,
    resourceVersion: resourceVersionReducer,
    resourceHotUpdate: resourceHotUpdateReducer,
    git: gitReducer,
    terrain: terrainReducer,
    optimizedEditor: optimizedEditorReducer},
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // 忽略某些非可序列化的值
        ignoredActions: ['editor/setActiveCamera', 'editor/setSelectedObject'],
        ignoredPaths: ['editor.activeCamera', 'editor.selectedObject']}})});

// 导出类型
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// 导出钩子
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
