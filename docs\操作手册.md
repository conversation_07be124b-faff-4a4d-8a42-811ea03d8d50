# DL引擎操作手册

## 目录
1. [编辑器操作详解](#编辑器操作详解)
2. [项目管理操作](#项目管理操作)
3. [场景编辑操作](#场景编辑操作)
4. [视觉脚本操作](#视觉脚本操作)
5. [材质编辑操作](#材质编辑操作)
6. [动画制作操作](#动画制作操作)
7. [协作功能操作](#协作功能操作)
8. [发布部署操作](#发布部署操作)
9. [故障排除](#故障排除)

## 编辑器操作详解

### 界面布局管理

#### 面板操作
1. **调整面板大小**
   - 拖拽面板边界调整宽度/高度
   - 双击边界自动调整到合适大小
   - 右键面板标题选择"重置大小"

2. **面板停靠**
   - 拖拽面板标题到目标位置
   - 出现蓝色停靠区域时释放鼠标
   - 支持上下左右四个方向停靠

3. **面板浮动**
   - 拖拽面板标题到空白区域
   - 创建独立的浮动窗口
   - 可在多显示器间移动

4. **工作区保存**
   - 菜单栏 → 窗口 → 保存工作区布局
   - 输入布局名称
   - 可创建多个自定义布局

#### 视口操作
1. **单视口模式**
   - 默认显示主3D视口
   - 最大化显示区域
   - 适合详细编辑工作

2. **多视口模式**
   - 点击视口右上角的分割按钮
   - 选择2分割、4分割等模式
   - 每个视口可独立设置视角

3. **视口设置**
   - 右键视口空白区域
   - 选择显示模式：线框/实体/材质
   - 设置网格、坐标轴显示

### 工具栏详细操作

#### 选择工具 (Q)
1. **单选模式**
   - 左键点击选择单个对象
   - 选中对象显示橙色轮廓
   - 属性面板显示对象信息

2. **多选模式**
   - 按住Ctrl+左键点击添加选择
   - 按住Shift+左键点击范围选择
   - 拖拽框选多个对象

3. **选择过滤**
   - 工具栏选择过滤按钮
   - 可过滤：网格、光源、相机、空对象
   - 避免误选不需要的对象

#### 移动工具 (W)
1. **轴向移动**
   - 拖拽红色箭头：X轴移动
   - 拖拽绿色箭头：Y轴移动
   - 拖拽蓝色箭头：Z轴移动

2. **平面移动**
   - 拖拽红绿平面：XY平面移动
   - 拖拽红蓝平面：XZ平面移动
   - 拖拽绿蓝平面：YZ平面移动

3. **自由移动**
   - 拖拽中心白色方块
   - 根据视角方向移动
   - 适合快速定位

#### 旋转工具 (E)
1. **轴向旋转**
   - 拖拽红色圆环：绕X轴旋转
   - 拖拽绿色圆环：绕Y轴旋转
   - 拖拽蓝色圆环：绕Z轴旋转

2. **自由旋转**
   - 拖拽外层白色圆环
   - 根据鼠标移动方向旋转
   - 适合快速调整朝向

3. **精确旋转**
   - 按住Shift进行精确旋转
   - 每次旋转15度增量
   - 适合精确对齐

#### 缩放工具 (R)
1. **等比缩放**
   - 拖拽中心白色方块
   - 保持对象比例缩放
   - 最常用的缩放方式

2. **轴向缩放**
   - 拖拽红色方块：X轴缩放
   - 拖拽绿色方块：Y轴缩放
   - 拖拽蓝色方块：Z轴缩放

3. **平面缩放**
   - 拖拽平面连接线
   - 在指定平面内缩放
   - 保持第三轴不变

### 相机控制

#### 视角操作
1. **轨道旋转**
   - 鼠标中键拖拽
   - 围绕焦点旋转视角
   - 保持焦点不变

2. **平移视角**
   - Shift+鼠标中键拖拽
   - 平移相机位置
   - 改变观察焦点

3. **缩放视角**
   - 鼠标滚轮上下滚动
   - 拉近或拉远视角
   - 不改变相机朝向

#### 预设视角
1. **正交视角**
   - 数字键1：前视图
   - 数字键3：右视图
   - 数字键7：顶视图
   - Ctrl+数字键：对应的后视图

2. **透视视角**
   - 数字键0：相机视角
   - 数字键5：切换正交/透视
   - 小键盘句号：聚焦到选中对象

#### 相机设置
1. **视野角度**
   - 属性面板调整FOV值
   - 范围：10-120度
   - 影响透视变形程度

2. **裁剪距离**
   - 近裁剪面：最近可见距离
   - 远裁剪面：最远可见距离
   - 影响渲染性能

## 项目管理操作

### 项目创建与设置

#### 新建项目
1. **从模板创建**
   ```
   文件 → 新建项目 → 选择模板
   ```
   - 空白项目：完全空白的场景
   - 基础场景：包含地面和光照
   - 游戏模板：预设游戏框架
   - 建筑可视化：建筑展示模板
   - 教育演示：教学演示模板

2. **项目配置**
   - 项目名称：英文或中文名称
   - 项目描述：详细说明项目用途
   - 项目标签：便于分类和搜索
   - 隐私设置：公开/私有/团队可见

#### 项目设置
1. **渲染设置**
   ```
   编辑 → 项目设置 → 渲染
   ```
   - 渲染管线：Forward/Deferred
   - 抗锯齿：FXAA/MSAA/TAA
   - 阴影质量：低/中/高/超高
   - 后处理：色调映射、泛光等

2. **物理设置**
   ```
   编辑 → 项目设置 → 物理
   ```
   - 重力：默认-9.81 m/s²
   - 时间步长：固定/可变
   - 碰撞层：定义碰撞分组
   - 求解器迭代：影响精度和性能

3. **音频设置**
   ```
   编辑 → 项目设置 → 音频
   ```
   - 主音量：全局音量控制
   - 音频格式：采样率和位深度
   - 3D音频：距离衰减模型
   - 多普勒效应：启用/禁用

### 场景管理

#### 场景操作
1. **创建场景**
   ```
   文件 → 新建场景
   ```
   - 输入场景名称
   - 选择场景模板
   - 设置初始对象

2. **场景切换**
   ```
   窗口 → 场景管理器
   ```
   - 双击场景名称切换
   - 拖拽调整场景顺序
   - 设置启动场景

3. **场景设置**
   ```
   编辑 → 场景设置
   ```
   - 环境光：整体环境照明
   - 天空盒：背景天空
   - 雾效：距离雾效果
   - 后处理：场景级后处理

#### 层级管理
1. **创建层级**
   ```
   层级面板 → 右键 → 创建空对象
   ```
   - 作为父对象组织子对象
   - 便于批量操作
   - 保持场景整洁

2. **对象分组**
   - 选择多个对象
   - 右键选择"创建分组"
   - 或拖拽到现有父对象下

3. **层级操作**
   - 拖拽调整父子关系
   - Shift+点击展开/折叠所有子级
   - 右键菜单快速操作

### 资源管理操作

#### 资源导入
1. **拖拽导入**
   - 从文件管理器拖拽文件
   - 拖拽到资源面板或3D视口
   - 自动识别文件类型

2. **菜单导入**
   ```
   文件 → 导入资源
   ```
   - 选择文件或文件夹
   - 设置导入参数
   - 批量导入处理

3. **导入设置**
   - 模型：缩放、材质、动画
   - 纹理：压缩、过滤、Mipmap
   - 音频：压缩、质量、循环

#### 资源组织
1. **文件夹管理**
   ```
   资源面板 → 右键 → 新建文件夹
   ```
   - 按类型分类：模型/纹理/音频
   - 按功能分类：角色/环境/UI
   - 按场景分类：关卡1/关卡2

2. **资源标签**
   - 右键资源选择"编辑标签"
   - 添加描述性标签
   - 便于搜索和过滤

3. **资源预览**
   - 悬停显示预览图
   - 双击打开详细预览
   - 支持模型、纹理、音频预览

## 场景编辑操作

### 对象创建与编辑

#### 基础几何体
1. **创建立方体**
   ```
   右键3D视口 → 添加 → 网格 → 立方体
   ```
   - 默认1x1x1单位大小
   - 可在属性面板调整尺寸
   - 支持细分级别设置

2. **创建球体**
   ```
   右键3D视口 → 添加 → 网格 → 球体
   ```
   - 设置半径和细分数
   - 细分数影响圆滑程度
   - 注意面数对性能的影响

3. **创建平面**
   ```
   右键3D视口 → 添加 → 网格 → 平面
   ```
   - 常用作地面或墙面
   - 可设置细分数用于变形
   - 支持双面材质

#### 高级建模
1. **挤出操作**
   - 选择面模式
   - 按E键挤出选中面
   - 拖拽调整挤出距离

2. **倒角操作**
   - 选择边模式
   - 按Ctrl+B倒角选中边
   - 滚轮调整倒角段数

3. **细分操作**
   - 右键选择"细分表面"
   - 增加模型细节
   - 用于有机建模

### 材质与纹理

#### 材质创建
1. **新建材质**
   ```
   属性面板 → 材质 → 新建
   ```
   - 选择材质类型：PBR/卡通/透明
   - 设置基础属性
   - 命名材质便于管理

2. **材质参数**
   - 基础颜色：物体的主要颜色
   - 金属度：0=非金属，1=金属
   - 粗糙度：0=镜面，1=完全粗糙
   - 法线：表面细节凹凸
   - 发光：自发光强度和颜色

#### 纹理映射
1. **UV展开**
   - 进入编辑模式
   - 选择所有面
   - 按U键选择展开方式

2. **纹理应用**
   - 在材质编辑器中
   - 点击颜色旁的纹理按钮
   - 选择或上传纹理文件

3. **UV调整**
   - 在UV编辑器中
   - 调整UV岛的位置和大小
   - 避免纹理拉伸变形

### 光照设置

#### 光源类型
1. **方向光设置**
   ```
   添加 → 光源 → 方向光
   ```
   - 模拟太阳光照
   - 设置方向和强度
   - 配置阴影参数

2. **点光源设置**
   ```
   添加 → 光源 → 点光源
   ```
   - 设置位置和范围
   - 调整衰减曲线
   - 配置颜色和强度

3. **聚光灯设置**
   ```
   添加 → 光源 → 聚光灯
   ```
   - 设置锥角和边缘软化
   - 调整投射方向
   - 配置阴影精度

#### 全局光照
1. **环境光设置**
   ```
   场景设置 → 环境光
   ```
   - 设置环境光颜色
   - 调整环境光强度
   - 选择环境贴图

2. **光照烘焙**
   ```
   渲染 → 光照烘焙
   ```
   - 设置烘焙质量
   - 选择烘焙对象
   - 开始烘焙计算

## 视觉脚本操作

### 脚本编辑器界面

#### 节点操作
1. **添加节点**
   - 右键画布空白区域
   - 从节点库拖拽节点
   - 使用搜索快速查找

2. **连接节点**
   - 拖拽输出端口到输入端口
   - 相同类型端口才能连接
   - 连接线显示数据流向

3. **节点编辑**
   - 选中节点查看属性
   - 在属性面板修改参数
   - 双击节点快速编辑

#### 常用节点操作
1. **事件节点**
   ```
   事件 → OnStart
   ```
   - 场景开始时执行
   - 连接到其他逻辑节点
   - 作为脚本入口点

2. **变量节点**
   ```
   变量 → Set Variable
   ```
   - 存储和获取数据
   - 设置变量名和类型
   - 在多个地方使用

3. **控制流节点**
   ```
   控制流 → If
   ```
   - 条件判断分支
   - 连接条件输入
   - 分别连接True/False输出

### 脚本调试

#### 断点调试
1. **设置断点**
   - 点击节点左侧设置断点
   - 红色圆点表示断点
   - 执行时会在此暂停

2. **单步执行**
   - F10：执行下一步
   - F11：进入函数内部
   - F5：继续执行

3. **变量监视**
   - 在监视面板添加变量
   - 实时查看变量值
   - 支持表达式监视

#### 性能分析
1. **性能监控**
   ```
   调试 → 性能分析器
   ```
   - 查看节点执行时间
   - 识别性能瓶颈
   - 优化脚本性能

2. **内存监控**
   ```
   调试 → 内存分析器
   ```
   - 监控内存使用
   - 检测内存泄漏
   - 优化内存分配

## 材质编辑操作

### 节点编辑器

#### 材质节点
1. **基础节点**
   - 颜色节点：设置固定颜色
   - 纹理节点：采样纹理贴图
   - 数值节点：设置数值参数

2. **数学节点**
   - 加法：颜色或数值相加
   - 乘法：颜色或数值相乘
   - 混合：两个输入混合

3. **输出节点**
   - 基础颜色输出
   - 金属度输出
   - 粗糙度输出
   - 法线输出

#### 高级材质
1. **程序纹理**
   - 噪声节点：生成程序噪声
   - 渐变节点：创建颜色渐变
   - 图案节点：几何图案生成

2. **动态材质**
   - 时间节点：获取时间参数
   - 正弦波节点：周期性变化
   - 参数节点：外部控制参数

## 动画制作操作

### 关键帧动画

#### 设置关键帧
1. **自动关键帧**
   - 启用自动关键帧模式
   - 移动时间轴到指定帧
   - 修改对象属性自动记录

2. **手动关键帧**
   - 选择要动画的属性
   - 右键选择"插入关键帧"
   - 在时间轴上显示关键帧

#### 动画曲线编辑
1. **曲线编辑器**
   ```
   窗口 → 动画 → 曲线编辑器
   ```
   - 查看和编辑动画曲线
   - 调整关键帧切线
   - 设置插值类型

2. **缓动设置**
   - 线性：匀速运动
   - 缓入：慢速开始
   - 缓出：慢速结束
   - 缓入缓出：两端慢速

### 骨骼动画

#### 骨骼绑定
1. **创建骨骼**
   ```
   添加 → 骨架 → 单骨骼
   ```
   - 在编辑模式下挤出骨骼
   - 构建完整骨骼层次
   - 命名骨骼便于管理

2. **权重绘制**
   - 选择网格对象
   - 进入权重绘制模式
   - 绘制顶点权重影响

#### 动画状态机
1. **状态创建**
   ```
   动画 → 状态机编辑器
   ```
   - 创建动画状态
   - 设置状态动画片段
   - 配置状态参数

2. **过渡设置**
   - 连接状态之间的过渡
   - 设置过渡条件
   - 调整过渡时间

## 协作功能操作

### 团队管理

#### 邀请成员
1. **发送邀请**
   ```
   项目 → 团队管理 → 邀请成员
   ```
   - 输入邮箱地址
   - 选择权限级别
   - 发送邀请邮件

2. **权限设置**
   - 所有者：完全控制
   - 管理员：管理项目和成员
   - 编辑者：编辑项目内容
   - 查看者：只读权限

#### 实时协作
1. **查看在线用户**
   ```
   协作面板 → 在线用户
   ```
   - 显示当前在线成员
   - 查看成员操作状态
   - 快速定位到成员视角

2. **冲突解决**
   - 系统自动检测冲突
   - 显示冲突提示对话框
   - 选择保留的版本

### 版本控制

#### 提交更改
1. **查看更改**
   ```
   版本控制 → 更改列表
   ```
   - 显示所有修改的文件
   - 查看具体更改内容
   - 选择要提交的文件

2. **提交操作**
   ```
   版本控制 → 提交
   ```
   - 输入提交说明
   - 选择提交文件
   - 点击提交按钮

#### 分支管理
1. **创建分支**
   ```
   版本控制 → 分支 → 新建分支
   ```
   - 输入分支名称
   - 选择基础分支
   - 创建并切换到新分支

2. **合并分支**
   ```
   版本控制 → 分支 → 合并
   ```
   - 选择要合并的分支
   - 解决可能的冲突
   - 完成合并操作

## 发布部署操作

### 构建设置

#### 平台配置
1. **Web平台**
   ```
   文件 → 构建设置 → Web
   ```
   - 选择压缩级别
   - 设置资源优化
   - 配置PWA选项

2. **移动平台**
   ```
   文件 → 构建设置 → 移动端
   ```
   - 选择目标设备
   - 设置屏幕适配
   - 配置触摸控制

#### 优化设置
1. **资源优化**
   - 纹理压缩：自动选择最佳格式
   - 模型简化：减少不必要的面数
   - 音频压缩：平衡质量和大小

2. **代码优化**
   - 代码压缩：移除空格和注释
   - 死代码消除：移除未使用代码
   - 模块打包：合并相关模块

### 发布流程

#### 本地构建
1. **开始构建**
   ```
   文件 → 构建项目
   ```
   - 选择构建配置
   - 点击开始构建
   - 等待构建完成

2. **测试构建**
   - 在本地服务器测试
   - 检查功能完整性
   - 验证性能表现

#### 云端发布
1. **上传到云端**
   ```
   文件 → 发布 → 云端发布
   ```
   - 选择发布平台
   - 配置发布参数
   - 开始上传过程

2. **发布管理**
   - 查看发布状态
   - 管理发布版本
   - 配置访问权限

## 故障排除

### 常见问题解决

#### 性能问题
1. **帧率过低**
   - 检查模型面数是否过高
   - 降低纹理分辨率
   - 减少光源数量
   - 关闭不必要的后处理

2. **内存不足**
   - 清理未使用的资源
   - 优化纹理大小
   - 使用对象池技术
   - 及时释放临时对象

#### 渲染问题
1. **材质显示异常**
   - 检查纹理文件是否损坏
   - 确认材质参数设置
   - 重新应用材质
   - 检查UV映射

2. **光照效果不正确**
   - 检查光源设置
   - 确认法线方向
   - 重新计算光照
   - 检查材质属性

#### 脚本问题
1. **脚本执行错误**
   - 查看控制台错误信息
   - 检查节点连接
   - 验证参数类型
   - 使用调试模式

2. **性能瓶颈**
   - 使用性能分析器
   - 优化循环逻辑
   - 减少不必要的计算
   - 使用事件驱动模式

### 技术支持

#### 获取帮助
1. **在线文档**
   - 访问官方文档网站
   - 搜索相关问题
   - 查看视频教程
   - 下载示例项目

2. **社区支持**
   - 加入官方QQ群
   - 访问论坛讨论
   - 提交问题反馈
   - 参与在线培训

#### 问题反馈
1. **Bug报告**
   ```
   帮助 → 反馈问题
   ```
   - 详细描述问题
   - 提供重现步骤
   - 附加相关截图
   - 提交系统信息

2. **功能建议**
   ```
   帮助 → 功能建议
   ```
   - 描述期望功能
   - 说明使用场景
   - 提供参考案例
   - 评估实现难度

---

*本操作手册持续更新，如有疑问请联系技术支持团队*
