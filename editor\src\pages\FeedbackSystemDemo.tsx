/**
 * 反馈系统演示页面
 * 用于展示反馈系统的各种组件和功能
 */
import React, { useState } from 'react';
import { 
  Layout, 
  Menu, 
  Card, 
  Button, 
  Space, 
  Divider, 
  Typography, 
  Row, 
  Col,
  Tabs,
  Alert
} from 'antd';
import {
  CommentOutlined,
  BulbOutlined,
  ToolOutlined,
  Pie<PERSON><PERSON>Outlined,
  SettingOutlined,
  DashboardOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import {
  FeedbackButton,
  FeedbackForm,
  ContextAwareFeedbackButton,
  FeedbackManager,
  FeedbackStatistics
} from '../components/feedback';

const { Header, Content, Sider } = Layout;
const { Title, Paragraph } = Typography;

/**
 * 反馈系统演示页面
 */
const FeedbackSystemDemo: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <Layout style={{ height: '100vh' }}>
      <Header className="header">
        <div className="logo" />
        <Title level={3} style={{ color: 'white', margin: 0 }}>
          {t('feedback.manager.title')} - 演示页面
        </Title>
      </Header>
      <Layout>
        <Sider width={200} className="site-layout-background">
          <Menu
            mode="inline"
            defaultSelectedKeys={['overview']}
            selectedKeys={[activeTab]}
            onSelect={({ key }) => setActiveTab(key as string)}
            style={{ height: '100%', borderRight: 0 }}
          >
            <Menu.Item key="overview" icon={<DashboardOutlined />}>
              概览
            </Menu.Item>
            <Menu.Item key="buttons" icon={<BulbOutlined />}>
              反馈按钮
            </Menu.Item>
            <Menu.Item key="forms" icon={<CommentOutlined />}>
              反馈表单
            </Menu.Item>
            <Menu.Item key="contextAware" icon={<ToolOutlined />}>
              上下文感知反馈
            </Menu.Item>
            <Menu.Item key="manager" icon={<SettingOutlined />}>
              反馈管理
            </Menu.Item>
            <Menu.Item key="statistics" icon={<PieChartOutlined />}>
              反馈统计
            </Menu.Item>
          </Menu>
        </Sider>
        <Layout style={{ padding: '0 24px 24px' }}>
          <Content
            className="site-layout-background"
            style={{
              padding: 24,
              margin: 0,
              minHeight: 280,
              overflow: 'auto'
            }}
          >
            {activeTab === 'overview' && (
              <div className="overview-tab">
                <Title level={2}>反馈系统概览</Title>
                <Paragraph>
                  反馈系统是编辑器的重要组成部分，用于收集用户对系统功能的反馈和建议。
                  本页面展示了反馈系统的各种组件和功能，包括反馈按钮、反馈表单、上下文感知反馈、反馈管理和反馈统计等。
                </Paragraph>
                
                <Alert
                  message="系统特点"
                  description={
                    <ul>
                      <li>支持多种类型的反馈（缺陷、功能请求、改进建议、性能问题等）</li>
                      <li>上下文感知反馈，自动收集当前操作上下文</li>
                      <li>自动截图功能，方便用户提交问题截图</li>
                      <li>完善的反馈管理功能，包括状态管理、评论等</li>
                      <li>详细的反馈统计分析，帮助开发团队了解用户需求和问题</li>
                    </ul>
                  }
                  type="info"
                  showIcon
                />
                
                <Divider />
                
                <Row gutter={[16, 16]}>
                  <Col span={8}>
                    <Card 
                      title={<><BulbOutlined /> 反馈按钮</>} 
                      extra={<Button type="link" onClick={() => setActiveTab('buttons')}>查看</Button>}
                    >
                      <Paragraph>
                        提供多种样式的反馈按钮，可以放置在编辑器的不同位置，方便用户随时提交反馈。
                      </Paragraph>
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card 
                      title={<><CommentOutlined /> 反馈表单</>} 
                      extra={<Button type="link" onClick={() => setActiveTab('forms')}>查看</Button>}
                    >
                      <Paragraph>
                        提供丰富的反馈表单，支持多种类型的反馈，包括缺陷、功能请求、改进建议等。
                      </Paragraph>
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card 
                      title={<><ToolOutlined /> 上下文感知反馈</>} 
                      extra={<Button type="link" onClick={() => setActiveTab('contextAware')}>查看</Button>}
                    >
                      <Paragraph>
                        自动收集当前操作上下文，包括选中的实体、当前工具、最近操作等，帮助开发团队更好地理解和解决问题。
                      </Paragraph>
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card 
                      title={<><SettingOutlined /> 反馈管理</>} 
                      extra={<Button type="link" onClick={() => setActiveTab('manager')}>查看</Button>}
                    >
                      <Paragraph>
                        提供完善的反馈管理功能，包括反馈列表、反馈详情、状态管理、评论等，帮助开发团队跟踪和处理用户反馈。
                      </Paragraph>
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card 
                      title={<><PieChartOutlined /> 反馈统计</>} 
                      extra={<Button type="link" onClick={() => setActiveTab('statistics')}>查看</Button>}
                    >
                      <Paragraph>
                        提供详细的反馈统计分析，包括按类型统计、按子类型统计、趋势分析等，帮助开发团队了解用户需求和问题。
                      </Paragraph>
                    </Card>
                  </Col>
                </Row>
              </div>
            )}
            
            {activeTab === 'buttons' && (
              <div className="buttons-tab">
                <Title level={2}>反馈按钮</Title>
                <Paragraph>
                  反馈按钮是用户提交反馈的入口，可以放置在编辑器的不同位置，方便用户随时提交反馈。
                  以下展示了不同类型和样式的反馈按钮。
                </Paragraph>
                
                <Divider orientation="left">基本反馈按钮</Divider>
                <Space size="large" wrap>
                  <FeedbackButton type="general" />
                  <FeedbackButton type="animation" />
                  <FeedbackButton type="physics" />
                  <FeedbackButton type="rendering" />
                  <FeedbackButton type="editor" />
                </Space>
                
                <Divider orientation="left">不同样式的反馈按钮</Divider>
                <Space size="large" wrap>
                  <FeedbackButton type="general" buttonType="primary" />
                  <FeedbackButton type="general" buttonType="dashed" />
                  <FeedbackButton type="general" buttonType="text" />
                  <FeedbackButton type="general" buttonType="link" />
                </Space>
                
                <Divider orientation="left">不同大小的反馈按钮</Divider>
                <Space size="large" wrap>
                  <FeedbackButton type="general" buttonSize="large" />
                  <FeedbackButton type="general" buttonSize="middle" />
                  <FeedbackButton type="general" buttonSize="small" />
                </Space>
                
                <Divider orientation="left">带徽标的反馈按钮</Divider>
                <Space size="large" wrap>
                  <FeedbackButton type="general" showBadge badgeCount={5} />
                  <FeedbackButton type="animation" showBadge badgeCount={0} />
                </Space>
                
                <Divider orientation="left">不同弹出方式的反馈按钮</Divider>
                <Space size="large" wrap>
                  <FeedbackButton type="general" useDrawer />
                  <FeedbackButton type="general" usePopover />
                </Space>
              </div>
            )}
            
            {activeTab === 'forms' && (
              <div className="forms-tab">
                <Title level={2}>反馈表单</Title>
                <Paragraph>
                  反馈表单用于收集用户对系统功能的反馈和建议，支持多种类型的反馈，包括缺陷、功能请求、改进建议等。
                </Paragraph>
                
                <Tabs
                  defaultActiveKey="general"
                  items={[
                    {
                      key: 'general',
                      label: '通用反馈表单',
                      children: <FeedbackForm type="general" />
                    },
                    {
                      key: 'animation',
                      label: '动画反馈表单',
                      children: <FeedbackForm type="animation" subType="blend" />
                    }
                  ]}
                />
              </div>
            )}
            
            {activeTab === 'contextAware' && (
              <div className="context-aware-tab">
                <Title level={2}>上下文感知反馈</Title>
                <Paragraph>
                  上下文感知反馈可以自动收集当前操作上下文，包括选中的实体、当前工具、最近操作等，
                  帮助开发团队更好地理解和解决问题。
                </Paragraph>
                
                <Divider orientation="left">上下文感知反馈按钮</Divider>
                <Space size="large" wrap>
                  <ContextAwareFeedbackButton type="general" />
                  <ContextAwareFeedbackButton type="animation" />
                  <ContextAwareFeedbackButton type="editor" />
                </Space>
                
                <Divider orientation="left">上下文感知反馈表单</Divider>
                <ContextAwareFeedbackButton
                  type="general"
                  buttonType="primary"
                  useModal
                  contextTooltip="点击打开上下文感知反馈表单"
                  showText={true}
                />
              </div>
            )}
            
            {activeTab === 'manager' && (
              <div className="manager-tab">
                <Title level={2}>反馈管理</Title>
                <Paragraph>
                  反馈管理用于管理和处理用户提交的反馈，包括反馈列表、反馈详情、状态管理、评论等。
                </Paragraph>
                
                <FeedbackManager />
              </div>
            )}
            
            {activeTab === 'statistics' && (
              <div className="statistics-tab">
                <Title level={2}>反馈统计</Title>
                <Paragraph>
                  反馈统计用于分析用户提交的反馈数据，帮助开发团队了解用户需求和问题。
                </Paragraph>
                
                <FeedbackStatistics />
              </div>
            )}
          </Content>
        </Layout>
      </Layout>
    </Layout>
  );
};

export default FeedbackSystemDemo;
