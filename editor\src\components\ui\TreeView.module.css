/**
 * TreeView.module.css
 * 
 * 树形控件样式
 */

.tree-view-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--background-color, #ffffff);
  border: 1px solid var(--border-color, #d9d9d9);
  border-radius: 6px;
  overflow: hidden;
}

.tree-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border-bottom: 1px solid var(--border-color, #d9d9d9);
  background: var(--surface-color, #fafafa);
  flex-shrink: 0;
}

.tree-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* 加载状态 */
.tree-view-container.loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.tree-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-color-secondary, #8c8c8c);
  font-size: 14px;
}

/* 空状态 */
.tree-view-container.empty {
  display: flex;
  align-items: center;
  justify-content: center;
}

.tree-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-color-secondary, #8c8c8c);
  font-size: 14px;
}

/* 树节点样式 */
.tree-view-container :global(.ant-tree) {
  background: transparent;
  color: var(--text-color, #000000);
}

.tree-view-container :global(.ant-tree-node-content-wrapper) {
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.tree-view-container :global(.ant-tree-node-content-wrapper:hover) {
  background-color: var(--item-hover-background, #f5f5f5);
}

.tree-view-container :global(.ant-tree-node-content-wrapper.ant-tree-node-selected) {
  background-color: var(--primary-color, #1890ff);
  color: white;
}

.tree-view-container :global(.ant-tree-node-content-wrapper.ant-tree-node-selected:hover) {
  background-color: var(--primary-color-hover, #40a9ff);
}

/* 树节点图标 */
.tree-view-container :global(.ant-tree-iconEle) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.tree-view-container :global(.ant-tree-switcher) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-right: 4px;
  color: var(--text-color-secondary, #8c8c8c);
  transition: color 0.2s ease;
}

.tree-view-container :global(.ant-tree-switcher:hover) {
  color: var(--primary-color, #1890ff);
}

.tree-view-container :global(.ant-tree-switcher-icon) {
  font-size: 10px;
}

/* 连接线 */
.tree-view-container :global(.ant-tree-show-line .ant-tree-indent-unit::before) {
  border-left: 1px solid var(--border-color, #d9d9d9);
}

.tree-view-container :global(.ant-tree-show-line .ant-tree-switcher) {
  background: var(--background-color, #ffffff);
}

/* 复选框 */
.tree-view-container :global(.ant-tree-checkbox) {
  margin-right: 8px;
}

.tree-view-container :global(.ant-tree-checkbox-checked .ant-tree-checkbox-inner) {
  background-color: var(--primary-color, #1890ff);
  border-color: var(--primary-color, #1890ff);
}

.tree-view-container :global(.ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner) {
  background-color: var(--primary-color, #1890ff);
  border-color: var(--primary-color, #1890ff);
}

/* 拖拽状态 */
.tree-view-container :global(.ant-tree-node-content-wrapper.dragging) {
  opacity: 0.5;
  background-color: var(--item-drag-background, #e6f7ff);
}

.tree-view-container :global(.ant-tree-drop-indicator) {
  background-color: var(--primary-color, #1890ff);
  height: 2px;
}

/* 禁用状态 */
.tree-view-container :global(.ant-tree-treenode-disabled) {
  opacity: 0.5;
  cursor: not-allowed;
}

.tree-view-container :global(.ant-tree-treenode-disabled .ant-tree-node-content-wrapper) {
  cursor: not-allowed;
  color: var(--text-color-disabled, #bfbfbf);
}

.tree-view-container :global(.ant-tree-treenode-disabled .ant-tree-node-content-wrapper:hover) {
  background-color: transparent;
}

/* 虚拟滚动 */
.tree-view-container :global(.ant-tree-list) {
  height: 100%;
}

.tree-view-container :global(.ant-tree-list-holder) {
  height: 100%;
  overflow: auto;
}

.tree-view-container :global(.ant-tree-list-holder-inner) {
  position: relative;
}

/* 搜索高亮 */
.tree-view-container :global(.tree-search-highlight) {
  background-color: var(--highlight-background, #ffd591);
  color: var(--highlight-color, #f50);
  padding: 0 2px;
  border-radius: 2px;
}

/* 工具栏按钮组 */
.tree-toolbar :global(.ant-btn-group) {
  display: flex;
}

.tree-toolbar :global(.ant-btn-group .ant-btn) {
  border-radius: 0;
}

.tree-toolbar :global(.ant-btn-group .ant-btn:first-child) {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.tree-toolbar :global(.ant-btn-group .ant-btn:last-child) {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

/* 搜索框 */
.tree-toolbar :global(.ant-input-search) {
  border-radius: 6px;
}

.tree-toolbar :global(.ant-input-search .ant-input) {
  border-radius: 6px 0 0 6px;
}

.tree-toolbar :global(.ant-input-search .ant-input-search-button) {
  border-radius: 0 6px 6px 0;
}

/* 下拉菜单 */
.tree-toolbar :global(.ant-dropdown-trigger) {
  border-radius: 6px;
}

/* 暗色主题 */
[data-theme="dark"] .tree-view-container {
  --background-color: #141414;
  --surface-color: #1f1f1f;
  --border-color: #434343;
  --text-color: #ffffff;
  --text-color-secondary: #a6a6a6;
  --text-color-disabled: #595959;
  --item-hover-background: #262626;
  --item-drag-background: #111b26;
  --primary-color: #177ddc;
  --primary-color-hover: #3c9be8;
  --highlight-background: #614700;
  --highlight-color: #ffa940;
}

/* 紧凑模式 */
.tree-view-container.compact :global(.ant-tree-node-content-wrapper) {
  padding: 2px 6px;
  min-height: 24px;
}

.tree-view-container.compact :global(.ant-tree-iconEle) {
  width: 14px;
  height: 14px;
}

.tree-view-container.compact :global(.ant-tree-switcher) {
  width: 14px;
  height: 14px;
}

.tree-view-container.compact .tree-toolbar {
  padding: 6px 8px;
}

/* 宽松模式 */
.tree-view-container.comfortable :global(.ant-tree-node-content-wrapper) {
  padding: 6px 12px;
  min-height: 36px;
}

.tree-view-container.comfortable :global(.ant-tree-iconEle) {
  width: 18px;
  height: 18px;
}

.tree-view-container.comfortable :global(.ant-tree-switcher) {
  width: 18px;
  height: 18px;
}

.tree-view-container.comfortable .tree-toolbar {
  padding: 12px 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tree-toolbar {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .tree-toolbar :global(.ant-input-search) {
    width: 100%;
  }
  
  .tree-view-container :global(.ant-tree-node-content-wrapper) {
    padding: 6px 8px;
    min-height: 40px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .tree-view-container {
    border-width: 2px;
  }
  
  .tree-view-container :global(.ant-tree-node-content-wrapper.ant-tree-node-selected) {
    outline: 2px solid var(--primary-color, #1890ff);
    outline-offset: -2px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .tree-view-container :global(.ant-tree-node-content-wrapper) {
    transition: none;
  }
  
  .tree-view-container :global(.ant-tree-switcher) {
    transition: none;
  }
}

/* 焦点状态 */
.tree-view-container :global(.ant-tree-focused) {
  outline: 2px solid var(--primary-color, #1890ff);
  outline-offset: -2px;
}

/* 自定义滚动条 */
.tree-view-container :global(.ant-tree-list-holder)::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.tree-view-container :global(.ant-tree-list-holder)::-webkit-scrollbar-track {
  background: var(--scrollbar-track-color, #f1f1f1);
  border-radius: 4px;
}

.tree-view-container :global(.ant-tree-list-holder)::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-color, #c1c1c1);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.tree-view-container :global(.ant-tree-list-holder)::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-color, #a8a8a8);
}

/* 性能优化 */
.tree-view-container {
  contain: layout style paint;
}

.tree-view-container :global(.ant-tree-node-content-wrapper) {
  contain: layout style paint;
}

/* 打印样式 */
@media print {
  .tree-toolbar {
    display: none;
  }
  
  .tree-view-container {
    border: none;
    height: auto;
  }
  
  .tree-content {
    height: auto;
    overflow: visible;
  }
}
