/**
 * TransformEditor 组件测试
 */
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import TransformEditor from '../TransformEditor';
import editorReducer from '../../../store/editor/editorSlice';

// Mock EngineService
jest.mock('../../../services/EngineService', () => ({
  getActiveScene: jest.fn(() => ({
    getEntities: jest.fn(() => [])
  })),
  selectEntity: jest.fn(),
  clearSelection: jest.fn()
}));

// Mock lodash debounce
jest.mock('lodash', () => ({
  debounce: jest.fn((fn) => fn)
}));

// 创建测试store
const createTestStore = (initialState: any = {}) => {
  return configureStore({
    reducer: {
      editor: editorReducer
    },
    preloadedState: {
      editor: {
        isLoading: false,
        error: null,
        activeCamera: null,
        selectedObject: null,
        selectedObjects: [],
        transformMode: 'translate' as any,
        transformSpace: 'local' as any,
        snapMode: 'disabled' as any,
        gridSize: 1,
        showGrid: true,
        showAxes: true,
        showStats: false,
        undoStack: [],
        redoStack: [],
        isPlaying: false,
        viewportSize: { width: 0, height: 0 },
        sceneGraph: [],
        ...initialState
      }
    }
  });
};

// Mock实体和变换组件
const mockTransform = {
  getPosition: jest.fn(() => ({ x: 0, y: 0, z: 0 })),
  getRotation: jest.fn(() => ({ x: 0, y: 0, z: 0 })),
  getScale: jest.fn(() => ({ x: 1, y: 1, z: 1 })),
  setPosition: jest.fn(),
  setRotation: jest.fn(),
  setScale: jest.fn(),
  on: jest.fn(),
  off: jest.fn()
};

const mockEntity = {
  id: 'test-entity-1',
  name: 'Test Entity',
  getTransform: jest.fn(() => mockTransform)
};

describe('TransformEditor', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('应该渲染无选中实体的提示', () => {
    const store = createTestStore();
    
    render(
      <Provider store={store}>
        <TransformEditor />
      </Provider>
    );

    expect(screen.getByText('请选择一个实体来编辑变换')).toBeInTheDocument();
  });

  it('应该渲染变换编辑器当有选中实体时', () => {
    const store = createTestStore({
      selectedObject: mockEntity
    });

    render(
      <Provider store={store}>
        <TransformEditor />
      </Provider>
    );

    expect(screen.getByText('变换')).toBeInTheDocument();
    expect(screen.getByText('位置 (Position)')).toBeInTheDocument();
    expect(screen.getByText('旋转 (Rotation °)')).toBeInTheDocument();
    expect(screen.getByText('缩放 (Scale)')).toBeInTheDocument();
  });

  it('应该显示实体信息', () => {
    const store = createTestStore({
      selectedObject: mockEntity
    });

    render(
      <Provider store={store}>
        <TransformEditor />
      </Provider>
    );

    expect(screen.getByText('实体: Test Entity')).toBeInTheDocument();
  });

  it('应该在只读模式下禁用输入', () => {
    const store = createTestStore({
      selectedObject: mockEntity
    });

    render(
      <Provider store={store}>
        <TransformEditor readonly={true} />
      </Provider>
    );

    const inputs = screen.getAllByRole('spinbutton');
    inputs.forEach(input => {
      expect(input).toBeDisabled();
    });
  });

  it('应该处理外部数据变化', () => {
    const store = createTestStore();
    const transformData = {
      position: { x: 1, y: 2, z: 3 },
      rotation: { x: 45, y: 90, z: 180 },
      scale: { x: 2, y: 2, z: 2 }
    };

    render(
      <Provider store={store}>
        <TransformEditor data={transformData} />
      </Provider>
    );

    // 验证表单是否显示了正确的数据
    const positionXInput = screen.getByDisplayValue('1');
    const positionYInput = screen.getByDisplayValue('2');
    const positionZInput = screen.getByDisplayValue('3');
    
    expect(positionXInput).toBeInTheDocument();
    expect(positionYInput).toBeInTheDocument();
    expect(positionZInput).toBeInTheDocument();
  });

  it('应该调用onChange回调当数据变化时', async () => {
    const store = createTestStore({
      selectedObject: mockEntity
    });
    const onChangeMock = jest.fn();

    render(
      <Provider store={store}>
        <TransformEditor onChange={onChangeMock} />
      </Provider>
    );

    // 模拟输入变化
    const positionXInput = screen.getAllByRole('spinbutton')[0];
    fireEvent.change(positionXInput, { target: { value: '5' } });

    await waitFor(() => {
      expect(onChangeMock).toHaveBeenCalled();
    });
  });

  it('应该显示工具栏按钮在非只读模式', () => {
    const store = createTestStore({
      selectedObject: mockEntity
    });

    render(
      <Provider store={store}>
        <TransformEditor />
      </Provider>
    );

    expect(screen.getByLabelText('复制变换')).toBeInTheDocument();
    expect(screen.getByLabelText('粘贴变换')).toBeInTheDocument();
    expect(screen.getByLabelText('重置变换')).toBeInTheDocument();
  });

  it('应该隐藏工具栏按钮在只读模式', () => {
    const store = createTestStore({
      selectedObject: mockEntity
    });

    render(
      <Provider store={store}>
        <TransformEditor readonly={true} />
      </Provider>
    );

    expect(screen.queryByLabelText('复制变换')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('粘贴变换')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('重置变换')).not.toBeInTheDocument();
  });
});
