function D(n) {
  const e = n.length;
  let t = 0, s = 0;
  for (; s < e; ) {
    let i = n.charCodeAt(s++);
    if (i & 4294967168)
      if (!(i & 4294965248))
        t += 2;
      else {
        if (i >= 55296 && i <= 56319 && s < e) {
          const r = n.charCodeAt(s);
          (r & 64512) === 56320 && (++s, i = ((i & 1023) << 10) + (r & 1023) + 65536);
        }
        i & 4294901760 ? t += 4 : t += 3;
      }
    else {
      t++;
      continue;
    }
  }
  return t;
}
function M(n, e, t) {
  const s = n.length;
  let i = t, r = 0;
  for (; r < s; ) {
    let h = n.charCodeAt(r++);
    if (h & 4294967168)
      if (!(h & 4294965248))
        e[i++] = h >> 6 & 31 | 192;
      else {
        if (h >= 55296 && h <= 56319 && r < s) {
          const o = n.charCodeAt(r);
          (o & 64512) === 56320 && (++r, h = ((h & 1023) << 10) + (o & 1023) + 65536);
        }
        h & 4294901760 ? (e[i++] = h >> 18 & 7 | 240, e[i++] = h >> 12 & 63 | 128, e[i++] = h >> 6 & 63 | 128) : (e[i++] = h >> 12 & 15 | 224, e[i++] = h >> 6 & 63 | 128);
      }
    else {
      e[i++] = h;
      continue;
    }
    e[i++] = h & 63 | 128;
  }
}
const L = new TextEncoder(), C = 50;
function F(n, e, t) {
  L.encodeInto(n, e.subarray(t));
}
function _(n, e, t) {
  n.length > C ? F(n, e, t) : M(n, e, t);
}
const P = 4096;
function I(n, e, t) {
  let s = e;
  const i = s + t, r = [];
  let h = "";
  for (; s < i; ) {
    const o = n[s++];
    if (!(o & 128))
      r.push(o);
    else if ((o & 224) === 192) {
      const c = n[s++] & 63;
      r.push((o & 31) << 6 | c);
    } else if ((o & 240) === 224) {
      const c = n[s++] & 63, g = n[s++] & 63;
      r.push((o & 31) << 12 | c << 6 | g);
    } else if ((o & 248) === 240) {
      const c = n[s++] & 63, g = n[s++] & 63, k = n[s++] & 63;
      let f = (o & 7) << 18 | c << 12 | g << 6 | k;
      f > 65535 && (f -= 65536, r.push(f >>> 10 & 1023 | 55296), f = 56320 | f & 1023), r.push(f);
    } else
      r.push(o);
    r.length >= P && (h += String.fromCharCode(...r), r.length = 0);
  }
  return r.length > 0 && (h += String.fromCharCode(...r)), h;
}
const R = new TextDecoder(), $ = 200;
function H(n, e, t) {
  const s = n.subarray(e, e + t);
  return R.decode(s);
}
function K(n, e, t) {
  return t > $ ? H(n, e, t) : I(n, e, t);
}
class w {
  constructor(e, t) {
    this.type = e, this.data = t;
  }
}
class a extends Error {
  constructor(e) {
    super(e);
    const t = Object.create(a.prototype);
    Object.setPrototypeOf(this, t), Object.defineProperty(this, "name", {
      configurable: !0,
      enumerable: !1,
      value: a.name
    });
  }
}
const l = 4294967295;
function b(n, e, t) {
  const s = t / 4294967296, i = t;
  n.setUint32(e, s), n.setUint32(e + 4, i);
}
function T(n, e, t) {
  const s = Math.floor(t / 4294967296), i = t;
  n.setUint32(e, s), n.setUint32(e + 4, i);
}
function v(n, e) {
  const t = n.getInt32(e), s = n.getUint32(e + 4);
  return t * 4294967296 + s;
}
function W(n, e) {
  const t = n.getUint32(e), s = n.getUint32(e + 4);
  return t * 4294967296 + s;
}
const N = -1, V = 4294967296 - 1, X = 17179869184 - 1;
function Y({ sec: n, nsec: e }) {
  if (n >= 0 && e >= 0 && n <= X)
    if (e === 0 && n <= V) {
      const t = new Uint8Array(4);
      return new DataView(t.buffer).setUint32(0, n), t;
    } else {
      const t = n / 4294967296, s = n & 4294967295, i = new Uint8Array(8), r = new DataView(i.buffer);
      return r.setUint32(0, e << 2 | t & 3), r.setUint32(4, s), i;
    }
  else {
    const t = new Uint8Array(12), s = new DataView(t.buffer);
    return s.setUint32(0, e), T(s, 4, n), t;
  }
}
function O(n) {
  const e = n.getTime(), t = Math.floor(e / 1e3), s = (e - t * 1e3) * 1e6, i = Math.floor(s / 1e9);
  return {
    sec: t + i,
    nsec: s - i * 1e9
  };
}
function J(n) {
  if (n instanceof Date) {
    const e = O(n);
    return Y(e);
  } else
    return null;
}
function q(n) {
  const e = new DataView(n.buffer, n.byteOffset, n.byteLength);
  switch (n.byteLength) {
    case 4:
      return { sec: e.getUint32(0), nsec: 0 };
    case 8: {
      const t = e.getUint32(0), s = e.getUint32(4), i = (t & 3) * 4294967296 + s, r = t >>> 2;
      return { sec: i, nsec: r };
    }
    case 12: {
      const t = v(e, 4), s = e.getUint32(0);
      return { sec: t, nsec: s };
    }
    default:
      throw new a(`Unrecognized data size for timestamp (expected 4, 8, or 12): ${n.length}`);
  }
}
function G(n) {
  const e = q(n);
  return new Date(e.sec * 1e3 + e.nsec / 1e6);
}
const Z = {
  type: N,
  encode: J,
  decode: G
};
class y {
  constructor() {
    this.builtInEncoders = [], this.builtInDecoders = [], this.encoders = [], this.decoders = [], this.register(Z);
  }
  register({ type: e, encode: t, decode: s }) {
    if (e >= 0)
      this.encoders[e] = t, this.decoders[e] = s;
    else {
      const i = -1 - e;
      this.builtInEncoders[i] = t, this.builtInDecoders[i] = s;
    }
  }
  tryToEncode(e, t) {
    for (let s = 0; s < this.builtInEncoders.length; s++) {
      const i = this.builtInEncoders[s];
      if (i != null) {
        const r = i(e, t);
        if (r != null) {
          const h = -1 - s;
          return new w(h, r);
        }
      }
    }
    for (let s = 0; s < this.encoders.length; s++) {
      const i = this.encoders[s];
      if (i != null) {
        const r = i(e, t);
        if (r != null) {
          const h = s;
          return new w(h, r);
        }
      }
    }
    return e instanceof w ? e : null;
  }
  decode(e, t, s) {
    const i = t < 0 ? this.builtInDecoders[-1 - t] : this.decoders[t];
    return i ? i(e, t, s) : new w(t, e);
  }
}
y.defaultCodec = new y();
function Q(n) {
  return n instanceof ArrayBuffer || typeof SharedArrayBuffer < "u" && n instanceof SharedArrayBuffer;
}
function m(n) {
  return n instanceof Uint8Array ? n : ArrayBuffer.isView(n) ? new Uint8Array(n.buffer, n.byteOffset, n.byteLength) : Q(n) ? new Uint8Array(n) : Uint8Array.from(n);
}
const j = 100, ee = 2048;
class B {
  constructor(e) {
    this.entered = !1, this.extensionCodec = (e == null ? void 0 : e.extensionCodec) ?? y.defaultCodec, this.context = e == null ? void 0 : e.context, this.useBigInt64 = (e == null ? void 0 : e.useBigInt64) ?? !1, this.maxDepth = (e == null ? void 0 : e.maxDepth) ?? j, this.initialBufferSize = (e == null ? void 0 : e.initialBufferSize) ?? ee, this.sortKeys = (e == null ? void 0 : e.sortKeys) ?? !1, this.forceFloat32 = (e == null ? void 0 : e.forceFloat32) ?? !1, this.ignoreUndefined = (e == null ? void 0 : e.ignoreUndefined) ?? !1, this.forceIntegerToFloat = (e == null ? void 0 : e.forceIntegerToFloat) ?? !1, this.pos = 0, this.view = new DataView(new ArrayBuffer(this.initialBufferSize)), this.bytes = new Uint8Array(this.view.buffer);
  }
  clone() {
    return new B({
      extensionCodec: this.extensionCodec,
      context: this.context,
      useBigInt64: this.useBigInt64,
      maxDepth: this.maxDepth,
      initialBufferSize: this.initialBufferSize,
      sortKeys: this.sortKeys,
      forceFloat32: this.forceFloat32,
      ignoreUndefined: this.ignoreUndefined,
      forceIntegerToFloat: this.forceIntegerToFloat
    });
  }
  reinitializeState() {
    this.pos = 0;
  }
  /**
   * This is almost equivalent to {@link Encoder#encode}, but it returns an reference of the encoder's internal buffer and thus much faster than {@link Encoder#encode}.
   *
   * @returns Encodes the object and returns a shared reference the encoder's internal buffer.
   */
  encodeSharedRef(e) {
    if (this.entered)
      return this.clone().encodeSharedRef(e);
    try {
      return this.entered = !0, this.reinitializeState(), this.doEncode(e, 1), this.bytes.subarray(0, this.pos);
    } finally {
      this.entered = !1;
    }
  }
  /**
   * @returns Encodes the object and returns a copy of the encoder's internal buffer.
   */
  encode(e) {
    if (this.entered)
      return this.clone().encode(e);
    try {
      return this.entered = !0, this.reinitializeState(), this.doEncode(e, 1), this.bytes.slice(0, this.pos);
    } finally {
      this.entered = !1;
    }
  }
  doEncode(e, t) {
    if (t > this.maxDepth)
      throw new Error(`Too deep objects in depth ${t}`);
    e == null ? this.encodeNil() : typeof e == "boolean" ? this.encodeBoolean(e) : typeof e == "number" ? this.forceIntegerToFloat ? this.encodeNumberAsFloat(e) : this.encodeNumber(e) : typeof e == "string" ? this.encodeString(e) : this.useBigInt64 && typeof e == "bigint" ? this.encodeBigInt64(e) : this.encodeObject(e, t);
  }
  ensureBufferSizeToWrite(e) {
    const t = this.pos + e;
    this.view.byteLength < t && this.resizeBuffer(t * 2);
  }
  resizeBuffer(e) {
    const t = new ArrayBuffer(e), s = new Uint8Array(t), i = new DataView(t);
    s.set(this.bytes), this.view = i, this.bytes = s;
  }
  encodeNil() {
    this.writeU8(192);
  }
  encodeBoolean(e) {
    e === !1 ? this.writeU8(194) : this.writeU8(195);
  }
  encodeNumber(e) {
    !this.forceIntegerToFloat && Number.isSafeInteger(e) ? e >= 0 ? e < 128 ? this.writeU8(e) : e < 256 ? (this.writeU8(204), this.writeU8(e)) : e < 65536 ? (this.writeU8(205), this.writeU16(e)) : e < 4294967296 ? (this.writeU8(206), this.writeU32(e)) : this.useBigInt64 ? this.encodeNumberAsFloat(e) : (this.writeU8(207), this.writeU64(e)) : e >= -32 ? this.writeU8(224 | e + 32) : e >= -128 ? (this.writeU8(208), this.writeI8(e)) : e >= -32768 ? (this.writeU8(209), this.writeI16(e)) : e >= -2147483648 ? (this.writeU8(210), this.writeI32(e)) : this.useBigInt64 ? this.encodeNumberAsFloat(e) : (this.writeU8(211), this.writeI64(e)) : this.encodeNumberAsFloat(e);
  }
  encodeNumberAsFloat(e) {
    this.forceFloat32 ? (this.writeU8(202), this.writeF32(e)) : (this.writeU8(203), this.writeF64(e));
  }
  encodeBigInt64(e) {
    e >= BigInt(0) ? (this.writeU8(207), this.writeBigUint64(e)) : (this.writeU8(211), this.writeBigInt64(e));
  }
  writeStringHeader(e) {
    if (e < 32)
      this.writeU8(160 + e);
    else if (e < 256)
      this.writeU8(217), this.writeU8(e);
    else if (e < 65536)
      this.writeU8(218), this.writeU16(e);
    else if (e < 4294967296)
      this.writeU8(219), this.writeU32(e);
    else
      throw new Error(`Too long string: ${e} bytes in UTF-8`);
  }
  encodeString(e) {
    const s = D(e);
    this.ensureBufferSizeToWrite(5 + s), this.writeStringHeader(s), _(e, this.bytes, this.pos), this.pos += s;
  }
  encodeObject(e, t) {
    const s = this.extensionCodec.tryToEncode(e, this.context);
    if (s != null)
      this.encodeExtension(s);
    else if (Array.isArray(e))
      this.encodeArray(e, t);
    else if (ArrayBuffer.isView(e))
      this.encodeBinary(e);
    else if (typeof e == "object")
      this.encodeMap(e, t);
    else
      throw new Error(`Unrecognized object: ${Object.prototype.toString.apply(e)}`);
  }
  encodeBinary(e) {
    const t = e.byteLength;
    if (t < 256)
      this.writeU8(196), this.writeU8(t);
    else if (t < 65536)
      this.writeU8(197), this.writeU16(t);
    else if (t < 4294967296)
      this.writeU8(198), this.writeU32(t);
    else
      throw new Error(`Too large binary: ${t}`);
    const s = m(e);
    this.writeU8a(s);
  }
  encodeArray(e, t) {
    const s = e.length;
    if (s < 16)
      this.writeU8(144 + s);
    else if (s < 65536)
      this.writeU8(220), this.writeU16(s);
    else if (s < 4294967296)
      this.writeU8(221), this.writeU32(s);
    else
      throw new Error(`Too large array: ${s}`);
    for (const i of e)
      this.doEncode(i, t + 1);
  }
  countWithoutUndefined(e, t) {
    let s = 0;
    for (const i of t)
      e[i] !== void 0 && s++;
    return s;
  }
  encodeMap(e, t) {
    const s = Object.keys(e);
    this.sortKeys && s.sort();
    const i = this.ignoreUndefined ? this.countWithoutUndefined(e, s) : s.length;
    if (i < 16)
      this.writeU8(128 + i);
    else if (i < 65536)
      this.writeU8(222), this.writeU16(i);
    else if (i < 4294967296)
      this.writeU8(223), this.writeU32(i);
    else
      throw new Error(`Too large map object: ${i}`);
    for (const r of s) {
      const h = e[r];
      this.ignoreUndefined && h === void 0 || (this.encodeString(r), this.doEncode(h, t + 1));
    }
  }
  encodeExtension(e) {
    if (typeof e.data == "function") {
      const s = e.data(this.pos + 6), i = s.length;
      if (i >= 4294967296)
        throw new Error(`Too large extension object: ${i}`);
      this.writeU8(201), this.writeU32(i), this.writeI8(e.type), this.writeU8a(s);
      return;
    }
    const t = e.data.length;
    if (t === 1)
      this.writeU8(212);
    else if (t === 2)
      this.writeU8(213);
    else if (t === 4)
      this.writeU8(214);
    else if (t === 8)
      this.writeU8(215);
    else if (t === 16)
      this.writeU8(216);
    else if (t < 256)
      this.writeU8(199), this.writeU8(t);
    else if (t < 65536)
      this.writeU8(200), this.writeU16(t);
    else if (t < 4294967296)
      this.writeU8(201), this.writeU32(t);
    else
      throw new Error(`Too large extension object: ${t}`);
    this.writeI8(e.type), this.writeU8a(e.data);
  }
  writeU8(e) {
    this.ensureBufferSizeToWrite(1), this.view.setUint8(this.pos, e), this.pos++;
  }
  writeU8a(e) {
    const t = e.length;
    this.ensureBufferSizeToWrite(t), this.bytes.set(e, this.pos), this.pos += t;
  }
  writeI8(e) {
    this.ensureBufferSizeToWrite(1), this.view.setInt8(this.pos, e), this.pos++;
  }
  writeU16(e) {
    this.ensureBufferSizeToWrite(2), this.view.setUint16(this.pos, e), this.pos += 2;
  }
  writeI16(e) {
    this.ensureBufferSizeToWrite(2), this.view.setInt16(this.pos, e), this.pos += 2;
  }
  writeU32(e) {
    this.ensureBufferSizeToWrite(4), this.view.setUint32(this.pos, e), this.pos += 4;
  }
  writeI32(e) {
    this.ensureBufferSizeToWrite(4), this.view.setInt32(this.pos, e), this.pos += 4;
  }
  writeF32(e) {
    this.ensureBufferSizeToWrite(4), this.view.setFloat32(this.pos, e), this.pos += 4;
  }
  writeF64(e) {
    this.ensureBufferSizeToWrite(8), this.view.setFloat64(this.pos, e), this.pos += 8;
  }
  writeU64(e) {
    this.ensureBufferSizeToWrite(8), b(this.view, this.pos, e), this.pos += 8;
  }
  writeI64(e) {
    this.ensureBufferSizeToWrite(8), T(this.view, this.pos, e), this.pos += 8;
  }
  writeBigUint64(e) {
    this.ensureBufferSizeToWrite(8), this.view.setBigUint64(this.pos, e), this.pos += 8;
  }
  writeBigInt64(e) {
    this.ensureBufferSizeToWrite(8), this.view.setBigInt64(this.pos, e), this.pos += 8;
  }
}
function de(n, e) {
  return new B(e).encodeSharedRef(n);
}
function U(n) {
  return `${n < 0 ? "-" : ""}0x${Math.abs(n).toString(16).padStart(2, "0")}`;
}
const te = 16, ie = 16;
class se {
  constructor(e = te, t = ie) {
    this.hit = 0, this.miss = 0, this.maxKeyLength = e, this.maxLengthPerKey = t, this.caches = [];
    for (let s = 0; s < this.maxKeyLength; s++)
      this.caches.push([]);
  }
  canBeCached(e) {
    return e > 0 && e <= this.maxKeyLength;
  }
  find(e, t, s) {
    const i = this.caches[s - 1];
    e:
      for (const r of i) {
        const h = r.bytes;
        for (let o = 0; o < s; o++)
          if (h[o] !== e[t + o])
            continue e;
        return r.str;
      }
    return null;
  }
  store(e, t) {
    const s = this.caches[e.length - 1], i = { bytes: e, str: t };
    s.length >= this.maxLengthPerKey ? s[Math.random() * s.length | 0] = i : s.push(i);
  }
  decode(e, t, s) {
    const i = this.find(e, t, s);
    if (i != null)
      return this.hit++, i;
    this.miss++;
    const r = I(e, t, s), h = Uint8Array.prototype.slice.call(e, t, t + s);
    return this.store(h, r), r;
  }
}
const S = "array", x = "map_key", z = "map_value", ne = (n) => {
  if (typeof n == "string" || typeof n == "number")
    return n;
  throw new a("The type of key must be string or number but " + typeof n);
};
class re {
  constructor() {
    this.stack = [], this.stackHeadPosition = -1;
  }
  get length() {
    return this.stackHeadPosition + 1;
  }
  top() {
    return this.stack[this.stackHeadPosition];
  }
  pushArrayState(e) {
    const t = this.getUninitializedStateFromPool();
    t.type = S, t.position = 0, t.size = e, t.array = new Array(e);
  }
  pushMapState(e) {
    const t = this.getUninitializedStateFromPool();
    t.type = x, t.readCount = 0, t.size = e, t.map = {};
  }
  getUninitializedStateFromPool() {
    if (this.stackHeadPosition++, this.stackHeadPosition === this.stack.length) {
      const e = {
        type: void 0,
        size: 0,
        array: void 0,
        position: 0,
        readCount: 0,
        map: void 0,
        key: null
      };
      this.stack.push(e);
    }
    return this.stack[this.stackHeadPosition];
  }
  release(e) {
    if (this.stack[this.stackHeadPosition] !== e)
      throw new Error("Invalid stack state. Released state is not on top of the stack.");
    if (e.type === S) {
      const s = e;
      s.size = 0, s.array = void 0, s.position = 0, s.type = void 0;
    }
    if (e.type === x || e.type === z) {
      const s = e;
      s.size = 0, s.map = void 0, s.readCount = 0, s.type = void 0;
    }
    this.stackHeadPosition--;
  }
  reset() {
    this.stack.length = 0, this.stackHeadPosition = -1;
  }
}
const u = -1, E = new DataView(new ArrayBuffer(0)), he = new Uint8Array(E.buffer);
try {
  E.getInt8(0);
} catch (n) {
  if (!(n instanceof RangeError))
    throw new Error("This module is not supported in the current JavaScript engine because DataView does not throw RangeError on out-of-bounds access");
}
const A = new RangeError("Insufficient data"), oe = new se();
class d {
  constructor(e) {
    this.totalPos = 0, this.pos = 0, this.view = E, this.bytes = he, this.headByte = u, this.stack = new re(), this.entered = !1, this.extensionCodec = (e == null ? void 0 : e.extensionCodec) ?? y.defaultCodec, this.context = e == null ? void 0 : e.context, this.useBigInt64 = (e == null ? void 0 : e.useBigInt64) ?? !1, this.rawStrings = (e == null ? void 0 : e.rawStrings) ?? !1, this.maxStrLength = (e == null ? void 0 : e.maxStrLength) ?? l, this.maxBinLength = (e == null ? void 0 : e.maxBinLength) ?? l, this.maxArrayLength = (e == null ? void 0 : e.maxArrayLength) ?? l, this.maxMapLength = (e == null ? void 0 : e.maxMapLength) ?? l, this.maxExtLength = (e == null ? void 0 : e.maxExtLength) ?? l, this.keyDecoder = (e == null ? void 0 : e.keyDecoder) !== void 0 ? e.keyDecoder : oe, this.mapKeyConverter = (e == null ? void 0 : e.mapKeyConverter) ?? ne;
  }
  clone() {
    return new d({
      extensionCodec: this.extensionCodec,
      context: this.context,
      useBigInt64: this.useBigInt64,
      rawStrings: this.rawStrings,
      maxStrLength: this.maxStrLength,
      maxBinLength: this.maxBinLength,
      maxArrayLength: this.maxArrayLength,
      maxMapLength: this.maxMapLength,
      maxExtLength: this.maxExtLength,
      keyDecoder: this.keyDecoder
    });
  }
  reinitializeState() {
    this.totalPos = 0, this.headByte = u, this.stack.reset();
  }
  setBuffer(e) {
    const t = m(e);
    this.bytes = t, this.view = new DataView(t.buffer, t.byteOffset, t.byteLength), this.pos = 0;
  }
  appendBuffer(e) {
    if (this.headByte === u && !this.hasRemaining(1))
      this.setBuffer(e);
    else {
      const t = this.bytes.subarray(this.pos), s = m(e), i = new Uint8Array(t.length + s.length);
      i.set(t), i.set(s, t.length), this.setBuffer(i);
    }
  }
  hasRemaining(e) {
    return this.view.byteLength - this.pos >= e;
  }
  createExtraByteError(e) {
    const { view: t, pos: s } = this;
    return new RangeError(`Extra ${t.byteLength - s} of ${t.byteLength} byte(s) found at buffer[${e}]`);
  }
  /**
   * @throws {@link DecodeError}
   * @throws {@link RangeError}
   */
  decode(e) {
    if (this.entered)
      return this.clone().decode(e);
    try {
      this.entered = !0, this.reinitializeState(), this.setBuffer(e);
      const t = this.doDecodeSync();
      if (this.hasRemaining(1))
        throw this.createExtraByteError(this.pos);
      return t;
    } finally {
      this.entered = !1;
    }
  }
  *decodeMulti(e) {
    if (this.entered) {
      yield* this.clone().decodeMulti(e);
      return;
    }
    try {
      for (this.entered = !0, this.reinitializeState(), this.setBuffer(e); this.hasRemaining(1); )
        yield this.doDecodeSync();
    } finally {
      this.entered = !1;
    }
  }
  async decodeAsync(e) {
    if (this.entered)
      return this.clone().decodeAsync(e);
    try {
      this.entered = !0;
      let t = !1, s;
      for await (const o of e) {
        if (t)
          throw this.entered = !1, this.createExtraByteError(this.totalPos);
        this.appendBuffer(o);
        try {
          s = this.doDecodeSync(), t = !0;
        } catch (c) {
          if (!(c instanceof RangeError))
            throw c;
        }
        this.totalPos += this.pos;
      }
      if (t) {
        if (this.hasRemaining(1))
          throw this.createExtraByteError(this.totalPos);
        return s;
      }
      const { headByte: i, pos: r, totalPos: h } = this;
      throw new RangeError(`Insufficient data in parsing ${U(i)} at ${h} (${r} in the current buffer)`);
    } finally {
      this.entered = !1;
    }
  }
  decodeArrayStream(e) {
    return this.decodeMultiAsync(e, !0);
  }
  decodeStream(e) {
    return this.decodeMultiAsync(e, !1);
  }
  async *decodeMultiAsync(e, t) {
    if (this.entered) {
      yield* this.clone().decodeMultiAsync(e, t);
      return;
    }
    try {
      this.entered = !0;
      let s = t, i = -1;
      for await (const r of e) {
        if (t && i === 0)
          throw this.createExtraByteError(this.totalPos);
        this.appendBuffer(r), s && (i = this.readArraySize(), s = !1, this.complete());
        try {
          for (; yield this.doDecodeSync(), --i !== 0; )
            ;
        } catch (h) {
          if (!(h instanceof RangeError))
            throw h;
        }
        this.totalPos += this.pos;
      }
    } finally {
      this.entered = !1;
    }
  }
  doDecodeSync() {
    e:
      for (; ; ) {
        const e = this.readHeadByte();
        let t;
        if (e >= 224)
          t = e - 256;
        else if (e < 192)
          if (e < 128)
            t = e;
          else if (e < 144) {
            const i = e - 128;
            if (i !== 0) {
              this.pushMapState(i), this.complete();
              continue e;
            } else
              t = {};
          } else if (e < 160) {
            const i = e - 144;
            if (i !== 0) {
              this.pushArrayState(i), this.complete();
              continue e;
            } else
              t = [];
          } else {
            const i = e - 160;
            t = this.decodeString(i, 0);
          }
        else if (e === 192)
          t = null;
        else if (e === 194)
          t = !1;
        else if (e === 195)
          t = !0;
        else if (e === 202)
          t = this.readF32();
        else if (e === 203)
          t = this.readF64();
        else if (e === 204)
          t = this.readU8();
        else if (e === 205)
          t = this.readU16();
        else if (e === 206)
          t = this.readU32();
        else if (e === 207)
          this.useBigInt64 ? t = this.readU64AsBigInt() : t = this.readU64();
        else if (e === 208)
          t = this.readI8();
        else if (e === 209)
          t = this.readI16();
        else if (e === 210)
          t = this.readI32();
        else if (e === 211)
          this.useBigInt64 ? t = this.readI64AsBigInt() : t = this.readI64();
        else if (e === 217) {
          const i = this.lookU8();
          t = this.decodeString(i, 1);
        } else if (e === 218) {
          const i = this.lookU16();
          t = this.decodeString(i, 2);
        } else if (e === 219) {
          const i = this.lookU32();
          t = this.decodeString(i, 4);
        } else if (e === 220) {
          const i = this.readU16();
          if (i !== 0) {
            this.pushArrayState(i), this.complete();
            continue e;
          } else
            t = [];
        } else if (e === 221) {
          const i = this.readU32();
          if (i !== 0) {
            this.pushArrayState(i), this.complete();
            continue e;
          } else
            t = [];
        } else if (e === 222) {
          const i = this.readU16();
          if (i !== 0) {
            this.pushMapState(i), this.complete();
            continue e;
          } else
            t = {};
        } else if (e === 223) {
          const i = this.readU32();
          if (i !== 0) {
            this.pushMapState(i), this.complete();
            continue e;
          } else
            t = {};
        } else if (e === 196) {
          const i = this.lookU8();
          t = this.decodeBinary(i, 1);
        } else if (e === 197) {
          const i = this.lookU16();
          t = this.decodeBinary(i, 2);
        } else if (e === 198) {
          const i = this.lookU32();
          t = this.decodeBinary(i, 4);
        } else if (e === 212)
          t = this.decodeExtension(1, 0);
        else if (e === 213)
          t = this.decodeExtension(2, 0);
        else if (e === 214)
          t = this.decodeExtension(4, 0);
        else if (e === 215)
          t = this.decodeExtension(8, 0);
        else if (e === 216)
          t = this.decodeExtension(16, 0);
        else if (e === 199) {
          const i = this.lookU8();
          t = this.decodeExtension(i, 1);
        } else if (e === 200) {
          const i = this.lookU16();
          t = this.decodeExtension(i, 2);
        } else if (e === 201) {
          const i = this.lookU32();
          t = this.decodeExtension(i, 4);
        } else
          throw new a(`Unrecognized type byte: ${U(e)}`);
        this.complete();
        const s = this.stack;
        for (; s.length > 0; ) {
          const i = s.top();
          if (i.type === S)
            if (i.array[i.position] = t, i.position++, i.position === i.size)
              t = i.array, s.release(i);
            else
              continue e;
          else if (i.type === x) {
            if (t === "__proto__")
              throw new a("The key __proto__ is not allowed");
            i.key = this.mapKeyConverter(t), i.type = z;
            continue e;
          } else if (i.map[i.key] = t, i.readCount++, i.readCount === i.size)
            t = i.map, s.release(i);
          else {
            i.key = null, i.type = x;
            continue e;
          }
        }
        return t;
      }
  }
  readHeadByte() {
    return this.headByte === u && (this.headByte = this.readU8()), this.headByte;
  }
  complete() {
    this.headByte = u;
  }
  readArraySize() {
    const e = this.readHeadByte();
    switch (e) {
      case 220:
        return this.readU16();
      case 221:
        return this.readU32();
      default: {
        if (e < 160)
          return e - 144;
        throw new a(`Unrecognized array type byte: ${U(e)}`);
      }
    }
  }
  pushMapState(e) {
    if (e > this.maxMapLength)
      throw new a(`Max length exceeded: map length (${e}) > maxMapLengthLength (${this.maxMapLength})`);
    this.stack.pushMapState(e);
  }
  pushArrayState(e) {
    if (e > this.maxArrayLength)
      throw new a(`Max length exceeded: array length (${e}) > maxArrayLength (${this.maxArrayLength})`);
    this.stack.pushArrayState(e);
  }
  decodeString(e, t) {
    return !this.rawStrings || this.stateIsMapKey() ? this.decodeUtf8String(e, t) : this.decodeBinary(e, t);
  }
  /**
   * @throws {@link RangeError}
   */
  decodeUtf8String(e, t) {
    var r;
    if (e > this.maxStrLength)
      throw new a(`Max length exceeded: UTF-8 byte length (${e}) > maxStrLength (${this.maxStrLength})`);
    if (this.bytes.byteLength < this.pos + t + e)
      throw A;
    const s = this.pos + t;
    let i;
    return this.stateIsMapKey() && ((r = this.keyDecoder) != null && r.canBeCached(e)) ? i = this.keyDecoder.decode(this.bytes, s, e) : i = K(this.bytes, s, e), this.pos += t + e, i;
  }
  stateIsMapKey() {
    return this.stack.length > 0 ? this.stack.top().type === x : !1;
  }
  /**
   * @throws {@link RangeError}
   */
  decodeBinary(e, t) {
    if (e > this.maxBinLength)
      throw new a(`Max length exceeded: bin length (${e}) > maxBinLength (${this.maxBinLength})`);
    if (!this.hasRemaining(e + t))
      throw A;
    const s = this.pos + t, i = this.bytes.subarray(s, s + e);
    return this.pos += t + e, i;
  }
  decodeExtension(e, t) {
    if (e > this.maxExtLength)
      throw new a(`Max length exceeded: ext length (${e}) > maxExtLength (${this.maxExtLength})`);
    const s = this.view.getInt8(this.pos + t), i = this.decodeBinary(
      e,
      t + 1
      /* extType */
    );
    return this.extensionCodec.decode(i, s, this.context);
  }
  lookU8() {
    return this.view.getUint8(this.pos);
  }
  lookU16() {
    return this.view.getUint16(this.pos);
  }
  lookU32() {
    return this.view.getUint32(this.pos);
  }
  readU8() {
    const e = this.view.getUint8(this.pos);
    return this.pos++, e;
  }
  readI8() {
    const e = this.view.getInt8(this.pos);
    return this.pos++, e;
  }
  readU16() {
    const e = this.view.getUint16(this.pos);
    return this.pos += 2, e;
  }
  readI16() {
    const e = this.view.getInt16(this.pos);
    return this.pos += 2, e;
  }
  readU32() {
    const e = this.view.getUint32(this.pos);
    return this.pos += 4, e;
  }
  readI32() {
    const e = this.view.getInt32(this.pos);
    return this.pos += 4, e;
  }
  readU64() {
    const e = W(this.view, this.pos);
    return this.pos += 8, e;
  }
  readI64() {
    const e = v(this.view, this.pos);
    return this.pos += 8, e;
  }
  readU64AsBigInt() {
    const e = this.view.getBigUint64(this.pos);
    return this.pos += 8, e;
  }
  readI64AsBigInt() {
    const e = this.view.getBigInt64(this.pos);
    return this.pos += 8, e;
  }
  readF32() {
    const e = this.view.getFloat32(this.pos);
    return this.pos += 4, e;
  }
  readF64() {
    const e = this.view.getFloat64(this.pos);
    return this.pos += 8, e;
  }
}
function fe(n, e) {
  return new d(e).decode(n);
}
function le(n, e) {
  return new d(e).decodeMulti(n);
}
function ae(n) {
  return n[Symbol.asyncIterator] != null;
}
async function* ce(n) {
  const e = n.getReader();
  try {
    for (; ; ) {
      const { done: t, value: s } = await e.read();
      if (t)
        return;
      yield s;
    }
  } finally {
    e.releaseLock();
  }
}
function p(n) {
  return ae(n) ? n : ce(n);
}
async function ue(n, e) {
  const t = p(n);
  return new d(e).decodeAsync(t);
}
function xe(n, e) {
  const t = p(n);
  return new d(e).decodeArrayStream(t);
}
function we(n, e) {
  const t = p(n);
  return new d(e).decodeStream(t);
}
export {
  a as DecodeError,
  d as Decoder,
  N as EXT_TIMESTAMP,
  B as Encoder,
  w as ExtData,
  y as ExtensionCodec,
  fe as decode,
  xe as decodeArrayStream,
  ue as decodeAsync,
  le as decodeMulti,
  we as decodeMultiStream,
  G as decodeTimestampExtension,
  q as decodeTimestampToTimeSpec,
  de as encode,
  O as encodeDateToTimeSpec,
  Y as encodeTimeSpecToTimestamp,
  J as encodeTimestampExtension
};
