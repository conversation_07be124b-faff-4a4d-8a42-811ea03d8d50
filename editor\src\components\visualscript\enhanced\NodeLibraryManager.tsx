/**
 * NodeLibraryManager.tsx
 * 
 * 节点库管理组件 - 提供完整的节点库管理功能
 */

import React, { useState, useCallback } from 'react';
import {
  Card,
  Button,
  Space,
  Input,
  Select,
  Table,
  Modal,
  Form,
  Upload,
  message,
  Popconfirm,
  Tag,
  Tooltip,
  Drawer,
  Typography,
  Tabs,
  List,
  Avatar,
  Rate,
  Divider
} from 'antd';
import {
  PlusOutlined,
  ImportOutlined,
  ExportOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  DownloadOutlined,
  SearchOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;
const { Option } = Select;

/**
 * 节点定义接口
 */
export interface NodeDefinition {
  id: string;
  type: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  version: string;
  author: string;
  icon?: string;
  color?: string;
  inputs: SocketDefinition[];
  outputs: SocketDefinition[];
  properties: PropertyDefinition[];
  documentation?: string;
  examples?: NodeExample[];
  rating?: number;
  downloads?: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * 插槽定义接口
 */
export interface SocketDefinition {
  name: string;
  type: 'flow' | 'data';
  dataType?: string;
  description: string;
  required?: boolean;
  defaultValue?: any;
}

/**
 * 属性定义接口
 */
export interface PropertyDefinition {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'select' | 'color' | 'file';
  description: string;
  defaultValue?: any;
  options?: string[];
  min?: number;
  max?: number;
  required?: boolean;
}

/**
 * 节点示例接口
 */
export interface NodeExample {
  title: string;
  description: string;
  code: string;
  image?: string;
}

/**
 * 节点库管理组件属性
 */
export interface NodeLibraryManagerProps {
  /** 节点列表 */
  nodes: NodeDefinition[];
  /** 节点分类列表 */
  categories: string[];
  /** 是否只读模式 */
  readOnly?: boolean;
  /** 节点导入回调 */
  onNodeImport?: (nodeData: NodeDefinition) => void;
  /** 节点导出回调 */
  onNodeExport?: (nodeId: string) => void;
  /** 节点创建回调 */
  onNodeCreate?: (nodeDefinition: NodeDefinition) => void;
  /** 节点更新回调 */
  onNodeUpdate?: (nodeId: string, nodeDefinition: NodeDefinition) => void;
  /** 节点删除回调 */
  onNodeDelete?: (nodeId: string) => void;
  /** 节点安装回调 */
  onNodeInstall?: (nodeDefinition: NodeDefinition) => void;
}

/**
 * 节点库管理组件
 */
const NodeLibraryManager: React.FC<NodeLibraryManagerProps> = ({
  nodes,
  categories,
  readOnly = false,
  onNodeImport,
  onNodeExport,
  onNodeCreate,
  onNodeUpdate,
  onNodeDelete,
  onNodeInstall
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  // 状态管理
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<string>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  
  // 模态框状态
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);
  
  // 选中的节点
  const [selectedNode, setSelectedNode] = useState<NodeDefinition | null>(null);
  const [editingNode, setEditingNode] = useState<NodeDefinition | null>(null);

  /**
   * 过滤和排序节点
   */
  const filteredAndSortedNodes = useCallback(() => {
    let filtered = nodes.filter(node => {
      // 搜索过滤
      if (searchText && !node.name.toLowerCase().includes(searchText.toLowerCase()) &&
          !node.description.toLowerCase().includes(searchText.toLowerCase())) {
        return false;
      }
      
      // 分类过滤
      if (selectedCategory !== 'all' && node.category !== selectedCategory) {
        return false;
      }
      
      // 标签过滤
      if (selectedTags.length > 0 && !selectedTags.some(tag => node.tags.includes(tag))) {
        return false;
      }
      
      return true;
    });

    // 排序
    filtered.sort((a, b) => {
      let aValue: any = a[sortBy as keyof NodeDefinition];
      let bValue: any = b[sortBy as keyof NodeDefinition];
      
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [nodes, searchText, selectedCategory, selectedTags, sortBy, sortOrder]);

  /**
   * 获取所有标签
   */
  const allTags = useCallback((): string[] => {
    const tags = new Set<string>();
    nodes.forEach(node => {
      node.tags.forEach(tag => tags.add(tag));
    });
    return Array.from(tags);
  }, [nodes]);

  /**
   * 处理节点创建
   */
  const handleNodeCreate = useCallback(async () => {
    try {
      const values = await form.validateFields();
      const newNode: NodeDefinition = {
        id: `custom_${Date.now()}`,
        type: values.type,
        name: values.name,
        description: values.description,
        category: values.category,
        tags: values.tags || [],
        version: '1.0.0',
        author: 'User',
        inputs: values.inputs || [],
        outputs: values.outputs || [],
        properties: values.properties || [],
        rating: 0,
        downloads: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      if (onNodeCreate) {
        onNodeCreate(newNode);
      }

      setCreateModalVisible(false);
      form.resetFields();
      message.success(t('节点库.创建成功'));
    } catch (error) {
      console.error('创建节点失败:', error);
    }
  }, [form, onNodeCreate, t]);

  /**
   * 处理节点编辑
   */
  const handleNodeEdit = useCallback(async () => {
    if (!editingNode) return;

    try {
      const values = await form.validateFields();
      const updatedNode: NodeDefinition = {
        ...editingNode,
        ...values,
        updatedAt: new Date().toISOString()
      };

      if (onNodeUpdate) {
        onNodeUpdate(editingNode.id, updatedNode);
      }

      setEditModalVisible(false);
      setEditingNode(null);
      form.resetFields();
      message.success(t('节点库.更新成功'));
    } catch (error) {
      console.error('更新节点失败:', error);
    }
  }, [editingNode, form, onNodeUpdate, t]);

  /**
   * 处理节点删除
   */
  const handleNodeDelete = useCallback((nodeId: string) => {
    if (onNodeDelete) {
      onNodeDelete(nodeId);
    }
    message.success(t('节点库.删除成功'));
  }, [onNodeDelete, t]);

  /**
   * 处理节点导出
   */
  const handleNodeExport = useCallback((nodeId: string) => {
    if (onNodeExport) {
      onNodeExport(nodeId);
    }
    message.success(t('节点库.导出成功'));
  }, [onNodeExport, t]);

  /**
   * 处理节点导入
   */
  const handleNodeImport = useCallback((file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const nodeData = JSON.parse(e.target?.result as string);
        if (onNodeImport) {
          onNodeImport(nodeData);
        }
        message.success(t('节点库.导入成功'));
      } catch (error) {
        message.error(t('节点库.导入失败'));
      }
    };
    reader.readAsText(file);
    return false; // 阻止默认上传行为
  }, [onNodeImport, t]);

  /**
   * 渲染搜索和过滤工具栏
   */
  const renderToolbar = () => (
    <Card size="small">
      <Space wrap>
        <Input
          prefix={<SearchOutlined />}
          placeholder={t('节点库.搜索节点') as string}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 200 }}
          allowClear
        />
        
        <Select
          value={selectedCategory}
          onChange={setSelectedCategory}
          style={{ width: 120 }}
          placeholder={t('节点库.选择分类')}
        >
          <Option value="all">{t('节点库.全部分类')}</Option>
          {categories.map(category => (
            <Option key={category} value={category}>
              {t(`节点分类.${category}`)}
            </Option>
          ))}
        </Select>
        
        <Select
          mode="multiple"
          value={selectedTags}
          onChange={setSelectedTags}
          style={{ width: 200 }}
          placeholder={t('节点库.选择标签')}
          maxTagCount={2}
        >
          {allTags().map(tag => (
            <Option key={tag} value={tag}>{tag}</Option>
          ))}
        </Select>
        
        <Select
          value={`${sortBy}-${sortOrder}`}
          onChange={(value) => {
            const [field, order] = value.split('-');
            setSortBy(field);
            setSortOrder(order as 'asc' | 'desc');
          }}
          style={{ width: 150 }}
        >
          <Option value="name-asc">{t('节点库.按名称升序')}</Option>
          <Option value="name-desc">{t('节点库.按名称降序')}</Option>
          <Option value="createdAt-desc">{t('节点库.按创建时间')}</Option>
          <Option value="rating-desc">{t('节点库.按评分')}</Option>
          <Option value="downloads-desc">{t('节点库.按下载量')}</Option>
        </Select>
        
        <Divider type="vertical" />
        
        {!readOnly && (
          <>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              {t('节点库.创建节点')}
            </Button>
            
            <Upload
              accept=".json"
              beforeUpload={handleNodeImport}
              showUploadList={false}
            >
              <Button icon={<ImportOutlined />}>
                {t('节点库.导入节点')}
              </Button>
            </Upload>
          </>
        )}
      </Space>
    </Card>
  );

  /**
   * 渲染节点列表
   */
  const renderNodeList = () => {
    const dataSource = filteredAndSortedNodes();

    const columns = [
      {
        title: t('节点库.名称'),
        dataIndex: 'name',
        key: 'name',
        render: (text: string, record: NodeDefinition) => (
          <Space>
            {record.icon && <Avatar src={record.icon} size="small" />}
            <Text strong>{text}</Text>
            <Tag color={record.color}>{record.category}</Tag>
          </Space>
        )
      },
      {
        title: t('节点库.描述'),
        dataIndex: 'description',
        key: 'description',
        ellipsis: true
      },
      {
        title: t('节点库.标签'),
        dataIndex: 'tags',
        key: 'tags',
        render: (tags: string[]) => (
          <Space wrap>
            {tags.slice(0, 3).map(tag => (
              <Tag key={tag}>{tag}</Tag>
            ))}
            {tags.length > 3 && <Text type="secondary">+{tags.length - 3}</Text>}
          </Space>
        )
      },
      {
        title: t('节点库.评分'),
        dataIndex: 'rating',
        key: 'rating',
        render: (rating: number) => <Rate disabled value={rating} count={5} />
      },
      {
        title: t('节点库.版本'),
        dataIndex: 'version',
        key: 'version'
      },
      {
        title: t('节点库.操作'),
        key: 'actions',
        render: (_: any, record: NodeDefinition) => (
          <Space>
            <Tooltip title={t('节点库.查看详情')}>
              <Button
                size="small"
                icon={<EyeOutlined />}
                onClick={() => {
                  setSelectedNode(record);
                  setDetailDrawerVisible(true);
                }}
              />
            </Tooltip>
            
            {!readOnly && (
              <>
                <Tooltip title={t('节点库.编辑')}>
                  <Button
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => {
                      setEditingNode(record);
                      form.setFieldsValue(record);
                      setEditModalVisible(true);
                    }}
                  />
                </Tooltip>
                
                <Tooltip title={t('节点库.导出')}>
                  <Button
                    size="small"
                    icon={<ExportOutlined />}
                    onClick={() => handleNodeExport(record.id)}
                  />
                </Tooltip>
                
                <Popconfirm
                  title={t('节点库.确认删除')}
                  onConfirm={() => handleNodeDelete(record.id)}
                >
                  <Tooltip title={t('节点库.删除')}>
                    <Button
                      size="small"
                      icon={<DeleteOutlined />}
                      danger
                    />
                  </Tooltip>
                </Popconfirm>
              </>
            )}
            
            {onNodeInstall && (
              <Tooltip title={t('节点库.安装')}>
                <Button
                  size="small"
                  icon={<DownloadOutlined />}
                  onClick={() => onNodeInstall(record)}
                />
              </Tooltip>
            )}
          </Space>
        )
      }
    ];

    return (
      <Table
        dataSource={dataSource}
        columns={columns}
        rowKey="id"
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => t('节点库.共计节点', { total })
        }}
      />
    );
  };

  /**
   * 渲染节点详情抽屉
   */
  const renderDetailDrawer = () => (
    <Drawer
      title={selectedNode?.name}
      placement="right"
      width={600}
      open={detailDrawerVisible}
      onClose={() => setDetailDrawerVisible(false)}
    >
      {selectedNode && (
        <Tabs defaultActiveKey="info">
          <TabPane tab={t('节点库.基本信息')} key="info">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>{t('节点库.描述')}: </Text>
                <Paragraph>{selectedNode.description}</Paragraph>
              </div>
              
              <div>
                <Text strong>{t('节点库.分类')}: </Text>
                <Tag color={selectedNode.color}>{selectedNode.category}</Tag>
              </div>
              
              <div>
                <Text strong>{t('节点库.标签')}: </Text>
                <Space wrap>
                  {selectedNode.tags.map(tag => (
                    <Tag key={tag}>{tag}</Tag>
                  ))}
                </Space>
              </div>
              
              <div>
                <Text strong>{t('节点库.版本')}: </Text>
                <Text code>{selectedNode.version}</Text>
              </div>
              
              <div>
                <Text strong>{t('节点库.作者')}: </Text>
                <Text>{selectedNode.author}</Text>
              </div>
              
              <div>
                <Text strong>{t('节点库.评分')}: </Text>
                <Rate disabled value={selectedNode.rating} />
              </div>
            </Space>
          </TabPane>
          
          <TabPane tab={t('节点库.接口定义')} key="interface">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Title level={5}>{t('节点库.输入插槽')}</Title>
                <List
                  dataSource={selectedNode.inputs}
                  renderItem={(input) => (
                    <List.Item>
                      <Space>
                        <Tag color={input.type === 'flow' ? 'blue' : 'green'}>
                          {input.type}
                        </Tag>
                        <Text strong>{input.name}</Text>
                        <Text type="secondary">{input.description}</Text>
                      </Space>
                    </List.Item>
                  )}
                />
              </div>
              
              <div>
                <Title level={5}>{t('节点库.输出插槽')}</Title>
                <List
                  dataSource={selectedNode.outputs}
                  renderItem={(output) => (
                    <List.Item>
                      <Space>
                        <Tag color={output.type === 'flow' ? 'blue' : 'green'}>
                          {output.type}
                        </Tag>
                        <Text strong>{output.name}</Text>
                        <Text type="secondary">{output.description}</Text>
                      </Space>
                    </List.Item>
                  )}
                />
              </div>
            </Space>
          </TabPane>
          
          {selectedNode.documentation && (
            <TabPane tab={t('节点库.文档')} key="documentation">
              <div dangerouslySetInnerHTML={{ __html: selectedNode.documentation }} />
            </TabPane>
          )}
          
          {selectedNode.examples && selectedNode.examples.length > 0 && (
            <TabPane tab={t('节点库.示例')} key="examples">
              <List
                dataSource={selectedNode.examples}
                renderItem={(example) => (
                  <List.Item>
                    <Card size="small" title={example.title}>
                      <Paragraph>{example.description}</Paragraph>
                      <pre style={{ background: '#f5f5f5', padding: 8 }}>
                        {example.code}
                      </pre>
                    </Card>
                  </List.Item>
                )}
              />
            </TabPane>
          )}
        </Tabs>
      )}
    </Drawer>
  );

  return (
    <div className="node-library-manager">
      <Space direction="vertical" style={{ width: '100%' }}>
        {renderToolbar()}
        {renderNodeList()}
      </Space>
      
      {renderDetailDrawer()}
      
      {/* 创建节点模态框 */}
      <Modal
        title={t('节点库.创建节点')}
        open={createModalVisible}
        onOk={handleNodeCreate}
        onCancel={() => setCreateModalVisible(false)}
        width={800}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label={t('节点库.节点名称') as string}
            rules={[{ required: true, message: t('节点库.请输入节点名称') as string }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="type"
            label={t('节点库.节点类型') as string}
            rules={[{ required: true, message: t('节点库.请输入节点类型') as string }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="description"
            label={t('节点库.描述') as string}
            rules={[{ required: true, message: t('节点库.请输入描述') as string }]}
          >
            <TextArea rows={3} />
          </Form.Item>

          <Form.Item
            name="category"
            label={t('节点库.分类') as string}
            rules={[{ required: true, message: t('节点库.请选择分类') as string }]}
          >
            <Select>
              {categories.map(category => (
                <Option key={category} value={category}>
                  {t(`节点分类.${category}`)}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="tags"
            label={t('节点库.标签')}
          >
            <Select mode="tags" placeholder={t('节点库.输入标签')}>
              {allTags().map(tag => (
                <Option key={tag} value={tag}>{tag}</Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 编辑节点模态框 */}
      <Modal
        title={t('节点库.编辑节点')}
        open={editModalVisible}
        onOk={handleNodeEdit}
        onCancel={() => setEditModalVisible(false)}
        width={800}
      >
        <Form form={form} layout="vertical">
          {/* 与创建表单相同的字段 */}
        </Form>
      </Modal>
    </div>
  );
};

export default NodeLibraryManager;
