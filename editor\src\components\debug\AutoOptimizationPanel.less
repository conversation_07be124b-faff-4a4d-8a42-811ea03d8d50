/**
 * 一键优化面板样式
 */

.auto-optimization-panel {
  .ant-modal-body {
    padding: 16px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .optimization-controls {
    margin-bottom: 16px;

    .ant-btn {
      height: 48px;
      font-size: 16px;
      font-weight: 500;

      &.ant-btn-primary {
        background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
        border: none;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);

        &:hover {
          background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
          box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
        }

        &:disabled {
          background: #f5f5f5;
          box-shadow: none;
        }
      }
    }
  }

  .optimization-config {
    .ant-checkbox-wrapper {
      display: block;
      margin-bottom: 12px;
      padding: 8px 0;

      &:hover {
        background-color: #f9f9f9;
        border-radius: 4px;
        padding-left: 8px;
        margin-left: -8px;
      }
    }

    .ant-typography {
      margin-bottom: 12px;
    }
  }

  .ant-card {
    .ant-card-head {
      .ant-card-head-title {
        font-size: 16px;
        font-weight: 600;
      }

      .ant-card-extra {
        .ant-btn {
          border: none;
          box-shadow: none;
        }
      }
    }

    .ant-card-body {
      padding: 16px;
    }

    &.optimization-progress-card {
      border: 2px solid #1890ff;
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);

      .ant-card-head {
        background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
        border-bottom-color: #1890ff;
      }
    }

    &.optimization-results-card {
      border: 2px solid #52c41a;
      box-shadow: 0 4px 12px rgba(82, 196, 26, 0.1);

      .ant-card-head {
        background: linear-gradient(135deg, #f6ffed 0%, #fcffe6 100%);
        border-bottom-color: #52c41a;
      }
    }
  }

  .ant-progress {
    .ant-progress-bg {
      background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
    }

    &.ant-progress-status-active {
      .ant-progress-bg {
        background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
      }
    }
  }

  .ant-statistic {
    .ant-statistic-title {
      font-size: 12px;
      color: #666666;
      margin-bottom: 4px;
    }

    .ant-statistic-content {
      .ant-statistic-content-value {
        font-size: 20px;
        font-weight: 600;
      }

      .ant-statistic-content-suffix {
        font-size: 14px;
        margin-left: 4px;
      }
    }
  }

  .ant-list {
    .ant-list-item {
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;

      &:hover {
        background-color: #f9f9f9;
      }

      .ant-list-item-meta {
        .ant-list-item-meta-avatar {
          margin-right: 12px;
          font-size: 16px;
        }

        .ant-list-item-meta-title {
          margin-bottom: 4px;
          font-size: 14px;
        }

        .ant-list-item-meta-description {
          font-size: 13px;
          line-height: 1.5;

          .ant-progress {
            margin-top: 8px;
          }

          .ant-alert {
            margin-top: 8px;
          }
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;

    .empty-icon {
      font-size: 48px;
      color: #d9d9d9;
      margin-bottom: 16px;
    }

    .ant-typography {
      margin-bottom: 8px;
    }
  }
}

// 深色主题
.dark-theme {
  .auto-optimization-panel {
    .ant-card {
      background: #2d2d2d;
      border-color: #404040;

      .ant-card-head {
        background: #2d2d2d;
        border-bottom-color: #404040;

        .ant-card-head-title {
          color: #ffffff;
        }
      }

      .ant-card-body {
        background: #2d2d2d;
        color: #cccccc;
      }

      &.optimization-progress-card {
        border-color: #1890ff;

        .ant-card-head {
          background: #1f3a5f;
        }
      }

      &.optimization-results-card {
        border-color: #52c41a;

        .ant-card-head {
          background: #2d4a22;
        }
      }
    }

    .optimization-config {
      .ant-checkbox-wrapper {
        color: #cccccc;

        &:hover {
          background-color: #404040;
        }
      }
    }

    .ant-list {
      .ant-list-item {
        border-bottom-color: #404040;

        &:hover {
          background-color: #404040;
        }

        .ant-list-item-meta {
          .ant-list-item-meta-title {
            color: #ffffff;
          }

          .ant-list-item-meta-description {
            color: #cccccc;
          }
        }
      }
    }

    .ant-statistic {
      .ant-statistic-title {
        color: #cccccc;
      }

      .ant-statistic-content {
        color: #ffffff;
      }
    }

    .empty-state {
      .empty-icon {
        color: #666666;
      }
    }
  }
}

// 紧凑模式
.compact-theme {
  .auto-optimization-panel {
    .ant-modal-body {
      padding: 12px;
    }

    .optimization-controls {
      margin-bottom: 12px;

      .ant-btn {
        height: 40px;
        font-size: 14px;
      }
    }

    .ant-card {
      .ant-card-body {
        padding: 12px;
      }
    }

    .ant-statistic {
      .ant-statistic-content {
        .ant-statistic-content-value {
          font-size: 18px;
        }
      }
    }

    .ant-list {
      .ant-list-item {
        padding: 8px 12px;

        .ant-list-item-meta {
          .ant-list-item-meta-title {
            font-size: 13px;
          }

          .ant-list-item-meta-description {
            font-size: 12px;
          }
        }
      }
    }

    .optimization-config {
      .ant-checkbox-wrapper {
        margin-bottom: 8px;
        padding: 6px 0;
      }
    }

    .empty-state {
      padding: 30px 0;

      .empty-icon {
        font-size: 36px;
        margin-bottom: 12px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .auto-optimization-panel {
    .ant-modal {
      width: 95% !important;
      max-width: none;
    }

    .optimization-controls {
      .ant-row {
        .ant-col {
          margin-bottom: 8px;
        }
      }
    }

    .ant-statistic {
      .ant-statistic-content {
        .ant-statistic-content-value {
          font-size: 16px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .auto-optimization-panel {
    .optimization-controls {
      .ant-row {
        .ant-col {
          flex: 0 0 100%;
          max-width: 100%;
          margin-bottom: 8px;
        }
      }
    }

    .ant-card {
      .ant-card-head {
        .ant-card-head-title {
          font-size: 14px;
        }
      }
    }

    .ant-statistic {
      text-align: center;

      .ant-statistic-content {
        .ant-statistic-content-value {
          font-size: 18px;
        }
      }
    }
  }
}

// 动画效果
.auto-optimization-panel {
  .ant-btn {
    transition: all 0.3s ease;

    &.ant-btn-primary {
      &:hover {
        transform: translateY(-2px);
      }
    }
  }

  .ant-card {
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &.optimization-progress-card,
    &.optimization-results-card {
      animation: cardPulse 2s ease-in-out infinite;
    }
  }

  .ant-list-item {
    transition: background-color 0.2s ease;
  }

  .ant-progress {
    .ant-progress-bg {
      transition: background-color 0.3s ease;
    }
  }

  .optimization-config {
    .ant-checkbox-wrapper {
      transition: all 0.2s ease;
    }
  }
}

@keyframes cardPulse {
  0%, 100% {
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
  }
  50% {
    box-shadow: 0 6px 16px rgba(24, 144, 255, 0.2);
  }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
  .auto-optimization-panel {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }

    .ant-btn {
      &:hover {
        transform: none;
      }
    }

    .ant-card {
      &.optimization-progress-card,
      &.optimization-results-card {
        animation: none;
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .auto-optimization-panel {
    .ant-card {
      border: 2px solid #000;

      &.optimization-progress-card {
        border-color: #0066cc;
      }

      &.optimization-results-card {
        border-color: #009900;
      }
    }

    .ant-list {
      .ant-list-item {
        border-bottom: 2px solid #000;
      }
    }

    .ant-btn {
      border: 2px solid #000;

      &.ant-btn-primary {
        background: #0066cc;
        border-color: #0066cc;
      }
    }
  }
}

// 状态指示器
.auto-optimization-panel {
  .task-status-pending {
    color: #1890ff;
  }

  .task-status-running {
    color: #faad14;
  }

  .task-status-completed {
    color: #52c41a;
  }

  .task-status-failed {
    color: #ff4d4f;
  }

  .task-status-cancelled {
    color: #d9d9d9;
  }

  .improvement-positive {
    color: #52c41a;
    font-weight: 600;
  }

  .improvement-neutral {
    color: #666666;
  }

  .improvement-negative {
    color: #ff4d4f;
  }
}
