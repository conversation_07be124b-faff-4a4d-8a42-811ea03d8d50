/**
 * 协作权限管理服务
 * 管理用户在协作环境中的权限和角色
 */
import { EventEmitter } from '../utils/EventEmitter';

// 权限类型枚举
export enum Permission {
  // 基础权限
  VIEW = 'view',
  EDIT = 'edit',
  DELETE = 'delete',
  CREATE = 'create',
  
  // 实体权限
  ENTITY_CREATE = 'entity_create',
  ENTITY_EDIT = 'entity_edit',
  ENTITY_DELETE = 'entity_delete',
  ENTITY_MOVE = 'entity_move',
  
  // 组件权限
  COMPONENT_ADD = 'component_add',
  COMPONENT_EDIT = 'component_edit',
  COMPONENT_REMOVE = 'component_remove',
  
  // 场景权限
  SCENE_EDIT = 'scene_edit',
  SCENE_SETTINGS = 'scene_settings',
  SCENE_EXPORT = 'scene_export',
  SCENE_IMPORT = 'scene_import',
  
  // 协作权限
  INVITE_USERS = 'invite_users',
  MANAGE_PERMISSIONS = 'manage_permissions',
  KICK_USERS = 'kick_users',
  
  // 高级权限
  ADMIN = 'admin',
  MODERATOR = 'moderator'
}

// 用户角色枚举
export enum UserRole {
  OWNER = 'owner',           // 项目所有者
  ADMIN = 'admin',           // 管理员
  EDITOR = 'editor',         // 编辑者
  VIEWER = 'viewer',         // 查看者
  GUEST = 'guest',           // 访客
  COLLABORATOR = 'collaborator' // 协作者
}

// 权限级别
export enum PermissionLevel {
  NONE = 0,
  READ = 1,
  WRITE = 2,
  ADMIN = 3
}

// 用户权限信息
export interface UserPermissions {
  userId: string;
  role: UserRole;
  permissions: Permission[];
  entityPermissions: Map<string, Permission[]>; // 特定实体的权限
  componentPermissions: Map<string, Permission[]>; // 特定组件的权限
  customPermissions: Record<string, any>; // 自定义权限
  expiresAt?: number; // 权限过期时间
  grantedBy: string; // 权限授予者
  grantedAt: number; // 权限授予时间
}

// 权限请求
export interface PermissionRequest {
  id: string;
  userId: string;
  requestedPermissions: Permission[];
  reason: string;
  timestamp: number;
  status: 'pending' | 'approved' | 'denied';
  reviewedBy?: string;
  reviewedAt?: number;
}

// 权限变更记录
export interface PermissionChange {
  id: string;
  userId: string;
  changedBy: string;
  oldPermissions: Permission[];
  newPermissions: Permission[];
  reason: string;
  timestamp: number;
}

/**
 * 协作权限管理服务类
 */
export class CollaborationPermissionService extends EventEmitter {
  private static instance: CollaborationPermissionService;
  private userPermissions: Map<string, UserPermissions> = new Map();
  private permissionRequests: Map<string, PermissionRequest> = new Map();
  private permissionHistory: PermissionChange[] = [];
  private rolePermissionMap: Map<UserRole, Permission[]> = new Map();

  private constructor() {
    super();
    this.initializeRolePermissions();
  }

  public static getInstance(): CollaborationPermissionService {
    if (!CollaborationPermissionService.instance) {
      CollaborationPermissionService.instance = new CollaborationPermissionService();
    }
    return CollaborationPermissionService.instance;
  }

  /**
   * 初始化角色权限映射
   */
  private initializeRolePermissions(): void {
    // 所有者权限
    this.rolePermissionMap.set(UserRole.OWNER, [
      ...Object.values(Permission)
    ]);

    // 管理员权限
    this.rolePermissionMap.set(UserRole.ADMIN, [
      Permission.VIEW, Permission.EDIT, Permission.DELETE, Permission.CREATE,
      Permission.ENTITY_CREATE, Permission.ENTITY_EDIT, Permission.ENTITY_DELETE, Permission.ENTITY_MOVE,
      Permission.COMPONENT_ADD, Permission.COMPONENT_EDIT, Permission.COMPONENT_REMOVE,
      Permission.SCENE_EDIT, Permission.SCENE_SETTINGS, Permission.SCENE_EXPORT, Permission.SCENE_IMPORT,
      Permission.INVITE_USERS, Permission.MANAGE_PERMISSIONS,
      Permission.MODERATOR
    ]);

    // 编辑者权限
    this.rolePermissionMap.set(UserRole.EDITOR, [
      Permission.VIEW, Permission.EDIT, Permission.CREATE,
      Permission.ENTITY_CREATE, Permission.ENTITY_EDIT, Permission.ENTITY_MOVE,
      Permission.COMPONENT_ADD, Permission.COMPONENT_EDIT,
      Permission.SCENE_EDIT
    ]);

    // 协作者权限
    this.rolePermissionMap.set(UserRole.COLLABORATOR, [
      Permission.VIEW, Permission.EDIT,
      Permission.ENTITY_EDIT, Permission.ENTITY_MOVE,
      Permission.COMPONENT_EDIT
    ]);

    // 查看者权限
    this.rolePermissionMap.set(UserRole.VIEWER, [
      Permission.VIEW
    ]);

    // 访客权限
    this.rolePermissionMap.set(UserRole.GUEST, [
      Permission.VIEW
    ]);
  }

  /**
   * 设置用户权限
   */
  public setUserPermissions(
    userId: string, 
    role: UserRole, 
    customPermissions?: Permission[],
    grantedBy?: string
  ): void {
    const basePermissions = this.rolePermissionMap.get(role) || [];
    const allPermissions = customPermissions ? 
      [...new Set([...basePermissions, ...customPermissions])] : 
      basePermissions;

    const userPermissions: UserPermissions = {
      userId,
      role,
      permissions: allPermissions,
      entityPermissions: new Map(),
      componentPermissions: new Map(),
      customPermissions: {},
      grantedBy: grantedBy || 'system',
      grantedAt: Date.now()
    };

    const oldPermissions = this.userPermissions.get(userId);
    this.userPermissions.set(userId, userPermissions);

    // 记录权限变更
    if (oldPermissions) {
      this.recordPermissionChange(
        userId,
        grantedBy || 'system',
        oldPermissions.permissions,
        allPermissions,
        'Role and permissions updated'
      );
    }

    this.emit('permissionsChanged', { userId, permissions: userPermissions });
  }

  /**
   * 检查用户是否有特定权限
   */
  public hasPermission(userId: string, permission: Permission, entityId?: string, componentId?: string): boolean {
    const userPermissions = this.userPermissions.get(userId);
    if (!userPermissions) return false;

    // 检查全局权限
    if (userPermissions.permissions.includes(permission)) {
      return true;
    }

    // 检查实体特定权限
    if (entityId) {
      const entityPermissions = userPermissions.entityPermissions.get(entityId);
      if (entityPermissions && entityPermissions.includes(permission)) {
        return true;
      }
    }

    // 检查组件特定权限
    if (componentId) {
      const componentPermissions = userPermissions.componentPermissions.get(componentId);
      if (componentPermissions && componentPermissions.includes(permission)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 检查用户是否有任一权限
   */
  public hasAnyPermission(userId: string, permissions: Permission[]): boolean {
    return permissions.some(permission => this.hasPermission(userId, permission));
  }

  /**
   * 检查用户是否有所有权限
   */
  public hasAllPermissions(userId: string, permissions: Permission[]): boolean {
    return permissions.every(permission => this.hasPermission(userId, permission));
  }

  /**
   * 获取用户权限
   */
  public getUserPermissions(userId: string): UserPermissions | null {
    return this.userPermissions.get(userId) || null;
  }

  /**
   * 获取用户角色
   */
  public getUserRole(userId: string): UserRole | null {
    const permissions = this.userPermissions.get(userId);
    return permissions ? permissions.role : null;
  }

  /**
   * 设置实体特定权限
   */
  public setEntityPermissions(userId: string, entityId: string, permissions: Permission[]): void {
    const userPermissions = this.userPermissions.get(userId);
    if (!userPermissions) return;

    userPermissions.entityPermissions.set(entityId, permissions);
    this.emit('entityPermissionsChanged', { userId, entityId, permissions });
  }

  /**
   * 设置组件特定权限
   */
  public setComponentPermissions(userId: string, componentId: string, permissions: Permission[]): void {
    const userPermissions = this.userPermissions.get(userId);
    if (!userPermissions) return;

    userPermissions.componentPermissions.set(componentId, permissions);
    this.emit('componentPermissionsChanged', { userId, componentId, permissions });
  }

  /**
   * 请求权限
   */
  public requestPermission(
    userId: string, 
    permissions: Permission[], 
    reason: string
  ): string {
    const requestId = `perm_req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const request: PermissionRequest = {
      id: requestId,
      userId,
      requestedPermissions: permissions,
      reason,
      timestamp: Date.now(),
      status: 'pending'
    };

    this.permissionRequests.set(requestId, request);
    this.emit('permissionRequested', request);

    return requestId;
  }

  /**
   * 审批权限请求
   */
  public approvePermissionRequest(
    requestId: string, 
    reviewerId: string, 
    approved: boolean,
    reason?: string
  ): boolean {
    const request = this.permissionRequests.get(requestId);
    if (!request || request.status !== 'pending') return false;

    // 检查审批者权限
    if (!this.hasPermission(reviewerId, Permission.MANAGE_PERMISSIONS)) {
      throw new Error('Insufficient permissions to approve requests');
    }

    request.status = approved ? 'approved' : 'denied';
    request.reviewedBy = reviewerId;
    request.reviewedAt = Date.now();

    if (approved) {
      // 授予权限
      const userPermissions = this.userPermissions.get(request.userId);
      if (userPermissions) {
        const newPermissions = [...new Set([...userPermissions.permissions, ...request.requestedPermissions])];
        userPermissions.permissions = newPermissions;
        
        this.recordPermissionChange(
          request.userId,
          reviewerId,
          userPermissions.permissions,
          newPermissions,
          reason || 'Permission request approved'
        );
      }
    }

    this.emit('permissionRequestReviewed', request);
    return true;
  }

  /**
   * 撤销用户权限
   */
  public revokePermissions(
    userId: string, 
    permissions: Permission[], 
    revokedBy: string,
    reason: string
  ): void {
    const userPermissions = this.userPermissions.get(userId);
    if (!userPermissions) return;

    // 检查撤销者权限
    if (!this.hasPermission(revokedBy, Permission.MANAGE_PERMISSIONS)) {
      throw new Error('Insufficient permissions to revoke permissions');
    }

    const oldPermissions = [...userPermissions.permissions];
    userPermissions.permissions = userPermissions.permissions.filter(p => !permissions.includes(p));

    this.recordPermissionChange(userId, revokedBy, oldPermissions, userPermissions.permissions, reason);
    this.emit('permissionsRevoked', { userId, revokedPermissions: permissions });
  }

  /**
   * 移除用户
   */
  public removeUser(userId: string, removedBy: string): void {
    // 检查移除者权限
    if (!this.hasPermission(removedBy, Permission.KICK_USERS)) {
      throw new Error('Insufficient permissions to remove users');
    }

    const userPermissions = this.userPermissions.get(userId);
    if (userPermissions) {
      this.userPermissions.delete(userId);
      this.recordPermissionChange(
        userId, 
        removedBy, 
        userPermissions.permissions, 
        [], 
        'User removed from collaboration'
      );
      this.emit('userRemoved', { userId, removedBy });
    }
  }

  /**
   * 获取所有用户权限
   */
  public getAllUserPermissions(): UserPermissions[] {
    return Array.from(this.userPermissions.values());
  }

  /**
   * 获取待审批的权限请求
   */
  public getPendingRequests(): PermissionRequest[] {
    return Array.from(this.permissionRequests.values()).filter(req => req.status === 'pending');
  }

  /**
   * 获取权限变更历史
   */
  public getPermissionHistory(userId?: string): PermissionChange[] {
    if (userId) {
      return this.permissionHistory.filter(change => change.userId === userId);
    }
    return [...this.permissionHistory];
  }

  /**
   * 检查权限冲突
   */
  public checkPermissionConflicts(userId: string, operation: string, entityId?: string): boolean {
    // 实现权限冲突检查逻辑
    const userPermissions = this.userPermissions.get(userId);
    if (!userPermissions) return true; // 无权限即为冲突

    // 根据操作类型检查相应权限
    switch (operation) {
      case 'create':
        return !this.hasPermission(userId, Permission.CREATE);
      case 'edit':
        return !this.hasPermission(userId, Permission.EDIT, entityId);
      case 'delete':
        return !this.hasPermission(userId, Permission.DELETE, entityId);
      default:
        return false;
    }
  }

  /**
   * 记录权限变更
   */
  private recordPermissionChange(
    userId: string,
    changedBy: string,
    oldPermissions: Permission[],
    newPermissions: Permission[],
    reason: string
  ): void {
    const change: PermissionChange = {
      id: `change_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      changedBy,
      oldPermissions,
      newPermissions,
      reason,
      timestamp: Date.now()
    };

    this.permissionHistory.push(change);
    
    // 保持历史记录在合理范围内
    if (this.permissionHistory.length > 1000) {
      this.permissionHistory = this.permissionHistory.slice(-500);
    }
  }

  /**
   * 清理过期权限
   */
  public cleanupExpiredPermissions(): void {
    const now = Date.now();
    
    for (const [userId, permissions] of this.userPermissions) {
      if (permissions.expiresAt && permissions.expiresAt < now) {
        this.userPermissions.delete(userId);
        this.emit('permissionsExpired', { userId });
      }
    }
  }
}

export default CollaborationPermissionService;
