/**
 * 后处理特效面板样式
 */

.post-processing-panel {
  .ant-modal-body {
    padding: 16px;
    height: 70vh;
    overflow: hidden;
  }

  // 效果列表
  .effects-list {
    height: 100%;
    display: flex;
    flex-direction: column;

    .effects-header {
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;

      .ant-btn {
        margin-right: 8px;
        margin-bottom: 8px;
      }
    }

    .chain-effects {
      flex: 1;
      overflow-y: auto;

      .add-effect-section {
        margin-bottom: 16px;
        padding: 12px;
        background: #f9f9f9;
        border-radius: 6px;

        .ant-btn {
          margin: 2px;
          font-size: 11px;
          height: 24px;
          padding: 0 8px;
        }
      }

      .effect-item {
        margin-bottom: 8px;
        padding: 12px;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        background: #ffffff;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          border-color: #d9d9d9;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        &.selected {
          border-color: #1890ff;
          background-color: #e6f7ff;
        }

        &.disabled {
          opacity: 0.6;
          background-color: #f5f5f5;
        }

        &.dragging {
          transform: rotate(5deg);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .effect-header {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          .drag-handle {
            margin-right: 8px;
            color: #d9d9d9;
            cursor: grab;

            &:active {
              cursor: grabbing;
            }
          }

          .effect-info {
            flex: 1;

            .ant-typography {
              margin-bottom: 4px;
              font-size: 14px;
            }

            .ant-tag {
              font-size: 10px;
              padding: 0 4px;
              height: 18px;
              line-height: 18px;
            }
          }

          .effect-controls {
            display: flex;
            align-items: center;
            gap: 8px;
          }
        }

        .effect-preview {
          .ant-progress {
            margin-bottom: 4px;
          }
        }
      }
    }
  }

  // 效果编辑器
  .effect-editor {
    height: 100%;
    overflow-y: auto;

    .editor-header {
      margin-bottom: 16px;

      .ant-typography {
        margin-bottom: 4px;
      }
    }

    .no-effect-selected {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #d9d9d9;
    }

    .ant-form {
      .ant-form-item {
        margin-bottom: 16px;

        .ant-form-item-label {
          font-weight: 500;
        }
      }

      .ant-slider {
        margin: 8px 0;
      }
    }

    .effect-stats {
      margin-top: 16px;
      padding: 12px;
      background: #f9f9f9;
      border-radius: 4px;

      .ant-progress {
        margin-top: 4px;
      }
    }
  }

  // 预览区域
  .preview-area {
    height: 100%;
    display: flex;
    flex-direction: column;

    .preview-controls {
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;
    }

    .preview-viewport {
      flex: 1;
      margin-bottom: 12px;
    }

    .chain-stats {
      padding: 12px;
      background: #f9f9f9;
      border-radius: 4px;

      .ant-typography {
        font-size: 12px;
      }
    }
  }
}

// 拖拽样式
.post-processing-panel {
  .effect-item {
    &.dragging {
      z-index: 1000;
    }
  }

  .droppable-area {
    min-height: 200px;
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    padding: 16px;

    &.drag-over {
      border-color: #1890ff;
      background-color: #e6f7ff;
    }
  }
}

// 预设选择对话框
.preset-modal {
  .ant-card {
    .ant-card-cover {
      height: 120px;
      overflow: hidden;

      img {
        transition: transform 0.3s ease;
      }
    }

    &:hover {
      .ant-card-cover img {
        transform: scale(1.05);
      }
    }

    .ant-card-meta {
      .ant-card-meta-title {
        font-size: 14px;
      }

      .ant-card-meta-description {
        font-size: 12px;

        .ant-tag {
          margin-bottom: 2px;
        }
      }
    }
  }
}

// 深色主题
.dark-theme {
  .post-processing-panel {
    .ant-modal-content {
      background: #2d2d2d;
      color: #cccccc;
    }

    .effects-list {
      .effects-header {
        border-bottom-color: #404040;
      }

      .chain-effects {
        .add-effect-section {
          background: #404040;
          color: #cccccc;
        }

        .effect-item {
          background: #2d2d2d;
          border-color: #404040;

          &:hover {
            border-color: #555555;
            background-color: #404040;
          }

          &.selected {
            border-color: #1890ff;
            background-color: #1f3a5f;
          }

          &.disabled {
            background-color: #333333;
          }

          .effect-header {
            .effect-info {
              .ant-typography {
                color: #ffffff;
              }
            }

            .drag-handle {
              color: #666666;
            }
          }
        }
      }
    }

    .effect-editor {
      .editor-header {
        .ant-typography {
          color: #ffffff;
        }
      }

      .no-effect-selected {
        color: #666666;
      }

      .effect-stats {
        background: #404040;
        color: #cccccc;
      }
    }

    .preview-area {
      .preview-controls {
        border-bottom-color: #404040;
      }

      .chain-stats {
        background: #404040;
        color: #cccccc;
      }
    }
  }
}

// 紧凑模式
.compact-theme {
  .post-processing-panel {
    .ant-modal-body {
      padding: 12px;
    }

    .effects-list {
      .effects-header {
        margin-bottom: 12px;
        padding-bottom: 8px;
      }

      .chain-effects {
        .add-effect-section {
          margin-bottom: 12px;
          padding: 8px;
        }

        .effect-item {
          margin-bottom: 6px;
          padding: 8px;

          .effect-header {
            margin-bottom: 6px;

            .effect-info {
              .ant-typography {
                font-size: 13px;
              }
            }
          }
        }
      }
    }

    .effect-editor {
      .editor-header {
        margin-bottom: 12px;
      }

      .ant-form {
        .ant-form-item {
          margin-bottom: 12px;
        }
      }

      .effect-stats {
        margin-top: 12px;
        padding: 8px;
      }
    }

    .preview-area {
      .chain-stats {
        padding: 8px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .post-processing-panel {
    .ant-modal {
      width: 95% !important;
      max-width: none;
    }
  }
}

@media (max-width: 768px) {
  .post-processing-panel {
    .ant-row {
      .ant-col {
        margin-bottom: 16px;
      }
    }

    .effects-list {
      .effects-header {
        .ant-space {
          flex-direction: column;
          align-items: stretch;
          width: 100%;
        }
      }

      .chain-effects {
        .add-effect-section {
          .ant-row {
            .ant-col {
              flex: 0 0 50%;
              max-width: 50%;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .post-processing-panel {
    .effects-list {
      .chain-effects {
        .add-effect-section {
          .ant-row {
            .ant-col {
              flex: 0 0 100%;
              max-width: 100%;
            }
          }
        }
      }
    }

    .effect-editor {
      .ant-form {
        .ant-row {
          .ant-col {
            flex: 0 0 100%;
            max-width: 100%;
          }
        }
      }
    }
  }
}

// 动画效果
.post-processing-panel {
  .effect-item {
    transition: all 0.2s ease;
  }

  .preset-modal {
    .ant-card {
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .ant-btn {
    transition: all 0.2s ease;
  }

  .ant-slider {
    .ant-slider-handle {
      transition: all 0.2s ease;
    }
  }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
  .post-processing-panel {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }

    .preset-modal {
      .ant-card {
        &:hover {
          transform: none;
        }
      }
    }

    .effect-item {
      &.dragging {
        transform: none;
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .post-processing-panel {
    .effect-item {
      border: 2px solid #000;

      &.selected {
        border-color: #0066cc;
      }
    }

    .add-effect-section {
      border: 2px solid #000;
    }

    .preview-viewport {
      border: 2px solid #000;
    }

    .chain-stats {
      border: 2px solid #000;
    }
  }
}

// 后处理特效特定样式
.post-processing-panel {
  .effect-type-bloom {
    border-left: 4px solid #faad14;
  }

  .effect-type-color-grading {
    border-left: 4px solid #1890ff;
  }

  .effect-type-depth-of-field {
    border-left: 4px solid #52c41a;
  }

  .effect-type-motion-blur {
    border-left: 4px solid #722ed1;
  }

  .effect-type-vignette {
    border-left: 4px solid #eb2f96;
  }

  .performance-excellent {
    color: #52c41a;
  }

  .performance-good {
    color: #faad14;
  }

  .performance-poor {
    color: #ff4d4f;
  }

  .quality-high {
    color: #52c41a;
  }

  .quality-medium {
    color: #faad14;
  }

  .quality-low {
    color: #ff4d4f;
  }

  .processing-indicator {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, transparent, rgba(24, 144, 255, 0.3), transparent);
      animation: processing 2s infinite;
    }
  }

  .effect-enabled {
    &::before {
      content: '';
      position: absolute;
      top: 4px;
      right: 4px;
      width: 8px;
      height: 8px;
      background: #52c41a;
      border-radius: 50%;
    }
  }

  .chain-performance-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 4px;

    &.excellent {
      background: #52c41a;
    }

    &.good {
      background: #faad14;
    }

    &.poor {
      background: #ff4d4f;
    }
  }
}

@keyframes processing {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
