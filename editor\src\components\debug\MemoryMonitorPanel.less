/**
 * 内存监控面板样式
 */

.memory-monitor-panel {
  .ant-modal-body {
    padding: 16px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .memory-overview {
    .ant-card {
      .ant-card-body {
        padding: 16px;
      }

      .ant-statistic {
        .ant-statistic-title {
          font-size: 12px;
          color: #666666;
          margin-bottom: 4px;
        }

        .ant-statistic-content {
          .ant-statistic-content-value {
            font-size: 24px;
            font-weight: 600;
          }

          .ant-statistic-content-suffix {
            font-size: 14px;
            margin-left: 4px;
          }
        }
      }
    }

    .ant-descriptions {
      .ant-descriptions-item-label {
        font-weight: 500;
        color: #333333;
        width: 120px;
      }

      .ant-descriptions-item-content {
        color: #666666;

        .ant-progress {
          margin-bottom: 0;
        }
      }
    }
  }

  .memory-snapshots {
    .snapshots-toolbar {
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;
    }

    .ant-table {
      .ant-table-tbody {
        .ant-table-row {
          &:hover {
            background-color: #f5f5f5;
          }

          &.ant-table-row-selected {
            background-color: #e6f7ff;
          }
        }
      }

      .ant-table-cell {
        padding: 8px 12px;
        font-size: 13px;
      }
    }
  }

  .memory-leaks {
    .leaks-toolbar {
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;
    }

    .ant-list {
      .ant-list-item {
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;

        &:hover {
          background-color: #f9f9f9;
        }

        .ant-list-item-meta {
          .ant-list-item-meta-avatar {
            margin-right: 12px;
          }

          .ant-list-item-meta-title {
            margin-bottom: 4px;
            font-size: 14px;
          }

          .ant-list-item-meta-description {
            font-size: 12px;
            line-height: 1.4;
          }
        }

        .ant-list-item-action {
          margin-left: 16px;
        }
      }
    }
  }

  .leak-details {
    .ant-descriptions {
      .ant-descriptions-item-label {
        font-weight: 500;
        color: #333333;
      }

      .ant-descriptions-item-content {
        color: #666666;

        .ant-list {
          .ant-list-item {
            padding: 4px 0;
            border: none;
          }
        }
      }
    }
  }

  .ant-tabs {
    .ant-tabs-tab {
      .ant-badge {
        .ant-badge-count {
          font-size: 10px;
          min-width: 16px;
          height: 16px;
          line-height: 16px;
        }
      }
    }
  }

  .ant-alert {
    .ant-alert-message {
      font-weight: 500;
    }
  }
}

// 深色主题
.dark-theme {
  .memory-monitor-panel {
    .ant-card {
      background: #2d2d2d;
      border-color: #404040;

      .ant-card-head {
        background: #2d2d2d;
        border-bottom-color: #404040;

        .ant-card-head-title {
          color: #ffffff;
        }
      }

      .ant-card-body {
        background: #2d2d2d;
        color: #cccccc;
      }
    }

    .memory-snapshots {
      .snapshots-toolbar {
        border-bottom-color: #404040;
      }

      .ant-table {
        background: #2d2d2d;

        .ant-table-thead {
          .ant-table-cell {
            background: #404040;
            color: #ffffff;
            border-bottom-color: #555555;
          }
        }

        .ant-table-tbody {
          .ant-table-row {
            background: #2d2d2d;
            color: #cccccc;

            &:hover {
              background-color: #404040;
            }

            &.ant-table-row-selected {
              background-color: #1f4d6b;
            }

            .ant-table-cell {
              border-bottom-color: #404040;
            }
          }
        }
      }
    }

    .memory-leaks {
      .leaks-toolbar {
        border-bottom-color: #404040;
      }

      .ant-list {
        .ant-list-item {
          border-bottom-color: #404040;

          &:hover {
            background-color: #404040;
          }

          .ant-list-item-meta {
            .ant-list-item-meta-title {
              color: #ffffff;
            }

            .ant-list-item-meta-description {
              color: #cccccc;
            }
          }
        }
      }
    }

    .ant-descriptions {
      background: #2d2d2d;
      border-color: #404040;

      .ant-descriptions-item-label {
        background: #2d2d2d;
        color: #ffffff;
      }

      .ant-descriptions-item-content {
        background: #2d2d2d;
        color: #cccccc;
      }
    }

    .ant-statistic {
      .ant-statistic-title {
        color: #cccccc;
      }

      .ant-statistic-content {
        color: #ffffff;
      }
    }
  }
}

// 紧凑模式
.compact-theme {
  .memory-monitor-panel {
    .ant-modal-body {
      padding: 12px;
    }

    .ant-card {
      .ant-card-body {
        padding: 12px;
      }
    }

    .memory-overview {
      .ant-statistic {
        .ant-statistic-content {
          .ant-statistic-content-value {
            font-size: 20px;
          }
        }
      }

      .ant-descriptions {
        .ant-descriptions-item-label {
          width: 100px;
          font-size: 12px;
        }

        .ant-descriptions-item-content {
          font-size: 12px;
        }
      }
    }

    .memory-snapshots {
      .snapshots-toolbar {
        margin-bottom: 12px;
        padding-bottom: 8px;
      }

      .ant-table {
        .ant-table-cell {
          padding: 6px 8px;
          font-size: 12px;
        }
      }
    }

    .memory-leaks {
      .leaks-toolbar {
        margin-bottom: 12px;
        padding-bottom: 8px;
      }

      .ant-list {
        .ant-list-item {
          padding: 8px 12px;

          .ant-list-item-meta {
            .ant-list-item-meta-title {
              font-size: 13px;
            }

            .ant-list-item-meta-description {
              font-size: 11px;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .memory-monitor-panel {
    .ant-modal {
      width: 95% !important;
      max-width: none;
    }

    .memory-overview {
      .ant-row {
        .ant-col {
          margin-bottom: 12px;
        }
      }
    }

    .ant-statistic {
      .ant-statistic-content {
        .ant-statistic-content-value {
          font-size: 18px;
        }
      }
    }

    .ant-descriptions {
      .ant-descriptions-item {
        padding: 8px 12px;
      }
    }

    .memory-snapshots {
      .snapshots-toolbar {
        .ant-space {
          flex-direction: column;
          align-items: stretch;
          width: 100%;
        }
      }
    }

    .memory-leaks {
      .leaks-toolbar {
        .ant-space {
          flex-direction: column;
          align-items: stretch;
          width: 100%;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .memory-monitor-panel {
    .memory-overview {
      .ant-row {
        .ant-col {
          flex: 0 0 100%;
          max-width: 100%;
        }
      }
    }

    .ant-tabs {
      .ant-tabs-tab {
        padding: 8px 12px;
        font-size: 12px;
      }
    }

    .memory-snapshots {
      .ant-table {
        .ant-table-thead {
          display: none;
        }

        .ant-table-tbody {
          .ant-table-row {
            display: block;
            border: 1px solid #f0f0f0;
            margin-bottom: 8px;
            border-radius: 4px;

            .ant-table-cell {
              display: block;
              border: none;
              padding: 8px;

              &:before {
                content: attr(data-label) ': ';
                font-weight: bold;
                display: inline-block;
                width: 80px;
              }
            }
          }
        }
      }
    }
  }
}

// 动画效果
.memory-monitor-panel {
  .ant-card {
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .ant-statistic {
    .ant-statistic-content-value {
      transition: color 0.3s ease;
    }
  }

  .memory-leaks {
    .ant-list-item {
      transition: background-color 0.2s ease;
    }
  }

  .memory-snapshots {
    .ant-table-row {
      transition: background-color 0.2s ease;
    }
  }

  .ant-btn {
    transition: all 0.2s ease;
  }

  .ant-tag {
    transition: all 0.2s ease;
  }

  .ant-progress {
    .ant-progress-bg {
      transition: background-color 0.3s ease;
    }
  }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
  .memory-monitor-panel {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .memory-monitor-panel {
    .ant-card {
      border: 2px solid #000;
    }

    .ant-table {
      border: 2px solid #000;

      .ant-table-cell {
        border: 1px solid #000;
      }
    }

    .ant-list {
      .ant-list-item {
        border-bottom: 2px solid #000;
      }
    }

    .ant-descriptions {
      border: 2px solid #000;

      .ant-descriptions-item {
        border: 1px solid #000;
      }
    }

    .snapshots-toolbar,
    .leaks-toolbar {
      border-bottom: 2px solid #000;
    }
  }
}

// 内存使用指示器
.memory-monitor-panel {
  .memory-usage-good {
    color: #52c41a;
  }

  .memory-usage-warning {
    color: #faad14;
  }

  .memory-usage-critical {
    color: #ff4d4f;
  }

  .leak-severity-low {
    color: #1890ff;
  }

  .leak-severity-medium {
    color: #faad14;
  }

  .leak-severity-high {
    color: #ff4d4f;
  }

  .leak-severity-critical {
    color: #ff4d4f;
    font-weight: bold;
  }
}
