/**
 * 面部动画预览组件
 * 提供面部动画实时预览功能
 */
import React, { useRef, useEffect, useState } from 'react';
import { Select, Button, Space, Tooltip } from 'antd';
import {
  CameraOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import * as THREE from 'three';
// 注意：OrbitControls 需要单独安装或使用CDN
// import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import './FacialAnimationPreview.less';

const { Option } = Select;

/**
 * 面部动画预览属性
 */
interface FacialAnimationPreviewProps {
  /** 实体ID */
  entityId?: string;
  /** 当前时间 */
  currentTime: number;
  /** 是否播放中 */
  isPlaying: boolean;
}

/**
 * 面部动画预览组件
 */
const FacialAnimationPreview: React.FC<FacialAnimationPreviewProps> = ({
  entityId,
  currentTime,
  isPlaying
}) => {
  const { t } = useTranslation();
  
  // 引用
  const containerRef = useRef<HTMLDivElement>(null);
  
  // 状态
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  const [viewMode, setViewMode] = useState<string>('default');
  const [cameraPosition, setCameraPosition] = useState<string>('front');

  // Three.js 对象
  const sceneRef = useRef<THREE.Scene | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const controlsRef = useRef<any>(null); // 使用any类型避免OrbitControls导入问题
  const modelRef = useRef<THREE.Object3D | null>(null);
  const animationMixerRef = useRef<THREE.AnimationMixer | null>(null);
  const requestRef = useRef<number | null>(null);
  const previousTimeRef = useRef<number>(0);
  
  // 初始化Three.js场景
  useEffect(() => {
    if (!containerRef.current) return;
    
    // 创建场景
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x1e1e1e);
    sceneRef.current = scene;
    
    // 创建相机
    const camera = new THREE.PerspectiveCamera(
      50,
      containerRef.current.clientWidth / containerRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.set(0, 1.6, 2);
    cameraRef.current = camera;
    
    // 创建渲染器
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.shadowMap.enabled = true;
    containerRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;
    
    // 创建控制器（简化版本，避免OrbitControls导入问题）
    // 注意：在实际项目中需要正确导入OrbitControls
    // const controls = new OrbitControls(camera, renderer.domElement);
    // controls.target.set(0, 1.6, 0);
    // controls.update();
    // controlsRef.current = controls;

    // 临时使用简单的鼠标控制
    let isMouseDown = false;
    let mouseX = 0;
    let mouseY = 0;

    const handleMouseDown = (event: MouseEvent) => {
      isMouseDown = true;
      mouseX = event.clientX;
      mouseY = event.clientY;
    };

    const handleMouseMove = (event: MouseEvent) => {
      if (!isMouseDown) return;

      const deltaX = event.clientX - mouseX;
      const deltaY = event.clientY - mouseY;

      // 简单的相机旋转
      camera.position.x += deltaX * 0.01;
      camera.position.y += deltaY * 0.01;

      mouseX = event.clientX;
      mouseY = event.clientY;
    };

    const handleMouseUp = () => {
      isMouseDown = false;
    };

    renderer.domElement.addEventListener('mousedown', handleMouseDown);
    renderer.domElement.addEventListener('mousemove', handleMouseMove);
    renderer.domElement.addEventListener('mouseup', handleMouseUp);
    
    // 添加灯光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    scene.add(ambientLight);
    
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 2, 3);
    directionalLight.castShadow = true;
    scene.add(directionalLight);
    
    // 添加网格
    const gridHelper = new THREE.GridHelper(10, 10, 0x555555, 0x333333);
    scene.add(gridHelper);
    
    // 添加坐标轴
    const axesHelper = new THREE.AxesHelper(1);
    scene.add(axesHelper);
    
    // 加载模型
    loadModel();
    
    // 处理窗口大小变化
    const handleResize = () => {
      if (!containerRef.current || !cameraRef.current || !rendererRef.current) return;
      
      cameraRef.current.aspect = containerRef.current.clientWidth / containerRef.current.clientHeight;
      cameraRef.current.updateProjectionMatrix();
      rendererRef.current.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
    };
    
    window.addEventListener('resize', handleResize);
    
    // 开始动画循环
    const animate = (time: number) => {
      if (!sceneRef.current || !cameraRef.current || !rendererRef.current) return;
      
      requestRef.current = requestAnimationFrame(animate);
      
      // 计算帧间隔时间
      const deltaTime = (time - previousTimeRef.current) / 1000;
      previousTimeRef.current = time;
      
      // 更新动画混合器
      if (animationMixerRef.current) {
        animationMixerRef.current.update(deltaTime);
      }
      
      // 更新控制器（如果使用OrbitControls）
      if (controlsRef.current && controlsRef.current.update) {
        controlsRef.current.update();
      }
      
      // 渲染场景
      rendererRef.current.render(sceneRef.current, cameraRef.current);
    };
    
    requestRef.current = requestAnimationFrame(animate);
    
    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize);

      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }

      // 移除鼠标事件监听器
      if (rendererRef.current) {
        rendererRef.current.domElement.removeEventListener('mousedown', handleMouseDown);
        rendererRef.current.domElement.removeEventListener('mousemove', handleMouseMove);
        rendererRef.current.domElement.removeEventListener('mouseup', handleMouseUp);
      }

      if (rendererRef.current && containerRef.current) {
        try {
          containerRef.current.removeChild(rendererRef.current.domElement);
        } catch (error) {
          // 忽略移除DOM元素时的错误
        }
      }

      if (rendererRef.current) {
        rendererRef.current.dispose();
      }
    };
  }, []);
  
  // 加载模型
  const loadModel = () => {
    // 这里应该从引擎中加载实体的模型
    // 示例代码，实际实现需要与引擎集成
    
    // 创建一个简单的头部模型作为示例
    if (!sceneRef.current) return;
    
    const headGeometry = new THREE.SphereGeometry(0.5, 32, 32);
    const headMaterial = new THREE.MeshStandardMaterial({ color: 0xffdbac });
    const head = new THREE.Mesh(headGeometry, headMaterial);
    head.position.set(0, 1.6, 0);
    sceneRef.current.add(head);
    
    // 创建眼睛
    const eyeGeometry = new THREE.SphereGeometry(0.08, 16, 16);
    const eyeMaterial = new THREE.MeshStandardMaterial({ color: 0x222222 });
    
    const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    leftEye.position.set(-0.2, 1.7, 0.4);
    sceneRef.current.add(leftEye);
    
    const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    rightEye.position.set(0.2, 1.7, 0.4);
    sceneRef.current.add(rightEye);
    
    // 创建嘴巴
    const mouthGeometry = new THREE.BoxGeometry(0.3, 0.05, 0.1);
    const mouthMaterial = new THREE.MeshStandardMaterial({ color: 0x333333 });
    const mouth = new THREE.Mesh(mouthGeometry, mouthMaterial);
    mouth.position.set(0, 1.4, 0.4);
    sceneRef.current.add(mouth);
    
    // 保存模型引用
    modelRef.current = head;
    
    // 创建动画混合器
    const mixer = new THREE.AnimationMixer(head);
    animationMixerRef.current = mixer;
  };
  
  // 更新面部动画
  useEffect(() => {
    if (!modelRef.current || !entityId) return;
    
    // 这里应该从引擎中获取实体的面部动画数据并应用到模型
    // 示例代码，实际实现需要与引擎集成
    
    // 模拟面部动画
    if (modelRef.current instanceof THREE.Mesh) {
      // 根据当前时间调整模型
      const scale = 1.0 + Math.sin(currentTime * 2) * 0.05;
      modelRef.current.scale.set(scale, scale, scale);
      
      // 如果有嘴巴，调整嘴巴
      const mouth = sceneRef.current?.getObjectByProperty('position', new THREE.Vector3(0, 1.4, 0.4));
      if (mouth && mouth instanceof THREE.Mesh) {
        mouth.scale.y = 1.0 + Math.sin(currentTime * 5) * 0.5;
      }
    }
  }, [entityId, currentTime, isPlaying]);
  
  // 切换全屏
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    
    // 调整渲染器大小
    setTimeout(() => {
      if (!containerRef.current || !cameraRef.current || !rendererRef.current) return;
      
      cameraRef.current.aspect = containerRef.current.clientWidth / containerRef.current.clientHeight;
      cameraRef.current.updateProjectionMatrix();
      rendererRef.current.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
    }, 100);
  };
  
  // 重置相机
  const resetCamera = () => {
    if (!cameraRef.current) return;

    // 根据选择的相机位置设置
    switch (cameraPosition) {
      case 'front':
        cameraRef.current.position.set(0, 1.6, 2);
        break;
      case 'side':
        cameraRef.current.position.set(2, 1.6, 0);
        break;
      case 'top':
        cameraRef.current.position.set(0, 3, 0.5);
        break;
      default:
        cameraRef.current.position.set(0, 1.6, 2);
    }

    // 如果有控制器，更新目标和控制器
    if (controlsRef.current && controlsRef.current.target && controlsRef.current.update) {
      controlsRef.current.target.set(0, 1.6, 0);
      controlsRef.current.update();
    }
  };
  
  // 切换视图模式
  const handleViewModeChange = (mode: string) => {
    setViewMode(mode);
    
    // 更新材质
    if (!sceneRef.current) return;
    
    sceneRef.current.traverse((object) => {
      if (object instanceof THREE.Mesh) {
        if (mode === 'wireframe') {
          // 使用类型断言解决wireframe属性问题
          if (object.material instanceof THREE.MeshStandardMaterial ||
              object.material instanceof THREE.MeshBasicMaterial ||
              object.material instanceof THREE.MeshPhongMaterial) {
            (object.material as any).wireframe = true;
          }
        } else {
          if (object.material instanceof THREE.MeshStandardMaterial ||
              object.material instanceof THREE.MeshBasicMaterial ||
              object.material instanceof THREE.MeshPhongMaterial) {
            (object.material as any).wireframe = false;
          }
        }
      }
    });
  };
  
  // 切换相机位置
  const handleCameraPositionChange = (position: string) => {
    setCameraPosition(position);
    resetCamera();
  };
  
  // 截图
  const takeScreenshot = () => {
    if (!rendererRef.current) return;
    
    // 渲染一帧
    if (sceneRef.current && cameraRef.current) {
      rendererRef.current.render(sceneRef.current, cameraRef.current);
    }
    
    // 获取图像数据
    const dataURL = rendererRef.current.domElement.toDataURL('image/png');
    
    // 创建下载链接
    const link = document.createElement('a');
    link.href = dataURL;
    link.download = `facial-animation-${Date.now()}.png`;
    link.click();
  };
  
  return (
    <div 
      className={`facial-animation-preview ${isFullscreen ? 'fullscreen' : ''}`}
      ref={containerRef}
    >
      <div className="preview-toolbar">
        <Space>
          <Select
            value={viewMode}
            onChange={handleViewModeChange}
            style={{ width: 120 }}
          >
            <Option value="default">{t('editor.animation.defaultView') as string}</Option>
            <Option value="wireframe">{t('editor.animation.wireframeView') as string}</Option>
          </Select>
          
          <Select
            value={cameraPosition}
            onChange={handleCameraPositionChange}
            style={{ width: 120 }}
          >
            <Option value="front">{t('editor.animation.frontView') as string}</Option>
            <Option value="side">{t('editor.animation.sideView') as string}</Option>
            <Option value="top">{t('editor.animation.topView') as string}</Option>
          </Select>
          
          <Tooltip title={t('editor.animation.resetCamera') as string}>
            <Button icon={<ReloadOutlined />} onClick={resetCamera} />
          </Tooltip>

          <Tooltip title={t('editor.animation.takeScreenshot') as string}>
            <Button icon={<CameraOutlined />} onClick={takeScreenshot} />
          </Tooltip>

          <Tooltip title={isFullscreen ? t('editor.animation.exitFullscreen') as string : t('editor.animation.enterFullscreen') as string}>
            <Button
              icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
              onClick={toggleFullscreen}
            />
          </Tooltip>
        </Space>
      </div>
    </div>
  );
};

export default FacialAnimationPreview;
