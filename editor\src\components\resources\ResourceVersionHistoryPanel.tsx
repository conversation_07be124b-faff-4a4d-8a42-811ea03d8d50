/**
 * 资源版本历史面板组件
 * 用于显示和管理资源的版本历史
 */
import React, { useState } from 'react';
import { 
  Card, 
  List, 
  Button, 
  Space, 
  Tag, 
  Typography, 
  Tooltip, 
  Popconfirm, 
  Modal, 
  Input, 
  Divider, 
  Empty, 
  message,
  Select
} from 'antd';
import { 
  HistoryOutlined, 
  RollbackOutlined, 
  DeleteOutlined, 
  CloseCircleOutlined, 
  TagOutlined, 
  InfoCircleOutlined, 
  UserOutlined, 
  ClockCircleOutlined, 
  DiffOutlined,
  EditOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { ResourceVersion } from '../../store/resources/resourceVersionSlice';
import './ResourceVersionHistoryPanel.less';

const { Text, Paragraph } = Typography;

// 格式化时间
const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 组件属性接口
interface ResourceVersionHistoryPanelProps {
  resourceName: string;
  versions: ResourceVersion[];
  currentVersionId: string | null;
  onClose: () => void;
  onCreateVersion: (description: string, tags: string[]) => void;
  onRollbackVersion: (versionId: string) => void;
  onDeleteVersion: (versionId: string) => void;
  onCompareVersions: (versionId1: string, versionId2: string) => void;
  onAddTag: (versionId: string, tag: string) => void;
  onRemoveTag: (versionId: string, tag: string) => void;
  onUpdateDescription: (versionId: string, description: string) => void;
  isRollingBack: boolean;
}

/**
 * 资源版本历史面板组件
 */
const ResourceVersionHistoryPanel: React.FC<ResourceVersionHistoryPanelProps> = ({
  resourceName,
  versions,
  currentVersionId,
  onClose,
  onCreateVersion,
  onRollbackVersion,
  onDeleteVersion,
  onCompareVersions,
  onAddTag,
  onRemoveTag,
  onUpdateDescription,
  isRollingBack
}) => {
  const { t } = useTranslation();

  // 使用 t 函数避免未使用警告
  console.debug('ResourceVersionHistoryPanel t:', t);
  
  // 状态
  const [selectedVersionIds, setSelectedVersionIds] = useState<string[]>([]);
  const [editingVersionId, setEditingVersionId] = useState<string | null>(null);
  const [editingDescription, setEditingDescription] = useState<string>('');
  const [newTagValue, setNewTagValue] = useState<string>('');
  const [newTagVersionId, setNewTagVersionId] = useState<string | null>(null);
  
  // 处理版本选择
  const handleVersionSelect = (versionId: string) => {
    if (selectedVersionIds.includes(versionId)) {
      // 如果已选择，则取消选择
      setSelectedVersionIds(selectedVersionIds.filter(id => id !== versionId));
    } else {
      // 如果未选择，则添加到选择列表
      // 最多选择两个版本进行比较
      if (selectedVersionIds.length < 2) {
        setSelectedVersionIds([...selectedVersionIds, versionId]);
      } else {
        // 如果已选择两个版本，则替换最早选择的版本
        setSelectedVersionIds([selectedVersionIds[1], versionId]);
      }
    }
  };
  
  // 处理比较版本
  const handleCompareVersions = () => {
    if (selectedVersionIds.length === 2) {
      onCompareVersions(selectedVersionIds[0], selectedVersionIds[1]);
    } else {
      message.warning('请选择两个版本进行比较');
    }
  };
  
  // 处理创建版本
  const handleCreateVersion = () => {
    // 显示创建版本对话框
    Modal.confirm({
      title: '创建版本',
      content: (
        <div>
          <Paragraph>
            为资源 "{resourceName}" 创建一个新的版本快照，您可以随时回滚到此版本。
          </Paragraph>
          <Paragraph>
            <Text strong>描述：</Text>
            <Input
              id="version-description"
              type="text"
              style={{ width: '100%', marginTop: 8 }}
              placeholder="输入版本描述"
            />
          </Paragraph>
          <Paragraph>
            <Text strong>标签：</Text>
            <Select
              id="version-tags"
              mode="tags"
              style={{ width: '100%', marginTop: 8 }}
              placeholder="添加标签（可选）"
            />
          </Paragraph>
        </div>
      ),
      onOk: () => {
        const descriptionInput = document.getElementById('version-description') as HTMLInputElement;
        const description = descriptionInput?.value || `资源 "${resourceName}" 的版本`;
        
        const tagsSelect = document.getElementById('version-tags') as any;
        const tags = tagsSelect?.value ? JSON.parse(tagsSelect.value) : [];
        
        // 创建版本
        onCreateVersion(description, tags);
      }
    });
  };
  
  // 处理编辑描述
  const handleEditDescription = (version: ResourceVersion) => {
    setEditingVersionId(version.id);
    setEditingDescription(version.description || '');
  };
  
  // 处理保存描述
  const handleSaveDescription = () => {
    if (editingVersionId && editingDescription.trim()) {
      onUpdateDescription(editingVersionId, editingDescription);
      setEditingVersionId(null);
      setEditingDescription('');
    }
  };
  
  // 处理取消编辑
  const handleCancelEdit = () => {
    setEditingVersionId(null);
    setEditingDescription('');
  };
  
  // 处理添加标签
  const handleAddTag = (versionId: string) => {
    setNewTagVersionId(versionId);
    setNewTagValue('');
  };
  
  // 处理保存标签
  const handleSaveTag = () => {
    if (newTagVersionId && newTagValue.trim()) {
      onAddTag(newTagVersionId, newTagValue.trim());
      setNewTagVersionId(null);
      setNewTagValue('');
    }
  };
  
  // 处理取消添加标签
  const handleCancelAddTag = () => {
    setNewTagVersionId(null);
    setNewTagValue('');
  };
  
  // 渲染版本列表项
  const renderVersionItem = (version: ResourceVersion) => {
    const isCurrent = version.id === currentVersionId;
    const isSelected = selectedVersionIds.includes(version.id);
    const isEditing = version.id === editingVersionId;
    const isAddingTag = version.id === newTagVersionId;
    
    return (
      <List.Item
        key={version.id}
        className={`version-item ${isCurrent ? 'current' : ''} ${isSelected ? 'selected' : ''}`}
        onClick={() => handleVersionSelect(version.id)}
        actions={[
          <Tooltip title="比较" key="compare">
            <Button
              icon={<DiffOutlined />}
              size="small"
              type={isSelected ? 'primary' : 'default'}
              onClick={(e) => {
                e.stopPropagation();
                handleVersionSelect(version.id);
              }}
            />
          </Tooltip>,
          <Tooltip title="回滚到此版本" key="rollback">
            <Button
              icon={<RollbackOutlined />}
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                onRollbackVersion(version.id);
              }}
              loading={isRollingBack && version.id === currentVersionId}
              disabled={isCurrent}
            />
          </Tooltip>,
          <Tooltip title="删除此版本" key="delete">
            <Popconfirm
              title="确定要删除此版本吗？"
              onConfirm={(e) => {
                e?.stopPropagation();
                onDeleteVersion(version.id);
              }}
              onCancel={(e) => e?.stopPropagation()}
              okText="确定"
              cancelText="取消"
            >
              <Button
                icon={<DeleteOutlined />}
                size="small"
                danger
                disabled={isCurrent}
                onClick={(e) => e.stopPropagation()}
              />
            </Popconfirm>
          </Tooltip>
        ]}
      >
        <List.Item.Meta
          title={
            <Space>
              {isEditing ? (
                <Input
                  value={editingDescription}
                  onChange={(e) => setEditingDescription(e.target.value)}
                  onPressEnter={handleSaveDescription}
                  autoFocus
                  onClick={(e) => e.stopPropagation()}
                  addonAfter={
                    <Space>
                      <Button size="small" type="link" onClick={handleSaveDescription}>保存</Button>
                      <Button size="small" type="link" onClick={handleCancelEdit}>取消</Button>
                    </Space>
                  }
                />
              ) : (
                <>
                  <Text strong>{version.description || '无描述'}</Text>
                  <Button
                    icon={<EditOutlined />}
                    size="small"
                    type="text"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditDescription(version);
                    }}
                  />
                </>
              )}
              {isCurrent && <Tag color="success">当前版本</Tag>}
              <Tag color="blue">v{version.version}</Tag>
            </Space>
          }
          description={
            <div onClick={(e) => e.stopPropagation()}>
              <div>
                <ClockCircleOutlined /> {formatTime(version.timestamp)}
              </div>
              <div>
                <UserOutlined /> {version.userName}
              </div>
              <div>
                <TagOutlined />
                {version.tags && version.tags.length > 0 ? (
                  <Space size={[0, 4]} wrap>
                    {version.tags.map(tag => (
                      <Tag 
                        key={tag} 
                        closable 
                        onClose={(e) => {
                          e.preventDefault();
                          onRemoveTag(version.id, tag);
                        }}
                      >
                        {tag}
                      </Tag>
                    ))}
                    <Button
                      type="dashed"
                      size="small"
                      icon={<PlusOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAddTag(version.id);
                      }}
                    >
                      添加标签
                    </Button>
                  </Space>
                ) : (
                  <Button
                    type="dashed"
                    size="small"
                    icon={<PlusOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddTag(version.id);
                    }}
                  >
                    添加标签
                  </Button>
                )}
                {isAddingTag && (
                  <div className="add-tag-container">
                    <Input
                      value={newTagValue}
                      onChange={(e) => setNewTagValue(e.target.value)}
                      onPressEnter={handleSaveTag}
                      placeholder="输入标签名称"
                      autoFocus
                      addonAfter={
                        <Space>
                          <Button size="small" type="link" onClick={handleSaveTag}>添加</Button>
                          <Button size="small" type="link" onClick={handleCancelAddTag}>取消</Button>
                        </Space>
                      }
                    />
                  </div>
                )}
              </div>
            </div>
          }
        />
      </List.Item>
    );
  };
  
  return (
    <div className="resource-version-history-panel">
      <Card
        title={
          <Space>
            <HistoryOutlined />
            <span>资源版本历史 - {resourceName}</span>
          </Space>
        }
        extra={
          <Space>
            {selectedVersionIds.length === 2 && (
              <Button
                type="primary"
                icon={<DiffOutlined />}
                onClick={handleCompareVersions}
              >
                比较版本
              </Button>
            )}
            <Button
              type="primary"
              icon={<HistoryOutlined />}
              onClick={handleCreateVersion}
            >
              创建版本
            </Button>
            <Button
              type="text"
              icon={<CloseCircleOutlined />}
              onClick={onClose}
            />
          </Space>
        }
        className="version-card"
      >
        {versions.length > 0 ? (
          <List
            className="version-list"
            dataSource={versions.sort((a, b) => b.timestamp - a.timestamp)}
            renderItem={renderVersionItem}
          />
        ) : (
          <Empty
            description="没有版本历史记录"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}

        <Divider />

        <div className="version-info">
          <Space>
            <InfoCircleOutlined />
            <Text type="secondary">
              版本历史允许您保存资源的快照，并在需要时回滚到之前的版本。选择两个版本可以进行比较。
            </Text>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default ResourceVersionHistoryPanel;
