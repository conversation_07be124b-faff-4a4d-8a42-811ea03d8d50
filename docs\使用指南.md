# DL引擎使用指南

## 目录
1. [快速开始](#快速开始)
2. [基础操作](#基础操作)
3. [编辑器界面](#编辑器界面)
4. [项目管理](#项目管理)
5. [场景编辑](#场景编辑)
6. [视觉脚本编程](#视觉脚本编程)
7. [资源管理](#资源管理)
8. [协作功能](#协作功能)
9. [高级功能](#高级功能)
10. [常见问题](#常见问题)

## 快速开始

### 系统要求
- **操作系统**：Windows 10+、macOS 10.15+、Linux (Ubuntu 18.04+)
- **浏览器**：Chrome 90+、Firefox 88+、Safari 14+、Edge 90+
- **硬件要求**：
  - CPU：Intel i5 或 AMD Ryzen 5 以上
  - 内存：8GB RAM（推荐16GB）
  - 显卡：支持WebGL 2.0的独立显卡
  - 网络：稳定的互联网连接

### 安装与启动

#### 方式一：在线使用（推荐）
1. 打开浏览器，访问：`https://dl-engine.com`
2. 点击"开始使用"按钮
3. 注册账号或使用现有账号登录
4. 选择"创建新项目"开始使用

#### 方式二：本地部署
```bash
# 克隆项目
git clone https://github.com/your-org/dl-engine.git
cd dl-engine

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问 http://localhost:3000
```

### 创建第一个项目
1. 登录后点击"新建项目"
2. 选择项目模板：
   - **空白项目**：从零开始创建
   - **基础场景**：包含基本光照和地面
   - **游戏模板**：预设游戏框架
   - **教育模板**：适合教学演示
3. 输入项目名称和描述
4. 点击"创建项目"

## 基础操作

### 界面导航
- **鼠标左键**：选择对象
- **鼠标右键**：旋转视角
- **鼠标中键**：平移视角
- **滚轮**：缩放视角
- **Shift + 鼠标中键**：快速平移

### 快捷键
| 快捷键 | 功能 |
|--------|------|
| Ctrl+N | 新建项目 |
| Ctrl+O | 打开项目 |
| Ctrl+S | 保存项目 |
| Ctrl+Z | 撤销操作 |
| Ctrl+Y | 重做操作 |
| Delete | 删除选中对象 |
| F | 聚焦到选中对象 |
| G | 移动工具 |
| R | 旋转工具 |
| S | 缩放工具 |
| Tab | 切换编辑模式 |

### 工具栏
- **选择工具**：选择和操作对象
- **移动工具**：移动对象位置
- **旋转工具**：旋转对象方向
- **缩放工具**：调整对象大小
- **相机工具**：调整视角和相机设置
- **测量工具**：测量距离和角度

## 编辑器界面

### 主要区域

#### 1. 菜单栏
- **文件**：新建、打开、保存、导入、导出
- **编辑**：撤销、重做、复制、粘贴、删除
- **视图**：显示设置、网格、辅助线
- **工具**：各种专业工具和插件
- **帮助**：文档、教程、反馈

#### 2. 工具栏
- **基础工具**：选择、移动、旋转、缩放
- **创建工具**：添加几何体、光源、相机
- **编辑工具**：材质编辑、动画编辑、脚本编辑
- **视图工具**：视角切换、显示模式

#### 3. 3D视口
- **主视口**：实时3D场景预览
- **多视角**：前视图、侧视图、顶视图
- **渲染模式**：线框、实体、材质预览
- **网格显示**：辅助网格、坐标轴

#### 4. 层级面板
- **场景树**：显示所有场景对象
- **分组管理**：对象分组和层级关系
- **可见性控制**：显示/隐藏对象
- **锁定功能**：防止误操作

#### 5. 属性面板
- **变换属性**：位置、旋转、缩放
- **材质属性**：颜色、纹理、着色器
- **组件属性**：物理、动画、脚本
- **自定义属性**：用户定义的属性

#### 6. 资源面板
- **模型库**：3D模型资源
- **材质库**：材质和纹理
- **音频库**：音效和音乐
- **脚本库**：代码和视觉脚本

#### 7. 时间轴
- **动画编辑**：关键帧动画制作
- **时间控制**：播放、暂停、停止
- **帧率设置**：动画帧率调整
- **曲线编辑**：动画曲线调整

## 项目管理

### 项目结构
```
我的项目/
├── 场景/
│   ├── 主场景.scene
│   ├── 菜单场景.scene
│   └── 游戏场景.scene
├── 资源/
│   ├── 模型/
│   ├── 纹理/
│   ├── 音频/
│   └── 脚本/
├── 设置/
│   ├── 项目设置.json
│   ├── 构建设置.json
│   └── 发布设置.json
└── 输出/
    ├── Web/
    ├── 移动端/
    └── 桌面端/
```

### 项目设置
1. **基本信息**
   - 项目名称和描述
   - 版本号和作者信息
   - 标签和分类

2. **渲染设置**
   - 渲染质量级别
   - 阴影设置
   - 后处理效果

3. **物理设置**
   - 重力参数
   - 碰撞检测精度
   - 物理时间步长

4. **音频设置**
   - 主音量控制
   - 音频格式设置
   - 3D音频参数

### 版本控制
1. **自动保存**：每5分钟自动保存
2. **手动保存**：Ctrl+S 或点击保存按钮
3. **版本历史**：查看和恢复历史版本
4. **分支管理**：创建和切换开发分支

## 场景编辑

### 添加对象

#### 基础几何体
1. 右键点击3D视口
2. 选择"添加" → "几何体"
3. 选择所需几何体：
   - **立方体**：基础方块
   - **球体**：球形对象
   - **圆柱体**：柱状对象
   - **平面**：平面对象
   - **胶囊体**：胶囊形状

#### 导入模型
1. 点击"文件" → "导入"
2. 选择支持的格式：
   - **GLTF/GLB**：推荐格式
   - **FBX**：常用3D格式
   - **OBJ**：简单模型格式
   - **DAE**：Collada格式
3. 调整导入设置
4. 点击"导入"

### 对象操作

#### 选择对象
- **单选**：左键点击对象
- **多选**：按住Ctrl+左键点击
- **框选**：拖拽选择框
- **全选**：Ctrl+A

#### 变换操作
1. **移动**：选择移动工具(G)，拖拽箭头
2. **旋转**：选择旋转工具(R)，拖拽圆环
3. **缩放**：选择缩放工具(S)，拖拽方块
4. **精确输入**：在属性面板输入具体数值

#### 复制和删除
- **复制**：Ctrl+C 复制，Ctrl+V 粘贴
- **删除**：选中对象后按Delete键
- **克隆**：Shift+D 快速克隆

### 材质编辑

#### 基础材质
1. 选择对象
2. 在属性面板找到"材质"选项
3. 点击"新建材质"
4. 调整材质属性：
   - **基础颜色**：物体的基本颜色
   - **金属度**：金属质感强度
   - **粗糙度**：表面粗糙程度
   - **发光强度**：自发光效果

#### 纹理贴图
1. 在材质编辑器中
2. 点击颜色旁的"贴图"按钮
3. 选择或上传纹理图片
4. 调整UV映射和平铺设置

#### 高级材质
- **PBR材质**：基于物理的渲染
- **卡通材质**：非真实感渲染
- **透明材质**：玻璃、水等透明效果
- **发光材质**：霓虹灯、屏幕等发光效果

### 光照设置

#### 光源类型
1. **方向光**：模拟太阳光
2. **点光源**：灯泡式光源
3. **聚光灯**：手电筒式光源
4. **环境光**：整体环境照明

#### 光照调整
- **强度**：光照亮度
- **颜色**：光的颜色
- **阴影**：是否投射阴影
- **范围**：光照影响范围

## 视觉脚本编程

### 脚本编辑器界面

#### 节点面板
- **节点库**：所有可用节点分类显示
- **搜索功能**：快速查找所需节点
- **收藏夹**：常用节点快速访问
- **最近使用**：最近使用的节点

#### 画布区域
- **节点连接**：拖拽连接节点
- **缩放平移**：鼠标操作画布
- **网格对齐**：节点自动对齐
- **注释功能**：添加说明文字

#### 属性面板
- **节点属性**：当前选中节点的属性
- **参数设置**：节点参数配置
- **帮助信息**：节点使用说明
- **示例代码**：相关代码示例

### 基础节点使用

#### 事件节点
```
开始事件 → 执行逻辑 → 结束
```
- **OnStart**：场景开始时触发
- **OnUpdate**：每帧更新时触发
- **OnClick**：鼠标点击时触发
- **OnKeyPress**：按键时触发

#### 流程控制
```
条件判断 → 分支A / 分支B
```
- **If条件**：条件判断分支
- **For循环**：重复执行指定次数
- **While循环**：条件满足时重复执行
- **Switch分支**：多条件分支选择

#### 数据操作
```
获取数据 → 处理数据 → 设置数据
```
- **变量设置**：存储和获取变量
- **数学运算**：加减乘除等运算
- **字符串操作**：文本处理
- **数组操作**：列表数据处理

### 高级脚本功能

#### 自定义函数
1. 创建函数节点
2. 定义输入输出参数
3. 编写函数逻辑
4. 在其他地方调用

#### 事件系统
1. 定义自定义事件
2. 触发事件的条件
3. 监听和响应事件
4. 事件参数传递

#### 异步操作
1. 网络请求节点
2. 文件加载节点
3. 定时器节点
4. 协程控制节点

## 资源管理

### 资源类型

#### 3D模型
- **支持格式**：GLTF、GLB、FBX、OBJ、DAE
- **优化建议**：面数控制、纹理压缩
- **LOD设置**：多细节层次模型

#### 纹理图片
- **支持格式**：PNG、JPG、WebP、HDR、EXR
- **尺寸要求**：2的幂次方尺寸（推荐）
- **压缩设置**：自动压缩优化

#### 音频文件
- **支持格式**：MP3、WAV、OGG、AAC
- **质量设置**：采样率和比特率
- **3D音频**：空间音频设置

#### 脚本文件
- **JavaScript**：传统脚本编程
- **TypeScript**：类型安全的脚本
- **视觉脚本**：节点式编程

### 资源导入

#### 批量导入
1. 选择"文件" → "批量导入"
2. 选择文件夹或多个文件
3. 设置导入选项
4. 点击"开始导入"

#### 拖拽导入
1. 从文件管理器拖拽文件
2. 拖拽到编辑器窗口
3. 自动识别文件类型
4. 应用默认导入设置

### 资源优化

#### 自动优化
- **模型简化**：自动减少面数
- **纹理压缩**：自动选择最佳格式
- **音频压缩**：自动调整质量
- **脚本压缩**：代码混淆和压缩

#### 手动优化
- **LOD设置**：手动配置细节层次
- **纹理设置**：手动调整压缩参数
- **音频设置**：手动调整音质参数
- **脚本优化**：代码性能优化

## 协作功能

### 实时协作

#### 多用户编辑
1. 邀请团队成员加入项目
2. 设置成员权限级别
3. 实时查看其他用户操作
4. 自动同步所有更改

#### 冲突解决
- **自动合并**：无冲突的更改自动合并
- **冲突提示**：冲突时显示提示信息
- **手动解决**：用户选择保留哪个版本
- **历史记录**：查看所有更改历史

### 权限管理

#### 权限级别
- **所有者**：完全控制权限
- **管理员**：管理项目和成员
- **编辑者**：编辑项目内容
- **查看者**：只能查看项目

#### 细粒度权限
- **场景编辑**：是否可以编辑场景
- **资源管理**：是否可以上传资源
- **脚本编辑**：是否可以编辑脚本
- **项目设置**：是否可以修改设置

### 评论和反馈

#### 对象评论
1. 选择场景中的对象
2. 右键选择"添加评论"
3. 输入评论内容
4. 其他用户可以查看和回复

#### 项目讨论
- **讨论区**：项目整体讨论
- **任务分配**：分配具体任务
- **进度跟踪**：跟踪项目进度
- **问题反馈**：报告和解决问题

## 高级功能

### AI助手

#### 智能场景生成
1. 点击"AI助手" → "场景生成"
2. 输入场景描述文字
3. 选择生成风格和参数
4. 点击"生成场景"

#### 代码助手
- **智能补全**：代码自动补全
- **错误检测**：实时错误提示
- **优化建议**：性能优化建议
- **代码生成**：根据描述生成代码

### 性能优化

#### 渲染优化
- **LOD系统**：距离相关的细节层次
- **遮挡剔除**：不可见对象剔除
- **批量渲染**：减少绘制调用
- **纹理压缩**：减少显存占用

#### 脚本优化
- **代码分析**：性能瓶颈检测
- **内存监控**：内存使用情况
- **帧率监控**：实时帧率显示
- **优化建议**：自动优化建议

### 发布部署

#### 构建设置
1. 选择"文件" → "构建设置"
2. 选择目标平台：
   - **Web**：浏览器运行
   - **移动端**：手机和平板
   - **桌面端**：Windows、Mac、Linux
   - **VR/AR**：虚拟现实设备
3. 配置构建参数
4. 点击"开始构建"

#### 发布选项
- **本地发布**：生成本地文件
- **云端发布**：发布到云平台
- **应用商店**：发布到应用商店
- **自定义服务器**：部署到自己的服务器

## 常见问题

### 性能问题

#### Q: 编辑器运行缓慢怎么办？
A: 
1. 检查硬件配置是否满足要求
2. 关闭不必要的浏览器标签页
3. 降低渲染质量设置
4. 清理浏览器缓存

#### Q: 场景加载时间过长？
A:
1. 优化模型面数和纹理大小
2. 使用LOD系统
3. 启用资源预加载
4. 检查网络连接状态

### 操作问题

#### Q: 无法选择对象？
A:
1. 检查对象是否被锁定
2. 确认对象在当前图层
3. 检查选择工具是否激活
4. 尝试在层级面板中选择

#### Q: 材质显示不正确？
A:
1. 检查纹理文件是否正确加载
2. 确认材质参数设置
3. 检查光照设置
4. 尝试重新应用材质

### 协作问题

#### Q: 无法看到其他用户的更改？
A:
1. 检查网络连接
2. 刷新页面重新加载
3. 确认项目权限设置
4. 联系项目管理员

#### Q: 出现编辑冲突怎么办？
A:
1. 查看冲突提示信息
2. 选择保留的版本
3. 手动合并更改
4. 与团队成员沟通协调

---

*本指南持续更新，如有问题请访问帮助中心或联系技术支持*
