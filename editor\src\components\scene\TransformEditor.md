# TransformEditor 变换编辑器组件

## 概述

TransformEditor 是一个用于编辑3D实体变换属性（位置、旋转、缩放）的React组件。它与底层的 dl-engine 引擎深度集成，提供实时的变换编辑功能。

## 功能特性

### 核心功能
- **位置编辑**: 支持X、Y、Z轴的位置调整，单位为世界坐标
- **旋转编辑**: 支持欧拉角旋转编辑，显示为度数（-360° 到 360°）
- **缩放编辑**: 支持非均匀缩放，最小值为0.01
- **实时同步**: 与底层引擎的Transform组件实时同步
- **事件监听**: 监听引擎中的变换变化并更新UI

### 高级功能
- **链接缩放**: 可以链接X、Y、Z轴进行统一缩放
- **复制粘贴**: 支持变换数据的复制和粘贴
- **重置功能**: 一键重置变换到默认值
- **防抖处理**: 100ms防抖，避免频繁更新引擎
- **只读模式**: 支持只读模式，用于查看变换数据
- **实体信息**: 显示当前编辑的实体名称和ID

## 使用方法

### 基本用法

```tsx
import TransformEditor from './components/scene/TransformEditor';

// 编辑当前选中的实体
<TransformEditor />

// 编辑指定实体
<TransformEditor entityId="entity-123" />

// 只读模式
<TransformEditor readonly={true} />
```

### 外部数据控制

```tsx
const [transformData, setTransformData] = useState({
  position: { x: 0, y: 0, z: 0 },
  rotation: { x: 0, y: 0, z: 0 },
  scale: { x: 1, y: 1, z: 1 }
});

<TransformEditor 
  data={transformData}
  onChange={setTransformData}
/>
```

### 在属性面板中使用

```tsx
import { Card } from 'antd';
import TransformEditor from './TransformEditor';

const PropertiesPanel = () => {
  return (
    <div>
      <TransformEditor />
      {/* 其他组件编辑器 */}
    </div>
  );
};
```

## API 接口

### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| entityId | string | - | 要编辑的实体ID，如果不提供则使用当前选中的实体 |
| data | TransformData | - | 外部控制的变换数据 |
| onChange | (data: TransformData) => void | - | 数据变化回调函数 |
| readonly | boolean | false | 是否为只读模式 |

### TransformData 接口

```tsx
interface TransformData {
  position: Vector3;  // 位置
  rotation: Vector3;  // 旋转（度数）
  scale: Vector3;     // 缩放
}

interface Vector3 {
  x: number;
  y: number;
  z: number;
}
```

## 集成说明

### 与Redux状态管理集成

组件自动从Redux store中获取当前选中的实体：

```tsx
const selectedObject = useSelector((state: any) => state.editor.selectedObject);
```

### 与引擎服务集成

组件通过EngineService与底层引擎通信：

```tsx
import EngineService from '../../services/EngineService';

// 获取活动场景
const scene = EngineService.getActiveScene();

// 获取实体列表
const entities = scene.getEntities();
```

### 事件监听

组件监听Transform组件的变化事件：

```tsx
transform.on('positionChanged', handleTransformChange);
transform.on('rotationChanged', handleTransformChange);
transform.on('scaleChanged', handleTransformChange);
```

## 性能优化

### 防抖处理

使用lodash的debounce函数，100ms延迟更新引擎：

```tsx
const debouncedUpdateTransform = useCallback(
  debounce((transformData: TransformData) => {
    // 更新引擎
  }, 100),
  [currentTransform, onChange, readonly]
);
```

### 内存管理

组件正确清理事件监听器，避免内存泄漏：

```tsx
useEffect(() => {
  // 设置监听器
  return () => {
    // 清理监听器
    if (transform) {
      transform.off('positionChanged', handleTransformChange);
      transform.off('rotationChanged', handleTransformChange);
      transform.off('scaleChanged', handleTransformChange);
    }
  };
}, [targetEntity]);
```

## 样式定制

组件使用Ant Design的样式系统，支持主题定制：

```tsx
// 自定义样式
<TransformEditor 
  style={{ 
    backgroundColor: '#f5f5f5',
    borderRadius: '8px'
  }}
/>
```

## 注意事项

1. **坐标系统**: 位置使用世界坐标系，旋转使用欧拉角（度数）
2. **数值精度**: 位置和缩放支持小数点后多位，旋转限制在整数度数
3. **最小值限制**: 缩放的最小值为0.01，避免除零错误
4. **引擎依赖**: 组件依赖底层的dl-engine，确保引擎已正确初始化
5. **状态同步**: 组件会自动同步引擎状态，避免手动调用引擎API

## 扩展功能

### 自定义工具栏

可以通过修改组件来添加自定义工具栏按钮：

```tsx
// 在Card的extra属性中添加更多按钮
extra={
  <Space size="small">
    <Button icon={<CopyOutlined />} onClick={handleCopy} />
    <Button icon={<PasteOutlined />} onClick={handlePaste} />
    <Button icon={<ReloadOutlined />} onClick={handleReset} />
    {/* 自定义按钮 */}
    <Button icon={<SettingOutlined />} onClick={handleSettings} />
  </Space>
}
```

### 单位转换

可以扩展组件支持不同的单位系统：

```tsx
// 添加单位选择器
const [unit, setUnit] = useState('meters');

// 根据单位转换数值
const convertValue = (value: number, fromUnit: string, toUnit: string) => {
  // 实现单位转换逻辑
};
```
