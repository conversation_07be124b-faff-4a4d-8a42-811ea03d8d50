{"accessibility": {"title": "Accessibility", "description": "Configure editor accessibility features", "enabled": "Enabled", "disabled": "Disabled", "settings": {"masterSwitch": "Enable Accessibility Features", "screenReader": "Screen Reader Support", "screenReaderDescription": "Provide voice feedback and reading functionality for screen reader users", "keyboardNavigation": "Keyboard Navigation", "keyboardNavigationDescription": "Enable full keyboard navigation support", "highContrast": "High Contrast Mode", "highContrastDescription": "Improve contrast of interface elements for users with visual impairments", "textScaling": "Text Scaling", "textScalingDescription": "Adjust the size of interface text", "textScaleFactor": "Text Scale Factor", "reducedMotion": "Reduced Motion", "reducedMotionDescription": "Reduce or disable animations and transition effects", "focusIndicator": "Focus Indicator", "focusIndicatorDescription": "Enhance visual indication of focused elements", "colorAdjustment": "Color Adjustment", "colorAdjustmentDescription": "Adjust interface brightness, contrast, and saturation", "autoAnnounce": "Auto Announce", "autoAnnounceDescription": "Automatically announce interface changes and operation feedback", "brightness": "Brightness", "contrast": "Contrast", "saturation": "Saturation", "dark": "Dark", "normal": "Normal", "bright": "<PERSON>", "low": "Low", "high": "High", "grayscale": "Grayscale", "vivid": "Vivid"}, "systemSettingsDetected": "System accessibility settings detected", "systemReducedMotion": "System has reduced motion enabled", "systemHighContrast": "System has high contrast enabled", "resetToSystemSettings": "Reset to System Settings", "testScreenReader": "Test Screen Reader", "testMessage": "This is a test message to verify that the screen reader functionality is working properly.", "helpText": "Accessibility settings can help users with visual, hearing, or motor impairments use the editor more effectively. You can enable the appropriate features as needed.", "testColorContrast": "Color Contrast Test", "testKeyboardAccessibility": "Keyboard Accessibility Test", "testScreenReaderSupport": "Screen Reader Support Test", "testTextScaling": "Text Scaling Test", "testReducedMotion": "Reduced Motion Test", "testImageAltText": "Image Alt Text Test", "testFormLabels": "Form Labels Test", "testHeadingStructure": "Heading Structure Test", "testLanguageSettings": "Language Settings Test", "testErrorIdentification": "Error Identification Test", "testFocusOrder": "Focus Order Test", "testFocusVisibility": "Focus Visibility Test", "testPageTitle": "Page Title Test", "testLinkPurpose": "Link Purpose Test", "testContentHoverFocus": "Content Hover Focus Test", "testInputModalities": "Input Modalities Test", "testStatusMessages": "Status Messages Test", "resetConfig": "Reset Configuration"}}