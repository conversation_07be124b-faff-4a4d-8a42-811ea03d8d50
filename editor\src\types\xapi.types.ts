/**
 * 前端xAPI类型定义
 * 简化版的xAPI接口，用于前端数据采集
 */

// 语言映射
export interface LanguageMap {
  [languageCode: string]: string;
}

// 扩展数据
export interface Extensions {
  [iri: string]: any;
}

// 账户信息
export interface Account {
  homePage: string;
  name: string;
}

// 代理人（学习者）
export interface Agent {
  objectType?: 'Agent' | 'Group';
  name?: string;
  mbox?: string;
  mbox_sha1sum?: string;
  openid?: string;
  account?: Account;
}

// 组
export interface Group extends Agent {
  objectType: 'Group';
  member?: Agent[];
}

// 行为者
export type Actor = Agent | Group;

// 动词
export interface Verb {
  id: string;
  display?: LanguageMap;
}

// 分数
export interface Score {
  scaled?: number;
  raw?: number;
  min?: number;
  max?: number;
}

// 活动定义
export interface ActivityDefinition {
  name?: LanguageMap;
  description?: LanguageMap;
  type?: string;
  moreInfo?: string;
  extensions?: Extensions;
}

// 活动
export interface Activity {
  objectType?: 'Activity';
  id: string;
  definition?: ActivityDefinition;
}

// 语句引用
export interface StatementRef {
  objectType: 'StatementRef';
  id: string;
}

// 语句对象
export type StatementObject = Activity | Agent | Group | StatementRef;

// 结果
export interface Result {
  score?: Score;
  success?: boolean;
  completion?: boolean;
  response?: string;
  duration?: string;
  extensions?: Extensions;
}

// 上下文活动
export interface ContextActivities {
  parent?: Activity[];
  grouping?: Activity[];
  category?: Activity[];
  other?: Activity[];
}

// 上下文
export interface Context {
  registration?: string;
  instructor?: Actor;
  team?: Group;
  contextActivities?: ContextActivities;
  revision?: string;
  platform?: string;
  language?: string;
  statement?: StatementRef;
  extensions?: Extensions;
}

// xAPI语句
export interface XAPIStatement {
  id?: string;
  actor: Actor;
  verb: Verb;
  object: StatementObject;
  result?: Result;
  context?: Context;
  timestamp?: string;
  stored?: string;
  authority?: Actor;
  version?: string;
}

// 学习动作词汇
export const LEARNING_VERBS = {
  // 基础学习动作
  EXPERIENCED: 'http://adlnet.gov/expapi/verbs/experienced',
  ATTENDED: 'http://adlnet.gov/expapi/verbs/attended',
  ATTEMPTED: 'http://adlnet.gov/expapi/verbs/attempted',
  COMPLETED: 'http://adlnet.gov/expapi/verbs/completed',
  PASSED: 'http://adlnet.gov/expapi/verbs/passed',
  FAILED: 'http://adlnet.gov/expapi/verbs/failed',
  ANSWERED: 'http://adlnet.gov/expapi/verbs/answered',
  ASKED: 'http://adlnet.gov/expapi/verbs/asked',
  
  // 交互动作
  INTERACTED: 'http://adlnet.gov/expapi/verbs/interacted',
  RESPONDED: 'http://adlnet.gov/expapi/verbs/responded',
  COMMENTED: 'http://adlnet.gov/expapi/verbs/commented',
  SHARED: 'http://adlnet.gov/expapi/verbs/shared',
  
  // 数字人交互专用动作
  TALKED_WITH_AVATAR: 'http://dl-engine.com/xapi/verbs/talked-with-avatar',
  ASKED_AVATAR: 'http://dl-engine.com/xapi/verbs/asked-avatar',
  RECEIVED_RECOMMENDATION: 'http://dl-engine.com/xapi/verbs/received-recommendation',
  FOLLOWED_PATH: 'http://dl-engine.com/xapi/verbs/followed-path',
  EXPLORED_SCENE: 'http://dl-engine.com/xapi/verbs/explored-scene',
  
  // 情感相关动作
  EXPRESSED_EMOTION: 'http://dl-engine.com/xapi/verbs/expressed-emotion',
  SHOWED_INTEREST: 'http://dl-engine.com/xapi/verbs/showed-interest',
  SHOWED_CONFUSION: 'http://dl-engine.com/xapi/verbs/showed-confusion',
  
  // 学习进度动作
  STARTED_LEARNING: 'http://dl-engine.com/xapi/verbs/started-learning',
  PAUSED_LEARNING: 'http://dl-engine.com/xapi/verbs/paused-learning',
  RESUMED_LEARNING: 'http://dl-engine.com/xapi/verbs/resumed-learning',
  MASTERED_CONCEPT: 'http://dl-engine.com/xapi/verbs/mastered-concept',
  STRUGGLED_WITH: 'http://dl-engine.com/xapi/verbs/struggled-with'
} as const;

// DL引擎扩展字段
export const DL_ENGINE_EXTENSIONS = {
  QUESTION: 'http://dl-engine.com/xapi/extensions/question',
  EMOTION: 'http://dl-engine.com/xapi/extensions/emotion',
  KNOWLEDGE_AREA: 'http://dl-engine.com/xapi/extensions/knowledge-area',
  STOPPING_POINTS: 'http://dl-engine.com/xapi/extensions/stopping-points',
  DIFFICULTY: 'http://dl-engine.com/xapi/extensions/difficulty',
  CONTENT_TYPE: 'http://dl-engine.com/xapi/extensions/content-type',
  DECISION_TIME: 'http://dl-engine.com/xapi/extensions/decision-time',
  TRIGGER: 'http://dl-engine.com/xapi/extensions/trigger',
  CONTEXT: 'http://dl-engine.com/xapi/extensions/context',
  SESSION_ID: 'http://dl-engine.com/xapi/extensions/session-id',
  AVATAR_ID: 'http://dl-engine.com/xapi/extensions/avatar-id',
  SCENE_ID: 'http://dl-engine.com/xapi/extensions/scene-id'
} as const;

// 学习数据采集事件类型
export interface LearningEvent {
  type: string;
  timestamp: number;
  data: any;
}

// 数字人交互事件
export interface AvatarInteractionEvent extends LearningEvent {
  type: 'avatar_interaction';
  data: {
    avatarId: string;
    avatarName?: string;
    question: string;
    answer: string;
    emotion?: string;
    satisfaction?: number;
    duration?: number;
    knowledgeArea?: string;
    sceneId?: string;
  };
}

// 路径跟踪事件
export interface PathFollowingEvent extends LearningEvent {
  type: 'path_following';
  data: {
    pathId: string;
    pathName?: string;
    startTime: Date;
    endTime: Date;
    completionRate: number;
    stoppingPoints: Array<{
      position: { x: number; y: number; z: number };
      timestamp: number;
      duration: number;
      action?: string;
    }>;
    difficulty?: 'easy' | 'medium' | 'hard';
    sceneId?: string;
  };
}

// 推荐接收事件
export interface RecommendationEvent extends LearningEvent {
  type: 'recommendation_received';
  data: {
    recommendationId: string;
    contentTitle?: string;
    contentType: string;
    relevanceScore: number;
    userAction: 'accepted' | 'rejected' | 'ignored';
    timeToDecision?: number;
    knowledgeArea?: string;
  };
}

// 情感表达事件
export interface EmotionEvent extends LearningEvent {
  type: 'emotion_expression';
  data: {
    emotion: string;
    intensity: number;
    trigger: string;
    context: string;
    sceneId?: string;
    avatarId?: string;
  };
}

// 场景探索事件
export interface SceneExplorationEvent extends LearningEvent {
  type: 'scene_exploration';
  data: {
    sceneId: string;
    sceneName?: string;
    startTime: Date;
    endTime: Date;
    areasVisited: string[];
    interactionsCount: number;
    completionRate?: number;
  };
}

// 学习会话事件
export interface LearningSessionEvent extends LearningEvent {
  type: 'learning_session';
  data: {
    sessionId: string;
    startTime: Date;
    endTime?: Date;
    duration?: number;
    activitiesCount: number;
    knowledgeAreas: string[];
    emotions: { [emotion: string]: number };
    averageSatisfaction?: number;
  };
}

// 联合事件类型
export type LearningEventType = 
  | AvatarInteractionEvent
  | PathFollowingEvent
  | RecommendationEvent
  | EmotionEvent
  | SceneExplorationEvent
  | LearningSessionEvent;

// 学习数据统计
export interface LearningStats {
  totalEvents: number;
  eventsByType: { [type: string]: number };
  sessionDuration: number;
  averageInteractionTime: number;
  emotionalStates: { [emotion: string]: number };
  knowledgeAreas: { [area: string]: number };
  completionRates: { [activity: string]: number };
}

// 学习进度
export interface LearningProgress {
  userId: string;
  sessionId: string;
  startTime: Date;
  lastActivity: Date;
  totalActivities: number;
  completedActivities: number;
  currentKnowledgeArea?: string;
  currentEmotion?: string;
  averageSatisfaction: number;
  timeSpent: number;
  achievements: string[];
}

// 实时学习状态
export interface RealtimeLearningState {
  isActive: boolean;
  currentActivity?: string;
  currentEmotion?: string;
  attentionLevel: number; // 0-1
  engagementLevel: number; // 0-1
  difficultyLevel: 'easy' | 'medium' | 'hard';
  recommendedBreak: boolean;
  nextRecommendation?: string;
}
