/**
 * DL引擎市场合约测试套件
 */

const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("DLEngineMarketplace", function () {
  let nftContract;
  let marketplace;
  let testToken;
  let owner, seller, buyer, feeRecipient;
  let dlMetadata;

  beforeEach(async function () {
    [owner, seller, buyer, feeRecipient] = await ethers.getSigners();

    // 部署NFT合约
    const DLEngineNFT = await ethers.getContractFactory("DLEngineNFT");
    nftContract = await DLEngineNFT.deploy("DL Engine NFT", "DLNFT");
    await nftContract.deployed();

    // 部署测试ERC20代币
    const TestToken = await ethers.getContractFactory("TestERC20");
    testToken = await TestToken.deploy("Test Token", "TEST", ethers.utils.parseEther("1000000"));
    await testToken.deployed();

    // 部署市场合约
    const DLEngineMarketplace = await ethers.getContractFactory("DLEngineMarketplace");
    marketplace = await DLEngineMarketplace.deploy(feeRecipient.address);
    await marketplace.deployed();

    // 配置合约
    await marketplace.approveNFTContract(nftContract.address);
    await marketplace.approvePaymentToken(ethers.constants.AddressZero); // ETH
    await marketplace.approvePaymentToken(testToken.address);
    await nftContract.authorizeMinter(seller.address);

    // 准备测试元数据
    dlMetadata = {
      assetType: "model",
      licenseType: "CC0",
      subject: "数学",
      gradeLevel: "小学",
      difficulty: 5,
      isEducational: true,
      creator: seller.address,
      createdAt: Math.floor(Date.now() / 1000)
    };

    // 铸造测试NFT
    await nftContract.connect(seller).mintNFT(
      seller.address,
      "ipfs://QmTest123",
      seller.address,
      500, // 5% 版税
      dlMetadata
    );

    // 授权市场合约
    await nftContract.connect(seller).setApprovalForAll(marketplace.address, true);

    // 给买家分配测试代币
    await testToken.transfer(buyer.address, ethers.utils.parseEther("1000"));
    await testToken.connect(buyer).approve(marketplace.address, ethers.utils.parseEther("1000"));
  });

  describe("部署", function () {
    it("应该正确设置费用接收者", async function () {
      expect(await marketplace.feeRecipient()).to.equal(feeRecipient.address);
    });

    it("应该设置默认的市场费用", async function () {
      expect(await marketplace.marketplaceFee()).to.equal(250); // 2.5%
    });

    it("应该默认支持ETH支付", async function () {
      expect(await marketplace.approvedPaymentTokens(ethers.constants.AddressZero)).to.be.true;
    });
  });

  describe("NFT上架功能", function () {
    it("应该能够上架NFT", async function () {
      const price = ethers.utils.parseEther("1");
      const duration = 7 * 24 * 60 * 60; // 7天

      await marketplace.connect(seller).listItem(
        nftContract.address,
        0,
        price,
        ethers.constants.AddressZero,
        duration
      );

      const listing = await marketplace.listings(0);
      expect(listing.seller).to.equal(seller.address);
      expect(listing.nftContract).to.equal(nftContract.address);
      expect(listing.tokenId).to.equal(0);
      expect(listing.price).to.equal(price);
      expect(listing.active).to.be.true;
    });

    it("应该防止上架未拥有的NFT", async function () {
      const price = ethers.utils.parseEther("1");
      const duration = 7 * 24 * 60 * 60;

      await expect(
        marketplace.connect(buyer).listItem(
          nftContract.address,
          0,
          price,
          ethers.constants.AddressZero,
          duration
        )
      ).to.be.revertedWith("Not the owner");
    });

    it("应该防止上架未批准的NFT合约", async function () {
      // 部署新的NFT合约（未批准）
      const NewNFT = await ethers.getContractFactory("DLEngineNFT");
      const newNFT = await NewNFT.deploy("New NFT", "NEW");
      await newNFT.deployed();

      const price = ethers.utils.parseEther("1");
      const duration = 7 * 24 * 60 * 60;

      await expect(
        marketplace.connect(seller).listItem(
          newNFT.address,
          0,
          price,
          ethers.constants.AddressZero,
          duration
        )
      ).to.be.revertedWith("NFT contract not approved");
    });

    it("应该防止设置零价格", async function () {
      const duration = 7 * 24 * 60 * 60;

      await expect(
        marketplace.connect(seller).listItem(
          nftContract.address,
          0,
          0,
          ethers.constants.AddressZero,
          duration
        )
      ).to.be.revertedWith("Price must be greater than 0");
    });
  });

  describe("NFT购买功能", function () {
    beforeEach(async function () {
      const price = ethers.utils.parseEther("1");
      const duration = 7 * 24 * 60 * 60;

      await marketplace.connect(seller).listItem(
        nftContract.address,
        0,
        price,
        ethers.constants.AddressZero,
        duration
      );
    });

    it("应该能够用ETH购买NFT", async function () {
      const price = ethers.utils.parseEther("1");
      const initialSellerBalance = await seller.getBalance();
      const initialFeeRecipientBalance = await feeRecipient.getBalance();

      await marketplace.connect(buyer).buyItem(0, { value: price });

      // 验证NFT所有权转移
      expect(await nftContract.ownerOf(0)).to.equal(buyer.address);

      // 验证listing状态
      const listing = await marketplace.listings(0);
      expect(listing.active).to.be.false;

      // 验证资金分配
      const finalSellerBalance = await seller.getBalance();
      const finalFeeRecipientBalance = await feeRecipient.getBalance();

      // 计算预期收益（扣除版税和平台费用）
      const marketplaceFee = price.mul(250).div(10000); // 2.5%
      const royaltyFee = price.mul(500).div(10000); // 5%
      const sellerReceive = price.sub(marketplaceFee).sub(royaltyFee);

      expect(finalSellerBalance.sub(initialSellerBalance)).to.be.closeTo(sellerReceive, ethers.utils.parseEther("0.01"));
      expect(finalFeeRecipientBalance.sub(initialFeeRecipientBalance)).to.equal(marketplaceFee);
    });

    it("应该能够用ERC20代币购买NFT", async function () {
      // 先取消ETH listing
      await marketplace.connect(seller).cancelListing(0);

      // 用ERC20代币重新上架
      const price = ethers.utils.parseEther("100");
      const duration = 7 * 24 * 60 * 60;

      await marketplace.connect(seller).listItem(
        nftContract.address,
        0,
        price,
        testToken.address,
        duration
      );

      const initialBuyerBalance = await testToken.balanceOf(buyer.address);
      const initialSellerBalance = await testToken.balanceOf(seller.address);

      await marketplace.connect(buyer).buyItem(1);

      // 验证NFT所有权转移
      expect(await nftContract.ownerOf(0)).to.equal(buyer.address);

      // 验证代币转移
      const finalBuyerBalance = await testToken.balanceOf(buyer.address);
      const finalSellerBalance = await testToken.balanceOf(seller.address);

      expect(initialBuyerBalance.sub(finalBuyerBalance)).to.equal(price);
      expect(finalSellerBalance.gt(initialSellerBalance)).to.be.true;
    });

    it("应该防止支付不足", async function () {
      const price = ethers.utils.parseEther("1");
      const insufficientPayment = ethers.utils.parseEther("0.5");

      await expect(
        marketplace.connect(buyer).buyItem(0, { value: insufficientPayment })
      ).to.be.revertedWith("Insufficient payment");
    });

    it("应该防止卖家购买自己的NFT", async function () {
      const price = ethers.utils.parseEther("1");

      await expect(
        marketplace.connect(seller).buyItem(0, { value: price })
      ).to.be.revertedWith("Cannot buy your own item");
    });

    it("应该退还多余的ETH", async function () {
      const price = ethers.utils.parseEther("1");
      const overpayment = ethers.utils.parseEther("1.5");
      const initialBuyerBalance = await buyer.getBalance();

      const tx = await marketplace.connect(buyer).buyItem(0, { value: overpayment });
      const receipt = await tx.wait();
      const gasUsed = receipt.gasUsed.mul(receipt.effectiveGasPrice);

      const finalBuyerBalance = await buyer.getBalance();
      const expectedBalance = initialBuyerBalance.sub(price).sub(gasUsed);

      expect(finalBuyerBalance).to.be.closeTo(expectedBalance, ethers.utils.parseEther("0.01"));
    });
  });

  describe("拍卖功能", function () {
    it("应该能够创建拍卖", async function () {
      const startingPrice = ethers.utils.parseEther("0.5");
      const reservePrice = ethers.utils.parseEther("1");
      const duration = 24 * 60 * 60; // 1天

      await marketplace.connect(seller).createAuction(
        nftContract.address,
        0,
        startingPrice,
        reservePrice,
        ethers.constants.AddressZero,
        duration
      );

      const auction = await marketplace.auctions(0);
      expect(auction.seller).to.equal(seller.address);
      expect(auction.startingPrice).to.equal(startingPrice);
      expect(auction.reservePrice).to.equal(reservePrice);
      expect(auction.active).to.be.true;
    });

    it("应该能够出价", async function () {
      const startingPrice = ethers.utils.parseEther("0.5");
      const reservePrice = ethers.utils.parseEther("1");
      const duration = 24 * 60 * 60;

      await marketplace.connect(seller).createAuction(
        nftContract.address,
        0,
        startingPrice,
        reservePrice,
        ethers.constants.AddressZero,
        duration
      );

      const bidAmount = ethers.utils.parseEther("0.6");
      await marketplace.connect(buyer).placeBid(0, { value: bidAmount });

      const auction = await marketplace.auctions(0);
      expect(auction.currentBid).to.equal(bidAmount);
      expect(auction.currentBidder).to.equal(buyer.address);
    });

    it("应该防止出价过低", async function () {
      const startingPrice = ethers.utils.parseEther("0.5");
      const reservePrice = ethers.utils.parseEther("1");
      const duration = 24 * 60 * 60;

      await marketplace.connect(seller).createAuction(
        nftContract.address,
        0,
        startingPrice,
        reservePrice,
        ethers.constants.AddressZero,
        duration
      );

      const lowBid = ethers.utils.parseEther("0.3");
      await expect(
        marketplace.connect(buyer).placeBid(0, { value: lowBid })
      ).to.be.revertedWith("Bid too low");
    });

    it("应该在接近结束时延长拍卖时间", async function () {
      const startingPrice = ethers.utils.parseEther("0.5");
      const reservePrice = ethers.utils.parseEther("1");
      const duration = 16 * 60; // 16分钟

      await marketplace.connect(seller).createAuction(
        nftContract.address,
        0,
        startingPrice,
        reservePrice,
        ethers.constants.AddressZero,
        duration
      );

      // 快进到接近结束时间
      await ethers.provider.send("evm_increaseTime", [14 * 60]); // 14分钟后
      await ethers.provider.send("evm_mine");

      const auctionBefore = await marketplace.auctions(0);
      const endTimeBefore = auctionBefore.endTime;

      const bidAmount = ethers.utils.parseEther("0.6");
      await marketplace.connect(buyer).placeBid(0, { value: bidAmount });

      const auctionAfter = await marketplace.auctions(0);
      const endTimeAfter = auctionAfter.endTime;

      // 拍卖时间应该被延长
      expect(endTimeAfter.gt(endTimeBefore)).to.be.true;
    });
  });

  describe("拍卖结算", function () {
    beforeEach(async function () {
      const startingPrice = ethers.utils.parseEther("0.5");
      const reservePrice = ethers.utils.parseEther("1");
      const duration = 60; // 1分钟

      await marketplace.connect(seller).createAuction(
        nftContract.address,
        0,
        startingPrice,
        reservePrice,
        ethers.constants.AddressZero,
        duration
      );

      // 出价达到保留价
      const bidAmount = ethers.utils.parseEther("1.2");
      await marketplace.connect(buyer).placeBid(0, { value: bidAmount });

      // 等待拍卖结束
      await ethers.provider.send("evm_increaseTime", [61]);
      await ethers.provider.send("evm_mine");
    });

    it("应该能够结算成功的拍卖", async function () {
      await marketplace.settleAuction(0);

      // 验证NFT转移
      expect(await nftContract.ownerOf(0)).to.equal(buyer.address);

      // 验证拍卖状态
      const auction = await marketplace.auctions(0);
      expect(auction.active).to.be.false;
      expect(auction.settled).to.be.true;
    });

    it("应该防止重复结算", async function () {
      await marketplace.settleAuction(0);

      await expect(
        marketplace.settleAuction(0)
      ).to.be.revertedWith("Auction already settled");
    });
  });

  describe("费用管理", function () {
    it("应该允许所有者设置市场费用", async function () {
      const newFee = 300; // 3%
      await marketplace.setMarketplaceFee(newFee);
      expect(await marketplace.marketplaceFee()).to.equal(newFee);
    });

    it("应该防止设置过高的费用", async function () {
      const highFee = 1500; // 15%，超过最大值10%

      await expect(
        marketplace.setMarketplaceFee(highFee)
      ).to.be.revertedWith("Fee too high");
    });

    it("应该允许所有者更改费用接收者", async function () {
      const newFeeRecipient = buyer.address;
      await marketplace.setFeeRecipient(newFeeRecipient);
      expect(await marketplace.feeRecipient()).to.equal(newFeeRecipient);
    });
  });

  describe("合约管理", function () {
    it("应该允许所有者批准新的NFT合约", async function () {
      const newNFT = await ethers.getContractFactory("DLEngineNFT");
      const newNFTContract = await newNFT.deploy("New NFT", "NEW");
      await newNFTContract.deployed();

      await marketplace.approveNFTContract(newNFTContract.address);
      expect(await marketplace.approvedNFTContracts(newNFTContract.address)).to.be.true;
    });

    it("应该允许所有者批准新的支付代币", async function () {
      const newToken = await ethers.getContractFactory("TestERC20");
      const newTokenContract = await newToken.deploy("New Token", "NEW", ethers.utils.parseEther("1000000"));
      await newTokenContract.deployed();

      await marketplace.approvePaymentToken(newTokenContract.address);
      expect(await marketplace.approvedPaymentTokens(newTokenContract.address)).to.be.true;
    });
  });

  describe("提取功能", function () {
    it("应该允许用户提取待提取资金", async function () {
      // 模拟一个失败的转账，导致资金进入待提取状态
      // 这需要特殊的测试设置，这里简化处理
      const amount = ethers.utils.parseEther("1");
      
      // 直接设置待提取金额（在实际合约中这会通过失败的转账触发）
      await marketplace.connect(owner).setMarketplaceFee(0); // 临时设置为0以简化测试
      
      // 这里需要更复杂的测试设置来真正测试提取功能
      // 暂时跳过这个测试
    });
  });

  describe("统计功能", function () {
    it("应该正确统计活跃listing数量", async function () {
      const price = ethers.utils.parseEther("1");
      const duration = 7 * 24 * 60 * 60;

      // 创建多个listing
      await marketplace.connect(seller).listItem(
        nftContract.address,
        0,
        price,
        ethers.constants.AddressZero,
        duration
      );

      const activeCount = await marketplace.getActiveListingCount();
      expect(activeCount).to.equal(1);
    });

    it("应该正确统计活跃拍卖数量", async function () {
      const startingPrice = ethers.utils.parseEther("0.5");
      const reservePrice = ethers.utils.parseEther("1");
      const duration = 24 * 60 * 60;

      await marketplace.connect(seller).createAuction(
        nftContract.address,
        0,
        startingPrice,
        reservePrice,
        ethers.constants.AddressZero,
        duration
      );

      const activeCount = await marketplace.getActiveAuctionCount();
      expect(activeCount).to.equal(1);
    });
  });
});
