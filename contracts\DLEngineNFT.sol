// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721Burnable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Counters.sol";
import "@openzeppelin/contracts/interfaces/IERC2981.sol";

/**
 * @title DLEngineNFT
 * @dev DL引擎NFT合约，支持教育内容的NFT化
 * 支持版税、批量铸造、教育元数据等功能
 */
contract DLEngineNFT is 
    ERC721, 
    ERC721URIStorage, 
    ERC721Burnable, 
    Ownable, 
    Pausable, 
    ReentrancyGuard,
    IERC2981 
{
    using Counters for Counters.Counter;

    // 状态变量
    Counters.Counter private _tokenIdCounter;
    
    // 版税信息
    struct RoyaltyInfo {
        address recipient;
        uint96 percentage; // 基点 (1% = 100)
    }
    
    // NFT元数据扩展
    struct DLEngineMetadata {
        string assetType;      // 资产类型
        string licenseType;    // 许可证类型
        string subject;        // 学科
        string gradeLevel;     // 年级水平
        uint256 difficulty;    // 难度等级 (1-10)
        bool isEducational;    // 是否为教育内容
        address creator;       // 创建者地址
        uint256 createdAt;     // 创建时间
    }
    
    // 映射
    mapping(uint256 => RoyaltyInfo) private _royalties;
    mapping(uint256 => DLEngineMetadata) private _dlMetadata;
    mapping(address => bool) public authorizedMinters;
    mapping(string => bool) private _usedTokenURIs;
    
    // 常量
    uint96 public constant MAX_ROYALTY_PERCENTAGE = 1000; // 10%
    uint256 public constant MAX_BATCH_SIZE = 50;
    
    // 事件
    event NFTMinted(
        uint256 indexed tokenId,
        address indexed to,
        address indexed creator,
        string tokenURI,
        string assetType
    );
    
    event RoyaltySet(
        uint256 indexed tokenId,
        address indexed recipient,
        uint96 percentage
    );
    
    event BatchMinted(
        address indexed to,
        uint256[] tokenIds,
        uint256 count
    );
    
    event MinterAuthorized(address indexed minter);
    event MinterRevoked(address indexed minter);

    constructor(
        string memory name,
        string memory symbol
    ) ERC721(name, symbol) {
        // 授权合约部署者为铸造者
        authorizedMinters[msg.sender] = true;
        emit MinterAuthorized(msg.sender);
    }

    /**
     * @dev 铸造NFT
     * @param to 接收者地址
     * @param tokenURI 元数据URI
     * @param royaltyRecipient 版税接收者
     * @param royaltyPercentage 版税百分比 (基点)
     * @param dlMetadata DL引擎元数据
     */
    function mintNFT(
        address to,
        string memory tokenURI,
        address royaltyRecipient,
        uint96 royaltyPercentage,
        DLEngineMetadata memory dlMetadata
    ) public nonReentrant whenNotPaused returns (uint256) {
        require(authorizedMinters[msg.sender], "Not authorized to mint");
        require(to != address(0), "Cannot mint to zero address");
        require(bytes(tokenURI).length > 0, "Token URI cannot be empty");
        require(!_usedTokenURIs[tokenURI], "Token URI already used");
        require(royaltyPercentage <= MAX_ROYALTY_PERCENTAGE, "Royalty too high");

        uint256 tokenId = _tokenIdCounter.current();
        _tokenIdCounter.increment();

        // 铸造NFT
        _safeMint(to, tokenId);
        _setTokenURI(tokenId, tokenURI);
        
        // 标记URI为已使用
        _usedTokenURIs[tokenURI] = true;

        // 设置版税
        if (royaltyRecipient != address(0) && royaltyPercentage > 0) {
            _setRoyalty(tokenId, royaltyRecipient, royaltyPercentage);
        }

        // 设置DL引擎元数据
        dlMetadata.creator = msg.sender;
        dlMetadata.createdAt = block.timestamp;
        _dlMetadata[tokenId] = dlMetadata;

        emit NFTMinted(tokenId, to, msg.sender, tokenURI, dlMetadata.assetType);
        
        return tokenId;
    }

    /**
     * @dev 批量铸造NFT
     * @param to 接收者地址
     * @param tokenURIs 元数据URI数组
     * @param royaltyRecipient 版税接收者
     * @param royaltyPercentage 版税百分比
     * @param dlMetadataArray DL引擎元数据数组
     */
    function batchMintNFT(
        address to,
        string[] memory tokenURIs,
        address royaltyRecipient,
        uint96 royaltyPercentage,
        DLEngineMetadata[] memory dlMetadataArray
    ) public nonReentrant whenNotPaused returns (uint256[] memory) {
        require(authorizedMinters[msg.sender], "Not authorized to mint");
        require(to != address(0), "Cannot mint to zero address");
        require(tokenURIs.length > 0, "No token URIs provided");
        require(tokenURIs.length <= MAX_BATCH_SIZE, "Batch size too large");
        require(tokenURIs.length == dlMetadataArray.length, "Array length mismatch");
        require(royaltyPercentage <= MAX_ROYALTY_PERCENTAGE, "Royalty too high");

        uint256[] memory tokenIds = new uint256[](tokenURIs.length);

        for (uint256 i = 0; i < tokenURIs.length; i++) {
            require(bytes(tokenURIs[i]).length > 0, "Token URI cannot be empty");
            require(!_usedTokenURIs[tokenURIs[i]], "Token URI already used");

            uint256 tokenId = _tokenIdCounter.current();
            _tokenIdCounter.increment();

            // 铸造NFT
            _safeMint(to, tokenId);
            _setTokenURI(tokenId, tokenURIs[i]);
            
            // 标记URI为已使用
            _usedTokenURIs[tokenURIs[i]] = true;

            // 设置版税
            if (royaltyRecipient != address(0) && royaltyPercentage > 0) {
                _setRoyalty(tokenId, royaltyRecipient, royaltyPercentage);
            }

            // 设置DL引擎元数据
            dlMetadataArray[i].creator = msg.sender;
            dlMetadataArray[i].createdAt = block.timestamp;
            _dlMetadata[tokenId] = dlMetadataArray[i];

            tokenIds[i] = tokenId;
            
            emit NFTMinted(tokenId, to, msg.sender, tokenURIs[i], dlMetadataArray[i].assetType);
        }

        emit BatchMinted(to, tokenIds, tokenIds.length);
        return tokenIds;
    }

    /**
     * @dev 设置版税信息
     */
    function _setRoyalty(
        uint256 tokenId,
        address recipient,
        uint96 percentage
    ) internal {
        require(recipient != address(0), "Invalid royalty recipient");
        require(percentage <= MAX_ROYALTY_PERCENTAGE, "Royalty too high");

        _royalties[tokenId] = RoyaltyInfo(recipient, percentage);
        emit RoyaltySet(tokenId, recipient, percentage);
    }

    /**
     * @dev 获取版税信息 (EIP-2981)
     */
    function royaltyInfo(
        uint256 tokenId,
        uint256 salePrice
    ) public view override returns (address, uint256) {
        RoyaltyInfo memory royalty = _royalties[tokenId];
        uint256 royaltyAmount = (salePrice * royalty.percentage) / 10000;
        return (royalty.recipient, royaltyAmount);
    }

    /**
     * @dev 获取DL引擎元数据
     */
    function getDLMetadata(uint256 tokenId) public view returns (DLEngineMetadata memory) {
        require(_exists(tokenId), "Token does not exist");
        return _dlMetadata[tokenId];
    }

    /**
     * @dev 更新DL引擎元数据 (仅创建者)
     */
    function updateDLMetadata(
        uint256 tokenId,
        DLEngineMetadata memory newMetadata
    ) public {
        require(_exists(tokenId), "Token does not exist");
        require(_dlMetadata[tokenId].creator == msg.sender, "Not the creator");
        
        // 保持不可变字段
        newMetadata.creator = _dlMetadata[tokenId].creator;
        newMetadata.createdAt = _dlMetadata[tokenId].createdAt;
        
        _dlMetadata[tokenId] = newMetadata;
    }

    /**
     * @dev 授权铸造者
     */
    function authorizeMinter(address minter) public onlyOwner {
        require(minter != address(0), "Invalid minter address");
        authorizedMinters[minter] = true;
        emit MinterAuthorized(minter);
    }

    /**
     * @dev 撤销铸造者授权
     */
    function revokeMinter(address minter) public onlyOwner {
        authorizedMinters[minter] = false;
        emit MinterRevoked(minter);
    }

    /**
     * @dev 暂停合约
     */
    function pause() public onlyOwner {
        _pause();
    }

    /**
     * @dev 恢复合约
     */
    function unpause() public onlyOwner {
        _unpause();
    }

    /**
     * @dev 获取下一个Token ID
     */
    function nextTokenId() public view returns (uint256) {
        return _tokenIdCounter.current();
    }

    /**
     * @dev 获取总供应量
     */
    function totalSupply() public view returns (uint256) {
        return _tokenIdCounter.current();
    }

    /**
     * @dev 检查Token URI是否已使用
     */
    function isTokenURIUsed(string memory tokenURI) public view returns (bool) {
        return _usedTokenURIs[tokenURI];
    }

    /**
     * @dev 按资产类型获取Token数量
     */
    function getTokenCountByAssetType(string memory assetType) public view returns (uint256) {
        uint256 count = 0;
        uint256 totalTokens = totalSupply();
        
        for (uint256 i = 0; i < totalTokens; i++) {
            if (_exists(i) && 
                keccak256(bytes(_dlMetadata[i].assetType)) == keccak256(bytes(assetType))) {
                count++;
            }
        }
        
        return count;
    }

    /**
     * @dev 按创建者获取Token数量
     */
    function getTokenCountByCreator(address creator) public view returns (uint256) {
        uint256 count = 0;
        uint256 totalTokens = totalSupply();
        
        for (uint256 i = 0; i < totalTokens; i++) {
            if (_exists(i) && _dlMetadata[i].creator == creator) {
                count++;
            }
        }
        
        return count;
    }

    // 重写必要的函数
    function _burn(uint256 tokenId) internal override(ERC721, ERC721URIStorage) {
        super._burn(tokenId);
        
        // 清理版税信息
        delete _royalties[tokenId];
        
        // 清理DL引擎元数据
        delete _dlMetadata[tokenId];
        
        // 清理URI使用标记
        string memory tokenURI = tokenURI(tokenId);
        if (bytes(tokenURI).length > 0) {
            _usedTokenURIs[tokenURI] = false;
        }
    }

    function tokenURI(uint256 tokenId) public view override(ERC721, ERC721URIStorage) returns (string memory) {
        return super.tokenURI(tokenId);
    }

    function supportsInterface(bytes4 interfaceId) public view override(ERC721, IERC165) returns (bool) {
        return interfaceId == type(IERC2981).interfaceId || super.supportsInterface(interfaceId);
    }

    function _beforeTokenTransfer(
        address from,
        address to,
        uint256 tokenId,
        uint256 batchSize
    ) internal override whenNotPaused {
        super._beforeTokenTransfer(from, to, tokenId, batchSize);
    }
}
