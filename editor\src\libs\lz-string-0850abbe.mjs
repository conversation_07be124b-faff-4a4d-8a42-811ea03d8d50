import { g as k } from "./_commonjsHelpers-10dfc225.mjs";
function T(S, m) {
  for (var w = 0; w < m.length; w++) {
    const _ = m[w];
    if (typeof _ != "string" && !Array.isArray(_)) {
      for (const A in _)
        if (A !== "default" && !(A in S)) {
          const g = Object.getOwnPropertyDescriptor(_, A);
          g && Object.defineProperty(S, A, g.get ? g : {
            enumerable: !0,
            get: () => _[A]
          });
        }
    }
  }
  return Object.freeze(Object.defineProperty(S, Symbol.toStringTag, { value: "Module" }));
}
var M = { exports: {} };
M.exports;
(function(S) {
  var m = function() {
    var w = String.fromCharCode, _ = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=", A = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$", g = {};
    function O(e, r) {
      if (!g[e]) {
        g[e] = {};
        for (var s = 0; s < e.length; s++)
          g[e][e.charAt(s)] = s;
      }
      return g[e][r];
    }
    var d = {
      compressToBase64: function(e) {
        if (e == null)
          return "";
        var r = d._compress(e, 6, function(s) {
          return _.charAt(s);
        });
        switch (r.length % 4) {
          default:
          case 0:
            return r;
          case 1:
            return r + "===";
          case 2:
            return r + "==";
          case 3:
            return r + "=";
        }
      },
      decompressFromBase64: function(e) {
        return e == null ? "" : e == "" ? null : d._decompress(e.length, 32, function(r) {
          return O(_, e.charAt(r));
        });
      },
      compressToUTF16: function(e) {
        return e == null ? "" : d._compress(e, 15, function(r) {
          return w(r + 32);
        }) + " ";
      },
      decompressFromUTF16: function(e) {
        return e == null ? "" : e == "" ? null : d._decompress(e.length, 16384, function(r) {
          return e.charCodeAt(r) - 32;
        });
      },
      //compress into uint8array (UCS-2 big endian format)
      compressToUint8Array: function(e) {
        for (var r = d.compress(e), s = new Uint8Array(r.length * 2), o = 0, i = r.length; o < i; o++) {
          var p = r.charCodeAt(o);
          s[o * 2] = p >>> 8, s[o * 2 + 1] = p % 256;
        }
        return s;
      },
      //decompress from uint8array (UCS-2 big endian format)
      decompressFromUint8Array: function(e) {
        if (e == null)
          return d.decompress(e);
        for (var r = new Array(e.length / 2), s = 0, o = r.length; s < o; s++)
          r[s] = e[s * 2] * 256 + e[s * 2 + 1];
        var i = [];
        return r.forEach(function(p) {
          i.push(w(p));
        }), d.decompress(i.join(""));
      },
      //compress into a string that is already URI encoded
      compressToEncodedURIComponent: function(e) {
        return e == null ? "" : d._compress(e, 6, function(r) {
          return A.charAt(r);
        });
      },
      //decompress from an output of compressToEncodedURIComponent
      decompressFromEncodedURIComponent: function(e) {
        return e == null ? "" : e == "" ? null : (e = e.replace(/ /g, "+"), d._decompress(e.length, 32, function(r) {
          return O(A, e.charAt(r));
        }));
      },
      compress: function(e) {
        return d._compress(e, 16, function(r) {
          return w(r);
        });
      },
      _compress: function(e, r, s) {
        if (e == null)
          return "";
        var o, i, p = {}, y = {}, v = "", x = "", u = "", h = 2, a = 3, c = 2, l = [], n = 0, f = 0, t;
        for (t = 0; t < e.length; t += 1)
          if (v = e.charAt(t), Object.prototype.hasOwnProperty.call(p, v) || (p[v] = a++, y[v] = !0), x = u + v, Object.prototype.hasOwnProperty.call(p, x))
            u = x;
          else {
            if (Object.prototype.hasOwnProperty.call(y, u)) {
              if (u.charCodeAt(0) < 256) {
                for (o = 0; o < c; o++)
                  n = n << 1, f == r - 1 ? (f = 0, l.push(s(n)), n = 0) : f++;
                for (i = u.charCodeAt(0), o = 0; o < 8; o++)
                  n = n << 1 | i & 1, f == r - 1 ? (f = 0, l.push(s(n)), n = 0) : f++, i = i >> 1;
              } else {
                for (i = 1, o = 0; o < c; o++)
                  n = n << 1 | i, f == r - 1 ? (f = 0, l.push(s(n)), n = 0) : f++, i = 0;
                for (i = u.charCodeAt(0), o = 0; o < 16; o++)
                  n = n << 1 | i & 1, f == r - 1 ? (f = 0, l.push(s(n)), n = 0) : f++, i = i >> 1;
              }
              h--, h == 0 && (h = Math.pow(2, c), c++), delete y[u];
            } else
              for (i = p[u], o = 0; o < c; o++)
                n = n << 1 | i & 1, f == r - 1 ? (f = 0, l.push(s(n)), n = 0) : f++, i = i >> 1;
            h--, h == 0 && (h = Math.pow(2, c), c++), p[x] = a++, u = String(v);
          }
        if (u !== "") {
          if (Object.prototype.hasOwnProperty.call(y, u)) {
            if (u.charCodeAt(0) < 256) {
              for (o = 0; o < c; o++)
                n = n << 1, f == r - 1 ? (f = 0, l.push(s(n)), n = 0) : f++;
              for (i = u.charCodeAt(0), o = 0; o < 8; o++)
                n = n << 1 | i & 1, f == r - 1 ? (f = 0, l.push(s(n)), n = 0) : f++, i = i >> 1;
            } else {
              for (i = 1, o = 0; o < c; o++)
                n = n << 1 | i, f == r - 1 ? (f = 0, l.push(s(n)), n = 0) : f++, i = 0;
              for (i = u.charCodeAt(0), o = 0; o < 16; o++)
                n = n << 1 | i & 1, f == r - 1 ? (f = 0, l.push(s(n)), n = 0) : f++, i = i >> 1;
            }
            h--, h == 0 && (h = Math.pow(2, c), c++), delete y[u];
          } else
            for (i = p[u], o = 0; o < c; o++)
              n = n << 1 | i & 1, f == r - 1 ? (f = 0, l.push(s(n)), n = 0) : f++, i = i >> 1;
          h--, h == 0 && (h = Math.pow(2, c), c++);
        }
        for (i = 2, o = 0; o < c; o++)
          n = n << 1 | i & 1, f == r - 1 ? (f = 0, l.push(s(n)), n = 0) : f++, i = i >> 1;
        for (; ; )
          if (n = n << 1, f == r - 1) {
            l.push(s(n));
            break;
          } else
            f++;
        return l.join("");
      },
      decompress: function(e) {
        return e == null ? "" : e == "" ? null : d._decompress(e.length, 32768, function(r) {
          return e.charCodeAt(r);
        });
      },
      _decompress: function(e, r, s) {
        var o = [], i = 4, p = 4, y = 3, v = "", x = [], u, h, a, c, l, n, f, t = { val: s(0), position: r, index: 1 };
        for (u = 0; u < 3; u += 1)
          o[u] = u;
        for (a = 0, l = Math.pow(2, 2), n = 1; n != l; )
          c = t.val & t.position, t.position >>= 1, t.position == 0 && (t.position = r, t.val = s(t.index++)), a |= (c > 0 ? 1 : 0) * n, n <<= 1;
        switch (a) {
          case 0:
            for (a = 0, l = Math.pow(2, 8), n = 1; n != l; )
              c = t.val & t.position, t.position >>= 1, t.position == 0 && (t.position = r, t.val = s(t.index++)), a |= (c > 0 ? 1 : 0) * n, n <<= 1;
            f = w(a);
            break;
          case 1:
            for (a = 0, l = Math.pow(2, 16), n = 1; n != l; )
              c = t.val & t.position, t.position >>= 1, t.position == 0 && (t.position = r, t.val = s(t.index++)), a |= (c > 0 ? 1 : 0) * n, n <<= 1;
            f = w(a);
            break;
          case 2:
            return "";
        }
        for (o[3] = f, h = f, x.push(f); ; ) {
          if (t.index > e)
            return "";
          for (a = 0, l = Math.pow(2, y), n = 1; n != l; )
            c = t.val & t.position, t.position >>= 1, t.position == 0 && (t.position = r, t.val = s(t.index++)), a |= (c > 0 ? 1 : 0) * n, n <<= 1;
          switch (f = a) {
            case 0:
              for (a = 0, l = Math.pow(2, 8), n = 1; n != l; )
                c = t.val & t.position, t.position >>= 1, t.position == 0 && (t.position = r, t.val = s(t.index++)), a |= (c > 0 ? 1 : 0) * n, n <<= 1;
              o[p++] = w(a), f = p - 1, i--;
              break;
            case 1:
              for (a = 0, l = Math.pow(2, 16), n = 1; n != l; )
                c = t.val & t.position, t.position >>= 1, t.position == 0 && (t.position = r, t.val = s(t.index++)), a |= (c > 0 ? 1 : 0) * n, n <<= 1;
              o[p++] = w(a), f = p - 1, i--;
              break;
            case 2:
              return x.join("");
          }
          if (i == 0 && (i = Math.pow(2, y), y++), o[f])
            v = o[f];
          else if (f === p)
            v = h + h.charAt(0);
          else
            return null;
          x.push(v), o[p++] = h + v.charAt(0), i--, h = v, i == 0 && (i = Math.pow(2, y), y++);
        }
      }
    };
    return d;
  }();
  S != null ? S.exports = m : typeof angular < "u" && angular != null && angular.module("LZString", []).factory("LZString", function() {
    return m;
  });
})(M);
var j = M.exports;
const U = /* @__PURE__ */ k(j), B = /* @__PURE__ */ T({
  __proto__: null,
  default: U
}, [j]);
export {
  B as l
};
