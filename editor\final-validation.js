#!/usr/bin/env node

/**
 * 最终验证脚本
 * 检查所有修复后的文件是否还有导入错误
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 递归查找所有 TypeScript 文件
function findTSFiles(dir) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build', 'coverage', '__tests__', '__mocks__'].includes(file)) {
        results = results.concat(findTSFiles(filePath));
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      results.push(filePath);
    }
  });
  
  return results;
}

// 检查文件中的潜在导入问题
function checkImportIssues(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      const trimmed = line.trim();
      const lineNum = index + 1;
      
      // 检查可能的问题导入
      if (trimmed.startsWith('import ') && trimmed.includes('from ')) {
        // 检查是否从 ../libs 导入但不是 dl-engine-types
        if (trimmed.includes("from '../libs'") && !trimmed.includes('dl-engine-types')) {
          // 检查是否是允许的导入（如 System, Entity）
          if (!trimmed.includes('System') && !trimmed.includes('Entity')) {
            issues.push({
              line: lineNum,
              type: 'POTENTIAL_IMPORT_ERROR',
              message: `可能的导入错误: ${trimmed}`,
              suggestion: '考虑从 dl-engine-types 导入类型'
            });
          }
        }
        
        // 检查是否从 ../../libs/dl-engine 导入但不是 dl-engine-types
        if (trimmed.includes("from '../../libs/dl-engine'") && !trimmed.includes('dl-engine-types')) {
          issues.push({
            line: lineNum,
            type: 'POTENTIAL_IMPORT_ERROR',
            message: `可能的导入错误: ${trimmed}`,
            suggestion: '考虑从 ../../libs/dl-engine-types 导入类型'
          });
        }
        
        // 检查 .mjs 导入（除了 EngineService.ts）
        if (trimmed.includes('.mjs') && !filePath.includes('EngineService.ts')) {
          issues.push({
            line: lineNum,
            type: 'MJS_IMPORT',
            message: `使用了 .mjs 导入: ${trimmed}`,
            suggestion: '移除 .mjs 扩展名'
          });
        }
        
        // 检查重复的导入
        const importMatch = trimmed.match(/import\s+(.+?)\s+from\s+['"](.+?)['"]/);
        if (importMatch) {
          const [, imports, module] = importMatch;
          // 这里可以添加重复导入检查逻辑
        }
      }
      
      // 检查未使用的变量（简单检查）
      if (trimmed.includes('is declared but its value is never read')) {
        issues.push({
          line: lineNum,
          type: 'UNUSED_VARIABLE',
          message: `未使用的变量: ${trimmed}`,
          suggestion: '添加下划线前缀或移除变量'
        });
      }
    });
    
    return issues;
  } catch (error) {
    return [{
      line: 0,
      type: 'FILE_ERROR',
      message: `文件读取错误: ${error.message}`,
      suggestion: '检查文件是否存在且可读'
    }];
  }
}

// 主函数
function main() {
  console.log('🔍 开始最终验证...\n');
  
  const srcDir = path.join(__dirname, 'src');
  if (!fs.existsSync(srcDir)) {
    console.error('❌ src 目录不存在');
    process.exit(1);
  }
  
  const files = findTSFiles(srcDir);
  console.log(`📁 找到 ${files.length} 个 TypeScript 文件\n`);
  
  let totalIssues = 0;
  let filesWithIssues = 0;
  const issuesByType = {};
  
  files.forEach(file => {
    const issues = checkImportIssues(file);
    if (issues.length > 0) {
      filesWithIssues++;
      totalIssues += issues.length;
      
      console.log(`📄 ${path.relative(__dirname, file)} (${issues.length} 个问题):`);
      issues.forEach(issue => {
        console.log(`  ⚠️  第${issue.line}行 [${issue.type}]: ${issue.message}`);
        if (issue.suggestion) {
          console.log(`     💡 建议: ${issue.suggestion}`);
        }
        
        // 统计问题类型
        issuesByType[issue.type] = (issuesByType[issue.type] || 0) + 1;
      });
      console.log('');
    }
  });
  
  // 输出总结
  console.log('📊 验证总结:');
  console.log(`   总文件数: ${files.length}`);
  console.log(`   有问题的文件: ${filesWithIssues}`);
  console.log(`   总问题数: ${totalIssues}`);
  
  if (Object.keys(issuesByType).length > 0) {
    console.log('\n📈 问题类型统计:');
    Object.entries(issuesByType).forEach(([type, count]) => {
      console.log(`   ${type}: ${count} 个`);
    });
  }
  
  if (totalIssues === 0) {
    console.log('\n✅ 恭喜！没有发现导入问题！');
  } else {
    console.log(`\n⚠️  发现 ${totalIssues} 个潜在问题，建议进一步检查。`);
  }
  
  console.log('\n🎯 修复建议:');
  console.log('   1. 类型导入使用 dl-engine-types');
  console.log('   2. 运行时导入使用 dl-engine.mjs (仅限 EngineService)');
  console.log('   3. 未使用变量添加下划线前缀');
  console.log('   4. 移除重复的导入语句');
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { findTSFiles, checkImportIssues };
