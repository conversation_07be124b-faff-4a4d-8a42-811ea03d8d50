/**
 * 时间轴编辑器组件
 * 提供面部动画时间轴编辑功能
 */
import React, { useState, useRef, useEffect } from 'react';
import { Button, Tooltip, Slider, Space, Dropdown } from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  DownOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import './TimelineEditor.less';

/**
 * 轨道类型
 */
export enum TrackType {
  EXPRESSION = 'expression',
  VISEME = 'viseme',
  COMBINED = 'combined'
}

/**
 * 关键帧数据
 */
export interface Keyframe {
  id: string;
  time: number;
  value: any;
  type: string;
  easing?: string;
}

/**
 * 轨道数据
 */
export interface Track {
  id: string;
  name: string;
  type: TrackType;
  keyframes: Keyframe[];
  color?: string;
  locked?: boolean;
  visible?: boolean;
}

/**
 * 时间轴编辑器属性
 */
interface TimelineEditorProps {
  /** 实体ID */
  entityId?: string;
  /** 当前时间 */
  currentTime: number;
  /** 动画持续时间 */
  duration: number;
  /** 帧率 */
  frameRate: number;
  /** 时间变化回调 */
  onTimeChange: (time: number) => void;
}

/**
 * 时间轴编辑器组件
 */
const TimelineEditor: React.FC<TimelineEditorProps> = ({
  entityId,
  currentTime,
  duration,
  frameRate,
  onTimeChange
}) => {
  const { t } = useTranslation();
  
  // 引用
  const timelineRef = useRef<HTMLDivElement>(null);
  const tracksContainerRef = useRef<HTMLDivElement>(null);
  
  // 状态
  const [tracks, setTracks] = useState<Track[]>([]);
  const [selectedTrackId, setSelectedTrackId] = useState<string | null>(null);
  const [selectedKeyframeId, setSelectedKeyframeId] = useState<string | null>(null);
  const [zoom, setZoom] = useState<number>(1);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [dragStartX, setDragStartX] = useState<number>(0);
  const [dragStartTime, setDragStartTime] = useState<number>(0);
  
  // 计算像素/秒比例
  const pixelsPerSecond = 200 * zoom;
  
  // 加载轨道数据
  useEffect(() => {
    if (entityId) {
      // 这里应该从引擎中加载实体的轨道数据
      // 示例数据，实际实现需要与引擎集成
      const mockTracks: Track[] = [
        {
          id: '1',
          name: t('editor.animation.expressionTrack'),
          type: TrackType.EXPRESSION,
          keyframes: [
            { id: '1-1', time: 0, value: 'neutral', type: 'expression' },
            { id: '1-2', time: 1, value: 'happy', type: 'expression' },
            { id: '1-3', time: 2, value: 'surprised', type: 'expression' },
            { id: '1-4', time: 3, value: 'happy', type: 'expression' },
            { id: '1-5', time: 4, value: 'neutral', type: 'expression' }
          ],
          color: '#1890ff'
        },
        {
          id: '2',
          name: t('editor.animation.visemeTrack'),
          type: TrackType.VISEME,
          keyframes: [
            { id: '2-1', time: 0, value: 'silent', type: 'viseme' },
            { id: '2-2', time: 0.5, value: 'aa', type: 'viseme' },
            { id: '2-3', time: 1.5, value: 'oh', type: 'viseme' },
            { id: '2-4', time: 2.5, value: 'ee', type: 'viseme' },
            { id: '2-5', time: 3.5, value: 'silent', type: 'viseme' }
          ],
          color: '#52c41a'
        }
      ];
      
      setTracks(mockTracks);
      
      if (mockTracks.length > 0) {
        setSelectedTrackId(mockTracks[0].id);
      }
    }
  }, [entityId, t]);
  
  // 添加轨道
  const handleAddTrack = (type: TrackType) => {
    const newTrack: Track = {
      id: `track-${Date.now()}`,
      name: type === TrackType.EXPRESSION 
        ? `${t('editor.animation.expressionTrack')} ${tracks.length + 1}`
        : type === TrackType.VISEME
          ? `${t('editor.animation.visemeTrack')} ${tracks.length + 1}`
          : `${t('editor.animation.combinedTrack')} ${tracks.length + 1}`,
      type,
      keyframes: [],
      color: type === TrackType.EXPRESSION 
        ? '#1890ff' 
        : type === TrackType.VISEME 
          ? '#52c41a' 
          : '#722ed1'
    };
    
    setTracks([...tracks, newTrack]);
    setSelectedTrackId(newTrack.id);
  };
  
  // 删除轨道
  const handleDeleteTrack = (trackId: string) => {
    const newTracks = tracks.filter(track => track.id !== trackId);
    setTracks(newTracks);
    
    if (selectedTrackId === trackId) {
      setSelectedTrackId(newTracks.length > 0 ? newTracks[0].id : null);
    }
  };
  
  // 添加关键帧
  const handleAddKeyframe = (trackId: string, time: number) => {
    const track = tracks.find(t => t.id === trackId);
    if (!track) return;
    
    const newKeyframe: Keyframe = {
      id: `${trackId}-${Date.now()}`,
      time,
      value: track.type === TrackType.EXPRESSION 
        ? 'neutral' 
        : track.type === TrackType.VISEME 
          ? 'silent' 
          : { expression: 'neutral', viseme: 'silent' },
      type: track.type === TrackType.EXPRESSION 
        ? 'expression' 
        : track.type === TrackType.VISEME 
          ? 'viseme' 
          : 'combined'
    };
    
    const newTracks = tracks.map(t => {
      if (t.id === trackId) {
        return {
          ...t,
          keyframes: [...t.keyframes, newKeyframe].sort((a, b) => a.time - b.time)
        };
      }
      return t;
    });
    
    setTracks(newTracks);
    setSelectedKeyframeId(newKeyframe.id);
  };
  

  
  // 开始拖动关键帧
  const handleKeyframeDragStart = (e: React.MouseEvent, trackId: string, keyframeId: string) => {
    e.stopPropagation();
    setIsDragging(true);
    setDragStartX(e.clientX);
    
    const track = tracks.find(t => t.id === trackId);
    const keyframe = track?.keyframes.find(kf => kf.id === keyframeId);
    
    if (keyframe) {
      setDragStartTime(keyframe.time);
    }
    
    setSelectedTrackId(trackId);
    setSelectedKeyframeId(keyframeId);
    
    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };
  
  // 鼠标移动处理
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || !selectedTrackId || !selectedKeyframeId) return;
    
    const deltaX = e.clientX - dragStartX;
    const deltaTime = deltaX / pixelsPerSecond;
    const newTime = Math.max(0, Math.min(duration, dragStartTime + deltaTime));
    
    // 更新关键帧时间
    const newTracks = tracks.map(track => {
      if (track.id === selectedTrackId) {
        return {
          ...track,
          keyframes: track.keyframes.map(kf => {
            if (kf.id === selectedKeyframeId) {
              return {
                ...kf,
                time: newTime
              };
            }
            return kf;
          }).sort((a, b) => a.time - b.time)
        };
      }
      return track;
    });
    
    setTracks(newTracks);
  };
  
  // 鼠标释放处理
  const handleMouseUp = () => {
    setIsDragging(false);
    
    // 移除全局鼠标事件监听
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };
  
  // 时间轴点击处理
  const handleTimelineClick = (e: React.MouseEvent) => {
    if (!timelineRef.current) return;
    
    const rect = timelineRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const clickTime = x / pixelsPerSecond;
    
    if (clickTime >= 0 && clickTime <= duration) {
      onTimeChange(clickTime);
    }
  };
  
  // 渲染时间刻度
  const renderTimeRuler = () => {
    const secondMarks = [];
    const frameMarks = [];
    const secondsCount = Math.ceil(duration);
    const framesPerSecond = frameRate;
    
    // 添加秒刻度
    for (let i = 0; i <= secondsCount; i++) {
      secondMarks.push(
        <div 
          key={`second-${i}`} 
          className="time-mark second-mark"
          style={{ left: `${i * pixelsPerSecond}px` }}
        >
          <div className="mark-line" />
          <div className="mark-label">{i}s</div>
        </div>
      );
      
      // 添加帧刻度（如果缩放足够大）
      if (zoom > 0.5) {
        for (let j = 1; j < framesPerSecond; j++) {
          const frameTime = i + j / framesPerSecond;
          if (frameTime <= duration) {
            frameMarks.push(
              <div 
                key={`frame-${i}-${j}`} 
                className="time-mark frame-mark"
                style={{ left: `${frameTime * pixelsPerSecond}px` }}
              >
                <div className="mark-line" />
              </div>
            );
          }
        }
      }
    }
    
    return (
      <div className="time-ruler">
        {secondMarks}
        {frameMarks}
      </div>
    );
  };
  
  return (
    <div className="timeline-editor">
      <div className="timeline-toolbar">
        <Space>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'expression',
                  label: t('editor.animation.expressionTrack'),
                  onClick: () => handleAddTrack(TrackType.EXPRESSION)
                },
                {
                  key: 'viseme',
                  label: t('editor.animation.visemeTrack'),
                  onClick: () => handleAddTrack(TrackType.VISEME)
                },
                {
                  key: 'combined',
                  label: t('editor.animation.combinedTrack'),
                  onClick: () => handleAddTrack(TrackType.COMBINED)
                }
              ]
            }}
          >
            <Button type="primary" icon={<PlusOutlined />}>
              {t('editor.animation.addTrack')} <DownOutlined />
            </Button>
          </Dropdown>
          
          <Slider
            min={0.1}
            max={2}
            step={0.1}
            value={zoom}
            onChange={setZoom}
            style={{ width: 100 }}
          />
        </Space>
      </div>
      
      <div className="timeline-container">
        <div className="tracks-header">
          {tracks.map(track => (
            <div 
              key={track.id}
              className={`track-header ${selectedTrackId === track.id ? 'selected' : ''}`}
              onClick={() => setSelectedTrackId(track.id)}
            >
              <div className="track-color" style={{ backgroundColor: track.color }} />
              <div className="track-name">{track.name}</div>
              <div className="track-actions">
                <Tooltip title={t('editor.animation.addKeyframe')}>
                  <Button
                    type="text"
                    size="small"
                    icon={<PlusOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddKeyframe(track.id, currentTime);
                    }}
                  />
                </Tooltip>
                <Tooltip title={t('editor.animation.deleteTrack')}>
                  <Button
                    type="text"
                    size="small"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteTrack(track.id);
                    }}
                  />
                </Tooltip>
              </div>
            </div>
          ))}
        </div>
        
        <div className="timeline-view" ref={timelineRef} onClick={handleTimelineClick}>
          {renderTimeRuler()}
          
          <div className="current-time-indicator" style={{ left: `${currentTime * pixelsPerSecond}px` }} />
          
          <div className="tracks-container" ref={tracksContainerRef}>
            {tracks.map(track => (
              <div 
                key={track.id}
                className={`timeline-track ${selectedTrackId === track.id ? 'selected' : ''}`}
              >
                {track.keyframes.map(keyframe => (
                  <Tooltip 
                    key={keyframe.id}
                    title={`${keyframe.time.toFixed(2)}s: ${typeof keyframe.value === 'object' ? JSON.stringify(keyframe.value) : keyframe.value}`}
                  >
                    <div
                      className={`keyframe ${selectedKeyframeId === keyframe.id ? 'selected' : ''}`}
                      style={{ left: `${keyframe.time * pixelsPerSecond}px` }}
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedKeyframeId(keyframe.id);
                        onTimeChange(keyframe.time);
                      }}
                      onMouseDown={(e) => handleKeyframeDragStart(e, track.id, keyframe.id)}
                    />
                  </Tooltip>
                ))}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TimelineEditor;
