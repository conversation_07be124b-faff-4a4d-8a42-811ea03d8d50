/**
 * 物理交互编辑器组件
 * 用于编辑物理交互参数和预设
 */
import React, { useState, useEffect } from 'react';
import { Form, Input, InputNumber, Switch, Select, Button, Tabs, Card, Collapse, Space, Divider, message } from 'antd';
import { DeleteOutlined, SaveOutlined, ImportOutlined, ExportOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';

const { TabPane } = Tabs;
const { Panel } = Collapse;
const { Option } = Select;

// 物理交互类型枚举 - 与引擎保持一致
export enum InteractionType {
  /** 推动 */
  PUSH = 'push',
  /** 拉动 */
  PULL = 'pull',
  /** 举起 */
  LIFT = 'lift',
  /** 投掷 */
  THROW = 'throw',
  /** 攀爬 */
  CLIMB = 'climb',
  /** 悬挂 */
  HANG = 'hang'
}

/**
 * 物理交互编辑器属性
 */
interface PhysicsInteractionEditorProps {
  /** 选中的实体ID */
  selectedEntityId?: string;
  /** 是否可编辑 */
  editable?: boolean;
}

/**
 * 物理交互编辑器组件
 * @param props 组件属性
 * @returns 组件JSX
 */
const PhysicsInteractionEditor: React.FC<PhysicsInteractionEditorProps> = (props) => {
  const { selectedEntityId, editable = true } = props;
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // 从Redux状态中获取选中的实体
  const selectedEntity = useSelector((state: RootState) =>
    selectedEntityId ? state.scene.entities.find(e => e.id === selectedEntityId) : undefined
  );

  // 从Redux状态中获取物理交互组件
  const interactionComponent = useSelector((state: RootState) => {
    if (!selectedEntityId) return undefined;
    const entity = state.scene.entities.find(e => e.id === selectedEntityId);
    return entity?.components?.PhysicsInteractionComponent;
  });

  // 表单实例
  const [form] = Form.useForm();

  // 本地状态
  const [activeTab, setActiveTab] = useState<string>('basic');
  const [interactionTypes, setInteractionTypes] = useState<string[]>([]);
  const [presets, setPresets] = useState<any[]>([]);
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null);

  // 初始化表单数据
  useEffect(() => {
    if (interactionComponent) {
      // 设置表单初始值
      form.setFieldsValue({
        enabled: interactionComponent.enabled !== undefined ? interactionComponent.enabled : true,
        maxInteractionDistance: interactionComponent.maxInteractionDistance || 2.0,
        interactionForce: interactionComponent.interactionForce || 500.0,
        interactionForceDamping: interactionComponent.interactionForceDamping || 0.5,
        canBePushed: interactionComponent.canBePushed !== undefined ? interactionComponent.canBePushed : true,
        canBePulled: interactionComponent.canBePulled !== undefined ? interactionComponent.canBePulled : true,
        canBeLifted: interactionComponent.canBeLifted !== undefined ? interactionComponent.canBeLifted : true,
        canBeThrown: interactionComponent.canBeThrown !== undefined ? interactionComponent.canBeThrown : true,
        canBeClimbed: interactionComponent.canBeClimbed !== undefined ? interactionComponent.canBeClimbed : false,
        canBeHanged: interactionComponent.canBeHanged !== undefined ? interactionComponent.canBeHanged : false
      });

      // 设置交互类型
      setInteractionTypes(interactionComponent.allowedInteractionTypes || [
        InteractionType.PUSH,
        InteractionType.PULL,
        InteractionType.LIFT,
        InteractionType.THROW
      ]);
    } else {
      // 设置默认值
      form.resetFields();
      setInteractionTypes([
        InteractionType.PUSH,
        InteractionType.PULL,
        InteractionType.LIFT,
        InteractionType.THROW
      ]);
    }

    // 加载预设
    loadPresets();
  }, [interactionComponent, form]);

  /**
   * 加载预设
   */
  const loadPresets = () => {
    // 这里应该从服务器或本地存储加载预设
    // 暂时使用模拟数据
    setPresets([
      {
        id: 'preset1',
        name: t('physics.interaction.presets.lightObject'),
        description: t('physics.interaction.presets.lightObjectDesc'),
        values: {
          enabled: true,
          maxInteractionDistance: 2.0,
          interactionForce: 300.0,
          interactionForceDamping: 0.3,
          canBePushed: true,
          canBePulled: true,
          canBeLifted: true,
          canBeThrown: true,
          canBeClimbed: false,
          canBeHanged: false,
          allowedInteractionTypes: [
            InteractionType.PUSH,
            InteractionType.PULL,
            InteractionType.LIFT,
            InteractionType.THROW
          ]
        }
      },
      {
        id: 'preset2',
        name: t('physics.interaction.presets.heavyObject'),
        description: t('physics.interaction.presets.heavyObjectDesc'),
        values: {
          enabled: true,
          maxInteractionDistance: 1.5,
          interactionForce: 800.0,
          interactionForceDamping: 0.7,
          canBePushed: true,
          canBePulled: true,
          canBeLifted: false,
          canBeThrown: false,
          canBeClimbed: false,
          canBeHanged: false,
          allowedInteractionTypes: [
            InteractionType.PUSH,
            InteractionType.PULL
          ]
        }
      },
      {
        id: 'preset3',
        name: t('physics.interaction.presets.climbableObject'),
        description: t('physics.interaction.presets.climbableObjectDesc'),
        values: {
          enabled: true,
          maxInteractionDistance: 1.0,
          interactionForce: 500.0,
          interactionForceDamping: 0.5,
          canBePushed: false,
          canBePulled: false,
          canBeLifted: false,
          canBeThrown: false,
          canBeClimbed: true,
          canBeHanged: true,
          allowedInteractionTypes: [
            InteractionType.CLIMB,
            InteractionType.HANG
          ]
        }
      }
    ]);
  };

  /**
   * 应用预设
   * @param presetId 预设ID
   */
  const applyPreset = (presetId: string) => {
    const preset = presets.find(p => p.id === presetId);
    if (preset) {
      // 设置表单值
      form.setFieldsValue(preset.values);

      // 设置交互类型
      setInteractionTypes(preset.values.allowedInteractionTypes || []);

      // 更新选中的预设
      setSelectedPreset(presetId);

      // 显示成功消息
      message.success(t('physics.interaction.presetApplied'));
    }
  };

  /**
   * 保存预设
   */
  const savePreset = () => {
    // 获取表单值
    const values = form.getFieldsValue();

    // 添加交互类型
    values.allowedInteractionTypes = interactionTypes;

    // 创建预设对象
    const newPreset = {
      id: `preset${Date.now()}`,
      name: values.presetName || t('physics.interaction.presets.newPreset'),
      description: values.presetDescription || '',
      values: { ...values }
    };

    // 删除预设名称和描述字段
    delete newPreset.values.presetName;
    delete newPreset.values.presetDescription;

    // 添加到预设列表
    setPresets([...presets, newPreset]);

    // 选中新预设
    setSelectedPreset(newPreset.id);

    // 显示成功消息
    message.success(t('physics.interaction.presetSaved'));
  };

  /**
   * 删除预设
   * @param presetId 预设ID
   */
  const deletePreset = (presetId: string) => {
    // 过滤掉要删除的预设
    const newPresets = presets.filter(p => p.id !== presetId);
    setPresets(newPresets);

    // 如果删除的是当前选中的预设，清除选中状态
    if (selectedPreset === presetId) {
      setSelectedPreset(null);
    }

    // 显示成功消息
    message.success(t('physics.interaction.presetDeleted'));
  };

  /**
   * 导出预设
   */
  const exportPresets = () => {
    // 创建预设数据
    const presetsData = JSON.stringify(presets, null, 2);

    // 创建下载链接
    const blob = new Blob([presetsData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'physics_interaction_presets.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    // 显示成功消息
    message.success(t('physics.interaction.presetsExported'));
  };

  /**
   * 导入预设
   * @param event 文件输入事件
   */
  const importPresets = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedPresets = JSON.parse(e.target?.result as string);
          if (Array.isArray(importedPresets)) {
            setPresets([...presets, ...importedPresets]);
            message.success(t('physics.interaction.presetsImported'));
          } else {
            message.error(t('physics.interaction.invalidPresetFile'));
          }
        } catch (error) {
          message.error(t('physics.interaction.invalidPresetFile'));
        }
      };
      reader.readAsText(file);
    }
  };

  /**
   * 处理表单提交
   * @param values 表单值
   */
  const handleSubmit = (values: any) => {
    if (!selectedEntityId) return;

    // 添加交互类型
    values.allowedInteractionTypes = interactionTypes;

    // 更新组件
    dispatch({
      type: 'scene/updateEntity',
      payload: {
        id: selectedEntityId,
        components: {
          ...selectedEntity?.components,
          PhysicsInteractionComponent: values
        }
      }
    });

    // 显示成功消息
    message.success(t('physics.interaction.settingsSaved'));
  };

  /**
   * 添加交互类型
   * @param type 交互类型
   */
  const addInteractionType = (type: string) => {
    if (!interactionTypes.includes(type)) {
      setInteractionTypes([...interactionTypes, type]);
    }
  };

  /**
   * 移除交互类型
   * @param type 交互类型
   */
  const removeInteractionType = (type: string) => {
    setInteractionTypes(interactionTypes.filter(t => t !== type));
  };

  /**
   * 渲染基本设置标签页
   * @returns JSX元素
   */
  const renderBasicTab = () => (
    <Form.Item noStyle>
      <Card title={t('physics.interaction.basicSettings')}>
        <Form.Item
          name="enabled"
          label={t('physics.interaction.enabled')}
          valuePropName="checked"
        >
          <Switch disabled={!editable} />
        </Form.Item>

        <Form.Item
          name="maxInteractionDistance"
          label={t('physics.interaction.maxDistance')}
          rules={[{ required: true }]}
        >
          <InputNumber
            min={0.1}
            max={10}
            step={0.1}
            disabled={!editable}
            style={{ width: '100%' }}
          />
        </Form.Item>

        <Form.Item
          name="interactionForce"
          label={t('physics.interaction.force')}
          rules={[{ required: true }]}
        >
          <InputNumber
            min={10}
            max={2000}
            step={10}
            disabled={!editable}
            style={{ width: '100%' }}
          />
        </Form.Item>

        <Form.Item
          name="interactionForceDamping"
          label={t('physics.interaction.damping')}
          rules={[{ required: true }]}
        >
          <InputNumber
            min={0}
            max={1}
            step={0.05}
            disabled={!editable}
            style={{ width: '100%' }}
          />
        </Form.Item>
      </Card>
    </Form.Item>
  );

  /**
   * 渲染交互类型标签页
   * @returns JSX元素
   */
  const renderInteractionTypesTab = () => (
    <Form.Item noStyle>
      <Card title={t('physics.interaction.interactionTypes')}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            {t('physics.interaction.allowedTypes')}:
          </div>
          <div>
            {interactionTypes.map(type => (
              <Button
                key={type}
                type="primary"
                size="small"
                style={{ margin: '0 4px 4px 0' }}
                disabled={!editable}
                onClick={() => removeInteractionType(type)}
              >
                {t(`physics.interaction.types.${type.toLowerCase()}`)} <DeleteOutlined />
              </Button>
            ))}
          </div>
          <Divider />
          <div>
            {t('physics.interaction.addType')}:
          </div>
          <Select
            style={{ width: '100%' }}
            placeholder={t('physics.interaction.selectType')}
            disabled={!editable}
            onChange={(value) => addInteractionType(value)}
          >
            {Object.values(InteractionType).map(type => (
              <Option
                key={type}
                value={type}
                disabled={interactionTypes.includes(type)}
              >
                {t(`physics.interaction.types.${type.toLowerCase()}`)}
              </Option>
            ))}
          </Select>
        </Space>
      </Card>
    </Form.Item>
  );

  /**
   * 渲染交互标志标签页
   * @returns JSX元素
   */
  const renderFlagsTab = () => (
    <Form.Item noStyle>
      <Card title={t('physics.interaction.interactionFlags')}>
        <Form.Item
          name="canBePushed"
          label={t('physics.interaction.flags.canBePushed')}
          valuePropName="checked"
        >
          <Switch disabled={!editable} />
        </Form.Item>

        <Form.Item
          name="canBePulled"
          label={t('physics.interaction.flags.canBePulled')}
          valuePropName="checked"
        >
          <Switch disabled={!editable} />
        </Form.Item>

        <Form.Item
          name="canBeLifted"
          label={t('physics.interaction.flags.canBeLifted')}
          valuePropName="checked"
        >
          <Switch disabled={!editable} />
        </Form.Item>

        <Form.Item
          name="canBeThrown"
          label={t('physics.interaction.flags.canBeThrown')}
          valuePropName="checked"
        >
          <Switch disabled={!editable} />
        </Form.Item>

        <Form.Item
          name="canBeClimbed"
          label={t('physics.interaction.flags.canBeClimbed')}
          valuePropName="checked"
        >
          <Switch disabled={!editable} />
        </Form.Item>

        <Form.Item
          name="canBeHanged"
          label={t('physics.interaction.flags.canBeHanged')}
          valuePropName="checked"
        >
          <Switch disabled={!editable} />
        </Form.Item>
      </Card>
    </Form.Item>
  );

  /**
   * 渲染预设标签页
   * @returns JSX元素
   */
  const renderPresetsTab = () => (
    <Form.Item noStyle>
      <Card title={t('physics.interaction.presets')}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            {t('physics.interaction.selectPreset')}:
          </div>
          <Space.Compact style={{ width: '100%' }}>
            <Select
              style={{ width: '100%' }}
              placeholder={t('physics.interaction.selectPreset')}
              value={selectedPreset}
              onChange={applyPreset}
              disabled={!editable}
            >
              {presets.map(preset => (
                <Option key={preset.id} value={preset.id}>
                  {preset.name}
                </Option>
              ))}
            </Select>
            {selectedPreset && (
              <Button
                icon={<DeleteOutlined />}
                onClick={() => deletePreset(selectedPreset)}
                disabled={!editable}
                danger
              />
            )}
          </Space.Compact>

          <Divider />

          <Collapse>
            <Panel header={t('physics.interaction.savePreset')} key="savePreset">
              <Form.Item
                name="presetName"
                label={t('physics.interaction.presetName')}
                rules={[{ required: true }]}
              >
                <Input disabled={!editable} />
              </Form.Item>

              <Form.Item
                name="presetDescription"
                label={t('physics.interaction.presetDescription')}
              >
                <Input.TextArea disabled={!editable} rows={2} />
              </Form.Item>

              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={savePreset}
                disabled={!editable}
                style={{ marginRight: 8 }}
              >
                {t('physics.interaction.savePreset')}
              </Button>
            </Panel>
          </Collapse>

          <Divider />

          <Space>
            <Button
              icon={<ExportOutlined />}
              onClick={exportPresets}
              disabled={presets.length === 0}
            >
              {t('physics.interaction.exportPresets')}
            </Button>

            <Button
              icon={<ImportOutlined />}
              onClick={() => document.getElementById('presetFileInput')?.click()}
            >
              {t('physics.interaction.importPresets')}
            </Button>
            <input
              id="presetFileInput"
              type="file"
              accept=".json"
              style={{ display: 'none' }}
              onChange={importPresets}
            />
          </Space>
        </Space>
      </Card>
    </Form.Item>
  );

  // 如果没有选中实体，显示提示信息
  if (!selectedEntityId) {
    return (
      <div style={{ padding: 16, textAlign: 'center' }}>
        {t('physics.interaction.selectEntity')}
      </div>
    );
  }

  return (
    <div className="physics-interaction-editor">
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        disabled={!editable}
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={t('physics.interaction.tabs.basic')} key="basic">
            {renderBasicTab()}
          </TabPane>
          <TabPane tab={t('physics.interaction.tabs.types')} key="types">
            {renderInteractionTypesTab()}
          </TabPane>
          <TabPane tab={t('physics.interaction.tabs.flags')} key="flags">
            {renderFlagsTab()}
          </TabPane>
          <TabPane tab={t('physics.interaction.tabs.presets')} key="presets">
            {renderPresetsTab()}
          </TabPane>
        </Tabs>

        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Button type="primary" htmlType="submit" disabled={!editable}>
            {t('common.save')}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default PhysicsInteractionEditor;