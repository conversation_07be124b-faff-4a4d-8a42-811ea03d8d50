/**
 * 状态机编辑器组件
 * 提供状态机的可视化编辑功能
 */
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Button, Tooltip, Space, message, Modal, Form, Divider } from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  SaveOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  SettingOutlined,
  BugOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  EditOutlined,
  LinkOutlined,
  DisconnectOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { RootState, AppDispatch } from '../../store';
import {
  loadStateMachine,
  saveStateMachine,
  addState,
  updateState,
  removeState,
  addTransition,
  removeTransition,
  addParameter,
  setSelectedState,
  setSelectedTransition,
  setEditingMode,
  setDebuggingMode,
  setShowGrid,
  setZoom,
  setOffset,
  updateStatePosition,
  clearStateMachine,
  NodePosition
} from '../../store/animations/stateMachineSlice';
import { stateMachineService } from '../../services/stateMachineService';
import StateMachineNode from './StateMachineNode';
import StateMachineTransition from './StateMachineTransition';
import StateMachinePanel from './StateMachinePanel';
import StateMachineDebugger from './StateMachineDebugger';
import './StateMachineEditor.less';

const { confirm } = Modal;

/**
 * 状态机编辑器属性
 */
interface StateMachineEditorProps {
  /** 实体ID */
  entityId: string;
  /** 保存回调 */
  onSave?: () => void;
  /** 取消回调 */
  onCancel?: () => void;
}

/**
 * 状态机编辑器组件
 */
const StateMachineEditor: React.FC<StateMachineEditorProps> = ({ entityId, onSave, onCancel }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  
  // 从Redux获取状态
  const {
    stateMachine,
    selectedState,
    selectedTransition,
    isEditing,
    isDebugging,
    showGrid,
    showDebugger,
    zoom,
    offset,
    loading
  } = useSelector((state: RootState) => state.stateMachine);
  
  // 本地状态
  const [availableClips, setAvailableClips] = useState<string[]>([]);
  const [isCreatingTransition, setIsCreatingTransition] = useState(false);
  const [transitionStartState, setTransitionStartState] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartPos, setDragStartPos] = useState({ x: 0, y: 0 });
  const [dragStartOffset, setDragStartOffset] = useState({ x: 0, y: 0 });
  const [isFullscreen, setIsFullscreen] = useState(false);
  
  // 引用
  const editorRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLDivElement>(null);
  
  // 表单
  const [stateForm] = Form.useForm();
  const [transitionForm] = Form.useForm();
  const [parameterForm] = Form.useForm();
  
  // 模态框可见性
  const [stateModalVisible, setStateModalVisible] = useState(false);
  const [transitionModalVisible, setTransitionModalVisible] = useState(false);
  const [parameterModalVisible, setParameterModalVisible] = useState(false);

  // 加载可用的动画片段
  const loadAvailableClips = useCallback(async () => {
    try {
      const clips = await stateMachineService.getAvailableAnimationClips(entityId);
      setAvailableClips(clips);
    } catch (error) {
      console.error('加载动画片段失败:', error);
      message.error(t('editor.animation.loadClipsFailed'));
    }
  }, [entityId, t]);

  // 加载状态机
  useEffect(() => {
    if (entityId) {
      dispatch(loadStateMachine(entityId));
      loadAvailableClips();
    }

    return () => {
      dispatch(clearStateMachine());
    };
  }, [dispatch, entityId, loadAvailableClips]);
  
  // 处理保存
  const handleSave = () => {
    if (!stateMachine) return;
    
    dispatch(saveStateMachine({ entityId, data: stateMachine }))
      .unwrap()
      .then(() => {
        message.success(t('editor.animation.saveSuccess'));
        if (onSave) onSave();
      })
      .catch((error: any) => {
        message.error(t('editor.animation.saveFailed') + ': ' + error);
      });
  };
  
  // 处理取消
  const handleCancel = () => {
    if (isEditing) {
      confirm({
        title: t('editor.animation.confirmCancel'),
        content: t('editor.animation.confirmCancelContent'),
        onOk: () => {
          if (onCancel) onCancel();
        }
      });
    } else {
      if (onCancel) onCancel();
    }
  };
  
  // 处理添加状态
  const handleAddState = () => {
    stateForm.resetFields();
    setStateModalVisible(true);
  };
  
  // 处理编辑状态
  const handleEditState = () => {
    if (!selectedState || !stateMachine) return;
    
    const state = stateMachine.states.find(s => s.name === selectedState);
    if (!state) return;
    
    stateForm.setFieldsValue({
      name: state.name,
      type: state.type,
      clipName: state.clipName,
      loop: state.loop,
      clamp: state.clamp,
      parameterName: state.parameterName,
      blendSpaceType: state.blendSpaceType
    });
    
    setStateModalVisible(true);
  };
  
  // 处理删除状态
  const handleDeleteState = () => {
    if (!selectedState) return;
    
    confirm({
      title: t('editor.animation.confirmDeleteState'),
      content: t('editor.animation.confirmDeleteStateContent'),
      onOk: () => {
        dispatch(removeState(selectedState));
        dispatch(setEditingMode(true));
      }
    });
  };
  
  // 处理添加转换
  const handleAddTransition = () => {
    setIsCreatingTransition(true);
    setTransitionStartState(null);
    message.info(t('editor.animation.selectStartState'));
  };
  
  // 处理编辑转换
  const handleEditTransition = () => {
    if (!selectedTransition || !stateMachine) return;
    
    const transition = stateMachine.transitions.find(
      t => t.from === selectedTransition.from && t.to === selectedTransition.to
    );
    if (!transition) return;
    
    transitionForm.setFieldsValue({
      from: transition.from,
      to: transition.to,
      conditionExpression: transition.conditionExpression,
      duration: transition.duration,
      canInterrupt: transition.canInterrupt,
      curveType: transition.curveType,
      priority: transition.priority
    });
    
    setTransitionModalVisible(true);
  };
  
  // 处理删除转换
  const handleDeleteTransition = () => {
    if (!selectedTransition) return;
    
    confirm({
      title: t('editor.animation.confirmDeleteTransition'),
      content: t('editor.animation.confirmDeleteTransitionContent'),
      onOk: () => {
        dispatch(removeTransition(selectedTransition));
        dispatch(setEditingMode(true));
      }
    });
  };
  
  // 处理添加参数
  const handleAddParameter = () => {
    parameterForm.resetFields();
    setParameterModalVisible(true);
  };
  
  // 处理状态表单提交
  const handleStateFormSubmit = (values: any) => {
    if (!stateMachine) return;
    
    const isEdit = stateMachine.states.some(s => s.name === values.name);
    
    if (isEdit) {
      // 更新状态
      dispatch(updateState({
        name: values.name,
        state: {
          type: values.type,
          clipName: values.clipName,
          loop: values.loop,
          clamp: values.clamp,
          parameterName: values.parameterName,
          blendSpaceType: values.blendSpaceType,
          blendSpaceConfig: values.blendSpaceConfig
        }
      }));
    } else {
      // 添加状态
      const position = {
        x: Math.random() * 500,
        y: Math.random() * 300
      };
      
      dispatch(addState({
        name: values.name,
        type: values.type,
        clipName: values.clipName,
        loop: values.loop,
        clamp: values.clamp,
        parameterName: values.parameterName,
        blendSpaceType: values.blendSpaceType,
        blendSpaceConfig: values.blendSpaceConfig,
        position
      }));
    }
    
    dispatch(setEditingMode(true));
    setStateModalVisible(false);
  };
  
  // 处理转换表单提交
  const handleTransitionFormSubmit = (values: any) => {
    dispatch(addTransition({
      from: values.from,
      to: values.to,
      conditionExpression: values.conditionExpression,
      duration: values.duration,
      canInterrupt: values.canInterrupt,
      curveType: values.curveType,
      priority: values.priority
    }));
    
    dispatch(setEditingMode(true));
    setTransitionModalVisible(false);
  };
  
  // 处理参数表单提交
  const handleParameterFormSubmit = (values: any) => {
    dispatch(addParameter({
      name: values.name,
      type: values.type,
      defaultValue: values.defaultValue,
      minValue: values.minValue,
      maxValue: values.maxValue
    }));
    
    dispatch(setEditingMode(true));
    setParameterModalVisible(false);
  };
  
  // 处理状态点击
  const handleStateClick = (stateName: string) => {
    if (isCreatingTransition) {
      if (!transitionStartState) {
        // 设置转换起始状态
        setTransitionStartState(stateName);
        message.info(t('editor.animation.selectEndState'));
      } else if (transitionStartState !== stateName) {
        // 创建转换
        transitionForm.setFieldsValue({
          from: transitionStartState,
          to: stateName,
          duration: 0.3,
          canInterrupt: true
        });
        
        setTransitionModalVisible(true);
        setIsCreatingTransition(false);
        setTransitionStartState(null);
      }
    } else {
      // 选中状态
      dispatch(setSelectedState(stateName));
    }
  };
  
  // 处理转换点击
  const handleTransitionClick = (from: string, to: string) => {
    dispatch(setSelectedTransition({ from, to }));
  };
  
  // 处理状态拖动
  const handleStateDrag = (stateName: string, position: NodePosition) => {
    dispatch(updateStatePosition({ name: stateName, position }));
    dispatch(setEditingMode(true));
  };
  
  // 处理画布鼠标按下
  const handleCanvasMouseDown = (e: React.MouseEvent) => {
    if (e.button === 1 || e.button === 0 && e.altKey) {
      setIsDragging(true);
      setDragStartPos({ x: e.clientX, y: e.clientY });
      setDragStartOffset({ ...offset });
    } else if (e.button === 0 && !isCreatingTransition) {
      // 取消选中
      dispatch(setSelectedState(null));
      dispatch(setSelectedTransition(null));
    }
  };
  
  // 处理画布鼠标移动
  const handleCanvasMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      const dx = e.clientX - dragStartPos.x;
      const dy = e.clientY - dragStartPos.y;
      
      dispatch(setOffset({
        x: dragStartOffset.x + dx,
        y: dragStartOffset.y + dy
      }));
    }
  };
  
  // 处理画布鼠标抬起
  const handleCanvasMouseUp = () => {
    setIsDragging(false);
  };
  
  // 处理画布鼠标离开
  const handleCanvasMouseLeave = () => {
    setIsDragging(false);
  };
  
  // 处理画布滚轮
  const handleCanvasWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    
    const delta = e.deltaY < 0 ? 0.1 : -0.1;
    const newZoom = Math.max(0.1, Math.min(2, zoom + delta));
    
    dispatch(setZoom(newZoom));
  };
  
  // 处理缩放重置
  const handleResetZoom = () => {
    dispatch(setZoom(1));
    dispatch(setOffset({ x: 0, y: 0 }));
  };
  
  // 处理全屏切换
  const handleToggleFullscreen = () => {
    if (editorRef.current) {
      if (!isFullscreen) {
        if (editorRef.current.requestFullscreen) {
          editorRef.current.requestFullscreen();
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        }
      }
      
      setIsFullscreen(!isFullscreen);
    }
  };
  
  // 处理调试模式切换
  const handleToggleDebug = () => {
    const newDebugMode = !isDebugging;
    dispatch(setDebuggingMode(newDebugMode));
    stateMachineService.enableStateMachineDebug(entityId, newDebugMode);
  };
  
  // 处理播放
  const handlePlay = async () => {
    try {
      await stateMachineService.playStateMachine(entityId);
    } catch (error) {
      message.error(t('editor.animation.playFailed'));
    }
  };
  
  // 处理暂停
  const handlePause = async () => {
    try {
      await stateMachineService.pauseStateMachine(entityId);
    } catch (error) {
      message.error(t('editor.animation.pauseFailed'));
    }
  };
  
  // 处理停止
  const handleStop = async () => {
    try {
      await stateMachineService.stopStateMachine(entityId);
    } catch (error) {
      message.error(t('editor.animation.stopFailed'));
    }
  };
  
  return (
    <div className="state-machine-editor" ref={editorRef}>
      <div className="state-machine-editor-header">
        <div className="editor-title">
          <h2>{t('editor.animation.stateMachine')}</h2>
        </div>
        
        <div className="editor-actions">
          <Space>
            <Button onClick={handleCancel}>{t('editor.cancel')}</Button>
            <Button 
              type="primary" 
              icon={<SaveOutlined />} 
              onClick={handleSave}
              loading={loading}
              disabled={!isEditing}
            >
              {t('editor.save')}
            </Button>
          </Space>
        </div>
      </div>
      
      <div className="state-machine-editor-toolbar">
        <Space>
          <Tooltip title={t('editor.animation.addState')}>
            <Button 
              icon={<PlusOutlined />} 
              onClick={handleAddState}
            />
          </Tooltip>
          
          <Tooltip title={t('editor.animation.editState')}>
            <Button 
              icon={<EditOutlined />} 
              onClick={handleEditState}
              disabled={!selectedState}
            />
          </Tooltip>
          
          <Tooltip title={t('editor.animation.deleteState')}>
            <Button 
              icon={<DeleteOutlined />} 
              onClick={handleDeleteState}
              disabled={!selectedState}
              danger
            />
          </Tooltip>
          
          <Divider type="vertical" />
          
          <Tooltip title={t('editor.animation.addTransition')}>
            <Button 
              icon={<LinkOutlined />} 
              onClick={handleAddTransition}
            />
          </Tooltip>
          
          <Tooltip title={t('editor.animation.editTransition')}>
            <Button 
              icon={<EditOutlined />} 
              onClick={handleEditTransition}
              disabled={!selectedTransition}
            />
          </Tooltip>
          
          <Tooltip title={t('editor.animation.deleteTransition')}>
            <Button 
              icon={<DisconnectOutlined />} 
              onClick={handleDeleteTransition}
              disabled={!selectedTransition}
              danger
            />
          </Tooltip>
          
          <Divider type="vertical" />
          
          <Tooltip title={t('editor.animation.addParameter')}>
            <Button 
              icon={<SettingOutlined />} 
              onClick={handleAddParameter}
            />
          </Tooltip>
          
          <Divider type="vertical" />
          
          <Tooltip title={t('editor.animation.play')}>
            <Button 
              icon={<PlayCircleOutlined />} 
              onClick={handlePlay}
            />
          </Tooltip>
          
          <Tooltip title={t('editor.animation.pause')}>
            <Button 
              icon={<PauseCircleOutlined />} 
              onClick={handlePause}
            />
          </Tooltip>
          
          <Tooltip title={t('editor.animation.stop')}>
            <Button 
              icon={<StopOutlined />} 
              onClick={handleStop}
            />
          </Tooltip>
          
          <Divider type="vertical" />
          
          <Tooltip title={t('editor.animation.debug')}>
            <Button 
              icon={<BugOutlined />} 
              type={isDebugging ? 'primary' : 'default'}
              onClick={handleToggleDebug}
            />
          </Tooltip>
          
          <Tooltip title={t('editor.animation.showGrid')}>
            <Button 
              icon={showGrid ? <EyeOutlined /> : <EyeInvisibleOutlined />} 
              type={showGrid ? 'primary' : 'default'}
              onClick={() => dispatch(setShowGrid(!showGrid))}
            />
          </Tooltip>
          
          <Tooltip title={t('editor.animation.resetView')}>
            <Button 
              icon={<FullscreenOutlined />} 
              onClick={handleResetZoom}
            />
          </Tooltip>
          
          <Tooltip title={isFullscreen ? t('editor.animation.exitFullscreen') : t('editor.animation.fullscreen')}>
            <Button 
              icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />} 
              onClick={handleToggleFullscreen}
            />
          </Tooltip>
        </Space>
      </div>
      
      <div className="state-machine-editor-content">
        <div 
          className="state-machine-canvas"
          ref={canvasRef}
          onMouseDown={handleCanvasMouseDown}
          onMouseMove={handleCanvasMouseMove}
          onMouseUp={handleCanvasMouseUp}
          onMouseLeave={handleCanvasMouseLeave}
          onWheel={handleCanvasWheel}
        >
          {/* 网格背景 */}
          {showGrid && (
            <div 
              className="grid-background"
              style={{
                backgroundSize: `${20 * zoom}px ${20 * zoom}px`,
                transform: `translate(${offset.x}px, ${offset.y}px)`
              }}
            />
          )}
          
          {/* 状态和转换 */}
          <div 
            className="canvas-content"
            style={{
              transform: `translate(${offset.x}px, ${offset.y}px) scale(${zoom})`
            }}
          >
            {/* 转换 */}
            {stateMachine && stateMachine.transitions.map((transition) => (
              <StateMachineTransition
                key={`${transition.from}-${transition.to}`}
                from={transition.from}
                to={transition.to}
                fromPosition={stateMachine.states.find(s => s.name === transition.from)?.position || { x: 0, y: 0 }}
                toPosition={stateMachine.states.find(s => s.name === transition.to)?.position || { x: 0, y: 0 }}
                selected={selectedTransition?.from === transition.from && selectedTransition?.to === transition.to}
                onClick={() => handleTransitionClick(transition.from, transition.to)}
                label={transition.conditionExpression}
                curveType={transition.curveType}
              />
            ))}
            
            {/* 状态 */}
            {stateMachine && stateMachine.states.map((state) => (
              <StateMachineNode
                key={state.name}
                name={state.name}
                type={state.type}
                position={state.position || { x: 0, y: 0 }}
                selected={selectedState === state.name}
                isCurrentState={stateMachine.currentState === state.name}
                onClick={() => handleStateClick(state.name)}
                onDrag={(position) => handleStateDrag(state.name, position)}
                color={state.color}
              />
            ))}
            
            {/* 创建转换时的临时线 */}
            {isCreatingTransition && transitionStartState && stateMachine && (
              <div className="temp-transition-line" />
            )}
          </div>
        </div>
        
        {/* 右侧面板 */}
        <div className="state-machine-panel">
          <StateMachinePanel
            stateMachine={stateMachine}
            selectedState={selectedState}
            selectedTransition={selectedTransition}
            entityId={entityId}
            availableClips={availableClips}
          />
        </div>
      </div>
      
      {/* 调试面板 */}
      {isDebugging && showDebugger && (
        <div className="state-machine-debugger">
          <StateMachineDebugger entityId={entityId} />
        </div>
      )}
      
      {/* 状态表单模态框 */}
      <Modal
        title={t('editor.animation.stateProperties')}
        open={stateModalVisible}
        onCancel={() => setStateModalVisible(false)}
        footer={null}
      >
        <Form
          form={stateForm}
          layout="vertical"
          onFinish={handleStateFormSubmit}
        >
          {/* 表单内容 */}
        </Form>
      </Modal>
      
      {/* 转换表单模态框 */}
      <Modal
        title={t('editor.animation.transitionProperties')}
        open={transitionModalVisible}
        onCancel={() => setTransitionModalVisible(false)}
        footer={null}
      >
        <Form
          form={transitionForm}
          layout="vertical"
          onFinish={handleTransitionFormSubmit}
        >
          {/* 表单内容 */}
        </Form>
      </Modal>
      
      {/* 参数表单模态框 */}
      <Modal
        title={t('editor.animation.parameterProperties')}
        open={parameterModalVisible}
        onCancel={() => setParameterModalVisible(false)}
        footer={null}
      >
        <Form
          form={parameterForm}
          layout="vertical"
          onFinish={handleParameterFormSubmit}
        >
          {/* 表单内容 */}
        </Form>
      </Modal>
    </div>
  );
};

export default StateMachineEditor;
