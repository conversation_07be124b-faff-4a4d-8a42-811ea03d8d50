.network-latency-simulator {
  margin-bottom: 16px;
}

.network-latency-simulator .ant-card-head-title {
  display: flex;
  align-items: center;
}

.network-latency-simulator .ant-card-head-title .anticon {
  margin-right: 8px;
  font-size: 16px;
}

.network-latency-simulator .ant-form-item-label {
  font-weight: 500;
}

.network-latency-simulator .ant-slider-mark-text {
  font-size: 12px;
}

.network-latency-simulator .ant-divider {
  margin: 16px 0;
  color: rgba(0, 0, 0, 0.45);
  font-weight: 500;
}

.network-latency-simulator .ant-alert {
  margin-bottom: 16px;
}

.network-latency-simulator .ant-form-item-extra {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.network-latency-simulator .ant-tooltip {
  max-width: 300px;
}

.network-latency-simulator .ant-tooltip-inner {
  white-space: pre-wrap;
}

.network-latency-simulator .scenario-progress {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.network-latency-simulator .scenario-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.network-latency-simulator .scenario-selection {
  max-height: 400px;
  overflow-y: auto;
}

.network-latency-simulator .scenario-details {
  margin-top: 16px;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.network-latency-simulator .ant-steps-item-description {
  font-size: 12px;
}

.network-latency-simulator .preset-card {
  cursor: pointer;
  transition: all 0.3s;
}

.network-latency-simulator .preset-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.network-latency-simulator .preset-card.selected {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.network-latency-simulator .preset-card .ant-card-head {
  padding: 0 12px;
  min-height: 40px;
}

.network-latency-simulator .preset-card .ant-card-head-title {
  padding: 8px 0;
}

.network-latency-simulator .preset-card .ant-card-body {
  padding: 12px;
}

.network-latency-simulator .preset-tag {
  margin-bottom: 8px;
}

.network-latency-simulator .preset-description {
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2; /* 标准属性，用于兼容性 */
  -webkit-box-orient: vertical;
}

.network-latency-simulator .preset-metrics {
  margin-top: 8px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
}

.network-latency-simulator .preset-metric {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.network-latency-simulator .preset-metric-value {
  font-weight: 500;
}
