/**
 * 智能负载均衡服务
 * 
 * 提供AI推理请求的智能负载均衡，包括：
 * - 多种负载均衡算法
 * - 实时性能监控和调整
 * - 预测性负载分配
 * - 自适应权重调整
 * - 故障检测和自动恢复
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import Redis from 'ioredis';

/**
 * 负载均衡算法类型
 */
export enum LoadBalancingAlgorithm {
  ROUND_ROBIN = 'round_robin',
  WEIGHTED_ROUND_ROBIN = 'weighted_round_robin',
  LEAST_CONNECTIONS = 'least_connections',
  WEIGHTED_LEAST_CONNECTIONS = 'weighted_least_connections',
  LATENCY_BASED = 'latency_based',
  RESOURCE_BASED = 'resource_based',
  AI_OPTIMIZED = 'ai_optimized',
  PREDICTIVE = 'predictive'
}

/**
 * 节点权重配置接口
 */
export interface NodeWeight {
  nodeId: string;
  weight: number;
  dynamicWeight: number;
  lastUpdated: Date;
  performanceScore: number;
}

/**
 * 负载预测结果接口
 */
export interface LoadPrediction {
  nodeId: string;
  predictedLoad: number;
  confidence: number;
  timeWindow: number;
  factors: {
    historicalPattern: number;
    currentTrend: number;
    seasonality: number;
    externalFactors: number;
  };
}

/**
 * 智能负载均衡服务
 */
@Injectable()
export class IntelligentLoadBalancerService {
  private readonly logger = new Logger(IntelligentLoadBalancerService.name);
  private readonly redis: Redis;
  
  // 负载均衡状态
  private currentAlgorithm: LoadBalancingAlgorithm = LoadBalancingAlgorithm.AI_OPTIMIZED;
  private nodeWeights = new Map<string, NodeWeight>();
  private roundRobinIndex = 0;
  private requestHistory: any[] = [];
  
  // 性能监控
  private performanceMetrics = new Map<string, any>();
  private loadPredictions = new Map<string, LoadPrediction>();
  
  // 配置参数
  private readonly maxHistorySize = 10000;
  private readonly weightUpdateInterval = 30000; // 30秒
  private readonly predictionWindow = 300000; // 5分钟
  private readonly adaptiveThreshold = 0.1; // 10%性能差异触发调整

  constructor(
    private readonly eventEmitter: EventEmitter2,
    redisConfig: any
  ) {
    this.redis = new Redis(redisConfig);
    this.initializeService();
  }

  /**
   * 初始化服务
   */
  private async initializeService(): Promise<void> {
    try {
      // 加载节点权重配置
      await this.loadNodeWeights();
      
      // 启动性能监控
      this.startPerformanceMonitoring();
      
      // 启动预测性分析
      this.startPredictiveAnalysis();
      
      this.logger.log('智能负载均衡服务已启动');
      
    } catch (error) {
      this.logger.error('服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 选择最优节点
   */
  public async selectOptimalNode(
    availableNodes: any[],
    requestContext?: {
      modelId?: string;
      priority?: number;
      expectedLatency?: number;
      resourceRequirements?: any;
    }
  ): Promise<any | null> {
    if (availableNodes.length === 0) {
      return null;
    }
    
    if (availableNodes.length === 1) {
      return availableNodes[0];
    }
    
    try {
      let selectedNode: any;
      
      switch (this.currentAlgorithm) {
        case LoadBalancingAlgorithm.ROUND_ROBIN:
          selectedNode = this.selectRoundRobin(availableNodes);
          break;
          
        case LoadBalancingAlgorithm.WEIGHTED_ROUND_ROBIN:
          selectedNode = this.selectWeightedRoundRobin(availableNodes);
          break;
          
        case LoadBalancingAlgorithm.LEAST_CONNECTIONS:
          selectedNode = this.selectLeastConnections(availableNodes);
          break;
          
        case LoadBalancingAlgorithm.WEIGHTED_LEAST_CONNECTIONS:
          selectedNode = this.selectWeightedLeastConnections(availableNodes);
          break;
          
        case LoadBalancingAlgorithm.LATENCY_BASED:
          selectedNode = this.selectLatencyBased(availableNodes);
          break;
          
        case LoadBalancingAlgorithm.RESOURCE_BASED:
          selectedNode = this.selectResourceBased(availableNodes);
          break;
          
        case LoadBalancingAlgorithm.AI_OPTIMIZED:
          selectedNode = this.selectAIOptimized(availableNodes, requestContext);
          break;
          
        case LoadBalancingAlgorithm.PREDICTIVE:
          selectedNode = await this.selectPredictive(availableNodes, requestContext);
          break;
          
        default:
          selectedNode = availableNodes[0];
      }
      
      // 记录选择结果
      this.recordSelection(selectedNode, availableNodes, requestContext);
      
      return selectedNode;
      
    } catch (error) {
      this.logger.error('选择最优节点失败:', error);
      return availableNodes[0]; // 降级到第一个可用节点
    }
  }

  /**
   * 更新节点性能指标
   */
  public async updateNodePerformance(
    nodeId: string,
    metrics: {
      responseTime: number;
      success: boolean;
      resourceUsage?: any;
      errorType?: string;
    }
  ): Promise<void> {
    try {
      // 更新性能指标
      if (!this.performanceMetrics.has(nodeId)) {
        this.performanceMetrics.set(nodeId, {
          totalRequests: 0,
          successfulRequests: 0,
          totalResponseTime: 0,
          averageResponseTime: 0,
          errorRate: 0,
          lastUpdated: new Date()
        });
      }
      
      const nodeMetrics = this.performanceMetrics.get(nodeId)!;
      nodeMetrics.totalRequests++;
      
      if (metrics.success) {
        nodeMetrics.successfulRequests++;
        nodeMetrics.totalResponseTime += metrics.responseTime;
        nodeMetrics.averageResponseTime = nodeMetrics.totalResponseTime / nodeMetrics.successfulRequests;
      }
      
      nodeMetrics.errorRate = 1 - (nodeMetrics.successfulRequests / nodeMetrics.totalRequests);
      nodeMetrics.lastUpdated = new Date();
      
      // 保存到Redis
      await this.redis.setex(
        `ai:lb:metrics:${nodeId}`,
        3600,
        JSON.stringify(nodeMetrics)
      );
      
      // 触发权重更新
      await this.updateNodeWeight(nodeId);
      
    } catch (error) {
      this.logger.error('更新节点性能指标失败:', error);
    }
  }

  /**
   * 设置负载均衡算法
   */
  public setLoadBalancingAlgorithm(algorithm: LoadBalancingAlgorithm): void {
    this.currentAlgorithm = algorithm;
    this.logger.log(`负载均衡算法已切换到: ${algorithm}`);
    
    this.eventEmitter.emit('load_balancer.algorithm.changed', {
      oldAlgorithm: this.currentAlgorithm,
      newAlgorithm: algorithm
    });
  }

  /**
   * 获取负载均衡统计信息
   */
  public getLoadBalancingStats(): {
    algorithm: LoadBalancingAlgorithm;
    nodeWeights: any;
    performanceMetrics: any;
    requestHistory: number;
    predictions: any;
  } {
    return {
      algorithm: this.currentAlgorithm,
      nodeWeights: Object.fromEntries(this.nodeWeights),
      performanceMetrics: Object.fromEntries(this.performanceMetrics),
      requestHistory: this.requestHistory.length,
      predictions: Object.fromEntries(this.loadPredictions)
    };
  }

  // ==================== 负载均衡算法实现 ====================

  /**
   * 轮询算法
   */
  private selectRoundRobin(nodes: any[]): any {
    const selectedNode = nodes[this.roundRobinIndex % nodes.length];
    this.roundRobinIndex = (this.roundRobinIndex + 1) % nodes.length;
    return selectedNode;
  }

  /**
   * 加权轮询算法
   */
  private selectWeightedRoundRobin(nodes: any[]): any {
    const weightedNodes: any[] = [];
    
    for (const node of nodes) {
      const weight = this.getNodeWeight(node.nodeId);
      for (let i = 0; i < weight.dynamicWeight; i++) {
        weightedNodes.push(node);
      }
    }
    
    if (weightedNodes.length === 0) {
      return nodes[0];
    }
    
    const selectedNode = weightedNodes[this.roundRobinIndex % weightedNodes.length];
    this.roundRobinIndex = (this.roundRobinIndex + 1) % weightedNodes.length;
    return selectedNode;
  }

  /**
   * 最少连接算法
   */
  private selectLeastConnections(nodes: any[]): any {
    return nodes.reduce((min, node) => 
      node.currentLoad.activeRequests < min.currentLoad.activeRequests ? node : min
    );
  }

  /**
   * 加权最少连接算法
   */
  private selectWeightedLeastConnections(nodes: any[]): any {
    let bestNode = nodes[0];
    let bestScore = Infinity;
    
    for (const node of nodes) {
      const weight = this.getNodeWeight(node.nodeId);
      const score = node.currentLoad.activeRequests / Math.max(weight.dynamicWeight, 1);
      
      if (score < bestScore) {
        bestScore = score;
        bestNode = node;
      }
    }
    
    return bestNode;
  }

  /**
   * 基于延迟的选择
   */
  private selectLatencyBased(nodes: any[]): any {
    return nodes.reduce((min, node) => 
      node.performance.averageLatency < min.performance.averageLatency ? node : min
    );
  }

  /**
   * 基于资源的选择
   */
  private selectResourceBased(nodes: any[]): any {
    let bestNode = nodes[0];
    let bestScore = -1;
    
    for (const node of nodes) {
      // 计算资源可用性评分
      const cpuAvailability = 1 - (node.currentLoad.cpuUsage / 100);
      const memoryAvailability = 1 - (node.currentLoad.memoryUsage / 100);
      const connectionAvailability = 1 - (node.currentLoad.activeRequests / node.capabilities.maxConcurrentRequests);
      
      const resourceScore = (cpuAvailability + memoryAvailability + connectionAvailability) / 3;
      
      if (resourceScore > bestScore) {
        bestScore = resourceScore;
        bestNode = node;
      }
    }
    
    return bestNode;
  }

  /**
   * AI优化选择
   */
  private selectAIOptimized(nodes: any[], requestContext?: any): any {
    let bestNode = nodes[0];
    let bestScore = -1;
    
    for (const node of nodes) {
      const weight = this.getNodeWeight(node.nodeId);
      const metrics = this.performanceMetrics.get(node.nodeId);
      
      // 计算综合评分
      const performanceScore = weight.performanceScore;
      const loadScore = 1 - (node.currentLoad.activeRequests / node.capabilities.maxConcurrentRequests);
      const latencyScore = metrics ? Math.min(1, 1000 / Math.max(metrics.averageResponseTime, 1)) : 0.5;
      const errorScore = metrics ? 1 - metrics.errorRate : 0.5;
      
      // 考虑请求上下文
      let contextScore = 1;
      if (requestContext?.priority) {
        contextScore *= (requestContext.priority / 10); // 假设优先级1-10
      }
      
      const totalScore = (
        performanceScore * 0.3 +
        loadScore * 0.25 +
        latencyScore * 0.25 +
        errorScore * 0.2
      ) * contextScore;
      
      if (totalScore > bestScore) {
        bestScore = totalScore;
        bestNode = node;
      }
    }
    
    return bestNode;
  }

  /**
   * 预测性选择
   */
  private async selectPredictive(nodes: any[], requestContext?: any): Promise<any> {
    let bestNode = nodes[0];
    let bestScore = -1;
    
    for (const node of nodes) {
      const prediction = this.loadPredictions.get(node.nodeId);
      
      if (prediction) {
        // 基于预测负载选择节点
        const predictedLoadScore = 1 - Math.min(1, prediction.predictedLoad / 100);
        const confidenceScore = prediction.confidence;
        
        const score = predictedLoadScore * confidenceScore;
        
        if (score > bestScore) {
          bestScore = score;
          bestNode = node;
        }
      } else {
        // 如果没有预测数据，降级到AI优化算法
        const aiScore = this.calculateAIScore(node, requestContext);
        if (aiScore > bestScore) {
          bestScore = aiScore;
          bestNode = node;
        }
      }
    }
    
    return bestNode;
  }

  // ==================== 辅助方法 ====================

  /**
   * 获取节点权重
   */
  private getNodeWeight(nodeId: string): NodeWeight {
    if (!this.nodeWeights.has(nodeId)) {
      this.nodeWeights.set(nodeId, {
        nodeId,
        weight: 1,
        dynamicWeight: 1,
        lastUpdated: new Date(),
        performanceScore: 0.5
      });
    }

    return this.nodeWeights.get(nodeId)!;
  }

  /**
   * 更新节点权重
   */
  private async updateNodeWeight(nodeId: string): Promise<void> {
    try {
      const metrics = this.performanceMetrics.get(nodeId);
      if (!metrics) return;

      const weight = this.getNodeWeight(nodeId);

      // 计算性能评分
      const latencyScore = Math.min(1, 1000 / Math.max(metrics.averageResponseTime, 1));
      const errorScore = 1 - metrics.errorRate;
      const performanceScore = (latencyScore + errorScore) / 2;

      // 更新权重
      weight.performanceScore = performanceScore;
      weight.dynamicWeight = Math.max(0.1, Math.min(10, weight.weight * performanceScore));
      weight.lastUpdated = new Date();

      // 保存到Redis
      await this.redis.setex(
        `ai:lb:weight:${nodeId}`,
        3600,
        JSON.stringify(weight)
      );

    } catch (error) {
      this.logger.error('更新节点权重失败:', error);
    }
  }

  /**
   * 计算AI评分
   */
  private calculateAIScore(node: any, requestContext?: any): number {
    const weight = this.getNodeWeight(node.nodeId);
    const metrics = this.performanceMetrics.get(node.nodeId);

    const performanceScore = weight.performanceScore;
    const loadScore = 1 - (node.currentLoad.activeRequests / node.capabilities.maxConcurrentRequests);
    const latencyScore = metrics ? Math.min(1, 1000 / Math.max(metrics.averageResponseTime, 1)) : 0.5;
    const errorScore = metrics ? 1 - metrics.errorRate : 0.5;

    return (
      performanceScore * 0.3 +
      loadScore * 0.25 +
      latencyScore * 0.25 +
      errorScore * 0.2
    );
  }

  /**
   * 记录选择结果
   */
  private recordSelection(selectedNode: any, availableNodes: any[], requestContext?: any): void {
    const record = {
      timestamp: Date.now(),
      selectedNodeId: selectedNode.nodeId,
      availableNodeCount: availableNodes.length,
      algorithm: this.currentAlgorithm,
      requestContext,
      nodeLoad: selectedNode.currentLoad.activeRequests,
      nodeCapacity: selectedNode.capabilities.maxConcurrentRequests
    };

    this.requestHistory.push(record);

    // 限制历史记录大小
    if (this.requestHistory.length > this.maxHistorySize) {
      this.requestHistory.shift();
    }
  }

  /**
   * 加载节点权重配置
   */
  private async loadNodeWeights(): Promise<void> {
    try {
      const weightKeys = await this.redis.keys('ai:lb:weight:*');

      for (const key of weightKeys) {
        const weightData = await this.redis.get(key);
        if (weightData) {
          const weight: NodeWeight = JSON.parse(weightData);
          this.nodeWeights.set(weight.nodeId, weight);
        }
      }

      this.logger.log(`已加载 ${weightKeys.length} 个节点权重配置`);

    } catch (error) {
      this.logger.error('加载节点权重失败:', error);
    }
  }

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring(): void {
    setInterval(async () => {
      await this.analyzePerformanceTrends();
      await this.adjustAlgorithmIfNeeded();
    }, this.weightUpdateInterval);

    this.logger.log('性能监控已启动');
  }

  /**
   * 启动预测性分析
   */
  private startPredictiveAnalysis(): void {
    setInterval(async () => {
      await this.generateLoadPredictions();
    }, this.predictionWindow);

    this.logger.log('预测性分析已启动');
  }

  /**
   * 分析性能趋势
   */
  private async analyzePerformanceTrends(): Promise<void> {
    try {
      // 分析最近的请求历史
      const recentHistory = this.requestHistory.slice(-1000); // 最近1000个请求

      if (recentHistory.length < 100) return; // 数据不足

      // 计算算法性能
      const algorithmPerformance = new Map<LoadBalancingAlgorithm, {
        totalRequests: number;
        averageLoad: number;
        loadVariance: number;
      }>();

      for (const record of recentHistory) {
        if (!algorithmPerformance.has(record.algorithm)) {
          algorithmPerformance.set(record.algorithm, {
            totalRequests: 0,
            averageLoad: 0,
            loadVariance: 0
          });
        }

        const perf = algorithmPerformance.get(record.algorithm)!;
        perf.totalRequests++;

        const loadRatio = record.nodeLoad / record.nodeCapacity;
        perf.averageLoad = (perf.averageLoad * (perf.totalRequests - 1) + loadRatio) / perf.totalRequests;
      }

      // 计算负载方差
      for (const [algorithm, perf] of algorithmPerformance) {
        let variance = 0;
        let count = 0;

        for (const record of recentHistory) {
          if (record.algorithm === algorithm) {
            const loadRatio = record.nodeLoad / record.nodeCapacity;
            variance += Math.pow(loadRatio - perf.averageLoad, 2);
            count++;
          }
        }

        perf.loadVariance = count > 0 ? variance / count : 0;
      }

      // 保存分析结果
      await this.redis.setex(
        'ai:lb:performance_analysis',
        3600,
        JSON.stringify(Object.fromEntries(algorithmPerformance))
      );

    } catch (error) {
      this.logger.error('分析性能趋势失败:', error);
    }
  }

  /**
   * 根据需要调整算法
   */
  private async adjustAlgorithmIfNeeded(): Promise<void> {
    try {
      const analysisData = await this.redis.get('ai:lb:performance_analysis');
      if (!analysisData) return;

      const analysis = JSON.parse(analysisData);
      const currentPerf = analysis[this.currentAlgorithm];

      if (!currentPerf) return;

      // 检查是否需要切换算法
      let bestAlgorithm = this.currentAlgorithm;
      let bestScore = this.calculateAlgorithmScore(currentPerf);

      for (const [algorithm, perf] of Object.entries(analysis)) {
        const score = this.calculateAlgorithmScore(perf as any);
        if (score > bestScore * (1 + this.adaptiveThreshold)) {
          bestScore = score;
          bestAlgorithm = algorithm as LoadBalancingAlgorithm;
        }
      }

      if (bestAlgorithm !== this.currentAlgorithm) {
        this.logger.log(`自动切换负载均衡算法: ${this.currentAlgorithm} -> ${bestAlgorithm}`);
        this.setLoadBalancingAlgorithm(bestAlgorithm);
      }

    } catch (error) {
      this.logger.error('调整算法失败:', error);
    }
  }

  /**
   * 计算算法评分
   */
  private calculateAlgorithmScore(performance: any): number {
    // 评分标准：负载均衡性（低方差）+ 效率（低平均负载）
    const balanceScore = 1 / (1 + performance.loadVariance);
    const efficiencyScore = 1 - performance.averageLoad;

    return (balanceScore * 0.6 + efficiencyScore * 0.4);
  }

  /**
   * 生成负载预测
   */
  private async generateLoadPredictions(): Promise<void> {
    try {
      const nodeKeys = await this.redis.keys('ai:inference:node:*');

      for (const key of nodeKeys) {
        const nodeData = await this.redis.get(key);
        if (nodeData) {
          const node = JSON.parse(nodeData);
          const prediction = await this.predictNodeLoad(node.nodeId);

          if (prediction) {
            this.loadPredictions.set(node.nodeId, prediction);

            // 保存预测结果
            await this.redis.setex(
              `ai:lb:prediction:${node.nodeId}`,
              this.predictionWindow / 1000,
              JSON.stringify(prediction)
            );
          }
        }
      }

      this.logger.log(`已生成 ${this.loadPredictions.size} 个节点的负载预测`);

    } catch (error) {
      this.logger.error('生成负载预测失败:', error);
    }
  }

  /**
   * 预测节点负载
   */
  private async predictNodeLoad(nodeId: string): Promise<LoadPrediction | null> {
    try {
      // 获取历史负载数据
      const historicalData = this.requestHistory
        .filter(record => record.selectedNodeId === nodeId)
        .slice(-100); // 最近100个请求

      if (historicalData.length < 10) {
        return null; // 数据不足
      }

      // 简化的负载预测算法
      const recentLoads = historicalData.map(record =>
        (record.nodeLoad / record.nodeCapacity) * 100
      );

      // 计算趋势
      const trend = this.calculateTrend(recentLoads);
      const average = recentLoads.reduce((sum, load) => sum + load, 0) / recentLoads.length;

      // 预测未来负载
      const predictedLoad = Math.max(0, Math.min(100, average + trend * 5)); // 5个时间单位后的预测

      // 计算置信度
      const variance = recentLoads.reduce((sum, load) => sum + Math.pow(load - average, 2), 0) / recentLoads.length;
      const confidence = Math.max(0.1, Math.min(1, 1 / (1 + variance / 100)));

      return {
        nodeId,
        predictedLoad,
        confidence,
        timeWindow: this.predictionWindow,
        factors: {
          historicalPattern: average,
          currentTrend: trend,
          seasonality: 0, // 简化实现
          externalFactors: 0 // 简化实现
        }
      };

    } catch (error) {
      this.logger.error('预测节点负载失败:', error);
      return null;
    }
  }

  /**
   * 计算趋势
   */
  private calculateTrend(data: number[]): number {
    if (data.length < 2) return 0;

    const n = data.length;
    const sumX = (n * (n - 1)) / 2;
    const sumY = data.reduce((sum, value) => sum + value, 0);
    const sumXY = data.reduce((sum, value, index) => sum + value * index, 0);
    const sumX2 = (n * (n - 1) * (2 * n - 1)) / 6;

    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);

    return slope;
  }

  /**
   * 定期优化
   */
  @Cron(CronExpression.EVERY_HOUR)
  public async performPeriodicOptimization(): Promise<void> {
    try {
      this.logger.log('开始执行定期负载均衡优化');

      // 清理过期的历史记录
      const cutoffTime = Date.now() - 24 * 3600 * 1000; // 24小时前
      this.requestHistory = this.requestHistory.filter(record => record.timestamp > cutoffTime);

      // 重新评估所有节点权重
      for (const nodeId of this.nodeWeights.keys()) {
        await this.updateNodeWeight(nodeId);
      }

      // 生成优化报告
      const optimizationReport = {
        timestamp: new Date(),
        currentAlgorithm: this.currentAlgorithm,
        totalNodes: this.nodeWeights.size,
        totalRequests: this.requestHistory.length,
        averageResponseTime: this.calculateAverageResponseTime(),
        loadDistribution: this.calculateLoadDistribution()
      };

      await this.redis.setex(
        'ai:lb:optimization_report',
        3600 * 24,
        JSON.stringify(optimizationReport)
      );

      this.eventEmitter.emit('load_balancer.optimization.completed', optimizationReport);

    } catch (error) {
      this.logger.error('定期优化失败:', error);
    }
  }

  /**
   * 计算平均响应时间
   */
  private calculateAverageResponseTime(): number {
    const metrics = Array.from(this.performanceMetrics.values());
    if (metrics.length === 0) return 0;

    const totalTime = metrics.reduce((sum, metric) => sum + metric.averageResponseTime, 0);
    return totalTime / metrics.length;
  }

  /**
   * 计算负载分布
   */
  private calculateLoadDistribution(): any {
    const distribution = new Map<string, number>();

    for (const record of this.requestHistory) {
      const nodeId = record.selectedNodeId;
      distribution.set(nodeId, (distribution.get(nodeId) || 0) + 1);
    }

    return Object.fromEntries(distribution);
  }

  /**
   * 关闭服务
   */
  public async shutdown(): Promise<void> {
    this.logger.log('正在关闭智能负载均衡服务...');

    // 保存当前状态
    const currentState = {
      algorithm: this.currentAlgorithm,
      nodeWeights: Object.fromEntries(this.nodeWeights),
      performanceMetrics: Object.fromEntries(this.performanceMetrics),
      requestHistoryCount: this.requestHistory.length,
      timestamp: Date.now()
    };

    await this.redis.setex(
      'ai:lb:final_state',
      3600,
      JSON.stringify(currentState)
    );

    this.redis.disconnect();
    this.logger.log('智能负载均衡服务已关闭');
  }
}
