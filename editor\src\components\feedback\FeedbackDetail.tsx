/**
 * 反馈详情组件
 * 用于显示反馈的详细信息
 */
import React, { useState } from 'react';
import {
  Card,
  Descriptions,
  Tag,
  Space,
  Button,
  Divider,
  Typography,
  Image,
  Collapse,
  message,
  Timeline,
  Input,
  Rate
} from 'antd';
import {
  BugOutlined,
  RocketOutlined,
  ToolOutlined,
  CommentOutlined,
  UserOutlined,
  ClockCircleOutlined,
  SendOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { FeedbackService, FeedbackData } from '../../services/FeedbackService';
import './FeedbackDetail.less';

const { Panel } = Collapse;
const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

export interface FeedbackDetailProps {
  /** 反馈数据 */
  feedback: FeedbackData;
  /** 状态变更回调 */
  onStatusChange?: () => void;
}

/**
 * 反馈详情组件
 */
const FeedbackDetail: React.FC<FeedbackDetailProps> = ({
  feedback,
  onStatusChange
}) => {
  const { t } = useTranslation();
  const [submitting, setSubmitting] = useState(false);
  const [currentStatus, setCurrentStatus] = useState<'new' | 'inProgress' | 'resolved' | 'closed'>(feedback.status || 'new');
  const [commentText, setCommentText] = useState('');

  // 获取反馈类型图标
  const getFeedbackTypeIcon = (type: string) => {
    switch (type) {
      case 'bug':
        return <BugOutlined />;
      case 'feature':
        return <RocketOutlined />;
      case 'improvement':
        return <ToolOutlined />;
      case 'performance':
        return <ClockCircleOutlined />;
      case 'usability':
        return <UserOutlined />;
      default:
        return <CommentOutlined />;
    }
  };

  // 获取状态标签颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new':
        return 'blue';
      case 'inProgress':
        return 'processing';
      case 'resolved':
        return 'success';
      case 'closed':
        return 'default';
      default:
        return 'blue';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'new':
        return t('feedback.manager.statusNew');
      case 'inProgress':
        return t('feedback.manager.statusInProgress');
      case 'resolved':
        return t('feedback.manager.statusResolved');
      case 'closed':
        return t('feedback.manager.statusClosed');
      default:
        return t('feedback.manager.statusNew');
    }
  };

  // 更新反馈状态
  const updateStatus = async (status: 'new' | 'inProgress' | 'resolved' | 'closed') => {
    try {
      setSubmitting(true);
      await FeedbackService.updateFeedback(feedback.id!, { status });
      setCurrentStatus(status);
      message.success(t('feedback.manager.updateSuccess'));
      
      if (onStatusChange) {
        onStatusChange();
      }
    } catch (error) {
      console.error('更新反馈状态失败:', error);
      message.error(t('feedback.manager.updateError'));
    } finally {
      setSubmitting(false);
    }
  };

  // 添加评论
  const addComment = async () => {
    if (!commentText.trim()) {
      return;
    }
    
    try {
      setSubmitting(true);
      await FeedbackService.addFeedbackComment(feedback.id!, commentText);
      setCommentText('');
      message.success(t('feedback.manager.commentAdded'));
      
      if (onStatusChange) {
        onStatusChange();
      }
    } catch (error) {
      console.error('添加评论失败:', error);
      message.error(t('feedback.manager.commentError'));
    } finally {
      setSubmitting(false);
    }
  };

  // 渲染反馈基本信息
  const renderBasicInfo = () => {
    return (
      <Card className="feedback-detail-card">
        <Descriptions title={feedback.title} bordered column={2}>
          <Descriptions.Item label={t('feedback.manager.id')}>
            {feedback.id}
          </Descriptions.Item>
          
          <Descriptions.Item label={t('feedback.manager.status')}>
            <Tag color={getStatusColor(currentStatus)}>
              {getStatusText(currentStatus)}
            </Tag>
          </Descriptions.Item>
          
          <Descriptions.Item label={t('feedback.manager.type')}>
            <Tag color="blue">{t(`feedback.title.${feedback.type}`)}</Tag>
            {feedback.subType && (
              <Tag color="cyan">{t(`feedback.${feedback.type}.subType.${feedback.subType}`)}</Tag>
            )}
          </Descriptions.Item>
          
          <Descriptions.Item label={t('feedback.manager.feedbackType')}>
            <Tag icon={getFeedbackTypeIcon(feedback.feedbackType)}>
              {t(`feedback.type.${feedback.feedbackType}`)}
            </Tag>
          </Descriptions.Item>
          
          <Descriptions.Item label={t('feedback.manager.date')} span={2}>
            {new Date(feedback.createdAt!).toLocaleString()}
          </Descriptions.Item>
          
          <Descriptions.Item label={t('feedback.form.satisfaction')}>
            {feedback.satisfaction ? (
              <Rate disabled defaultValue={feedback.satisfaction} />
            ) : (
              '-'
            )}
          </Descriptions.Item>
          
          <Descriptions.Item label={t('feedback.form.allowContact')}>
            {feedback.allowContact ? (
              <Tag color="green">{t('common.yes')}</Tag>
            ) : (
              <Tag color="red">{t('common.no')}</Tag>
            )}
          </Descriptions.Item>
        </Descriptions>
      </Card>
    );
  };

  // 渲染反馈内容
  const renderContent = () => {
    return (
      <Card className="feedback-detail-card" title={t('feedback.manager.content')}>
        <div className="feedback-detail-section">
          <Title level={5}>{t('feedback.form.description')}</Title>
          <Paragraph>{feedback.description}</Paragraph>
        </div>
        
        {feedback.reproductionSteps && (
          <div className="feedback-detail-section">
            <Title level={5}>{t('feedback.form.reproductionSteps')}</Title>
            <Paragraph>{feedback.reproductionSteps}</Paragraph>
          </div>
        )}
        
        {feedback.suggestions && (
          <div className="feedback-detail-section">
            <Title level={5}>{t('feedback.form.suggestions')}</Title>
            <Paragraph>{feedback.suggestions}</Paragraph>
          </div>
        )}
      </Card>
    );
  };

  // 渲染上下文数据
  const renderContextData = () => {
    if (!feedback.contextData) return null;
    
    return (
      <Card className="feedback-detail-card" title={t('feedback.manager.contextData')}>
        <Collapse ghost>
          <Panel header={t('feedback.manager.userContext')} key="userContext">
            <Descriptions bordered size="small" column={1}>
              <Descriptions.Item label={t('feedback.manager.browser')}>
                {feedback.browser}
              </Descriptions.Item>
              
              <Descriptions.Item label={t('feedback.manager.screenSize')}>
                {feedback.screenSize}
              </Descriptions.Item>
              
              {feedback.contextData.location && (
                <Descriptions.Item label={t('feedback.manager.location')}>
                  {feedback.contextData.location}
                </Descriptions.Item>
              )}
            </Descriptions>
          </Panel>
          
          <Panel header={t('feedback.manager.editorContext')} key="editorContext">
            <Descriptions bordered size="small" column={1}>
              {feedback.contextData.selectedEntity && (
                <Descriptions.Item label={t('feedback.manager.selectedEntity')}>
                  {feedback.contextData.selectedEntity.name} (ID: {feedback.contextData.selectedEntity.id})
                </Descriptions.Item>
              )}
              
              {feedback.contextData.activeTool && (
                <Descriptions.Item label={t('feedback.manager.activeTool')}>
                  {feedback.contextData.activeTool}
                </Descriptions.Item>
              )}
              
              {feedback.contextData.activePanels && (
                <Descriptions.Item label={t('feedback.manager.activePanels')}>
                  {Array.isArray(feedback.contextData.activePanels) 
                    ? feedback.contextData.activePanels.join(', ')
                    : feedback.contextData.activePanels}
                </Descriptions.Item>
              )}
            </Descriptions>
          </Panel>
          
          {feedback.contextData.recentOperations && feedback.contextData.recentOperations.length > 0 && (
            <Panel header={t('feedback.manager.recentOperations')} key="recentOperations">
              <Timeline>
                {feedback.contextData.recentOperations.map((op: any, index: number) => (
                  <Timeline.Item key={index}>
                    <Text strong>{op.type}</Text>
                    <br />
                    <Text type="secondary">{new Date(op.timestamp).toLocaleTimeString()}</Text>
                    {op.details && <div>{JSON.stringify(op.details)}</div>}
                  </Timeline.Item>
                ))}
              </Timeline>
            </Panel>
          )}
        </Collapse>
      </Card>
    );
  };

  // 渲染截图
  const renderScreenshots = () => {
    if (!feedback.screenshots || feedback.screenshots.length === 0) return null;
    
    return (
      <Card className="feedback-detail-card" title={t('feedback.form.screenshots')}>
        <div className="feedback-screenshots">
          <Image.PreviewGroup>
            {Array.isArray(feedback.screenshots) ? (
              feedback.screenshots.map((screenshot, index) => (
                <Image
                  key={index}
                  src={typeof screenshot === 'string' ? screenshot : URL.createObjectURL(screenshot)}
                  alt={`Screenshot ${index + 1}`}
                  style={{ maxWidth: '100%', marginBottom: 16 }}
                />
              ))
            ) : (
              <Image
                src={typeof feedback.screenshots === 'string' ? feedback.screenshots : URL.createObjectURL(feedback.screenshots)}
                alt="Screenshot"
                style={{ maxWidth: '100%' }}
              />
            )}
          </Image.PreviewGroup>
        </div>
      </Card>
    );
  };

  // 渲染状态管理
  const renderStatusManagement = () => {
    return (
      <Card className="feedback-detail-card" title={t('feedback.manager.statusManagement')}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text>{t('feedback.manager.currentStatus')}:</Text>
            <Tag color={getStatusColor(currentStatus)} style={{ marginLeft: 8 }}>
              {getStatusText(currentStatus)}
            </Tag>
          </div>
          
          <div>
            <Text>{t('feedback.manager.changeStatus')}:</Text>
            <div className="status-buttons">
              <Space>
                <Button
                  type={currentStatus === 'new' ? 'primary' : 'default'}
                  onClick={() => updateStatus('new')}
                  disabled={currentStatus === 'new'}
                  loading={submitting}
                >
                  {t('feedback.manager.statusNew')}
                </Button>
                
                <Button
                  type={currentStatus === 'inProgress' ? 'primary' : 'default'}
                  onClick={() => updateStatus('inProgress')}
                  disabled={currentStatus === 'inProgress'}
                  loading={submitting}
                >
                  {t('feedback.manager.statusInProgress')}
                </Button>
                
                <Button
                  type={currentStatus === 'resolved' ? 'primary' : 'default'}
                  onClick={() => updateStatus('resolved')}
                  disabled={currentStatus === 'resolved'}
                  loading={submitting}
                >
                  {t('feedback.manager.statusResolved')}
                </Button>
                
                <Button
                  type={currentStatus === 'closed' ? 'primary' : 'default'}
                  onClick={() => updateStatus('closed')}
                  disabled={currentStatus === 'closed'}
                  loading={submitting}
                >
                  {t('feedback.manager.statusClosed')}
                </Button>
              </Space>
            </div>
          </div>
        </Space>
      </Card>
    );
  };

  // 渲染评论
  const renderComments = () => {
    return (
      <Card className="feedback-detail-card" title={t('feedback.manager.comments')}>
        <div className="feedback-comments">
          {feedback.comments && feedback.comments.length > 0 ? (
            <Timeline>
              {feedback.comments.map((comment, index) => (
                <Timeline.Item key={index}>
                  <div className="comment-item">
                    <div className="comment-header">
                      <Text strong>{comment.userName || t('feedback.manager.staff')}</Text>
                      <Text type="secondary">{new Date(comment.timestamp).toLocaleString()}</Text>
                    </div>
                    <div className="comment-content">
                      <Paragraph>{comment.content}</Paragraph>
                    </div>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          ) : (
            <Text type="secondary">{t('feedback.manager.noComments')}</Text>
          )}
        </div>
        
        <Divider />
        
        <div className="add-comment">
          <TextArea
            rows={3}
            value={commentText}
            onChange={(e) => setCommentText(e.target.value)}
            placeholder={t('feedback.manager.addCommentPlaceholder') || ''}
          />
          <div className="comment-actions">
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={addComment}
              disabled={!commentText.trim()}
              loading={submitting}
            >
              {t('feedback.manager.addComment')}
            </Button>
          </div>
        </div>
      </Card>
    );
  };

  return (
    <div className="feedback-detail">
      {renderBasicInfo()}
      {renderContent()}
      {renderContextData()}
      {renderScreenshots()}
      {renderStatusManagement()}
      {renderComments()}
    </div>
  );
};

export default FeedbackDetail;
