/**
 * 空间信息系统主组件
 * 整合地图视图、地理数据编辑器和空间分析面板
 */
import React, { useState, useCallback } from 'react';
import { Layout, Card, Tabs, Button, Space, message, Modal } from 'antd';
import { 
  GlobalOutlined,
  EditOutlined,
  BarChartOutlined,
  SettingOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

import { MapView, GeographicCoordinate } from './MapView';
import { GeospatialEditor, GeospatialData } from './GeospatialEditor';
import { SpatialAnalysisPanel, AnalysisTask } from './SpatialAnalysisPanel';
import './SpatialInfoSystem.scss';

const { Content, Sider } = Layout;
const { TabPane } = Tabs;

/**
 * 空间信息系统配置接口
 */
export interface SpatialInfoSystemConfig {
  defaultCenter?: GeographicCoordinate;
  defaultZoom?: number;
  defaultMapType?: 'osm' | 'satellite' | 'terrain';
  enableAnalysis?: boolean;
  enableEditor?: boolean;
  siderWidth?: number;
}

/**
 * 空间信息系统属性接口
 */
export interface SpatialInfoSystemProps {
  config?: SpatialInfoSystemConfig;
  onDataChange?: (data: GeospatialData[]) => void;
  onAnalysisComplete?: (task: AnalysisTask, result: any) => void;
  className?: string;
}

/**
 * 空间信息系统主组件
 */
export const SpatialInfoSystem: React.FC<SpatialInfoSystemProps> = ({
  config = {},
  onDataChange,
  onAnalysisComplete,
  className
}) => {
  // 配置默认值
  const {
    defaultCenter = { longitude: 116.404, latitude: 39.915 },
    defaultZoom = 10,
    defaultMapType = 'osm',
    enableAnalysis = true,
    enableEditor = true,
    siderWidth = 400
  } = config;

  // 状态管理
  const [mapCenter, setMapCenter] = useState<GeographicCoordinate>(defaultCenter);
  const [mapZoom, setMapZoom] = useState<number>(defaultZoom);
  const [mapType, setMapType] = useState<string>(defaultMapType);
  const [spatialData, setSpatialData] = useState<GeospatialData[]>([]);
  const [selectedFeature, setSelectedFeature] = useState<GeospatialData | null>(null);
  const [activeTab, setActiveTab] = useState<string>('map');
  const [siderCollapsed, setSiderCollapsed] = useState(false);
  const [isInfoModalVisible, setIsInfoModalVisible] = useState(false);

  /**
   * 处理地图位置变化
   */
  const handleLocationChange = useCallback((location: GeographicCoordinate) => {
    setMapCenter(location);
  }, []);

  /**
   * 处理地图缩放变化
   */
  const handleZoomChange = useCallback((zoom: number) => {
    setMapZoom(zoom);
  }, []);

  /**
   * 处理地图类型变化
   */
  const handleMapTypeChange = useCallback((type: string) => {
    setMapType(type);
  }, []);

  /**
   * 处理空间数据变化
   */
  const handleDataChange = useCallback((data: GeospatialData[]) => {
    setSpatialData(data);
    onDataChange?.(data);
  }, [onDataChange]);

  /**
   * 处理要素选择
   */
  const handleFeatureSelect = useCallback((feature: GeospatialData | null) => {
    setSelectedFeature(feature);
    
    // 如果选择了要素，将地图中心移动到要素位置
    if (feature && feature.coordinates.length > 0) {
      setMapCenter(feature.coordinates[0]);
    }
  }, []);

  /**
   * 处理分析任务运行
   */
  const handleAnalysisRun = useCallback(async (task: AnalysisTask): Promise<any> => {
    // 模拟分析过程
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 模拟分析结果
    const result = {
      type: 'FeatureCollection',
      features: [
        {
          type: 'Feature',
          geometry: {
            type: 'Polygon',
            coordinates: [[
              [mapCenter.longitude - 0.01, mapCenter.latitude - 0.01],
              [mapCenter.longitude + 0.01, mapCenter.latitude - 0.01],
              [mapCenter.longitude + 0.01, mapCenter.latitude + 0.01],
              [mapCenter.longitude - 0.01, mapCenter.latitude + 0.01],
              [mapCenter.longitude - 0.01, mapCenter.latitude - 0.01]
            ]]
          },
          properties: {
            name: `${task.type}分析结果`,
            analysisType: task.type,
            parameters: task.parameters
          }
        }
      ]
    };
    
    onAnalysisComplete?.(task, result);
    return result;
  }, [mapCenter, onAnalysisComplete]);

  /**
   * 处理分析任务删除
   */
  const handleTaskDelete = useCallback((taskId: string) => {
    message.info(`任务 ${taskId} 已删除`);
  }, []);

  /**
   * 处理分析结果导出
   */
  const handleResultExport = useCallback((task: AnalysisTask) => {
    const blob = new Blob([JSON.stringify(task.result, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${task.name}_result.geojson`;
    a.click();
    URL.revokeObjectURL(url);
  }, []);

  /**
   * 获取可用图层列表
   */
  const getAvailableLayers = useCallback(() => {
    return spatialData.map(feature => ({
      id: feature.id,
      name: feature.name,
      type: feature.type
    }));
  }, [spatialData]);

  /**
   * 渲染侧边栏内容
   */
  const renderSiderContent = () => {
    switch (activeTab) {
      case 'editor':
        return enableEditor ? (
          <GeospatialEditor
            data={spatialData}
            onDataChange={handleDataChange}
            onFeatureSelect={handleFeatureSelect}
          />
        ) : null;
        
      case 'analysis':
        return enableAnalysis ? (
          <SpatialAnalysisPanel
            availableLayers={getAvailableLayers()}
            onAnalysisRun={handleAnalysisRun}
            onTaskDelete={handleTaskDelete}
            onResultExport={handleResultExport}
          />
        ) : null;
        
      default:
        return (
          <Card title="地图信息" size="small">
            <div className="map-info-panel">
              <div className="info-item">
                <span className="label">中心坐标:</span>
                <span className="value">
                  {mapCenter.longitude.toFixed(6)}, {mapCenter.latitude.toFixed(6)}
                </span>
              </div>
              <div className="info-item">
                <span className="label">缩放级别:</span>
                <span className="value">{mapZoom}</span>
              </div>
              <div className="info-item">
                <span className="label">地图类型:</span>
                <span className="value">{mapType}</span>
              </div>
              <div className="info-item">
                <span className="label">要素数量:</span>
                <span className="value">{spatialData.length}</span>
              </div>
              {selectedFeature && (
                <div className="selected-feature">
                  <h4>选中要素</h4>
                  <div className="info-item">
                    <span className="label">名称:</span>
                    <span className="value">{selectedFeature.name}</span>
                  </div>
                  <div className="info-item">
                    <span className="label">类型:</span>
                    <span className="value">{selectedFeature.type}</span>
                  </div>
                  <div className="info-item">
                    <span className="label">坐标数:</span>
                    <span className="value">{selectedFeature.coordinates.length}</span>
                  </div>
                </div>
              )}
            </div>
          </Card>
        );
    }
  };

  return (
    <div className={`spatial-info-system ${className || ''}`}>
      <Layout style={{ height: '100%' }}>
        {/* 主地图区域 */}
        <Content className="map-content">
          <Card 
            title={
              <Space>
                <GlobalOutlined />
                空间信息系统
              </Space>
            }
            extra={
              <Space>
                <Button 
                  icon={<InfoCircleOutlined />}
                  onClick={() => setIsInfoModalVisible(true)}
                  size="small"
                >
                  系统信息
                </Button>
                <Button 
                  icon={<SettingOutlined />}
                  size="small"
                >
                  设置
                </Button>
              </Space>
            }
            styles={{ body: { padding: 0, height: 'calc(100vh - 120px)' } }}
          >
            <MapView
              center={mapCenter}
              zoom={mapZoom}
              mapType={mapType as any}
              width="100%"
              height="100%"
              showControls={true}
              showCoordinates={true}
              onLocationChange={handleLocationChange}
              onZoomChange={handleZoomChange}
              onMapTypeChange={handleMapTypeChange}
            />
          </Card>
        </Content>

        {/* 侧边栏 */}
        <Sider 
          width={siderWidth}
          collapsible
          collapsed={siderCollapsed}
          onCollapse={setSiderCollapsed}
          className="spatial-sider"
          theme="light"
        >
          <div className="sider-content">
            <Tabs 
              activeKey={activeTab}
              onChange={setActiveTab}
              size="small"
              tabPosition="top"
            >
              <TabPane 
                tab={
                  <span>
                    <GlobalOutlined />
                    地图
                  </span>
                } 
                key="map"
              />
              
              {enableEditor && (
                <TabPane 
                  tab={
                    <span>
                      <EditOutlined />
                      编辑
                    </span>
                  } 
                  key="editor"
                />
              )}
              
              {enableAnalysis && (
                <TabPane 
                  tab={
                    <span>
                      <BarChartOutlined />
                      分析
                    </span>
                  } 
                  key="analysis"
                />
              )}
            </Tabs>
            
            <div className="tab-content">
              {renderSiderContent()}
            </div>
          </div>
        </Sider>
      </Layout>

      {/* 系统信息模态框 */}
      <Modal
        title="空间信息系统信息"
        open={isInfoModalVisible}
        onCancel={() => setIsInfoModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsInfoModalVisible(false)}>
            关闭
          </Button>
        ]}
      >
        <div className="system-info">
          <div className="info-section">
            <h4>系统状态</h4>
            <div className="info-item">
              <span className="label">版本:</span>
              <span className="value">1.0.0</span>
            </div>
            <div className="info-item">
              <span className="label">地图引擎:</span>
              <span className="value">DL Spatial Engine</span>
            </div>
            <div className="info-item">
              <span className="label">坐标系统:</span>
              <span className="value">WGS84</span>
            </div>
          </div>
          
          <div className="info-section">
            <h4>功能模块</h4>
            <div className="info-item">
              <span className="label">地图显示:</span>
              <span className="value">✓ 已启用</span>
            </div>
            <div className="info-item">
              <span className="label">数据编辑:</span>
              <span className="value">{enableEditor ? '✓ 已启用' : '✗ 未启用'}</span>
            </div>
            <div className="info-item">
              <span className="label">空间分析:</span>
              <span className="value">{enableAnalysis ? '✓ 已启用' : '✗ 未启用'}</span>
            </div>
          </div>
          
          <div className="info-section">
            <h4>数据统计</h4>
            <div className="info-item">
              <span className="label">空间要素:</span>
              <span className="value">{spatialData.length} 个</span>
            </div>
            <div className="info-item">
              <span className="label">点要素:</span>
              <span className="value">{spatialData.filter(f => f.type === 'Point').length} 个</span>
            </div>
            <div className="info-item">
              <span className="label">线要素:</span>
              <span className="value">{spatialData.filter(f => f.type === 'LineString').length} 个</span>
            </div>
            <div className="info-item">
              <span className="label">面要素:</span>
              <span className="value">{spatialData.filter(f => f.type === 'Polygon').length} 个</span>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
};
