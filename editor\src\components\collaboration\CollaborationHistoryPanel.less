/**
 * 协作历史面板样式
 */

.collaboration-history-panel {
  .ant-modal-body {
    padding: 16px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .versions-content {
    display: flex;
    gap: 16px;

    .versions-table {
      flex: 1;
    }

    .versions-timeline {
      flex: 1;
      max-height: 500px;
      overflow-y: auto;
    }
  }

  .version-timeline-item {
    .version-header {
      margin-bottom: 8px;
    }

    .version-meta {
      margin-top: 8px;
    }
  }

  .change-timeline-item {
    .affected-entities {
      margin-top: 8px;
    }
  }

  .branches-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;

    .branch-card {
      .branch-header {
        margin-bottom: 8px;
      }

      .branch-meta {
        margin-top: 8px;
      }
    }
  }

  .merge-requests-container {
    .merge-request-card {
      margin-bottom: 16px;

      .mr-header {
        margin-bottom: 8px;
      }

      .mr-meta {
        margin-top: 8px;
      }

      .mr-conflicts {
        margin-top: 8px;
        padding: 8px;
        background: #fff2f0;
        border: 1px solid #ffccc7;
        border-radius: 4px;
      }
    }
  }

  .collaboration-stats {
    .contributors-list {
      .contributor-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .contributor-info {
          flex: 1;
          margin-left: 12px;
        }
      }
    }
  }

  .ant-timeline {
    .ant-timeline-item {
      .ant-timeline-item-content {
        margin-left: 20px;
      }
    }
  }

  .ant-table {
    .ant-table-tbody {
      .ant-table-row {
        &:hover {
          background-color: #f5f5f5;
        }
      }
    }

    .ant-table-cell {
      padding: 8px 12px;
    }
  }

  .ant-card {
    .ant-card-body {
      padding: 16px;
    }

    &.branch-card,
    &.merge-request-card {
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }
    }
  }
}

// 深色主题
.dark-theme {
  .collaboration-history-panel {
    .ant-card {
      background: #2d2d2d;
      border-color: #404040;

      .ant-card-body {
        background: #2d2d2d;
        color: #cccccc;
      }
    }

    .ant-table {
      background: #2d2d2d;

      .ant-table-thead {
        .ant-table-cell {
          background: #404040;
          color: #ffffff;
          border-bottom-color: #555555;
        }
      }

      .ant-table-tbody {
        .ant-table-row {
          background: #2d2d2d;
          color: #cccccc;

          &:hover {
            background-color: #404040;
          }

          .ant-table-cell {
            border-bottom-color: #404040;
          }
        }
      }
    }

    .ant-timeline {
      .ant-timeline-item {
        .ant-timeline-item-tail {
          border-left-color: #404040;
        }

        .ant-timeline-item-head {
          background: #2d2d2d;
          border-color: #404040;
        }

        .ant-timeline-item-content {
          color: #cccccc;
        }
      }
    }

    .merge-requests-container {
      .merge-request-card {
        .mr-conflicts {
          background: #2d1b1b;
          border-color: #5c2626;
        }
      }
    }

    .collaboration-stats {
      .contributors-list {
        .contributor-item {
          border-bottom-color: #404040;
        }
      }
    }
  }
}

// 紧凑模式
.compact-theme {
  .collaboration-history-panel {
    .ant-modal-body {
      padding: 12px;
    }

    .versions-content {
      gap: 12px;
    }

    .ant-card {
      .ant-card-body {
        padding: 12px;
      }
    }

    .ant-table {
      .ant-table-cell {
        padding: 6px 8px;
        font-size: 12px;
      }
    }

    .version-timeline-item,
    .change-timeline-item {
      font-size: 12px;

      .version-header,
      .version-meta,
      .affected-entities {
        margin: 4px 0;
      }
    }

    .branches-container {
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 12px;
    }

    .merge-requests-container {
      .merge-request-card {
        margin-bottom: 12px;
      }
    }

    .collaboration-stats {
      .contributors-list {
        .contributor-item {
          padding: 8px 0;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .collaboration-history-panel {
    .ant-modal {
      width: 95% !important;
      max-width: none;
    }

    .versions-content {
      flex-direction: column;
    }

    .branches-container {
      grid-template-columns: 1fr;
    }

    .ant-table {
      .ant-table-cell {
        padding: 4px 6px;
        font-size: 12px;
      }
    }

    .collaboration-stats {
      .ant-row {
        .ant-col {
          margin-bottom: 12px;
        }
      }

      .contributors-list {
        .contributor-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;

          .ant-space {
            width: 100%;
            justify-content: space-between;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .collaboration-history-panel {
    .ant-tabs {
      .ant-tabs-tab {
        padding: 8px 12px;
        font-size: 12px;
      }
    }

    .ant-timeline {
      .ant-timeline-item {
        .ant-timeline-item-content {
          margin-left: 16px;
          font-size: 12px;
        }
      }
    }

    .collaboration-stats {
      .ant-statistic {
        .ant-statistic-title {
          font-size: 12px;
        }

        .ant-statistic-content {
          font-size: 16px;
        }
      }
    }
  }
}

// 动画效果
.collaboration-history-panel {
  .ant-card {
    transition: all 0.3s ease;
  }

  .ant-table {
    .ant-table-row {
      transition: background-color 0.2s ease;
    }
  }

  .ant-timeline {
    .ant-timeline-item {
      transition: all 0.2s ease;

      &:hover {
        .ant-timeline-item-content {
          background-color: #f9f9f9;
          padding: 8px;
          border-radius: 4px;
        }
      }
    }
  }

  .contributor-item {
    transition: all 0.2s ease;

    &:hover {
      background-color: #f9f9f9;
      padding-left: 8px;
      border-radius: 4px;
    }
  }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
  .collaboration-history-panel {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .collaboration-history-panel {
    .ant-table {
      border: 2px solid #000;

      .ant-table-cell {
        border: 1px solid #000;
      }
    }

    .ant-card {
      border: 2px solid #000;
    }

    .ant-timeline {
      .ant-timeline-item {
        .ant-timeline-item-tail {
          border-left: 2px solid #000;
        }

        .ant-timeline-item-head {
          border: 2px solid #000;
        }
      }
    }

    .contributor-item {
      border-bottom: 2px solid #000;
    }
  }
}
