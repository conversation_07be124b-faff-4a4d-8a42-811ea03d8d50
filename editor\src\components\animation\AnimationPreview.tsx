/**
 * 动画预览组件
 */
import { useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Card, Button, Space } from 'antd';
import { PlayCircleOutlined, PauseCircleOutlined, StopOutlined } from '@ant-design/icons';

/**
 * 动画预览属性
 */
interface AnimationPreviewProps {
  /** 当前时间 */
  currentTime: number;
  /** 持续时间 */
  duration: number;
  /** 是否播放中 */
  isPlaying: boolean;
  /** 混合层 */
  layers: any[];
  /** 时间更新回调 */
  onTimeUpdate: (time: number) => void;
  /** 播放状态改变回调 */
  onPlayStateChange: (isPlaying: boolean) => void;
}

/**
 * 动画预览组件
 */
export const AnimationPreview = forwardRef<any, AnimationPreviewProps>(({
  currentTime,
  duration,
  isPlaying,
  layers,
  onTimeUpdate,
  onPlayStateChange
}, ref) => {
  // 引用
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number>();
  const lastTimeRef = useRef<number>(0);

  // 暴露方法
  useImperativeHandle(ref, () => ({
    play: () => {
      onPlayStateChange(true);
    },
    pause: () => {
      onPlayStateChange(false);
    },
    stop: () => {
      onPlayStateChange(false);
      onTimeUpdate(0);
    },
    setCurrentTime: (time: number) => {
      onTimeUpdate(time);
    }
  }));

  // 动画循环
  useEffect(() => {
    if (isPlaying) {
      const animate = (timestamp: number) => {
        if (lastTimeRef.current === 0) {
          lastTimeRef.current = timestamp;
        }

        const deltaTime = (timestamp - lastTimeRef.current) / 1000;
        lastTimeRef.current = timestamp;

        const newTime = currentTime + deltaTime;
        if (newTime >= duration) {
          onTimeUpdate(0); // 循环播放
        } else {
          onTimeUpdate(newTime);
        }

        animationFrameRef.current = requestAnimationFrame(animate);
      };

      animationFrameRef.current = requestAnimationFrame(animate);
    } else {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      lastTimeRef.current = 0;
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [isPlaying, currentTime, duration, onTimeUpdate]);

  // 渲染预览
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 绘制背景
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 绘制网格
    ctx.strokeStyle = '#e0e0e0';
    ctx.lineWidth = 1;
    const gridSize = 20;
    
    for (let x = 0; x <= canvas.width; x += gridSize) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, canvas.height);
      ctx.stroke();
    }
    
    for (let y = 0; y <= canvas.height; y += gridSize) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(canvas.width, y);
      ctx.stroke();
    }

    // 绘制中心点
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    
    ctx.fillStyle = '#1890ff';
    ctx.beginPath();
    ctx.arc(centerX, centerY, 5, 0, Math.PI * 2);
    ctx.fill();

    // 绘制动画状态信息
    ctx.fillStyle = '#333';
    ctx.font = '14px Arial';
    ctx.fillText(`时间: ${currentTime.toFixed(2)}s / ${duration.toFixed(2)}s`, 10, 20);
    ctx.fillText(`混合层数量: ${layers.length}`, 10, 40);
    ctx.fillText(`状态: ${isPlaying ? '播放中' : '暂停'}`, 10, 60);

    // 绘制简单的动画效果（示例）
    if (layers.length > 0) {
      const progress = currentTime / duration;
      const radius = 20 + Math.sin(progress * Math.PI * 2) * 10;
      
      ctx.fillStyle = '#52c41a';
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
      ctx.fill();
    }
  }, [currentTime, duration, isPlaying, layers]);

  // 播放控制
  const handlePlay = () => {
    onPlayStateChange(!isPlaying);
  };

  const handleStop = () => {
    onPlayStateChange(false);
    onTimeUpdate(0);
  };

  return (
    <Card 
      title="动画预览" 
      size="small"
      extra={
        <Space>
          <Button 
            icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />} 
            onClick={handlePlay}
            type={isPlaying ? "default" : "primary"}
          />
          <Button icon={<StopOutlined />} onClick={handleStop} />
        </Space>
      }
    >
      <div className="animation-preview-container">
        <canvas
          ref={canvasRef}
          width={400}
          height={300}
          style={{
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            display: 'block',
            margin: '0 auto'
          }}
        />
      </div>
    </Card>
  );
});

export default AnimationPreview;
