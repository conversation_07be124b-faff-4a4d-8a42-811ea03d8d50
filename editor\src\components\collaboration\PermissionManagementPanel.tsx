/**
 * 权限管理面板组件
 * 提供用户权限和角色管理界面
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Select,
  Space,
  Tag,
  Avatar,
  Modal,
  Form,
  Input,
  Checkbox,
  Typography,
  Tabs,
  Badge,
  Tooltip,
  Popconfirm,
  message,
  Timeline,
  Descriptions
} from 'antd';
import {
  UserOutlined,
  SettingOutlined,
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  CrownOutlined,
  ShieldOutlined,
  EyeOutlined,
  TeamOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import CollaborationPermissionService, {
  UserPermissions,
  UserRole,
  Permission,
  PermissionRequest,
  PermissionChange
} from '../../services/CollaborationPermissionService';
import './PermissionManagementPanel.less';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

interface PermissionManagementPanelProps {
  visible: boolean;
  onClose: () => void;
  currentUserId: string;
}

const PermissionManagementPanel: React.FC<PermissionManagementPanelProps> = ({
  visible,
  onClose,
  currentUserId
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('users');
  const [users, setUsers] = useState<UserPermissions[]>([]);
  const [pendingRequests, setPendingRequests] = useState<PermissionRequest[]>([]);
  const [permissionHistory, setPermissionHistory] = useState<PermissionChange[]>([]);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserPermissions | null>(null);
  const [form] = Form.useForm();

  const permissionService = CollaborationPermissionService.getInstance();

  useEffect(() => {
    if (visible) {
      loadData();
    }
  }, [visible]);

  const loadData = () => {
    setUsers(permissionService.getAllUserPermissions());
    setPendingRequests(permissionService.getPendingRequests());
    setPermissionHistory(permissionService.getPermissionHistory());
  };

  // 获取角色图标
  const getRoleIcon = (role: UserRole) => {
    switch (role) {
      case UserRole.OWNER:
        return <CrownOutlined style={{ color: '#faad14' }} />;
      case UserRole.ADMIN:
        return <ShieldOutlined style={{ color: '#ff4d4f' }} />;
      case UserRole.EDITOR:
        return <EditOutlined style={{ color: '#1890ff' }} />;
      case UserRole.COLLABORATOR:
        return <TeamOutlined style={{ color: '#52c41a' }} />;
      case UserRole.VIEWER:
        return <EyeOutlined style={{ color: '#666666' }} />;
      default:
        return <UserOutlined style={{ color: '#999999' }} />;
    }
  };

  // 获取角色颜色
  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case UserRole.OWNER:
        return 'gold';
      case UserRole.ADMIN:
        return 'red';
      case UserRole.EDITOR:
        return 'blue';
      case UserRole.COLLABORATOR:
        return 'green';
      case UserRole.VIEWER:
        return 'default';
      default:
        return 'default';
    }
  };

  // 处理角色变更
  const handleRoleChange = (userId: string, newRole: UserRole) => {
    try {
      permissionService.setUserPermissions(userId, newRole, undefined, currentUserId);
      loadData();
      message.success(t('collaboration.permissions.roleUpdated'));
    } catch (error) {
      message.error(t('collaboration.permissions.updateFailed'));
    }
  };

  // 处理权限编辑
  const handleEditPermissions = (user: UserPermissions) => {
    setSelectedUser(user);
    form.setFieldsValue({
      role: user.role,
      permissions: user.permissions
    });
    setEditModalVisible(true);
  };

  // 保存权限编辑
  const handleSavePermissions = async () => {
    try {
      const values = await form.validateFields();
      if (selectedUser) {
        permissionService.setUserPermissions(
          selectedUser.userId,
          values.role,
          values.permissions,
          currentUserId
        );
        loadData();
        setEditModalVisible(false);
        message.success(t('collaboration.permissions.permissionsUpdated'));
      }
    } catch (error) {
      message.error(t('collaboration.permissions.updateFailed'));
    }
  };

  // 移除用户
  const handleRemoveUser = (userId: string) => {
    try {
      permissionService.removeUser(userId, currentUserId);
      loadData();
      message.success(t('collaboration.permissions.userRemoved'));
    } catch (error) {
      message.error(t('collaboration.permissions.removeFailed'));
    }
  };

  // 审批权限请求
  const handleApproveRequest = (requestId: string, approved: boolean) => {
    try {
      permissionService.approvePermissionRequest(requestId, currentUserId, approved);
      loadData();
      message.success(approved ? 
        t('collaboration.permissions.requestApproved') : 
        t('collaboration.permissions.requestDenied')
      );
    } catch (error) {
      message.error(t('collaboration.permissions.reviewFailed'));
    }
  };

  // 用户表格列定义
  const userColumns = [
    {
      title: t('collaboration.permissions.user'),
      dataIndex: 'userId',
      key: 'userId',
      render: (userId: string) => (
        <Space>
          <Avatar icon={<UserOutlined />} />
          <Text>{userId}</Text>
        </Space>
      )
    },
    {
      title: t('collaboration.permissions.role'),
      dataIndex: 'role',
      key: 'role',
      render: (role: UserRole, record: UserPermissions) => (
        <Space>
          {getRoleIcon(role)}
          <Select
            value={role}
            onChange={(newRole) => handleRoleChange(record.userId, newRole)}
            style={{ width: 120 }}
            disabled={!permissionService.hasPermission(currentUserId, Permission.MANAGE_PERMISSIONS)}
          >
            {Object.values(UserRole).map(r => (
              <Option key={r} value={r}>
                <Space>
                  {getRoleIcon(r)}
                  {r}
                </Space>
              </Option>
            ))}
          </Select>
        </Space>
      )
    },
    {
      title: t('collaboration.permissions.permissions'),
      dataIndex: 'permissions',
      key: 'permissions',
      render: (permissions: Permission[]) => (
        <Space wrap>
          {permissions.slice(0, 3).map(permission => (
            <Tag key={permission} size="small">{permission}</Tag>
          ))}
          {permissions.length > 3 && (
            <Tag size="small">+{permissions.length - 3}</Tag>
          )}
        </Space>
      )
    },
    {
      title: t('collaboration.permissions.grantedAt'),
      dataIndex: 'grantedAt',
      key: 'grantedAt',
      render: (timestamp: number) => new Date(timestamp).toLocaleString()
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (record: UserPermissions) => (
        <Space>
          <Tooltip title={t('collaboration.permissions.editPermissions')}>
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditPermissions(record)}
              disabled={!permissionService.hasPermission(currentUserId, Permission.MANAGE_PERMISSIONS)}
            />
          </Tooltip>
          <Popconfirm
            title={t('collaboration.permissions.confirmRemove')}
            onConfirm={() => handleRemoveUser(record.userId)}
            disabled={!permissionService.hasPermission(currentUserId, Permission.KICK_USERS)}
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              disabled={!permissionService.hasPermission(currentUserId, Permission.KICK_USERS)}
            />
          </Popconfirm>
        </Space>
      )
    }
  ];

  // 权限请求表格列定义
  const requestColumns = [
    {
      title: t('collaboration.permissions.requester'),
      dataIndex: 'userId',
      key: 'userId',
      render: (userId: string) => (
        <Space>
          <Avatar icon={<UserOutlined />} />
          <Text>{userId}</Text>
        </Space>
      )
    },
    {
      title: t('collaboration.permissions.requestedPermissions'),
      dataIndex: 'requestedPermissions',
      key: 'requestedPermissions',
      render: (permissions: Permission[]) => (
        <Space wrap>
          {permissions.map(permission => (
            <Tag key={permission} color="blue">{permission}</Tag>
          ))}
        </Space>
      )
    },
    {
      title: t('collaboration.permissions.reason'),
      dataIndex: 'reason',
      key: 'reason'
    },
    {
      title: t('collaboration.permissions.requestTime'),
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (timestamp: number) => new Date(timestamp).toLocaleString()
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (record: PermissionRequest) => (
        <Space>
          <Button
            type="primary"
            size="small"
            onClick={() => handleApproveRequest(record.id, true)}
          >
            {t('collaboration.permissions.approve')}
          </Button>
          <Button
            size="small"
            onClick={() => handleApproveRequest(record.id, false)}
          >
            {t('collaboration.permissions.deny')}
          </Button>
        </Space>
      )
    }
  ];

  return (
    <Modal
      title={
        <Space>
          <SettingOutlined />
          {t('collaboration.permissions.permissionManagement')}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1000}
      footer={null}
      className="permission-management-panel"
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <Space>
              <TeamOutlined />
              {t('collaboration.permissions.users')} ({users.length})
            </Space>
          }
          key="users"
        >
          <Card>
            <Table
              columns={userColumns}
              dataSource={users}
              rowKey="userId"
              pagination={{ pageSize: 10 }}
              size="small"
            />
          </Card>
        </TabPane>

        <TabPane
          tab={
            <Space>
              <Badge count={pendingRequests.length}>
                <SettingOutlined />
              </Badge>
              {t('collaboration.permissions.requests')}
            </Space>
          }
          key="requests"
        >
          <Card>
            <Table
              columns={requestColumns}
              dataSource={pendingRequests}
              rowKey="id"
              pagination={{ pageSize: 10 }}
              size="small"
              locale={{ emptyText: t('collaboration.permissions.noRequests') }}
            />
          </Card>
        </TabPane>

        <TabPane
          tab={
            <Space>
              <HistoryOutlined />
              {t('collaboration.permissions.history')}
            </Space>
          }
          key="history"
        >
          <Card>
            <Timeline>
              {permissionHistory.slice(0, 20).map(change => (
                <Timeline.Item key={change.id}>
                  <div className="permission-change-item">
                    <Text strong>{change.userId}</Text>
                    <Text type="secondary"> - {change.reason}</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {new Date(change.timestamp).toLocaleString()} by {change.changedBy}
                    </Text>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </Card>
        </TabPane>
      </Tabs>

      {/* 权限编辑对话框 */}
      <Modal
        title={t('collaboration.permissions.editPermissions')}
        open={editModalVisible}
        onOk={handleSavePermissions}
        onCancel={() => setEditModalVisible(false)}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="role"
            label={t('collaboration.permissions.role')}
            rules={[{ required: true }]}
          >
            <Select>
              {Object.values(UserRole).map(role => (
                <Option key={role} value={role}>
                  <Space>
                    {getRoleIcon(role)}
                    {role}
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="permissions"
            label={t('collaboration.permissions.customPermissions')}
          >
            <Checkbox.Group>
              <Space direction="vertical">
                {Object.values(Permission).map(permission => (
                  <Checkbox key={permission} value={permission}>
                    {permission}
                  </Checkbox>
                ))}
              </Space>
            </Checkbox.Group>
          </Form.Item>
        </Form>
      </Modal>
    </Modal>
  );
};

export default PermissionManagementPanel;
