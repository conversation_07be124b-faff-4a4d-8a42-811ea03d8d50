/**
 * 聊天消息组件样式
 */
.chat-message {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 100%;
  word-wrap: break-word;

  // 消息头部
  .message-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;

    .message-avatar {
      flex-shrink: 0;

      &--user {
        background: #1890ff;
      }

      &--assistant {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      &--system {
        background: #52c41a;
      }
    }

    .message-sender {
      font-weight: 600;
      font-size: 12px;
      color: #8c8c8c;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .message-status {
      margin-left: auto;
      font-size: 11px;
      padding: 2px 6px;
      border-radius: 10px;

      &--sending {
        background: #fff7e6;
        color: #fa8c16;
      }

      &--sent {
        background: #f6ffed;
        color: #52c41a;
      }

      &--error {
        background: #fff2f0;
        color: #ff4d4f;
      }
    }
  }

  // 消息主体
  .message-body {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  // 消息内容
  .message-content {
    padding: 12px 16px;
    border-radius: 12px;
    position: relative;
    max-width: 85%;

    .message-text {
      margin: 0;
      line-height: 1.6;
      font-size: 14px;
      white-space: pre-wrap;
    }
  }

  // 操作按钮区域
  .message-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-left: 32px;

    .actions-label {
      font-size: 12px;
      font-weight: 500;
    }

    .action-button {
      border-radius: 16px;
      height: 28px;
      font-size: 12px;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
  }

  // 建议区域
  .message-suggestions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-left: 32px;

    .suggestions-label {
      font-size: 12px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .suggestions-list {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;

      .suggestion-tag {
        cursor: pointer;
        border-radius: 12px;
        padding: 4px 8px;
        font-size: 12px;
        border: 1px solid #d9d9d9;
        background: #fafafa;
        color: #595959;
        transition: all 0.2s ease;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
          background: #f0f8ff;
          transform: translateY(-1px);
        }
      }
    }
  }

  // 消息工具栏
  .message-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-left: 32px;
    opacity: 0;
    transition: opacity 0.2s ease;

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 8px;

      .message-time {
        font-size: 11px;
        color: #bfbfbf;
      }

      .metadata-button {
        padding: 0;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #bfbfbf;

        &:hover {
          color: #1890ff;
        }
      }
    }

    .toolbar-right {
      .ant-btn {
        padding: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        color: #bfbfbf;

        &:hover {
          color: #1890ff;
          background: #f0f8ff;
        }

        &.feedback-active {
          color: #1890ff;
          background: #f0f8ff;
        }
      }
    }
  }

  // 鼠标悬停时显示工具栏
  &:hover .message-toolbar {
    opacity: 1;
  }

  // 不同类型消息的样式
  &--user {
    align-items: flex-end;

    .message-content {
      background: #1890ff;
      color: white;
      margin-left: auto;
      border-bottom-right-radius: 4px;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        right: -6px;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-left-color: #1890ff;
        border-bottom: none;
        border-right: none;
      }
    }

    .message-actions,
    .message-suggestions,
    .message-toolbar {
      margin-left: 0;
      margin-right: 32px;
      align-items: flex-end;
    }
  }

  &--assistant {
    align-items: flex-start;

    .message-content {
      background: #f6f7ff;
      color: #262626;
      border: 1px solid #e6f7ff;
      border-bottom-left-radius: 4px;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: -7px;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-right-color: #e6f7ff;
        border-bottom: none;
        border-left: none;
      }

      &::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: -6px;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-right-color: #f6f7ff;
        border-bottom: none;
        border-left: none;
      }
    }
  }

  &--system {
    align-items: center;

    .message-content {
      background: #f6ffed;
      color: #52c41a;
      border: 1px solid #b7eb8f;
      border-radius: 6px;
      text-align: center;
      font-size: 12px;
      padding: 8px 12px;
      max-width: 70%;
    }

    .message-toolbar {
      margin-left: 0;
      justify-content: center;
    }
  }

  // 发送状态样式
  &--sending {
    .message-content {
      opacity: 0.7;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        right: 8px;
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        border: 2px solid #ffffff;
        border-top: 2px solid transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }

  &--error {
    .message-content {
      background: #fff2f0;
      border-color: #ffccc7;
      color: #ff4d4f;
    }
  }

  // 元数据弹窗内容
  .metadata-content {
    max-width: 300px;

    .metadata-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-size: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .context-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        margin-top: 4px;
      }
    }
  }

  // 动画
  @keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .message-content {
      max-width: 90%;
      padding: 10px 12px;
      font-size: 13px;
    }

    .message-actions,
    .message-suggestions,
    .message-toolbar {
      margin-left: 24px;
    }

    &--user {
      .message-actions,
      .message-suggestions,
      .message-toolbar {
        margin-left: 0;
        margin-right: 24px;
      }
    }

    .message-actions .action-button {
      height: 24px;
      font-size: 11px;
      padding: 0 8px;
    }

    .suggestions-list .suggestion-tag {
      font-size: 11px;
      padding: 3px 6px;
    }
  }

  // 暗色主题
  .dark-theme & {
    &--assistant {
      .message-content {
        background: #262626;
        color: #ffffff;
        border-color: #434343;

        &::after {
          border-right-color: #434343;
        }

        &::before {
          border-right-color: #262626;
        }
      }
    }

    &--system {
      .message-content {
        background: #162312;
        color: #52c41a;
        border-color: #274916;
      }
    }

    .message-suggestions {
      .suggestion-tag {
        background: #262626;
        border-color: #434343;
        color: #ffffff;

        &:hover {
          background: #1f1f1f;
          border-color: #1890ff;
          color: #1890ff;
        }
      }
    }

    .message-toolbar {
      .toolbar-left .message-time {
        color: #595959;
      }

      .toolbar-right .ant-btn {
        color: #595959;

        &:hover {
          color: #1890ff;
          background: rgba(24, 144, 255, 0.1);
        }

        &.feedback-active {
          color: #1890ff;
          background: rgba(24, 144, 255, 0.1);
        }
      }
    }
  }
}
