# 交互编辑器系统

DL引擎编辑器的交互系统提供了完整的交互功能编辑、预览和管理能力，与底层引擎深度集成。

## 功能特性

### 🎯 交互类型
- **点击交互** - 用户点击对象触发
- **悬停交互** - 鼠标悬停时触发
- **接近交互** - 用户靠近对象时触发
- **抓取交互** - VR/AR环境中的抓取操作
- **触摸交互** - 触摸屏设备的触摸操作

### ✨ 视觉效果
- **轮廓高亮** - 显示对象轮廓
- **发光效果** - 对象发光显示
- **颜色变化** - 改变对象颜色
- **缩放动画** - 对象缩放效果
- **自定义提示** - 显示交互提示文本

### 🔧 高级功能
- **条件系统** - 基于距离、角度、状态等条件
- **事件处理** - 开始、更新、结束、取消事件
- **统计分析** - 交互数据统计和分析
- **调试工具** - 实时调试和可视化
- **预览模式** - 实时预览交互效果

## 组件架构

```
InteractionManager (管理器)
├── InteractionToolbar (工具栏)
├── InteractionEditor (编辑器)
├── InteractionPreview (预览)
└── InteractionService (服务)
```

## 快速开始

### 1. 基础使用

```tsx
import { InteractionEditor, createDefaultInteractionConfig } from '../components/interaction';

const MyComponent = () => {
  const [config, setConfig] = useState(createDefaultInteractionConfig());

  return (
    <InteractionEditor
      data={config}
      onChange={setConfig}
    />
  );
};
```

### 2. 完整管理器

```tsx
import { InteractionManager } from '../components/interaction';

const MyApp = () => {
  return (
    <InteractionManager
      showToolbar={true}
      showPreview={true}
      layout="horizontal"
    />
  );
};
```

### 3. 服务集成

```tsx
import { interactionService } from '../components/interaction';

// 初始化服务
await interactionService.initialize(world);

// 创建交互组件
const component = await interactionService.createInteractableComponent(entity, config);

// 监听交互事件
interactionService.on('interaction', (event) => {
  console.log('交互发生:', event);
});
```

## API 参考

### InteractionEditor

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| data | InteractableComponentConfig | null | 交互配置数据 |
| onChange | (data) => void | - | 配置变化回调 |
| readonly | boolean | false | 是否只读模式 |

### InteractionPreview

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| config | any | - | 预览配置 |
| autoPlay | boolean | false | 自动播放 |
| size | 'small' \| 'medium' \| 'large' | 'medium' | 预览尺寸 |
| showControls | boolean | true | 显示控制栏 |
| showStats | boolean | true | 显示统计信息 |

### InteractionManager

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| showToolbar | boolean | true | 显示工具栏 |
| showPreview | boolean | true | 显示预览 |
| defaultActiveTab | string | 'editor' | 默认激活标签 |
| layout | 'horizontal' \| 'vertical' | 'horizontal' | 布局方向 |

## 配置选项

### 基础配置

```typescript
interface InteractableComponentConfig {
  interactionType: InteractionType;     // 交互类型
  visible: boolean;                     // 是否可见
  interactive: boolean;                 // 是否可交互
  interactionDistance: number;          // 交互距离
  label: string;                        // 显示标签
  prompt: string;                       // 提示文本
  highlightColor: string;               // 高亮颜色
  highlightType: HighlightType;         // 高亮类型
  enableHighlight: boolean;             // 启用高亮
  enablePrompt: boolean;                // 启用提示
  enableSound: boolean;                 // 启用声音
  priority: number;                     // 优先级
  conditions: InteractionCondition[];   // 交互条件
  events: InteractionEventConfig[];     // 交互事件
}
```

### 预设配置

系统提供了多种预设配置：

```typescript
import { INTERACTION_PRESETS } from '../components/interaction';

// 按钮预设
const buttonConfig = INTERACTION_PRESETS.BUTTON;

// 门预设
const doorConfig = INTERACTION_PRESETS.DOOR;

// 拾取物品预设
const pickupConfig = INTERACTION_PRESETS.PICKUP;

// 信息点预设
const infoConfig = INTERACTION_PRESETS.INFO_POINT;
```

## 事件系统

### 监听交互事件

```typescript
interactionService.on('interaction', (event: InteractionEvent) => {
  console.log('交互类型:', event.interactionType);
  console.log('实体ID:', event.entityId);
  console.log('时间戳:', event.timestamp);
  console.log('数据:', event.data);
});
```

### 监听高亮事件

```typescript
interactionService.on('highlight', (data) => {
  console.log('高亮事件:', data);
});
```

### 监听提示事件

```typescript
interactionService.on('prompt', (data) => {
  console.log('提示事件:', data);
});
```

## 统计分析

### 获取统计信息

```typescript
const stats = interactionService.getStats();
console.log('总交互次数:', stats.totalInteractions);
console.log('按类型统计:', stats.interactionsByType);
console.log('最常交互实体:', stats.mostInteractedEntity);
```

### 获取交互日志

```typescript
const log = interactionService.getInteractionLog();
console.log('交互历史:', log);
```

## 调试功能

### 启用调试模式

```typescript
// 在服务配置中启用
const service = new InteractionService({
  debugMode: true,
  enableLogging: true,
  enableStats: true
});
```

### 调试面板

交互管理器提供了内置的调试面板，显示：
- 服务状态
- 选中实体信息
- 交互配置状态
- 实时统计数据

## 最佳实践

### 1. 性能优化
- 合理设置交互距离，避免过大范围
- 使用条件系统减少不必要的检测
- 定期清理统计数据和日志

### 2. 用户体验
- 提供清晰的视觉反馈
- 使用合适的交互类型
- 设置有意义的提示文本

### 3. 调试和测试
- 使用预览模式测试交互效果
- 启用调试模式查看详细信息
- 分析统计数据优化交互设计

## 故障排除

### 常见问题

1. **交互不响应**
   - 检查实体是否有交互组件
   - 确认交互距离设置
   - 验证交互条件是否满足

2. **预览不显示**
   - 确认服务是否正确初始化
   - 检查配置数据是否有效
   - 查看浏览器控制台错误

3. **统计数据不更新**
   - 确认统计功能已启用
   - 检查事件监听是否正确设置
   - 验证交互是否真正触发

### 调试步骤

1. 打开浏览器开发者工具
2. 启用交互系统调试模式
3. 查看控制台日志输出
4. 使用调试面板检查状态
5. 分析统计数据和事件日志

## 更新日志

### v1.0.0
- 初始版本发布
- 基础交互类型支持
- 编辑器和预览功能
- 统计和调试工具

### 未来计划
- 更多交互类型支持
- 高级动画效果
- 多人协作交互
- AI辅助交互设计
