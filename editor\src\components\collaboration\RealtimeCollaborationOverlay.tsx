/**
 * 实时协作覆盖层组件
 * 显示用户光标、选择区域、编辑状态等实时协作信息
 */
import React, { useEffect, useState, useRef } from 'react';
import { Avatar, Badge, Tooltip, Typography } from 'antd';
import { EditOutlined, EyeOutlined } from '@ant-design/icons';
import RealtimeCollaborationService, { 
  RealtimeUser, 
  UserCursor, 
  UserSelection, 
  EditingZone,
  UserPresenceStatus 
} from '../../services/RealtimeCollaborationService';
import './RealtimeCollaborationOverlay.less';

const { Text } = Typography;

interface RealtimeCollaborationOverlayProps {
  collaborationService: RealtimeCollaborationService;
  containerRef: React.RefObject<HTMLElement>;
}

interface CursorPosition {
  x: number;
  y: number;
  user: RealtimeUser;
  visible: boolean;
}

const RealtimeCollaborationOverlay: React.FC<RealtimeCollaborationOverlayProps> = ({
  collaborationService,
  containerRef
}) => {
  const [users, setUsers] = useState<RealtimeUser[]>([]);
  const [cursors, setCursors] = useState<Map<string, CursorPosition>>(new Map());
  const [selections, setSelections] = useState<Map<string, UserSelection>>(new Map());
  const [editingZones, setEditingZones] = useState<Map<string, EditingZone>>(new Map());
  const [typingUsers, setTypingUsers] = useState<Set<string>>(new Set());
  
  const overlayRef = useRef<HTMLDivElement>(null);
  const cursorTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map());

  useEffect(() => {
    if (!collaborationService) return;

    // 监听协作事件
    const handleUserJoin = (user: RealtimeUser) => {
      setUsers(prev => [...prev.filter(u => u.id !== user.id), user]);
    };

    const handleUserLeave = (user: RealtimeUser) => {
      setUsers(prev => prev.filter(u => u.id !== user.id));
      setCursors(prev => {
        const newCursors = new Map(prev);
        newCursors.delete(user.id);
        return newCursors;
      });
    };

    const handleUserListUpdate = (userList: RealtimeUser[]) => {
      setUsers(userList);
    };

    const handleCursorUpdate = (cursor: UserCursor) => {
      const user = users.find(u => u.id === cursor.userId);
      if (!user || !containerRef.current) return;

      // 转换坐标到容器相对位置
      const containerRect = containerRef.current.getBoundingClientRect();
      const relativeX = cursor.x - containerRect.left;
      const relativeY = cursor.y - containerRect.top;

      setCursors(prev => {
        const newCursors = new Map(prev);
        newCursors.set(cursor.userId, {
          x: relativeX,
          y: relativeY,
          user,
          visible: true
        });
        return newCursors;
      });

      // 设置光标隐藏定时器
      const existingTimeout = cursorTimeouts.current.get(cursor.userId);
      if (existingTimeout) {
        clearTimeout(existingTimeout);
      }

      const timeout = setTimeout(() => {
        setCursors(prev => {
          const newCursors = new Map(prev);
          const cursorPos = newCursors.get(cursor.userId);
          if (cursorPos) {
            newCursors.set(cursor.userId, { ...cursorPos, visible: false });
          }
          return newCursors;
        });
      }, 3000); // 3秒后隐藏光标

      cursorTimeouts.current.set(cursor.userId, timeout);
    };

    const handleSelectionUpdate = (selection: UserSelection) => {
      setSelections(prev => {
        const newSelections = new Map(prev);
        newSelections.set(selection.userId, selection);
        return newSelections;
      });
    };

    const handleEditingZoneEnter = (zone: EditingZone) => {
      setEditingZones(prev => {
        const newZones = new Map(prev);
        newZones.set(zone.id, zone);
        return newZones;
      });
    };

    const handleEditingZoneExit = (zone: EditingZone) => {
      setEditingZones(prev => {
        const newZones = new Map(prev);
        newZones.delete(zone.id);
        return newZones;
      });
    };

    const handleTypingStart = ({ user, location }: { user: RealtimeUser; location: string }) => {
      setTypingUsers(prev => new Set(prev).add(user.id));
    };

    const handleTypingStop = (user: RealtimeUser) => {
      setTypingUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(user.id);
        return newSet;
      });
    };

    // 注册事件监听器
    collaborationService.on('userJoin', handleUserJoin);
    collaborationService.on('userLeave', handleUserLeave);
    collaborationService.on('userListUpdate', handleUserListUpdate);
    collaborationService.on('cursorUpdate', handleCursorUpdate);
    collaborationService.on('selectionUpdate', handleSelectionUpdate);
    collaborationService.on('editingZoneEnter', handleEditingZoneEnter);
    collaborationService.on('editingZoneExit', handleEditingZoneExit);
    collaborationService.on('typingStart', handleTypingStart);
    collaborationService.on('typingStop', handleTypingStop);

    return () => {
      // 清理事件监听器
      collaborationService.off('userJoin', handleUserJoin);
      collaborationService.off('userLeave', handleUserLeave);
      collaborationService.off('userListUpdate', handleUserListUpdate);
      collaborationService.off('cursorUpdate', handleCursorUpdate);
      collaborationService.off('selectionUpdate', handleSelectionUpdate);
      collaborationService.off('editingZoneEnter', handleEditingZoneEnter);
      collaborationService.off('editingZoneExit', handleEditingZoneExit);
      collaborationService.off('typingStart', handleTypingStart);
      collaborationService.off('typingStop', handleTypingStop);

      // 清理定时器
      cursorTimeouts.current.forEach(timeout => clearTimeout(timeout));
      cursorTimeouts.current.clear();
    };
  }, [collaborationService, users, containerRef]);

  // 获取状态图标
  const getStatusIcon = (status: UserPresenceStatus) => {
    switch (status) {
      case UserPresenceStatus.ONLINE:
        return <Badge status="success" />;
      case UserPresenceStatus.AWAY:
        return <Badge status="warning" />;
      case UserPresenceStatus.BUSY:
        return <Badge status="error" />;
      default:
        return <Badge status="default" />;
    }
  };

  // 渲染用户光标
  const renderCursors = () => {
    return Array.from(cursors.entries()).map(([userId, cursor]) => {
      if (!cursor.visible) return null;

      return (
        <div
          key={`cursor-${userId}`}
          className="collaboration-cursor"
          style={{
            left: cursor.x,
            top: cursor.y,
            borderColor: cursor.user.color,
            transform: 'translate(-2px, -2px)'
          }}
        >
          <div 
            className="cursor-pointer"
            style={{ backgroundColor: cursor.user.color }}
          />
          <div 
            className="cursor-label"
            style={{ backgroundColor: cursor.user.color }}
          >
            <Avatar 
              size={16} 
              src={cursor.user.avatar} 
              style={{ backgroundColor: cursor.user.color }}
            >
              {cursor.user.name.charAt(0).toUpperCase()}
            </Avatar>
            <span className="cursor-name">{cursor.user.name}</span>
            {typingUsers.has(userId) && (
              <EditOutlined className="typing-indicator" />
            )}
          </div>
        </div>
      );
    });
  };

  // 渲染选择区域
  const renderSelections = () => {
    return Array.from(selections.entries()).map(([userId, selection]) => {
      const user = users.find(u => u.id === userId);
      if (!user || selection.entityIds.length === 0) return null;

      return selection.entityIds.map(entityId => (
        <div
          key={`selection-${userId}-${entityId}`}
          className="collaboration-selection"
          style={{
            borderColor: user.color,
            backgroundColor: `${user.color}20`
          }}
          data-entity-id={entityId}
        />
      ));
    });
  };

  // 渲染编辑区域
  const renderEditingZones = () => {
    return Array.from(editingZones.values()).map(zone => {
      const user = users.find(u => u.id === zone.userId);
      if (!user) return null;

      return (
        <div
          key={`zone-${zone.id}`}
          className="collaboration-editing-zone"
          style={{
            borderColor: user.color,
            backgroundColor: `${user.color}10`
          }}
          data-entity-id={zone.entityId}
          data-component-id={zone.componentId}
        >
          <div className="zone-indicator">
            <Avatar 
              size={20} 
              src={user.avatar} 
              style={{ backgroundColor: user.color }}
            >
              {user.name.charAt(0).toUpperCase()}
            </Avatar>
            <EditOutlined />
          </div>
        </div>
      );
    });
  };

  return (
    <div ref={overlayRef} className="realtime-collaboration-overlay">
      {/* 用户光标 */}
      {renderCursors()}
      
      {/* 用户选择区域 */}
      {renderSelections()}
      
      {/* 编辑区域 */}
      {renderEditingZones()}
      
      {/* 在线用户列表 */}
      <div className="online-users-panel">
        <div className="panel-header">
          <Text strong>在线用户 ({users.length})</Text>
        </div>
        <div className="users-list">
          {users.map(user => (
            <Tooltip 
              key={user.id} 
              title={`${user.name} - ${user.status}`}
              placement="left"
            >
              <div className="user-item">
                <Badge 
                  dot 
                  status={user.status === UserPresenceStatus.ONLINE ? 'success' : 'default'}
                  offset={[-2, 2]}
                >
                  <Avatar 
                    size={32} 
                    src={user.avatar} 
                    style={{ backgroundColor: user.color }}
                  >
                    {user.name.charAt(0).toUpperCase()}
                  </Avatar>
                </Badge>
                {user.isTyping && (
                  <div className="typing-indicator-badge">
                    <EditOutlined />
                  </div>
                )}
                {user.editingZones.length > 0 && (
                  <div className="editing-indicator-badge">
                    <EyeOutlined />
                  </div>
                )}
              </div>
            </Tooltip>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RealtimeCollaborationOverlay;
