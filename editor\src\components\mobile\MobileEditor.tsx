/**
 * 移动端编辑器组件
 * 提供移动端优化的编辑器界面和交互体验
 */
import React, { useState, useEffect, useRef } from 'react';
import {
  Layout,
  Button,
  Space,
  Typography,
  Drawer,
  FloatButton,
  Badge,
  Alert,
  Card,
  Row,
  Col,
  Switch,
  Slider,
  Select,
  Tooltip,
  Modal,
  List,
  Tag
} from 'antd';
import {
  MenuOutlined,
  SettingOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  WifiOutlined,
  DisconnectOutlined,
  ThunderboltOutlined,
  MobileOutlined,
  TabletOutlined,
  DesktopOutlined,
  RotateLeftOutlined,
  ZoomInOutlined,
  DragOutlined,
  SelectOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  UndoOutlined,
  RedoOutlined,
  SaveOutlined,
  CloudSyncOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import MobileEditorService, {
  DeviceInfo,
  DeviceType,
  ScreenOrientation,
  MobileLayoutConfig,
  MobileLayoutMode,
  PerformanceLevel,
  GestureType,
  TouchEvent,
  MobilePerformanceData
} from '../../services/MobileEditorService';
import './MobileEditor.less';

const { Header, Content, Footer } = Layout;
const { Title, Text } = Typography;
const { Option } = Select;

interface MobileEditorProps {
  className?: string;
  style?: React.CSSProperties;
}

const MobileEditor: React.FC<MobileEditorProps> = ({
  className,
  style
}) => {
  const { t } = useTranslation();
  const containerRef = useRef<HTMLDivElement>(null);
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);
  const [layoutConfig, setLayoutConfig] = useState<MobileLayoutConfig | null>(null);
  const [performanceData, setPerformanceData] = useState<MobilePerformanceData | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const [menuVisible, setMenuVisible] = useState(false);
  const [propertiesVisible, setPropertiesVisible] = useState(false);
  const [hierarchyVisible, setHierarchyVisible] = useState(false);
  const [settingsVisible, setSettingsVisible] = useState(false);
  const [performanceVisible, setPerformanceVisible] = useState(false);
  const [selectedTool, setSelectedTool] = useState('select');

  const mobileService = MobileEditorService.getInstance();

  useEffect(() => {
    setupEventListeners();
    loadInitialData();

    return () => {
      cleanupEventListeners();
    };
  }, []);

  const setupEventListeners = () => {
    mobileService.on('orientationChanged', handleOrientationChanged);
    mobileService.on('layoutAdapted', handleLayoutAdapted);
    mobileService.on('gestureDetected', handleGestureDetected);
    mobileService.on('performanceUpdated', handlePerformanceUpdated);
    mobileService.on('networkStatusChanged', handleNetworkStatusChanged);
    mobileService.on('performanceIssues', handlePerformanceIssues);
  };

  const cleanupEventListeners = () => {
    mobileService.off('orientationChanged', handleOrientationChanged);
    mobileService.off('layoutAdapted', handleLayoutAdapted);
    mobileService.off('gestureDetected', handleGestureDetected);
    mobileService.off('performanceUpdated', handlePerformanceUpdated);
    mobileService.off('networkStatusChanged', handleNetworkStatusChanged);
    mobileService.off('performanceIssues', handlePerformanceIssues);
  };

  const loadInitialData = () => {
    setDeviceInfo(mobileService.getDeviceInfo());
    setLayoutConfig(mobileService.getLayoutConfig());
    setPerformanceData(mobileService.getLatestPerformanceData());
    setIsOnline(!mobileService.isOffline());
  };

  const handleOrientationChanged = (_: { from: ScreenOrientation; to: ScreenOrientation }) => {
    setDeviceInfo(mobileService.getDeviceInfo());
    // 触发触觉反馈
    mobileService.triggerHapticFeedback('light');
  };

  const handleLayoutAdapted = (config: MobileLayoutConfig) => {
    setLayoutConfig(config);
  };

  const handleGestureDetected = (touchEvent: TouchEvent) => {
    // 处理手势事件
    console.log('Gesture detected:', touchEvent.type);
    
    // 根据手势类型执行相应操作
    switch (touchEvent.type) {
      case GestureType.TAP:
        handleTap(touchEvent);
        break;
      case GestureType.DOUBLE_TAP:
        handleDoubleTap(touchEvent);
        break;
      case GestureType.LONG_PRESS:
        handleLongPress(touchEvent);
        break;
      case GestureType.PAN:
        handlePan(touchEvent);
        break;
      case GestureType.PINCH:
        handlePinch(touchEvent);
        break;
      case GestureType.ROTATE:
        handleRotate(touchEvent);
        break;
    }
  };

  const handlePerformanceUpdated = (data: MobilePerformanceData) => {
    setPerformanceData(data);
  };

  const handleNetworkStatusChanged = ({ online }: { online: boolean }) => {
    setIsOnline(online);
  };

  const handlePerformanceIssues = (issues: string[]) => {
    Modal.warning({
      title: t('mobile.performanceWarning'),
      content: (
        <div>
          <Text>{t('mobile.performanceIssuesDetected')}</Text>
          <ul>
            {issues.map((issue, index) => (
              <li key={index}>{issue}</li>
            ))}
          </ul>
        </div>
      )
    });
  };

  // 手势处理方法
  const handleTap = (_touchEvent: TouchEvent) => {
    if (selectedTool === 'select') {
      // 选择对象
      mobileService.triggerHapticFeedback('light');
    }
  };

  const handleDoubleTap = (_touchEvent: TouchEvent) => {
    // 编辑对象
    mobileService.triggerHapticFeedback('medium');
  };

  const handleLongPress = (_touchEvent: TouchEvent) => {
    // 显示上下文菜单
    mobileService.triggerHapticFeedback('heavy');
  };

  const handlePan = (_touchEvent: TouchEvent) => {
    if (selectedTool === 'move') {
      // 移动对象
    } else {
      // 平移视图
    }
  };

  const handlePinch = (touchEvent: TouchEvent) => {
    // 缩放视图
    const scale = touchEvent.gestureData.scale || 1;
    console.log('Pinch scale:', scale);
  };

  const handleRotate = (touchEvent: TouchEvent) => {
    if (selectedTool === 'rotate') {
      // 旋转对象
      const rotation = touchEvent.gestureData.rotation || 0;
      console.log('Rotate angle:', rotation);
    }
  };

  const toggleFullscreen = () => {
    if (!isFullscreen) {
      if (containerRef.current?.requestFullscreen) {
        containerRef.current.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
    setIsFullscreen(!isFullscreen);
  };

  const getDeviceIcon = () => {
    switch (deviceInfo?.type) {
      case DeviceType.PHONE:
        return <MobileOutlined />;
      case DeviceType.TABLET:
        return <TabletOutlined />;
      case DeviceType.DESKTOP:
        return <DesktopOutlined />;
      default:
        return <MobileOutlined />;
    }
  };

  const getBatteryColor = () => {
    if (!performanceData?.battery) return 'green';
    const level = performanceData.battery.level;
    if (level > 50) return 'green';
    if (level > 20) return 'orange';
    return 'red';
  };

  const getPerformanceColor = () => {
    if (!performanceData) return 'green';
    if (performanceData.fps < 30) return 'red';
    if (performanceData.fps < 45) return 'orange';
    return 'green';
  };

  // 渲染工具栏
  const renderToolbar = () => {
    const tools = [
      { key: 'select', icon: <SelectOutlined />, label: t('mobile.select') },
      { key: 'move', icon: <DragOutlined />, label: t('mobile.move') },
      { key: 'rotate', icon: <RotateLeftOutlined />, label: t('mobile.rotate') },
      { key: 'scale', icon: <ZoomInOutlined />, label: t('mobile.scale') },
      { key: 'edit', icon: <EditOutlined />, label: t('mobile.edit') },
      { key: 'copy', icon: <CopyOutlined />, label: t('mobile.copy') },
      { key: 'delete', icon: <DeleteOutlined />, label: t('mobile.delete') }
    ];

    return (
      <div className="mobile-toolbar">
        <Space wrap>
          {tools.map(tool => (
            <Button
              key={tool.key}
              type={selectedTool === tool.key ? 'primary' : 'default'}
              icon={tool.icon}
              size="large"
              onClick={() => {
                setSelectedTool(tool.key);
                mobileService.triggerHapticFeedback('light');
              }}
            >
              {layoutConfig?.mode !== MobileLayoutMode.COMPACT && tool.label}
            </Button>
          ))}
        </Space>
      </div>
    );
  };

  // 渲染状态栏
  const renderStatusBar = () => (
    <div className="mobile-status-bar">
      <Space>
        {/* 设备信息 */}
        <Tooltip title={`${deviceInfo?.type} - ${deviceInfo?.orientation}`}>
          <Space size="small">
            {getDeviceIcon()}
            <Text style={{ fontSize: '12px' }}>
              {deviceInfo?.screenWidth}×{deviceInfo?.screenHeight}
            </Text>
          </Space>
        </Tooltip>

        {/* 网络状态 */}
        <Tooltip title={isOnline ? t('mobile.online') : t('mobile.offline')}>
          <Badge status={isOnline ? 'success' : 'error'}>
            {isOnline ? <WifiOutlined /> : <DisconnectOutlined />}
          </Badge>
        </Tooltip>

        {/* 电池状态 */}
        {performanceData?.battery && (
          <Tooltip title={`${t('mobile.battery')}: ${performanceData.battery.level.toFixed(0)}%`}>
            <Space size="small">
              <ThunderboltOutlined style={{ color: getBatteryColor() }} />
              <Text style={{ fontSize: '12px', color: getBatteryColor() }}>
                {performanceData.battery.level.toFixed(0)}%
              </Text>
            </Space>
          </Tooltip>
        )}

        {/* 性能状态 */}
        {performanceData && (
          <Tooltip title={`FPS: ${performanceData.fps}`}>
            <Space size="small">
              <div
                style={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  backgroundColor: getPerformanceColor()
                }}
              />
              <Text style={{ fontSize: '12px' }}>
                {performanceData.fps}
              </Text>
            </Space>
          </Tooltip>
        )}
      </Space>
    </div>
  );

  // 渲染属性面板
  const renderPropertiesPanel = () => (
    <Card size="small" title={t('mobile.properties')}>
      <div style={{ padding: '8px 0' }}>
        <Text type="secondary">{t('mobile.noObjectSelected')}</Text>
      </div>
    </Card>
  );

  // 渲染层级面板
  const renderHierarchyPanel = () => (
    <Card size="small" title={t('mobile.hierarchy')}>
      <List
        size="small"
        dataSource={[
          { id: '1', name: 'Scene', type: 'scene' },
          { id: '2', name: 'Camera', type: 'camera' },
          { id: '3', name: 'Light', type: 'light' }
        ]}
        renderItem={(item) => (
          <List.Item>
            <Text>{item.name}</Text>
            <Tag>{item.type}</Tag>
          </List.Item>
        )}
      />
    </Card>
  );

  // 渲染设置面板
  const renderSettingsPanel = () => (
    <div className="mobile-settings">
      <Card size="small" title={t('mobile.layoutSettings')} style={{ marginBottom: 16 }}>
        <div style={{ marginBottom: 16 }}>
          <Text>{t('mobile.layoutMode')}</Text>
          <Select
            value={layoutConfig?.mode}
            style={{ width: '100%', marginTop: 8 }}
            onChange={(value) => {
              mobileService.updateLayoutConfig({ mode: value });
            }}
          >
            {Object.values(MobileLayoutMode).map(mode => (
              <Option key={mode} value={mode}>{mode.toUpperCase()}</Option>
            ))}
          </Select>
        </div>

        <div style={{ marginBottom: 16 }}>
          <Text>{t('mobile.hapticFeedback')}</Text>
          <Switch
            checked={layoutConfig?.interaction.hapticFeedback}
            style={{ marginLeft: 8 }}
            onChange={(checked) => {
              mobileService.setHapticFeedback(checked);
            }}
          />
        </div>

        <div style={{ marginBottom: 16 }}>
          <Text>{t('mobile.touchSensitivity')}</Text>
          <Slider
            min={0.5}
            max={2.0}
            step={0.1}
            value={layoutConfig?.interaction.touchSensitivity}
            style={{ marginTop: 8 }}
            onChange={(value) => {
              mobileService.updateLayoutConfig({
                interaction: {
                  ...layoutConfig!.interaction,
                  touchSensitivity: value
                }
              });
            }}
          />
        </div>
      </Card>

      <Card size="small" title={t('mobile.performanceSettings')}>
        <div style={{ marginBottom: 16 }}>
          <Text>{t('mobile.performanceLevel')}</Text>
          <Select
            value={layoutConfig?.performance.level}
            style={{ width: '100%', marginTop: 8 }}
            onChange={(value) => {
              mobileService.updateLayoutConfig({
                performance: {
                  ...layoutConfig!.performance,
                  level: value
                }
              });
            }}
          >
            {Object.values(PerformanceLevel).map(level => (
              <Option key={level} value={level}>{level.toUpperCase()}</Option>
            ))}
          </Select>
        </div>

        <div style={{ marginBottom: 16 }}>
          <Text>{t('mobile.renderQuality')}</Text>
          <Slider
            min={0.5}
            max={1.0}
            step={0.1}
            value={layoutConfig?.performance.renderQuality}
            style={{ marginTop: 8 }}
            onChange={(value) => {
              mobileService.updateLayoutConfig({
                performance: {
                  ...layoutConfig!.performance,
                  renderQuality: value
                }
              });
            }}
          />
        </div>

        <div>
          <Text>{t('mobile.maxFPS')}</Text>
          <Slider
            min={30}
            max={60}
            step={5}
            value={layoutConfig?.performance.maxFPS}
            style={{ marginTop: 8 }}
            onChange={(value) => {
              mobileService.updateLayoutConfig({
                performance: {
                  ...layoutConfig!.performance,
                  maxFPS: value
                }
              });
            }}
          />
        </div>
      </Card>
    </div>
  );

  // 渲染性能监控面板
  const renderPerformancePanel = () => (
    <Card size="small" title={t('mobile.performanceMonitor')}>
      {performanceData && (
        <div>
          <Row gutter={[8, 8]}>
            <Col span={12}>
              <div className="performance-metric">
                <Text type="secondary" style={{ fontSize: '11px' }}>FPS</Text>
                <div>
                  <Text strong style={{ color: getPerformanceColor() }}>
                    {performanceData.fps}
                  </Text>
                </div>
              </div>
            </Col>
            <Col span={12}>
              <div className="performance-metric">
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  {t('mobile.memory')}
                </Text>
                <div>
                  <Text strong>
                    {performanceData.memoryUsage.percentage.toFixed(0)}%
                  </Text>
                </div>
              </div>
            </Col>
            <Col span={12}>
              <div className="performance-metric">
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  {t('mobile.battery')}
                </Text>
                <div>
                  <Text strong style={{ color: getBatteryColor() }}>
                    {performanceData.battery.level.toFixed(0)}%
                  </Text>
                </div>
              </div>
            </Col>
            <Col span={12}>
              <div className="performance-metric">
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  {t('mobile.network')}
                </Text>
                <div>
                  <Text strong>
                    {performanceData.network.effectiveType}
                  </Text>
                </div>
              </div>
            </Col>
          </Row>
        </div>
      )}
    </Card>
  );

  return (
    <div
      ref={containerRef}
      className={`mobile-editor ${className || ''}`}
      style={style}
    >
      <Layout style={{ height: '100vh' }}>
        {/* 顶部状态栏 */}
        {!isFullscreen && (
          <Header className="mobile-header">
            <div className="mobile-header-content">
              <Space>
                <Button
                  type="text"
                  icon={<MenuOutlined />}
                  onClick={() => setMenuVisible(true)}
                />
                <Title level={5} style={{ margin: 0, color: '#fff' }}>
                  {t('mobile.editor')}
                </Title>
              </Space>

              <Space>
                <Button
                  type="text"
                  icon={<UndoOutlined />}
                  style={{ color: '#fff' }}
                />
                <Button
                  type="text"
                  icon={<RedoOutlined />}
                  style={{ color: '#fff' }}
                />
                <Button
                  type="text"
                  icon={<SaveOutlined />}
                  style={{ color: '#fff' }}
                />
                <Button
                  type="text"
                  icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
                  onClick={toggleFullscreen}
                  style={{ color: '#fff' }}
                />
              </Space>
            </div>
          </Header>
        )}

        {/* 主要内容区域 */}
        <Content className="mobile-content">
          <div className="mobile-viewport">
            {/* 3D视口内容 */}
            <div className="viewport-content">
              <Text type="secondary">{t('mobile.viewportPlaceholder')}</Text>
            </div>

            {/* 浮动操作按钮 */}
            <FloatButton.Group
              trigger="click"
              type="primary"
              style={{ right: 16, bottom: 80 }}
              icon={<SettingOutlined />}
            >
              <FloatButton
                icon={<EyeOutlined />}
                tooltip={t('mobile.properties')}
                onClick={() => setPropertiesVisible(true)}
              />
              <FloatButton
                icon={<MenuOutlined />}
                tooltip={t('mobile.hierarchy')}
                onClick={() => setHierarchyVisible(true)}
              />
              <FloatButton
                icon={<SettingOutlined />}
                tooltip={t('mobile.settings')}
                onClick={() => setSettingsVisible(true)}
              />
            </FloatButton.Group>
          </div>
        </Content>

        {/* 底部工具栏 */}
        {layoutConfig?.panels.toolbar.position === 'bottom' && (
          <Footer className="mobile-footer">
            {renderToolbar()}
            {renderStatusBar()}
          </Footer>
        )}
      </Layout>

      {/* 侧边菜单 */}
      <Drawer
        title={t('mobile.menu')}
        placement="left"
        onClose={() => setMenuVisible(false)}
        open={menuVisible}
        width={280}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Button block icon={<SaveOutlined />}>
            {t('mobile.save')}
          </Button>
          <Button block icon={<CloudSyncOutlined />}>
            {t('mobile.sync')}
          </Button>
          <Button block icon={<SettingOutlined />} onClick={() => setSettingsVisible(true)}>
            {t('mobile.settings')}
          </Button>
        </Space>
      </Drawer>

      {/* 属性面板 */}
      <Drawer
        title={t('mobile.properties')}
        placement="right"
        onClose={() => setPropertiesVisible(false)}
        open={propertiesVisible}
        width={300}
      >
        {renderPropertiesPanel()}
      </Drawer>

      {/* 层级面板 */}
      <Drawer
        title={t('mobile.hierarchy')}
        placement="left"
        onClose={() => setHierarchyVisible(false)}
        open={hierarchyVisible}
        width={250}
      >
        {renderHierarchyPanel()}
      </Drawer>

      {/* 设置面板 */}
      <Drawer
        title={t('mobile.settings')}
        placement="right"
        onClose={() => setSettingsVisible(false)}
        open={settingsVisible}
        width={320}
      >
        {renderSettingsPanel()}
      </Drawer>

      {/* 性能监控面板 */}
      <Modal
        title={t('mobile.performanceMonitor')}
        open={performanceVisible}
        onCancel={() => setPerformanceVisible(false)}
        footer={null}
        width={400}
      >
        {renderPerformancePanel()}
      </Modal>

      {/* 离线提示 */}
      {!isOnline && (
        <Alert
          message={t('mobile.offlineMode')}
          description={t('mobile.offlineModeDescription')}
          type="warning"
          showIcon
          style={{
            position: 'fixed',
            top: 16,
            left: 16,
            right: 16,
            zIndex: 1000
          }}
        />
      )}
    </div>
  );
};

export default MobileEditor;
