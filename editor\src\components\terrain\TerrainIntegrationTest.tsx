/**
 * 地形集成测试组件
 * 用于测试地形系统与底层引擎的集成
 */
import React, { useState } from 'react';
import { Card, Button, Space, message, Typography, Divider, Alert } from 'antd';
import { PlayCircleOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  message?: string;
  duration?: number;
}

/**
 * 地形集成测试组件
 */
const TerrainIntegrationTest: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);

  // 测试项目
  const tests = [
    {
      name: '地形引擎服务初始化',
      test: async () => {
        const { terrainEngineService } = await import('../../services/TerrainEngineService');
        if (!terrainEngineService) {
          throw new Error('地形引擎服务未初始化');
        }
        return '地形引擎服务初始化成功';
      }
    },
    {
      name: '创建地形组件',
      test: async () => {
        const { terrainEngineService } = await import('../../services/TerrainEngineService');
        const testEntityId = `test_terrain_${Date.now()}`;

        await terrainEngineService.createTerrainComponent(testEntityId, {
          width: 100,
          height: 100,
          resolution: 64,
          maxHeight: 10,
          useLOD: false,
          usePhysics: false
        });

        return `地形组件创建成功 (ID: ${testEntityId})`;
      }
    },
    {
      name: '地形生成测试',
      test: async () => {
        const { terrainEngineService } = await import('../../services/TerrainEngineService');
        const testEntityId = `test_terrain_gen_${Date.now()}`;

        // 创建地形组件
        await terrainEngineService.createTerrainComponent(testEntityId, {
          width: 100,
          height: 100,
          resolution: 64,
          maxHeight: 10
        });

        // 生成地形
        await terrainEngineService.generateTerrain(testEntityId, 'perlin', {
          seed: 123,
          scale: 50,
          persistence: 0.5,
          octaves: 4,
          frequency: 0.02,
          amplitude: 1.0
        });

        return `地形生成测试成功 (ID: ${testEntityId})`;
      }
    },
    {
      name: '地形雕刻测试',
      test: async () => {
        const { terrainEngineService } = await import('../../services/TerrainEngineService');
        const testEntityId = `test_terrain_sculpt_${Date.now()}`;

        // 创建地形组件
        await terrainEngineService.createTerrainComponent(testEntityId, {
          width: 100,
          height: 100,
          resolution: 64,
          maxHeight: 10
        });

        // 雕刻地形
        await terrainEngineService.sculptTerrain(testEntityId, {
          brushType: 'raise',
          brushShape: 'circle',
          brushSize: 10,
          brushStrength: 0.5,
          brushFalloff: 2,
          position: { x: 0, z: 0 }
        });

        return `地形雕刻测试成功 (ID: ${testEntityId})`;
      }
    },
    {
      name: '底层引擎组件检查',
      test: async () => {
        try {
          // 使用类型断言来避免模块声明问题
          // @ts-ignore: dl-engine.mjs 是动态模块，没有类型声明
          const engineModule = await import('../../libs/dl-engine.mjs') as any;

          if (!engineModule.TerrainComponent) {
            throw new Error('TerrainComponent 未找到');
          }

          if (!engineModule.TerrainSystem) {
            throw new Error('TerrainSystem 未找到');
          }

          // 测试创建地形组件
          const testComponent = new engineModule.TerrainComponent({
            width: 100,
            height: 100,
            resolution: 32
          });

          if (!testComponent.heightData) {
            throw new Error('地形组件高度数据未初始化');
          }

          return '底层引擎组件检查通过';
        } catch (error: any) {
          throw new Error(`底层引擎组件检查失败: ${error.message}`);
        }
      }
    }
  ];

  // 运行所有测试
  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    const results: TestResult[] = [];
    
    for (const testCase of tests) {
      const startTime = Date.now();
      const result: TestResult = {
        name: testCase.name,
        status: 'pending'
      };
      
      try {
        const message = await testCase.test();
        result.status = 'success';
        result.message = message;
        result.duration = Date.now() - startTime;
      } catch (error: any) {
        result.status = 'error';
        result.message = error.message;
        result.duration = Date.now() - startTime;
      }
      
      results.push(result);
      setTestResults([...results]);
      
      // 短暂延迟以便观察进度
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    setIsRunning(false);
    
    // 显示总结
    const successCount = results.filter(r => r.status === 'success').length;
    const totalCount = results.length;
    
    if (successCount === totalCount) {
      message.success(`所有测试通过 (${successCount}/${totalCount})`);
    } else {
      message.warning(`部分测试失败 (${successCount}/${totalCount})`);
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <PlayCircleOutlined style={{ color: '#1890ff' }} />;
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return '#52c41a';
      case 'error':
        return '#ff4d4f';
      default:
        return '#1890ff';
    }
  };

  return (
    <Card title="地形系统集成测试" style={{ margin: 16 }}>
      <Alert
        message="地形系统集成测试"
        description="此测试用于验证编辑器地形组件与底层dl-engine的集成是否正常工作。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />
      
      <Space style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlayCircleOutlined />}
          onClick={runAllTests}
          loading={isRunning}
          disabled={isRunning}
        >
          运行所有测试
        </Button>
      </Space>
      
      <Divider />
      
      <div>
        <Title level={4}>测试结果</Title>
        {testResults.length === 0 && !isRunning && (
          <Text type="secondary">点击"运行所有测试"开始测试</Text>
        )}
        
        {testResults.map((result, index) => (
          <div key={index} style={{ marginBottom: 12, padding: 12, border: '1px solid #f0f0f0', borderRadius: 4 }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
              {getStatusIcon(result.status)}
              <Text strong style={{ marginLeft: 8, color: getStatusColor(result.status) }}>
                {result.name}
              </Text>
              {result.duration && (
                <Text type="secondary" style={{ marginLeft: 'auto' }}>
                  {result.duration}ms
                </Text>
              )}
            </div>
            {result.message && (
              <Paragraph style={{ margin: 0, fontSize: '12px', color: '#666' }}>
                {result.message}
              </Paragraph>
            )}
          </div>
        ))}
        
        {isRunning && testResults.length < tests.length && (
          <div style={{ textAlign: 'center', padding: 20 }}>
            <Text type="secondary">正在运行测试...</Text>
          </div>
        )}
      </div>
    </Card>
  );
};

export default TerrainIntegrationTest;
