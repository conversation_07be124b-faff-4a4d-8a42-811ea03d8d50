/**
 * 虚拟化身预览画布
 * 提供实时3D预览功能
 */
import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Card, Button, Space, Typography, Row, Col, Slider, Select, Tooltip } from 'antd';
import {
  FullscreenOutlined,
  FullscreenExitOutlined,
  ReloadOutlined,
  CameraOutlined,
  SettingOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

const { Text } = Typography;
const { Option } = Select;

/**
 * 预览设置接口
 */
export interface PreviewSettings {
  /** 渲染质量 */
  quality: 'low' | 'medium' | 'high';
  /** 是否显示网格 */
  showGrid: boolean;
  /** 是否显示坐标轴 */
  showAxes: boolean;
  /** 背景颜色 */
  backgroundColor: string;
  /** 光照强度 */
  lightIntensity: number;
  /** 相机视野角度 */
  cameraFOV: number;
}

/**
 * 渲染统计接口
 */
export interface RenderStats {
  fps: number;
  triangles: number;
  vertices: number;
  drawCalls: number;
  memoryUsage: number;
}

/**
 * 组件属性接口
 */
interface AvatarPreviewCanvasProps {
  /** 虚拟化身数据 */
  avatarData?: any;
  /** 画布宽度 */
  width?: number;
  /** 画布高度 */
  height?: number;
  /** 是否启用控制器 */
  enableControls?: boolean;
  /** 参数变化回调 */
  onParameterChange?: (category: string, parameter: string, value: any) => void;
}

/**
 * 虚拟化身预览画布组件
 */
export const AvatarPreviewCanvas: React.FC<AvatarPreviewCanvasProps> = ({
  avatarData,
  width = 600,
  height = 400,
  enableControls = true,
  onParameterChange: _onParameterChange
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [renderStats, setRenderStats] = useState<RenderStats>({
    fps: 60,
    triangles: 0,
    vertices: 0,
    drawCalls: 0,
    memoryUsage: 0
  });

  const [previewSettings, setPreviewSettings] = useState<PreviewSettings>({
    quality: 'medium',
    showGrid: true,
    showAxes: false,
    backgroundColor: '#f0f0f0',
    lightIntensity: 1.0,
    cameraFOV: 50
  });

  /**
   * 初始化Three.js场景
   */
  const initializeScene = useCallback(async () => {
    if (!canvasRef.current || isInitialized) return;

    try {
      // 这里会初始化Three.js场景
      // 由于这是演示代码，我们模拟初始化过程
      
      console.log('初始化Three.js场景...');
      
      // 模拟初始化延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟渲染统计更新
      const updateStats = () => {
        setRenderStats(prev => ({
          ...prev,
          fps: 58 + Math.random() * 4,
          triangles: Math.floor(5000 + Math.random() * 1000),
          vertices: Math.floor(3000 + Math.random() * 500),
          drawCalls: Math.floor(10 + Math.random() * 5),
          memoryUsage: Math.floor(50 + Math.random() * 20)
        }));
      };

      // 每秒更新一次统计
      const statsInterval = setInterval(updateStats, 1000);
      
      setIsInitialized(true);
      console.log('Three.js场景初始化完成');

      // 清理函数
      return () => {
        clearInterval(statsInterval);
      };
    } catch (error) {
      console.error('初始化Three.js场景失败:', error);
    }
  }, [isInitialized]);

  /**
   * 更新预览设置
   */
  const updatePreviewSettings = useCallback((key: keyof PreviewSettings, value: any) => {
    setPreviewSettings(prev => ({
      ...prev,
      [key]: value
    }));

    // 这里会更新Three.js场景设置
    console.log(`更新预览设置: ${key} = ${value}`);
  }, []);

  /**
   * 重置相机位置
   */
  const resetCamera = useCallback(() => {
    console.log('重置相机位置');
    // 这里会重置Three.js相机
  }, []);

  /**
   * 截图功能
   */
  const takeScreenshot = useCallback(() => {
    if (!canvasRef.current) return;

    try {
      // 创建截图
      const dataURL = canvasRef.current.toDataURL('image/png');
      
      // 创建下载链接
      const link = document.createElement('a');
      link.download = `avatar_screenshot_${Date.now()}.png`;
      link.href = dataURL;
      link.click();
      
      console.log('截图已保存');
    } catch (error) {
      console.error('截图失败:', error);
    }
  }, []);

  /**
   * 切换全屏
   */
  const toggleFullscreen = useCallback(() => {
    if (!containerRef.current) return;

    if (!isFullscreen) {
      if (containerRef.current.requestFullscreen) {
        containerRef.current.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  }, [isFullscreen]);

  /**
   * 监听全屏状态变化
   */
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  /**
   * 初始化场景
   */
  useEffect(() => {
    initializeScene();
  }, [initializeScene]);

  /**
   * 监听虚拟化身数据变化
   */
  useEffect(() => {
    if (avatarData && isInitialized) {
      console.log('更新虚拟化身数据:', avatarData);
      // 这里会更新Three.js场景中的虚拟化身
    }
  }, [avatarData, isInitialized]);

  /**
   * 渲染控制面板
   */
  const renderControls = () => {
    if (!enableControls) return null;

    return (
      <Card size="small" style={{ marginTop: 8 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Tooltip title="重置相机">
                <Button
                  type="text"
                  icon={<ReloadOutlined />}
                  onClick={resetCamera}
                />
              </Tooltip>
              <Tooltip title="截图">
                <Button
                  type="text"
                  icon={<CameraOutlined />}
                  onClick={takeScreenshot}
                />
              </Tooltip>
              <Tooltip title="设置">
                <Button
                  type="text"
                  icon={<SettingOutlined />}
                  onClick={() => setShowSettings(!showSettings)}
                />
              </Tooltip>
              <Tooltip title={isFullscreen ? "退出全屏" : "全屏"}>
                <Button
                  type="text"
                  icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
                  onClick={toggleFullscreen}
                />
              </Tooltip>
            </Space>
          </Col>
          <Col>
            <Space>
              <Text type="secondary" style={{ fontSize: 12 }}>
                FPS: {Math.round(renderStats.fps)}
              </Text>
              <Text type="secondary" style={{ fontSize: 12 }}>
                三角形: {renderStats.triangles.toLocaleString()}
              </Text>
              <Tooltip title="渲染统计">
                <InfoCircleOutlined style={{ color: '#999' }} />
              </Tooltip>
            </Space>
          </Col>
        </Row>

        {/* 设置面板 */}
        {showSettings && (
          <div style={{ marginTop: 16, padding: 16, backgroundColor: '#fafafa', borderRadius: 4 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <Text type="secondary">渲染质量</Text>
                  <Select
                    value={previewSettings.quality}
                    onChange={(value) => updatePreviewSettings('quality', value)}
                    style={{ width: '100%' }}
                    size="small"
                  >
                    <Option value="low">低</Option>
                    <Option value="medium">中</Option>
                    <Option value="high">高</Option>
                  </Select>
                </Space>
              </Col>
              <Col span={8}>
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <Text type="secondary">光照强度</Text>
                  <Slider
                    min={0.1}
                    max={2.0}
                    step={0.1}
                    value={previewSettings.lightIntensity}
                    onChange={(value) => updatePreviewSettings('lightIntensity', value)}
                  />
                </Space>
              </Col>
              <Col span={8}>
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <Text type="secondary">视野角度</Text>
                  <Slider
                    min={30}
                    max={90}
                    value={previewSettings.cameraFOV}
                    onChange={(value) => updatePreviewSettings('cameraFOV', value)}
                  />
                </Space>
              </Col>
            </Row>

            <Row style={{ marginTop: 16 }}>
              <Col span={24}>
                <Space>
                  <label>
                    <input
                      type="checkbox"
                      checked={previewSettings.showGrid}
                      onChange={(e) => updatePreviewSettings('showGrid', e.target.checked)}
                    />
                    <Text style={{ marginLeft: 8 }}>显示网格</Text>
                  </label>
                  <label>
                    <input
                      type="checkbox"
                      checked={previewSettings.showAxes}
                      onChange={(e) => updatePreviewSettings('showAxes', e.target.checked)}
                    />
                    <Text style={{ marginLeft: 8 }}>显示坐标轴</Text>
                  </label>
                </Space>
              </Col>
            </Row>
          </div>
        )}
      </Card>
    );
  };

  /**
   * 渲染统计信息
   */
  const renderStatsPanel = () => (
    <Card size="small" title="渲染统计" style={{ marginTop: 8 }}>
      <Row gutter={16}>
        <Col span={12}>
          <Space direction="vertical" size="small">
            <div>
              <Text type="secondary">帧率</Text>
              <div>{Math.round(renderStats.fps)} FPS</div>
            </div>
            <div>
              <Text type="secondary">三角形</Text>
              <div>{renderStats.triangles.toLocaleString()}</div>
            </div>
          </Space>
        </Col>
        <Col span={12}>
          <Space direction="vertical" size="small">
            <div>
              <Text type="secondary">顶点</Text>
              <div>{renderStats.vertices.toLocaleString()}</div>
            </div>
            <div>
              <Text type="secondary">绘制调用</Text>
              <div>{renderStats.drawCalls}</div>
            </div>
          </Space>
        </Col>
      </Row>
      <div style={{ marginTop: 8 }}>
        <Text type="secondary">内存使用</Text>
        <div>{renderStats.memoryUsage} MB</div>
      </div>
    </Card>
  );

  return (
    <div ref={containerRef} style={{ width: '100%', height: '100%' }}>
      {/* 主预览区域 */}
      <div style={{
        width: '100%',
        height: isFullscreen ? '100vh' : height,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: previewSettings.backgroundColor,
        border: '1px solid #d9d9d9',
        borderRadius: isFullscreen ? 0 : 4,
        position: 'relative'
      }}>
        <canvas
          ref={canvasRef}
          width={isFullscreen ? window.innerWidth : width}
          height={isFullscreen ? window.innerHeight : height}
          style={{
            display: 'block',
            maxWidth: '100%',
            maxHeight: '100%'
          }}
        />

        {/* 加载状态 */}
        {!isInitialized && (
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }}>
              🎭
            </div>
            <Text type="secondary">初始化预览系统...</Text>
          </div>
        )}

        {/* 无数据状态 */}
        {isInitialized && !avatarData && (
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }}>
              👤
            </div>
            <Text type="secondary">暂无虚拟化身数据</Text>
          </div>
        )}
      </div>

      {/* 控制面板 */}
      {!isFullscreen && renderControls()}

      {/* 统计信息 */}
      {!isFullscreen && showSettings && renderStatsPanel()}
    </div>
  );
};

export default AvatarPreviewCanvas;
