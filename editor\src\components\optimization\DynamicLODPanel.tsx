/**
 * 动态LOD面板组件
 * 用于配置和监控动态LOD系统
 */
import React, { useState, useEffect, useRef } from 'react';
import { Card, Row, Col, Switch, Slider, InputNumber, Button, Tooltip, Space, Tag, Collapse, Progress } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  ReloadOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import './DynamicLODPanel.less';

const { Panel } = Collapse;

// 临时类型定义，直到引擎模块可用
enum DynamicLODSystemEventType {
  QUALITY_LEVEL_ADJUSTED = 'quality_level_adjusted',
  PERFORMANCE_TARGET_ADJUSTED = 'performance_target_adjusted'
}

interface DynamicLODSystem {
  isEnabled(): boolean;
  setEnabled(enabled: boolean): void;
  isDynamicLODEnabled(): boolean;
  setDynamicLODEnabled(enabled: boolean): void;
  getTargetFPS(): number;
  setTargetFPS(fps: number): void;
  getQualityLevel(): number;
  setQualityLevel(level: number): void;
  addEventListener(event: string, handler: Function): void;
  removeEventListener(event: string, handler: Function): void;
}

// 模拟性能监控器
const PerformanceMonitor = {
  getInstance() {
    return {
      getFPS() {
        return 60 + Math.random() * 10 - 5; // 模拟FPS在55-65之间波动
      }
    };
  }
};

// 模拟引擎服务
const EngineService = {
  getInstance() {
    return {
      getEngine() {
        return {
          getSystem(_name: string) {
            return null; // 模拟系统不存在
          },
          addSystem(_system: any) {
            // 模拟添加系统
          }
        };
      }
    };
  }
};

// 模拟动态LOD系统
// eslint-disable-next-line @typescript-eslint/no-unused-vars
class MockDynamicLODSystem implements DynamicLODSystem {
  private enabled = true;
  private dynamicLODEnabled = true;
  private targetFPS = 60;
  private qualityLevel = 0.5;
  private eventListeners: Map<string, Function[]> = new Map();

  isEnabled(): boolean {
    return this.enabled;
  }

  setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  isDynamicLODEnabled(): boolean {
    return this.dynamicLODEnabled;
  }

  setDynamicLODEnabled(enabled: boolean): void {
    this.dynamicLODEnabled = enabled;
  }

  getTargetFPS(): number {
    return this.targetFPS;
  }

  setTargetFPS(fps: number): void {
    this.targetFPS = fps;
  }

  getQualityLevel(): number {
    return this.qualityLevel;
  }

  setQualityLevel(level: number): void {
    this.qualityLevel = level;
  }

  addEventListener(event: string, handler: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(handler);
  }

  removeEventListener(event: string, handler: Function): void {
    const handlers = this.eventListeners.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }
}

/**
 * 动态LOD面板组件
 */
const DynamicLODPanel: React.FC = () => {
  const { t } = useTranslation();
  
  // 状态
  const [isEnabled, setIsEnabled] = useState(true);
  const [isDynamicLODEnabled, setIsDynamicLODEnabled] = useState(true);
  const [targetFPS, setTargetFPS] = useState(60);
  const [qualityLevel, setQualityLevel] = useState(0.5);
  const [adjustmentSensitivity, setAdjustmentSensitivity] = useState(0.1);
  const [adjustmentInterval, setAdjustmentInterval] = useState(1000);
  const [useSmoothTransition, setUseSmoothTransition] = useState(true);
  const [transitionTime, setTransitionTime] = useState(500);
  const [useAutoQualityAdjustment, setUseAutoQualityAdjustment] = useState(true);
  const [useDistanceOffset, setUseDistanceOffset] = useState(true);
  const [distanceOffsetFactor, setDistanceOffsetFactor] = useState(1.0);
  const [currentFPS, setCurrentFPS] = useState(60);
  const [isInitialized, setIsInitialized] = useState(false);
  const [lodSystem, setLodSystem] = useState<DynamicLODSystem | null>(null);
  
  // 引用
  const timerRef = useRef<number | null>(null);
  
  // 初始化
  useEffect(() => {
    // 获取引擎
    const engine = EngineService.getInstance().getEngine();
    if (!engine) {
      return;
    }
    
    // 获取动态LOD系统
    let system = engine.getSystem('DynamicLODSystem') as DynamicLODSystem | null;

    // 如果系统不存在，则创建
    if (!system) {
      system = new MockDynamicLODSystem();

      // 添加到引擎
      engine.addSystem(system);
    }
    
    // 设置系统
    setLodSystem(system);
    
    // 更新状态
    setIsEnabled(system.isEnabled());
    setIsDynamicLODEnabled(system.isDynamicLODEnabled());
    setTargetFPS(system.getTargetFPS());
    setQualityLevel(system.getQualityLevel());
    
    // 添加事件监听器
    system.addEventListener(DynamicLODSystemEventType.QUALITY_LEVEL_ADJUSTED, handleQualityLevelAdjusted);
    system.addEventListener(DynamicLODSystemEventType.PERFORMANCE_TARGET_ADJUSTED, handlePerformanceTargetAdjusted);
    
    // 设置定时器定期更新FPS
    timerRef.current = window.setInterval(() => {
      setCurrentFPS(PerformanceMonitor.getInstance().getFPS());
    }, 500);
    
    setIsInitialized(true);
    
    // 清理函数
    return () => {
      // 移除事件监听器
      if (system) {
        system.removeEventListener(DynamicLODSystemEventType.QUALITY_LEVEL_ADJUSTED, handleQualityLevelAdjusted);
        system.removeEventListener(DynamicLODSystemEventType.PERFORMANCE_TARGET_ADJUSTED, handlePerformanceTargetAdjusted);
      }
      
      // 清除定时器
      if (timerRef.current !== null) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, []);
  
  // 处理质量级别调整事件
  const handleQualityLevelAdjusted = (newLevel: number, _oldLevel: number) => {
    setQualityLevel(newLevel);
  };

  // 处理性能目标调整事件
  const handlePerformanceTargetAdjusted = (newTarget: number, _oldTarget: number) => {
    setTargetFPS(newTarget);
  };
  
  // 启用/禁用系统
  const handleEnableChange = (checked: boolean) => {
    if (lodSystem) {
      lodSystem.setEnabled(checked);
      setIsEnabled(checked);
    }
  };
  
  // 启用/禁用动态LOD
  const handleDynamicLODEnableChange = (checked: boolean) => {
    if (lodSystem) {
      lodSystem.setDynamicLODEnabled(checked);
      setIsDynamicLODEnabled(checked);
    }
  };
  
  // 设置目标帧率
  const handleTargetFPSChange = (value: number | null) => {
    if (value !== null && lodSystem) {
      lodSystem.setTargetFPS(value);
      setTargetFPS(value);
    }
  };

  // 设置质量级别
  const handleQualityLevelChange = (value: number | null) => {
    if (value !== null && lodSystem) {
      lodSystem.setQualityLevel(value);
      setQualityLevel(value);
    }
  };

  // 设置调整灵敏度
  const handleAdjustmentSensitivityChange = (value: number | null) => {
    if (value !== null) {
      setAdjustmentSensitivity(value);
      if (lodSystem) {
        // 假设有这个方法
        (lodSystem as any).adjustmentSensitivity = value;
      }
    }
  };

  // 设置调整间隔
  const handleAdjustmentIntervalChange = (value: number | null) => {
    if (value !== null) {
      setAdjustmentInterval(value);
      if (lodSystem) {
        // 假设有这个方法
        (lodSystem as any).adjustmentInterval = value;
      }
    }
  };
  
  // 启用/禁用平滑过渡
  const handleSmoothTransitionChange = (checked: boolean) => {
    setUseSmoothTransition(checked);
    if (lodSystem) {
      // 假设有这个方法
      (lodSystem as any).useSmoothTransition = checked;
    }
  };
  
  // 设置过渡时间
  const handleTransitionTimeChange = (value: number | null) => {
    if (value !== null) {
      setTransitionTime(value);
      if (lodSystem) {
        // 假设有这个方法
        (lodSystem as any).transitionTime = value;
      }
    }
  };

  // 启用/禁用自动质量调整
  const handleAutoQualityAdjustmentChange = (checked: boolean) => {
    setUseAutoQualityAdjustment(checked);
    if (lodSystem) {
      // 假设有这个方法
      (lodSystem as any).useAutoQualityAdjustment = checked;
    }
  };

  // 启用/禁用距离偏移
  const handleDistanceOffsetChange = (checked: boolean) => {
    setUseDistanceOffset(checked);
    if (lodSystem) {
      // 假设有这个方法
      (lodSystem as any).useDistanceOffset = checked;
    }
  };

  // 设置距离偏移系数
  const handleDistanceOffsetFactorChange = (value: number | null) => {
    if (value !== null) {
      setDistanceOffsetFactor(value);
      if (lodSystem) {
        // 假设有这个方法
        (lodSystem as any).distanceOffsetFactor = value;
      }
    }
  };
  
  // 应用预设
  const applyPreset = (preset: 'low' | 'medium' | 'high' | 'ultra') => {
    if (!lodSystem) return;
    
    switch (preset) {
      case 'low':
        lodSystem.setQualityLevel(0.25);
        break;
      case 'medium':
        lodSystem.setQualityLevel(0.5);
        break;
      case 'high':
        lodSystem.setQualityLevel(0.75);
        break;
      case 'ultra':
        lodSystem.setQualityLevel(1.0);
        break;
    }
  };
  
  // 获取FPS状态标签
  const getFPSStatusTag = () => {
    if (currentFPS >= targetFPS) {
      return <Tag color="success">{t('optimization.dynamicLOD.fpsGood')}</Tag>;
    } else if (currentFPS >= targetFPS * 0.8) {
      return <Tag color="warning">{t('optimization.dynamicLOD.fpsWarning')}</Tag>;
    } else {
      return <Tag color="error">{t('optimization.dynamicLOD.fpsCritical')}</Tag>;
    }
  };
  
  // 获取质量级别文本
  const getQualityLevelText = () => {
    if (qualityLevel >= 0.75) {
      return t('optimization.dynamicLOD.qualityHigh');
    } else if (qualityLevel >= 0.5) {
      return t('optimization.dynamicLOD.qualityMedium');
    } else if (qualityLevel >= 0.25) {
      return t('optimization.dynamicLOD.qualityLow');
    } else {
      return t('optimization.dynamicLOD.qualityVeryLow');
    }
  };
  
  // 如果未初始化，显示加载中
  if (!isInitialized) {
    return (
      <Card title={t('optimization.dynamicLOD.title')} className="dynamic-lod-panel">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <div className="loading-text">{t('common.loading')}</div>
        </div>
      </Card>
    );
  }
  
  return (
    <Card 
      title={
        <Space>
          <span>{t('optimization.dynamicLOD.title')}</span>
          <Switch 
            checked={isEnabled} 
            onChange={handleEnableChange} 
            size="small"
          />
        </Space>
      } 
      className="dynamic-lod-panel"
      extra={
        <Tooltip title={t('common.refresh')}>
          <Button 
            icon={<ReloadOutlined />} 
            size="small" 
            type="text"
            onClick={() => {
              if (lodSystem) {
                setIsEnabled(lodSystem.isEnabled());
                setIsDynamicLODEnabled(lodSystem.isDynamicLODEnabled());
                setTargetFPS(lodSystem.getTargetFPS());
                setQualityLevel(lodSystem.getQualityLevel());
              }
            }}
          />
        </Tooltip>
      }
    >
      <Collapse defaultActiveKey={['1', '2']}>
        <Panel header={t('optimization.dynamicLOD.currentStatus')} key="1">
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <div className="status-item">
                <div className="status-label">{t('optimization.dynamicLOD.currentFPS')}</div>
                <div className="status-value">
                  {currentFPS.toFixed(1)}
                  {getFPSStatusTag()}
                </div>
              </div>
            </Col>
            <Col span={12}>
              <div className="status-item">
                <div className="status-label">{t('optimization.dynamicLOD.qualityLevel')}</div>
                <div className="status-value">
                  {(qualityLevel * 100).toFixed(0)}%
                  <Tag color="blue">{getQualityLevelText()}</Tag>
                </div>
              </div>
            </Col>
            <Col span={24}>
              <Progress 
                percent={qualityLevel * 100} 
                status="active" 
                strokeColor={{
                  '0%': '#ff4d4f',
                  '25%': '#faad14',
                  '50%': '#52c41a',
                  '75%': '#1890ff',
                  '100%': '#722ed1'
                }}
              />
            </Col>
            <Col span={24}>
              <Space>
                <Button size="small" onClick={() => applyPreset('low')}>
                  {t('optimization.dynamicLOD.presetLow')}
                </Button>
                <Button size="small" onClick={() => applyPreset('medium')}>
                  {t('optimization.dynamicLOD.presetMedium')}
                </Button>
                <Button size="small" onClick={() => applyPreset('high')}>
                  {t('optimization.dynamicLOD.presetHigh')}
                </Button>
                <Button size="small" onClick={() => applyPreset('ultra')}>
                  {t('optimization.dynamicLOD.presetUltra')}
                </Button>
              </Space>
            </Col>
          </Row>
        </Panel>
        
        <Panel header={t('optimization.dynamicLOD.settings')} key="2">
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.dynamicLOD.enableDynamicLOD')}
                  <Tooltip title={t('optimization.dynamicLOD.enableDynamicLODTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={isDynamicLODEnabled} 
                    onChange={handleDynamicLODEnableChange}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={24}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.dynamicLOD.targetFPS')}
                </div>
                <div className="setting-control">
                  <Slider
                    min={30}
                    max={120}
                    step={1}
                    value={targetFPS}
                    onChange={handleTargetFPSChange}
                    disabled={!isDynamicLODEnabled}
                    style={{ width: '80%', marginRight: 16 }}
                  />
                  <InputNumber
                    min={30}
                    max={120}
                    step={1}
                    value={targetFPS}
                    onChange={handleTargetFPSChange}
                    disabled={!isDynamicLODEnabled}
                    style={{ width: '20%' }}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={24}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.dynamicLOD.qualityLevel')}
                </div>
                <div className="setting-control">
                  <Slider
                    min={0}
                    max={1}
                    step={0.01}
                    value={qualityLevel}
                    onChange={handleQualityLevelChange}
                    disabled={!isEnabled}
                    style={{ width: '80%', marginRight: 16 }}
                  />
                  <InputNumber
                    min={0}
                    max={1}
                    step={0.01}
                    value={qualityLevel}
                    onChange={handleQualityLevelChange}
                    disabled={!isEnabled}
                    style={{ width: '20%' }}
                  />
                </div>
              </div>
            </Col>
          </Row>
        </Panel>
        
        <Panel header={t('optimization.dynamicLOD.advancedSettings')} key="3">
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.dynamicLOD.adjustmentSensitivity')}
                  <Tooltip title={t('optimization.dynamicLOD.adjustmentSensitivityTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Slider
                    min={0.01}
                    max={0.5}
                    step={0.01}
                    value={adjustmentSensitivity}
                    onChange={handleAdjustmentSensitivityChange}
                    disabled={!isDynamicLODEnabled}
                    style={{ width: '80%', marginRight: 16 }}
                  />
                  <InputNumber
                    min={0.01}
                    max={0.5}
                    step={0.01}
                    value={adjustmentSensitivity}
                    onChange={handleAdjustmentSensitivityChange}
                    disabled={!isDynamicLODEnabled}
                    style={{ width: '20%' }}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={24}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.dynamicLOD.adjustmentInterval')}
                </div>
                <div className="setting-control">
                  <Slider
                    min={100}
                    max={5000}
                    step={100}
                    value={adjustmentInterval}
                    onChange={handleAdjustmentIntervalChange}
                    disabled={!isDynamicLODEnabled}
                    style={{ width: '80%', marginRight: 16 }}
                  />
                  <InputNumber
                    min={100}
                    max={5000}
                    step={100}
                    value={adjustmentInterval}
                    onChange={handleAdjustmentIntervalChange}
                    disabled={!isDynamicLODEnabled}
                    style={{ width: '20%' }}
                    addonAfter="ms"
                  />
                </div>
              </div>
            </Col>
            
            <Col span={24}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.dynamicLOD.useSmoothTransition')}
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={useSmoothTransition} 
                    onChange={handleSmoothTransitionChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={24}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.dynamicLOD.transitionTime')}
                </div>
                <div className="setting-control">
                  <Slider
                    min={0}
                    max={2000}
                    step={100}
                    value={transitionTime}
                    onChange={handleTransitionTimeChange}
                    disabled={!isEnabled || !useSmoothTransition}
                    style={{ width: '80%', marginRight: 16 }}
                  />
                  <InputNumber
                    min={0}
                    max={2000}
                    step={100}
                    value={transitionTime}
                    onChange={handleTransitionTimeChange}
                    disabled={!isEnabled || !useSmoothTransition}
                    style={{ width: '20%' }}
                    addonAfter="ms"
                  />
                </div>
              </div>
            </Col>
            
            <Col span={24}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.dynamicLOD.useAutoQualityAdjustment')}
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={useAutoQualityAdjustment} 
                    onChange={handleAutoQualityAdjustmentChange}
                    disabled={!isDynamicLODEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={24}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.dynamicLOD.useDistanceOffset')}
                  <Tooltip title={t('optimization.dynamicLOD.useDistanceOffsetTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={useDistanceOffset} 
                    onChange={handleDistanceOffsetChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={24}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.dynamicLOD.distanceOffsetFactor')}
                </div>
                <div className="setting-control">
                  <Slider
                    min={0}
                    max={5}
                    step={0.1}
                    value={distanceOffsetFactor}
                    onChange={handleDistanceOffsetFactorChange}
                    disabled={!isEnabled || !useDistanceOffset}
                    style={{ width: '80%', marginRight: 16 }}
                  />
                  <InputNumber
                    min={0}
                    max={5}
                    step={0.1}
                    value={distanceOffsetFactor}
                    onChange={handleDistanceOffsetFactorChange}
                    disabled={!isEnabled || !useDistanceOffset}
                    style={{ width: '20%' }}
                  />
                </div>
              </div>
            </Col>
          </Row>
        </Panel>
      </Collapse>
    </Card>
  );
};

export default DynamicLODPanel;
