{"blockchain": {"title": "Blockchain", "wallet": {"title": "Wallet", "connect": "Connect Wallet", "disconnect": "Disconnect", "connected": "Connected", "disconnected": "Disconnected", "connecting": "Connecting...", "connectionFailed": "Connection Failed", "selectWallet": "Select Wallet", "metamask": "MetaMask", "walletConnect": "WalletConnect", "coinbaseWallet": "Coinbase Wallet", "account": "Account", "balance": "Balance", "network": "Network", "switchNetwork": "Switch Network", "refreshBalance": "Refresh Balance", "copyAddress": "Copy Address", "addressCopied": "Address Copied", "status": {"notConnected": "Wallet Not Connected", "connectPrompt": "Connect your Web3 wallet to use NFT and digital asset features", "connectionError": "Connection Error", "networkError": "Network Error", "unsupportedNetwork": "Unsupported Network"}}, "nft": {"title": "NFT Assets", "myNFTs": "My NFTs", "mint": {"title": "Mint New NFT", "name": "NFT Name", "description": "Description", "image": "Image", "assetType": "Asset Type", "licenseType": "License Type", "royalty": "Royalty", "royaltyPercentage": "Royalty Percentage", "royaltyRecipient": "Royalty Recipient", "educationalMetadata": "Educational Metadata", "subject": "Subject", "gradeLevel": "Grade Level", "difficulty": "Difficulty Level", "learningObjectives": "Learning Objectives", "estimatedTime": "Estimated Time", "prerequisites": "Prerequisites", "confirm": "Confirm Mint", "cancel": "Cancel", "success": "NFT minted successfully!", "error": "Failed to mint NFT", "processing": "Minting..."}, "transfer": {"title": "Transfer NFT", "recipient": "Recipient Address", "recipientPlaceholder": "Enter recipient wallet address", "confirm": "Confirm Transfer", "cancel": "Cancel", "success": "NFT transferred successfully!", "error": "Failed to transfer NFT", "processing": "Transferring...", "invalidAddress": "Invalid address format", "sameAddress": "Cannot transfer to yourself"}, "display": {"success": "NFT displayed in scene", "error": "Failed to display NFT", "hide": {"success": "NFT hidden from scene", "error": "Failed to hide NFT"}}, "hide": "<PERSON>de", "details": "Details", "metadata": "<PERSON><PERSON><PERSON>", "properties": "Properties", "history": "History", "search": "Search NFT name or description", "filter": "Filter", "sort": "Sort", "empty": {"title": "You don't have any NFT assets yet", "description": "Mint your first NFT to start your digital asset journey", "action": "Mint First NFT"}, "loading": "Loading...", "error": "Failed to load", "types": {"all": "All Types", "model": "3D Model", "texture": "Texture", "audio": "Audio", "scene": "Scene", "animation": "Animation", "material": "Material"}, "licenses": {"CC0": "CC0 Public Domain", "CC_BY": "CC BY Attribution", "CC_BY_SA": "CC BY-SA Attribution-ShareAlike", "CC_BY_NC": "CC BY-NC Attribution-NonCommercial", "MIT": "MIT License", "GPL": "GPL License", "Commercial": "Commercial License", "Custom": "Custom License"}, "subjects": {"math": "Mathematics", "physics": "Physics", "chemistry": "Chemistry", "biology": "Biology", "history": "History", "geography": "Geography", "language": "Language", "art": "Art", "music": "Music", "programming": "Programming", "engineering": "Engineering", "other": "Other"}, "gradeLevels": {"preschool": "Preschool", "elementary": "Elementary", "middle": "Middle School", "high": "High School", "college": "College", "graduate": "Graduate", "adult": "Adult Education"}}, "marketplace": {"title": "Digital Asset Marketplace", "browse": "Browse Marketplace", "search": "Search", "categories": "Categories", "featured": "Featured", "popular": "Popular", "latest": "Latest", "price": "Price", "priceRange": "Price Range", "currency": "<PERSON><PERSON><PERSON><PERSON>", "buy": "Buy", "sell": "<PERSON>ll", "auction": {"title": "Auction", "startingPrice": "Starting Price", "reservePrice": "Reserve Price", "currentBid": "Current Bid", "timeLeft": "Time Left", "placeBid": "Place Bid", "bidAmount": "<PERSON><PERSON>", "minimumBid": "Minimum Bid", "bidSuccess": "<PERSON><PERSON> placed successfully", "bidError": "Failed to place bid", "auctionEnded": "Auction Ended", "winner": "Winner", "winningBid": "Winning Bid"}, "bid": "Bid", "listing": {"title": "List NFT", "price": "Price", "currency": "<PERSON><PERSON><PERSON><PERSON>", "duration": "Duration", "confirm": "Confirm Listing", "cancel": "Cancel", "success": "NFT listed successfully", "error": "Failed to list NFT"}, "purchase": {"title": "Purchase NFT", "price": "Price", "total": "Total", "confirm": "Confirm Purchase", "cancel": "Cancel", "success": "Purchase successful!", "error": "Purchase failed", "processing": "Processing...", "insufficientFunds": "Insufficient funds"}, "filters": {"priceRange": "Price Range", "assetType": "Asset Type", "creator": "Creator", "verified": "Verified", "forSale": "For Sale", "hasOffers": "Has Offers"}, "sorting": {"newest": "Newest", "oldest": "Oldest", "priceLowToHigh": "Price: Low to High", "priceHighToLow": "Price: High to Low", "mostViewed": "Most Viewed", "mostLiked": "Most Liked"}}, "transactions": {"title": "Transaction History", "type": "Type", "amount": "Amount", "status": {"pending": "Pending", "confirmed": "Confirmed", "failed": "Failed", "cancelled": "Cancelled"}, "date": "Date", "hash": "Transaction Hash", "viewOnExplorer": "View on Block Explorer", "types": {"mint": "Mint", "transfer": "Transfer", "purchase": "Purchase", "sale": "Sale", "bid": "Bid", "auction": "Auction"}, "empty": "No transactions yet", "refresh": "Refresh", "filter": "Filter", "export": "Export"}, "networks": {"ethereum": "Ethereum Mainnet", "polygon": "Polygon", "bsc": "Binance Smart Chain", "arbitrum": "Arbitrum", "optimism": "Optimism", "goerli": "<PERSON><PERSON><PERSON>", "mumbai": "Mumbai Testnet", "unsupported": "Unsupported Network"}, "errors": {"walletNotConnected": "Wallet not connected", "networkMismatch": "Network mismatch", "insufficientBalance": "Insufficient balance", "transactionFailed": "Transaction failed", "userRejected": "User rejected the transaction", "contractError": "Contract execution error", "networkError": "Network error", "unknownError": "Unknown error", "invalidInput": "Invalid input", "notOwner": "You are not the owner of this NFT", "alreadyListed": "NFT is already listed", "notForSale": "NFT is not for sale", "auctionEnded": "Auction has ended", "bidTooLow": "Bid is too low"}, "success": {"walletConnected": "<PERSON><PERSON> connected successfully", "walletDisconnected": "Wallet disconnected", "networkSwitched": "Network switched successfully", "transactionSubmitted": "Transaction submitted", "transactionConfirmed": "Transaction confirmed", "nftMinted": "NFT minted successfully", "nftTransferred": "NFT transferred successfully", "nftListed": "NFT listed successfully", "nftPurchased": "NFT purchased successfully", "bidPlaced": "<PERSON><PERSON> placed successfully", "auctionCreated": "Auction created successfully"}, "common": {"loading": "Loading...", "confirm": "Confirm", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "copy": "Copy", "share": "Share", "download": "Download", "upload": "Upload", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "retry": "Retry", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "clear": "Clear", "select": "Select", "selectAll": "Select All", "none": "None", "all": "All", "yes": "Yes", "no": "No", "optional": "Optional", "required": "Required", "recommended": "Recommended", "advanced": "Advanced", "basic": "Basic", "settings": "Settings", "help": "Help", "about": "About", "version": "Version", "update": "Update", "new": "New", "create": "Create", "import": "Import", "export": "Export"}}}