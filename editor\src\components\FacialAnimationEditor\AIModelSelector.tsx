/**
 * AI模型选择器组件
 * 用于选择和配置AI模型
 */
import React, { useState } from 'react';
import { Select, Form, Switch, InputNumber, Slider, Tooltip, Button, Collapse, Tag, Space, Divider, Input } from 'antd';
import { InfoCircleOutlined, SettingOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Option } = Select;
const { Panel } = Collapse;

/**
 * AI模型类型
 */
export enum AIModelType {
  BERT = 'bert',
  ROBERTA = 'roberta',
  DISTILBERT = 'distilbert',
  ALBERT = 'albert',
  XLNET = 'xlnet',
  CUSTOM = 'custom'
}

/**
 * AI模型配置
 */
export interface AIModelConfig {
  modelType: AIModelType;
  modelVariant?: string;
  useLocalModel: boolean;
  modelPath?: string;
  useGPU: boolean;
  useQuantized: boolean;
  quantizationBits?: 8 | 16 | 32;
  batchSize?: number;
  emotionCategories?: string[];
  useCache: boolean;
  cacheSize?: number;
}

/**
 * AI模型选择器属性
 */
interface AIModelSelectorProps {
  /** 初始配置 */
  initialConfig?: Partial<AIModelConfig>;
  /** 配置变更回调 */
  onChange?: (config: AIModelConfig) => void;
  /** 是否禁用 */
  disabled?: boolean;
}

/**
 * 模型变体选项
 */
const MODEL_VARIANTS: Record<AIModelType, { label: string, value: string }[]> = {
  [AIModelType.BERT]: [
    { label: '基础版', value: 'base' },
    { label: '大型版', value: 'large' },
    { label: '多语言版', value: 'multilingual' }
  ],
  [AIModelType.ROBERTA]: [
    { label: '基础版', value: 'base' },
    { label: '大型版', value: 'large' },
    { label: '精简版', value: 'distilled' }
  ],
  [AIModelType.DISTILBERT]: [
    { label: '基础版', value: 'base' },
    { label: '多语言版', value: 'multilingual' }
  ],
  [AIModelType.ALBERT]: [
    { label: '基础版', value: 'base' },
    { label: '大型版', value: 'large' },
    { label: '超大型版', value: 'xlarge' }
  ],
  [AIModelType.XLNET]: [
    { label: '基础版', value: 'base' },
    { label: '大型版', value: 'large' }
  ],
  [AIModelType.CUSTOM]: [
    { label: '自定义', value: 'custom' }
  ]
};

/**
 * 默认情感类别
 */
const DEFAULT_EMOTION_CATEGORIES = [
  'happy', 'sad', 'angry', 'surprised', 'fear', 'disgust', 'neutral'
];

/**
 * 扩展情感类别
 */
const EXTENDED_EMOTION_CATEGORIES = [
  'happy', 'sad', 'angry', 'surprised', 'fear', 'disgust', 'neutral',
  'excited', 'anxious', 'content', 'bored', 'confused', 'disappointed',
  'proud', 'grateful', 'hopeful', 'lonely', 'loving', 'nostalgic'
];

/**
 * AI模型选择器组件
 */
const AIModelSelector: React.FC<AIModelSelectorProps> = ({
  initialConfig,
  onChange,
  disabled = false
}) => {
  const { t } = useTranslation();
  
  // 默认配置
  const defaultConfig: AIModelConfig = {
    modelType: AIModelType.BERT,
    modelVariant: 'base',
    useLocalModel: false,
    useGPU: false,
    useQuantized: false,
    quantizationBits: 8,
    batchSize: 1,
    emotionCategories: DEFAULT_EMOTION_CATEGORIES,
    useCache: true,
    cacheSize: 100
  };
  
  // 合并初始配置
  const mergedConfig = { ...defaultConfig, ...initialConfig };
  
  // 状态
  const [config, setConfig] = useState<AIModelConfig>(mergedConfig);
  const [showAdvanced, setShowAdvanced] = useState(false);
  
  // 处理配置变更
  const handleConfigChange = (key: keyof AIModelConfig, value: any) => {
    const newConfig = { ...config, [key]: value };
    
    // 如果模型类型变更，更新模型变体
    if (key === 'modelType') {
      newConfig.modelVariant = MODEL_VARIANTS[value as AIModelType][0].value;
    }
    
    setConfig(newConfig);
    onChange?.(newConfig);
  };
  
  // 处理情感类别变更
  const handleEmotionCategoriesChange = (categories: string[]) => {
    handleConfigChange('emotionCategories', categories);
  };
  
  // 使用扩展情感类别
  const useExtendedEmotions = () => {
    handleConfigChange('emotionCategories', EXTENDED_EMOTION_CATEGORIES);
  };
  
  // 使用默认情感类别
  const useDefaultEmotions = () => {
    handleConfigChange('emotionCategories', DEFAULT_EMOTION_CATEGORIES);
  };
  
  // 渲染模型类型选择器
  const renderModelTypeSelector = () => (
    <Form.Item label={t('ai.modelType')}>
      <Select
        value={config.modelType}
        onChange={(value) => handleConfigChange('modelType', value)}
        disabled={disabled}
      >
        <Option value={AIModelType.BERT}>BERT</Option>
        <Option value={AIModelType.ROBERTA}>RoBERTa</Option>
        <Option value={AIModelType.DISTILBERT}>DistilBERT</Option>
        <Option value={AIModelType.ALBERT}>ALBERT</Option>
        <Option value={AIModelType.XLNET}>XLNet</Option>
        <Option value={AIModelType.CUSTOM}>{t('ai.customModel')}</Option>
      </Select>
    </Form.Item>
  );
  
  // 渲染模型变体选择器
  const renderModelVariantSelector = () => (
    <Form.Item label={t('ai.modelVariant')}>
      <Select
        value={config.modelVariant}
        onChange={(value) => handleConfigChange('modelVariant', value)}
        disabled={disabled}
      >
        {MODEL_VARIANTS[config.modelType].map((variant) => (
          <Option key={variant.value} value={variant.value}>
            {variant.label}
          </Option>
        ))}
      </Select>
    </Form.Item>
  );
  
  // 渲染本地模型选项
  const renderLocalModelOptions = () => (
    <>
      <Form.Item label={t('ai.useLocalModel')}>
        <Switch
          checked={config.useLocalModel}
          onChange={(checked) => handleConfigChange('useLocalModel', checked)}
          disabled={disabled}
        />
      </Form.Item>
      
      {config.useLocalModel && (
        <Form.Item label={t('ai.modelPath')}>
          <Input
            value={config.modelPath}
            onChange={(e) => handleConfigChange('modelPath', e.target.value)}
            disabled={disabled}
            placeholder={t('ai.modelPathPlaceholder') as string}
            suffix={
              <Tooltip title={t('ai.modelPathTooltip') as string}>
                <InfoCircleOutlined style={{ color: 'rgba(0,0,0,.45)' }} />
              </Tooltip>
            }
          />
        </Form.Item>
      )}
      
      {config.useLocalModel && (
        <Form.Item label={t('ai.useGPU')}>
          <Switch
            checked={config.useGPU}
            onChange={(checked) => handleConfigChange('useGPU', checked)}
            disabled={disabled}
          />
        </Form.Item>
      )}
    </>
  );
  
  // 渲染高级选项
  const renderAdvancedOptions = () => (
    <Collapse
      bordered={false}
      activeKey={showAdvanced ? ['advanced'] : []}
      onChange={(key) => setShowAdvanced(Array.isArray(key) ? key.includes('advanced') : false)}
    >
      <Panel
        header={
          <span>
            <SettingOutlined /> {t('ai.advancedOptions')}
          </span>
        }
        key="advanced"
      >
        <Form.Item label={t('ai.useQuantized')}>
          <Switch
            checked={config.useQuantized}
            onChange={(checked) => handleConfigChange('useQuantized', checked)}
            disabled={disabled}
          />
        </Form.Item>
        
        {config.useQuantized && (
          <Form.Item label={t('ai.quantizationBits')}>
            <Select
              value={config.quantizationBits}
              onChange={(value) => handleConfigChange('quantizationBits', value)}
              disabled={disabled}
            >
              <Option value={8}>8位</Option>
              <Option value={16}>16位</Option>
              <Option value={32}>32位</Option>
            </Select>
          </Form.Item>
        )}
        
        <Form.Item label={t('ai.batchSize')}>
          <InputNumber
            min={1}
            max={32}
            value={config.batchSize}
            onChange={(value) => handleConfigChange('batchSize', value)}
            disabled={disabled}
          />
        </Form.Item>
        
        <Form.Item label={t('ai.useCache')}>
          <Switch
            checked={config.useCache}
            onChange={(checked) => handleConfigChange('useCache', checked)}
            disabled={disabled}
          />
        </Form.Item>
        
        {config.useCache && (
          <Form.Item label={t('ai.cacheSize')}>
            <Slider
              min={10}
              max={500}
              step={10}
              value={config.cacheSize}
              onChange={(value) => handleConfigChange('cacheSize', value)}
              disabled={disabled}
            />
          </Form.Item>
        )}
        
        <Divider>{t('ai.emotionCategories')}</Divider>
        
        <Form.Item>
          <Space wrap>
            {EXTENDED_EMOTION_CATEGORIES.map((emotion) => (
              <Tag
                key={emotion}
                color={config.emotionCategories?.includes(emotion) ? 'blue' : 'default'}
                onClick={() => {
                  const categories = [...(config.emotionCategories || [])];
                  if (categories.includes(emotion)) {
                    categories.splice(categories.indexOf(emotion), 1);
                  } else {
                    categories.push(emotion);
                  }
                  handleEmotionCategoriesChange(categories);
                }}
                style={{ cursor: 'pointer' }}
              >
                {emotion}
              </Tag>
            ))}
          </Space>
        </Form.Item>
        
        <Form.Item>
          <Space>
            <Button size="small" onClick={useDefaultEmotions}>
              {t('ai.useDefaultEmotions')}
            </Button>
            <Button size="small" onClick={useExtendedEmotions}>
              {t('ai.useExtendedEmotions')}
            </Button>
          </Space>
        </Form.Item>
      </Panel>
    </Collapse>
  );
  
  return (
    <div className="ai-model-selector">
      <Form layout="vertical">
        {renderModelTypeSelector()}
        {renderModelVariantSelector()}
        {renderLocalModelOptions()}
        {renderAdvancedOptions()}
      </Form>
    </div>
  );
};

export default AIModelSelector;
