/**
 * 地图视图组件样式
 */
.map-view {
  .map-container {
    position: relative;
    
    .map-controls {
      position: absolute;
      top: 10px;
      left: 10px;
      z-index: 1000;
      background: rgba(255, 255, 255, 0.9);
      padding: 12px;
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(4px);
      
      .coordinate-inputs {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-bottom: 12px;
        
        > div {
          display: flex;
          align-items: center;
          gap: 8px;
          
          span {
            min-width: 40px;
            font-size: 12px;
            color: #666;
          }
        }
      }
      
      .zoom-controls {
        border-top: 1px solid #f0f0f0;
        padding-top: 12px;
      }
    }
    
    .map-display {
      position: relative;
      overflow: hidden;
      
      &.loading {
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(45deg, #f5f5f5 25%, transparent 25%), 
                    linear-gradient(-45deg, #f5f5f5 25%, transparent 25%), 
                    linear-gradient(45deg, transparent 75%, #f5f5f5 75%), 
                    linear-gradient(-45deg, transparent 75%, #f5f5f5 75%);
        background-size: 20px 20px;
        background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        animation: loading-bg 1s linear infinite;
      }
      
      .map-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
        color: #666;
        
        .loading-spinner {
          width: 32px;
          height: 32px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #1890ff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }
      
      .map-content {
        width: 100%;
        height: 100%;
        position: relative;
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%);
        
        // 模拟地图网格
        background-image: 
          linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
          linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);
        background-size: 50px 50px;
        
        .center-marker {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 100;
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }
        
        .map-info {
          position: absolute;
          bottom: 10px;
          right: 10px;
          background: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 8px 12px;
          border-radius: 4px;
          font-size: 12px;
          line-height: 1.4;
          font-family: 'Courier New', monospace;
          
          div {
            margin: 2px 0;
          }
        }
      }
    }
    
    .coordinates-display {
      position: absolute;
      bottom: 10px;
      left: 10px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 6px 10px;
      border-radius: 4px;
      font-size: 11px;
      font-family: 'Courier New', monospace;
      z-index: 1000;
    }
  }
}

// 动画定义
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes loading-bg {
  0% { background-position: 0 0, 0 10px, 10px -10px, -10px 0px; }
  100% { background-position: 20px 20px, 20px 30px, 30px 10px, 10px 20px; }
}

// 响应式设计
@media (max-width: 768px) {
  .map-view {
    .map-container {
      .map-controls {
        position: static;
        margin-bottom: 12px;
        background: #fff;
        
        .coordinate-inputs {
          flex-direction: row;
          flex-wrap: wrap;
          gap: 12px;
          
          > div {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
            
            span {
              min-width: auto;
              font-weight: 500;
            }
          }
        }
      }
      
      .map-display {
        .map-info {
          font-size: 10px;
          padding: 6px 8px;
        }
      }
      
      .coordinates-display {
        font-size: 10px;
        padding: 4px 8px;
      }
    }
  }
}

// 暗色主题支持
.dark-theme {
  .map-view {
    .map-container {
      .map-controls {
        background: rgba(30, 30, 30, 0.9);
        color: #fff;
        
        .coordinate-inputs span {
          color: #ccc;
        }
      }
      
      .map-display {
        border-color: #434343;
        
        &.loading {
          background: linear-gradient(45deg, #2a2a2a 25%, transparent 25%), 
                      linear-gradient(-45deg, #2a2a2a 25%, transparent 25%), 
                      linear-gradient(45deg, transparent 75%, #2a2a2a 75%), 
                      linear-gradient(-45deg, transparent 75%, #2a2a2a 75%);
        }
        
        .map-content {
          background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
          
          background-image: 
            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
        }
      }
    }
  }
}

// 高对比度主题支持
.high-contrast-theme {
  .map-view {
    .map-container {
      .map-display {
        border: 2px solid #000;
        
        .map-content {
          background: #fff;
          
          background-image: 
            linear-gradient(#000 1px, transparent 1px),
            linear-gradient(90deg, #000 1px, transparent 1px);
          
          .center-marker {
            color: #000;
          }
          
          .map-info {
            background: #000;
            color: #fff;
            border: 1px solid #fff;
          }
        }
      }
      
      .coordinates-display {
        background: #000;
        color: #fff;
        border: 1px solid #fff;
      }
    }
  }
}
