/**
 * 使用rc-dock的新编辑器页面
 */
import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Tooltip, message } from 'antd';
import {
  SaveOutlined,
  PlayCircleOutlined,
  SettingOutlined,
  ExportOutlined,
  ImportOutlined,
  UndoOutlined,
  RedoOutlined,
  PlusOutlined,
  DeleteOutlined,
  CopyOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  BookOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { setPanelTheme } from '../../store/ui/uiSlice';
import DockLayout from '../../components/panels/DockLayout';
import { ExampleBrowser } from '../../components/ExampleBrowser';
import { initializePlugins } from '../../plugins';
import './Editor.less';

const { Header, Content } = Layout;

const EditorWithDock: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { panelTheme, panelSettings } = useSelector((state: RootState) => state.ui);
  const [exampleBrowserVisible, setExampleBrowserVisible] = useState<boolean>(false);

  // 初始化插件系统
  useEffect(() => {
    initializePlugins();
  }, []);

  // 监听快捷键事件
  useEffect(() => {
    const handleShortcutAction = (event: CustomEvent) => {
      const { actionId } = event.detail;

      switch (actionId) {
        case 'nlp.openSceneGenerator':
          // 这里可以添加打开NLP场景生成面板的逻辑
          break;
        case 'file.save':
          handleSave();
          break;
        case 'file.export':
          handleExport();
          break;
        case 'edit.undo':
          handleUndo();
          break;
        case 'edit.redo':
          handleRedo();
          break;
        default:
          break;
      }
    };

    window.addEventListener('shortcutAction', handleShortcutAction as EventListener);
    return () => {
      window.removeEventListener('shortcutAction', handleShortcutAction as EventListener);
    };
  }, []);

  // 菜单项配置
  const menuItems = [
    {
      key: 'file',
      label: t('editor.menu.file'),
      children: [
        { key: 'new', label: t('editor.menu.new'), icon: <PlusOutlined /> },
        { key: 'save', label: t('editor.menu.save'), icon: <SaveOutlined /> },
        { key: 'export', label: t('editor.menu.export'), icon: <ExportOutlined /> },
        { key: 'import', label: t('editor.menu.import'), icon: <ImportOutlined /> }
      ]
    },
    {
      key: 'edit',
      label: t('editor.menu.edit'),
      children: [
        { key: 'undo', label: t('editor.menu.undo'), icon: <UndoOutlined /> },
        { key: 'redo', label: t('editor.menu.redo'), icon: <RedoOutlined /> },
        { key: 'copy', label: t('editor.menu.copy'), icon: <CopyOutlined /> },
        { key: 'delete', label: t('editor.menu.delete'), icon: <DeleteOutlined /> }
      ]
    },
    {
      key: 'view',
      label: t('editor.menu.view'),
      children: [
        { key: 'show', label: t('editor.menu.show'), icon: <EyeOutlined /> },
        { key: 'hide', label: t('editor.menu.hide'), icon: <EyeInvisibleOutlined /> }
      ]
    },
    {
      key: 'examples',
      label: t('editor.menu.examples'),
      icon: <BookOutlined />
    },
    {
      key: 'settings',
      label: t('editor.menu.settings'),
      icon: <SettingOutlined />
    }
  ];

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'new':
        handleNew();
        break;
      case 'save':
        handleSave();
        break;
      case 'export':
        handleExport();
        break;
      case 'import':
        handleImport();
        break;
      case 'undo':
        handleUndo();
        break;
      case 'redo':
        handleRedo();
        break;
      case 'copy':
        handleCopy();
        break;
      case 'delete':
        handleDelete();
        break;
      case 'examples':
        setExampleBrowserVisible(true);
        break;
      case 'settings':
        handleSettings();
        break;
      default:
        break;
    }
  };

  // 菜单处理函数
  const handleNew = () => {
    message.info(t('editor.actions.new'));
  };

  const handleSave = () => {
    message.success(t('editor.actions.save'));
  };

  const handleExport = () => {
    message.info(t('editor.actions.export'));
  };

  const handleImport = () => {
    message.info(t('editor.actions.import'));
  };

  const handleUndo = () => {
    message.info(t('editor.actions.undo'));
  };

  const handleRedo = () => {
    message.info(t('editor.actions.redo'));
  };

  const handleCopy = () => {
    message.info(t('editor.actions.copy'));
  };

  const handleDelete = () => {
    message.info(t('editor.actions.delete'));
  };

  const handleSettings = () => {
    message.info(t('editor.actions.settings'));
  };

  // 处理面板主题切换
  const handleThemeChange = (theme: 'light' | 'dark' | 'compact') => {
    dispatch(setPanelTheme(theme));
  };



  return (
    <Layout className="editor-layout">
      <Header className="editor-header">
        <div className="logo">DL（Digital Learning）引擎编辑器 - 新面板系统</div>
        <Menu
          theme="dark"
          mode="horizontal"
          items={menuItems}
          onClick={handleMenuClick}
        />
        <div className="header-actions">
          <Tooltip title="切换面板主题">
            <Button 
              type="text" 
              onClick={() => handleThemeChange(panelTheme === 'light' ? 'dark' : 'light')}
            >
              {panelTheme === 'light' ? '🌙' : '☀️'}
            </Button>
          </Tooltip>
          <Button type="primary" icon={<PlayCircleOutlined />}>
            {t('editor.preview')}
          </Button>
        </div>
      </Header>
      
      <Content className="editor-content">
        <DockLayout 
          className={`dock-layout ${panelTheme}-theme ${panelSettings.compactMode ? 'compact-mode' : ''}`}
          onLayoutChange={(layout) => {
            console.log('Layout changed:', layout);
          }}
        />
      </Content>

      {/* 示例浏览器 */}
      <ExampleBrowser
        visible={exampleBrowserVisible}
        onClose={() => setExampleBrowserVisible(false)}
      />
    </Layout>
  );
};

export default EditorWithDock;
