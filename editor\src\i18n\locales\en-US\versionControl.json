{"versionControl": {"versionControl": "Version Control", "branches": "Branches", "commits": "Commits", "workingDirectory": "Working Directory", "newBranch": "New Branch", "createBranch": "Create Branch", "branchName": "Branch Name", "branchType": "Branch Type", "description": "Description", "baseBranch": "Base Branch", "switch": "Switch", "current": "Current", "protected": "Protected", "commit": "Commit", "createCommit": "Create Commit", "commitMessage": "Commit Message", "stage": "Stage", "unstage": "Unstage", "stagedChanges": "Staged Changes", "unstagedChanges": "Unstaged Changes", "noChanges": "No Changes", "workingDirectoryClean": "Working directory is clean", "fileDiff": "File Diff", "merge": "<PERSON><PERSON>", "mergeBranch": "Merge <PERSON>", "sourceBranch": "Source Branch", "targetBranch": "Target Branch", "mergeStrategy": "Merge Strategy", "mergeMessage": "Merge Message", "createTag": "Create Tag", "tagName": "Tag Name", "tagMessage": "Tag Message", "annotated": "Annotated", "version": "Version", "commitHash": "<PERSON><PERSON><PERSON>"}}