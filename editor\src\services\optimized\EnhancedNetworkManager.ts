/**
 * 增强的网络通信管理器
 * 专为支持100+并发用户优化的网络通信系统
 */

// 消息类型枚举
export enum MessageType {
  // 用户操作
  USER_JOIN = 'user_join',
  USER_LEAVE = 'user_leave',
  USER_CURSOR = 'user_cursor',
  
  // 对象操作
  OBJECT_CREATE = 'object_create',
  OBJECT_UPDATE = 'object_update',
  OBJECT_DELETE = 'object_delete',
  OBJECT_SELECT = 'object_select',
  OBJECT_LOCK = 'object_lock',
  OBJECT_UNLOCK = 'object_unlock',
  
  // 场景操作
  SCENE_LOAD = 'scene_load',
  SCENE_SAVE = 'scene_save',
  SCENE_SYNC = 'scene_sync',
  
  // 系统消息
  HEARTBEAT = 'heartbeat',
  ERROR = 'error',
  BATCH_UPDATE = 'batch_update',
}

// 消息接口
export interface NetworkMessage {
  id: string;
  type: MessageType;
  data: any;
  timestamp: number;
  userId?: string;
  priority: number; // 0-10，数值越高优先级越高
  reliable?: boolean; // 是否需要可靠传输
}

// 连接状态
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
}

// 网络统计信息
export interface NetworkStats {
  latency: number;
  packetLoss: number;
  bandwidth: number;
  messagesPerSecond: number;
  totalMessagesSent: number;
  totalMessagesReceived: number;
  reconnectCount: number;
  lastReconnectTime: number;
}

// 批处理配置
export interface BatchConfig {
  enabled: boolean;
  maxBatchSize: number;
  batchTimeout: number;
  priorityThreshold: number;
}

// 网络管理器配置
export interface NetworkManagerConfig {
  serverUrl: string;
  reconnectInterval: number;
  maxReconnectAttempts: number;
  heartbeatInterval: number;
  messageTimeout: number;
  enableCompression: boolean;
  enableBatching: boolean;
  batchConfig: BatchConfig;
  enablePriorityQueue: boolean;
  maxQueueSize: number;
}

/**
 * 增强的网络通信管理器类
 */
export class EnhancedNetworkManager {
  private static instance: EnhancedNetworkManager;
  
  private ws: WebSocket | null = null;
  private connectionState: ConnectionState = ConnectionState.DISCONNECTED;
  private config: NetworkManagerConfig;
  
  // 消息队列和批处理
  private messageQueue: NetworkMessage[] = [];
  private batchQueue: NetworkMessage[] = [];
  private batchTimer: NodeJS.Timeout | null = null;
  
  // 可靠传输
  private pendingMessages: Map<string, {
    message: NetworkMessage;
    resolve: (value: any) => void;
    reject: (error: Error) => void;
    timeout: NodeJS.Timeout;
    retryCount: number;
  }> = new Map();
  
  // 统计信息
  private stats: NetworkStats = {
    latency: 0,
    packetLoss: 0,
    bandwidth: 0,
    messagesPerSecond: 0,
    totalMessagesSent: 0,
    totalMessagesReceived: 0,
    reconnectCount: 0,
    lastReconnectTime: 0,
  };
  
  // 事件监听器
  private eventListeners: Map<string, Function[]> = new Map();
  
  // 重连相关
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  
  // 性能监控
  private lastStatsUpdate = 0;
  private messageCountInLastSecond = 0;
  private latencyMeasurements: number[] = [];
  
  private constructor(config: Partial<NetworkManagerConfig> = {}) {
    this.config = {
      serverUrl: 'ws://localhost:3005',
      reconnectInterval: 3000,
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000,
      messageTimeout: 10000,
      enableCompression: true,
      enableBatching: true,
      batchConfig: {
        enabled: true,
        maxBatchSize: 50,
        batchTimeout: 100, // 100ms
        priorityThreshold: 7,
      },
      enablePriorityQueue: true,
      maxQueueSize: 1000,
      ...config,
    };
    
    this.startStatsMonitoring();
  }
  
  /**
   * 获取单例实例
   */
  public static getInstance(config?: Partial<NetworkManagerConfig>): EnhancedNetworkManager {
    if (!EnhancedNetworkManager.instance) {
      EnhancedNetworkManager.instance = new EnhancedNetworkManager(config);
    }
    return EnhancedNetworkManager.instance;
  }
  
  /**
   * 连接到服务器
   */
  public async connect(): Promise<void> {
    if (this.connectionState === ConnectionState.CONNECTED) {
      return;
    }
    
    this.connectionState = ConnectionState.CONNECTING;
    this.emit('connectionStateChanged', this.connectionState);
    
    try {
      this.ws = new WebSocket(this.config.serverUrl);
      
      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);
      
      // 等待连接建立
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('连接超时'));
        }, 10000);
        
        const onOpen = () => {
          clearTimeout(timeout);
          resolve();
        };
        
        const onError = (_error: Event) => {
          clearTimeout(timeout);
          reject(new Error('连接失败'));
        };
        
        this.ws!.addEventListener('open', onOpen, { once: true });
        this.ws!.addEventListener('error', onError, { once: true });
      });
      
    } catch (error) {
      this.connectionState = ConnectionState.ERROR;
      this.emit('connectionStateChanged', this.connectionState);
      throw error;
    }
  }
  
  /**
   * 断开连接
   */
  public disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    
    this.connectionState = ConnectionState.DISCONNECTED;
    this.emit('connectionStateChanged', this.connectionState);
    
    this.clearTimers();
    this.clearPendingMessages();
  }
  
  /**
   * 发送消息
   */
  public async sendMessage(
    type: MessageType,
    data: any,
    options: {
      priority?: number;
      reliable?: boolean;
      timeout?: number;
    } = {}
  ): Promise<any> {
    const message: NetworkMessage = {
      id: this.generateMessageId(),
      type,
      data,
      timestamp: Date.now(),
      priority: options.priority || 5,
      reliable: options.reliable || false,
    };
    
    if (options.reliable) {
      return this.sendReliableMessage(message, options.timeout);
    } else {
      return this.sendUnreliableMessage(message);
    }
  }
  
  /**
   * 发送可靠消息
   */
  private async sendReliableMessage(message: NetworkMessage, timeout?: number): Promise<any> {
    return new Promise((resolve, reject) => {
      const timeoutMs = timeout || this.config.messageTimeout;
      const timeoutHandle = setTimeout(() => {
        this.pendingMessages.delete(message.id);
        reject(new Error('消息发送超时'));
      }, timeoutMs);
      
      this.pendingMessages.set(message.id, {
        message,
        resolve,
        reject,
        timeout: timeoutHandle,
        retryCount: 0,
      });
      
      this.queueMessage(message);
    });
  }
  
  /**
   * 发送不可靠消息
   */
  private async sendUnreliableMessage(message: NetworkMessage): Promise<void> {
    this.queueMessage(message);
  }
  
  /**
   * 将消息加入队列
   */
  private queueMessage(message: NetworkMessage): void {
    if (this.config.enablePriorityQueue) {
      // 按优先级插入队列
      const insertIndex = this.messageQueue.findIndex(m => m.priority < message.priority);
      if (insertIndex === -1) {
        this.messageQueue.push(message);
      } else {
        this.messageQueue.splice(insertIndex, 0, message);
      }
    } else {
      this.messageQueue.push(message);
    }
    
    // 限制队列大小
    if (this.messageQueue.length > this.config.maxQueueSize) {
      this.messageQueue.shift(); // 移除最旧的消息
    }
    
    this.processMessageQueue();
  }
  
  /**
   * 处理消息队列
   */
  private processMessageQueue(): void {
    if (this.connectionState !== ConnectionState.CONNECTED || this.messageQueue.length === 0) {
      return;
    }
    
    if (this.config.enableBatching) {
      this.processBatchQueue();
    } else {
      this.processIndividualMessages();
    }
  }
  
  /**
   * 处理批量消息
   */
  private processBatchQueue(): void {
    const { batchConfig } = this.config;
    
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()!;
      
      // 高优先级消息立即发送
      if (message.priority >= batchConfig.priorityThreshold) {
        this.sendMessageDirectly(message);
        continue;
      }
      
      // 加入批处理队列
      this.batchQueue.push(message);
      
      // 检查是否需要立即发送批次
      if (this.batchQueue.length >= batchConfig.maxBatchSize) {
        this.flushBatchQueue();
      } else if (!this.batchTimer) {
        // 设置批处理定时器
        this.batchTimer = setTimeout(() => {
          this.flushBatchQueue();
        }, batchConfig.batchTimeout);
      }
    }
  }
  
  /**
   * 刷新批处理队列
   */
  private flushBatchQueue(): void {
    if (this.batchQueue.length === 0) {
      return;
    }
    
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }
    
    const batchMessage: NetworkMessage = {
      id: this.generateMessageId(),
      type: MessageType.BATCH_UPDATE,
      data: this.batchQueue.slice(),
      timestamp: Date.now(),
      priority: 10, // 批处理消息高优先级
    };
    
    this.sendMessageDirectly(batchMessage);
    this.batchQueue = [];
  }
  
  /**
   * 处理单个消息
   */
  private processIndividualMessages(): void {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()!;
      this.sendMessageDirectly(message);
    }
  }
  
  /**
   * 直接发送消息
   */
  private sendMessageDirectly(message: NetworkMessage): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      return;
    }
    
    try {
      let data = JSON.stringify(message);
      
      // 压缩数据（如果启用）
      if (this.config.enableCompression && data.length > 1024) {
        // 这里可以实现压缩逻辑
        // data = compress(data);
      }
      
      this.ws.send(data);
      this.stats.totalMessagesSent++;
      this.messageCountInLastSecond++;
      
    } catch (error) {
      console.error('发送消息失败:', error);
      this.emit('error', error);
    }
  }
  
  /**
   * 处理连接打开
   */
  private handleOpen(): void {
    this.connectionState = ConnectionState.CONNECTED;
    this.reconnectAttempts = 0;
    this.emit('connectionStateChanged', this.connectionState);
    this.emit('connected');
    
    this.startHeartbeat();
    this.processMessageQueue();
  }
  
  /**
   * 处理接收到的消息
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const message: NetworkMessage = JSON.parse(event.data);
      this.stats.totalMessagesReceived++;
      
      // 处理心跳响应
      if (message.type === MessageType.HEARTBEAT) {
        this.handleHeartbeatResponse(message);
        return;
      }
      
      // 处理可靠消息的确认
      if (message.data && message.data.ackId) {
        this.handleMessageAck(message.data.ackId);
        return;
      }
      
      // 处理批量消息
      if (message.type === MessageType.BATCH_UPDATE) {
        this.handleBatchMessage(message);
        return;
      }
      
      // 发出消息事件
      this.emit('message', message);
      this.emit(message.type, message.data);
      
    } catch (error) {
      console.error('处理消息失败:', error);
      this.emit('error', error);
    }
  }
  
  /**
   * 处理连接关闭
   */
  private handleClose(event: CloseEvent): void {
    this.connectionState = ConnectionState.DISCONNECTED;
    this.emit('connectionStateChanged', this.connectionState);
    this.emit('disconnected', event);
    
    this.clearTimers();
    
    // 尝试重连
    if (this.reconnectAttempts < this.config.maxReconnectAttempts) {
      this.attemptReconnect();
    }
  }
  
  /**
   * 处理连接错误
   */
  private handleError(event: Event): void {
    this.connectionState = ConnectionState.ERROR;
    this.emit('connectionStateChanged', this.connectionState);
    this.emit('error', event);
  }
  
  /**
   * 尝试重连
   */
  private attemptReconnect(): void {
    this.connectionState = ConnectionState.RECONNECTING;
    this.emit('connectionStateChanged', this.connectionState);
    
    this.reconnectAttempts++;
    this.stats.reconnectCount++;
    this.stats.lastReconnectTime = Date.now();
    
    this.reconnectTimer = setTimeout(() => {
      this.connect().catch(() => {
        // 重连失败，继续尝试
        if (this.reconnectAttempts < this.config.maxReconnectAttempts) {
          this.attemptReconnect();
        }
      });
    }, this.config.reconnectInterval);
  }
  
  /**
   * 开始心跳
   */
  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      const startTime = Date.now();
      this.sendMessage(MessageType.HEARTBEAT, { timestamp: startTime });
    }, this.config.heartbeatInterval);
  }
  
  /**
   * 处理心跳响应
   */
  private handleHeartbeatResponse(message: NetworkMessage): void {
    const now = Date.now();
    const latency = now - message.data.timestamp;
    
    this.latencyMeasurements.push(latency);
    if (this.latencyMeasurements.length > 10) {
      this.latencyMeasurements.shift();
    }
    
    this.stats.latency = this.latencyMeasurements.reduce((a, b) => a + b, 0) / this.latencyMeasurements.length;
  }
  
  /**
   * 处理消息确认
   */
  private handleMessageAck(ackId: string): void {
    const pending = this.pendingMessages.get(ackId);
    if (pending) {
      clearTimeout(pending.timeout);
      pending.resolve(true);
      this.pendingMessages.delete(ackId);
    }
  }
  
  /**
   * 处理批量消息
   */
  private handleBatchMessage(batchMessage: NetworkMessage): void {
    const messages = batchMessage.data as NetworkMessage[];
    for (const message of messages) {
      this.emit('message', message);
      this.emit(message.type, message.data);
    }
  }
  
  /**
   * 开始统计监控
   */
  private startStatsMonitoring(): void {
    setInterval(() => {
      const now = Date.now();
      if (now - this.lastStatsUpdate >= 1000) {
        this.stats.messagesPerSecond = this.messageCountInLastSecond;
        this.messageCountInLastSecond = 0;
        this.lastStatsUpdate = now;
        
        this.emit('statsUpdated', this.stats);
      }
    }, 1000);
  }
  
  /**
   * 清理定时器
   */
  private clearTimers(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
    
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }
  }
  
  /**
   * 清理待处理消息
   */
  private clearPendingMessages(): void {
    for (const [_id, pending] of this.pendingMessages) {
      clearTimeout(pending.timeout);
      pending.reject(new Error('连接已断开'));
    }
    this.pendingMessages.clear();
  }
  
  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * 事件监听
   */
  public on(event: string, listener: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(listener);
  }
  
  /**
   * 移除事件监听
   */
  public off(event: string, listener: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }
  
  /**
   * 发出事件
   */
  private emit(event: string, ...args: any[]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      for (const listener of listeners) {
        try {
          listener(...args);
        } catch (error) {
          console.error('事件监听器执行失败:', error);
        }
      }
    }
  }
  
  /**
   * 获取连接状态
   */
  public getConnectionState(): ConnectionState {
    return this.connectionState;
  }
  
  /**
   * 获取统计信息
   */
  public getStats(): NetworkStats {
    return { ...this.stats };
  }
  
  /**
   * 获取配置
   */
  public getConfig(): NetworkManagerConfig {
    return { ...this.config };
  }
  
  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<NetworkManagerConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

// 导出消息类型和其他类型已在上面定义时直接导出
