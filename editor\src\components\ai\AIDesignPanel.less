.ai-design-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fafafa;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: #ffffff;
    border-bottom: 1px solid #f0f0f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);

    .header-title {
      .ant-typography {
        margin: 0;
        color: #262626;
      }

      .ant-badge {
        .ant-badge-count {
          background: #1890ff;
          box-shadow: 0 0 0 1px #ffffff;
        }
      }
    }

    .header-actions {
      .ant-btn {
        border: none;
        box-shadow: none;

        &:hover {
          background: #f5f5f5;
        }
      }

      .ant-switch {
        &.ant-switch-checked {
          background: #52c41a;
        }
      }
    }
  }

  .panel-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;

    .analyzing-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      gap: 16px;

      .ant-spin {
        .ant-spin-dot {
          .ant-spin-dot-item {
            background-color: #1890ff;
          }
        }
      }
    }

    .analysis-overview {
      margin-bottom: 16px;

      .overview-stats {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 16px;
        margin-bottom: 16px;

        .stat-item {
          text-align: center;

          .stat-value {
            font-size: 24px;
            font-weight: 700;
            line-height: 1;
            margin-bottom: 4px;

            &.critical {
              color: #ff4d4f;
            }

            &.high {
              color: #ff7a45;
            }

            &.applied {
              color: #52c41a;
            }
          }

          .stat-label {
            font-size: 12px;
            color: #8c8c8c;
          }
        }
      }

      .overview-progress {
        display: flex;
        align-items: center;
        gap: 8px;

        .ant-typography {
          margin: 0;
          font-size: 12px;
          color: #595959;
        }

        .ant-progress {
          flex: 1;
        }
      }
    }

    .ai-tabs {
      .ant-tabs-nav {
        margin-bottom: 16px;

        .ant-tabs-tab {
          .ant-badge {
            .ant-badge-count {
              background: #f0f0f0;
              color: #595959;
              font-size: 10px;
              height: 16px;
              line-height: 16px;
              min-width: 16px;
              padding: 0 4px;
              border-radius: 8px;
            }
          }

          &.ant-tabs-tab-active {
            .ant-badge {
              .ant-badge-count {
                background: #1890ff;
                color: #ffffff;
              }
            }
          }
        }
      }

      .ant-tabs-content {
        .suggestions-list {
          display: flex;
          flex-direction: column;
          gap: 12px;

          .suggestion-card {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;

            &:hover {
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
              transform: translateY(-2px);
            }

            &.applied {
              background: #f6ffed;
              border-color: #b7eb8f;

              .suggestion-header {
                opacity: 0.7;
              }
            }

            .ant-card-head {
              border-bottom: 1px solid #f0f0f0;
              padding: 12px 16px;

              .ant-card-head-title {
                padding: 0;

                .suggestion-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  width: 100%;

                  .suggestion-confidence {
                    display: flex;
                    align-items: center;
                    gap: 4px;

                    .ant-rate {
                      font-size: 12px;
                      line-height: 1;

                      .ant-rate-star {
                        margin-right: 2px;
                      }
                    }
                  }
                }
              }

              .ant-card-extra {
                padding: 0;
              }
            }

            .ant-card-body {
              padding: 16px;

              .suggestion-content {
                .suggestion-details {
                  .detail-item {
                    margin-bottom: 12px;

                    &:last-child {
                      margin-bottom: 0;
                    }

                    .ant-typography {
                      display: block;
                      margin-bottom: 4px;

                      &:last-child {
                        margin-bottom: 0;
                      }
                    }

                    .ant-list {
                      margin-top: 8px;

                      .ant-list-item {
                        padding: 4px 0;
                        border-bottom: none;
                      }
                    }
                  }
                }
              }
            }
          }
        }

        .patterns-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
          gap: 16px;

          .pattern-card {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;

            &:hover {
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
              transform: translateY(-2px);
            }

            .ant-card-cover {
              border-radius: 8px 8px 0 0;
              overflow: hidden;
            }

            .ant-card-body {
              padding: 16px;

              .pattern-title {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
              }

              .pattern-description {
                .pattern-tags {
                  margin: 8px 0;
                  display: flex;
                  flex-wrap: wrap;
                  gap: 4px;

                  .ant-tag {
                    margin: 0;
                    border-radius: 12px;
                    font-size: 11px;
                  }
                }

                .pattern-details {
                  .detail-section {
                    margin-bottom: 12px;

                    &:last-child {
                      margin-bottom: 0;
                    }

                    .ant-typography {
                      margin-bottom: 8px;
                    }

                    .ant-list {
                      .ant-list-item {
                        padding: 2px 0;
                        border-bottom: none;
                      }
                    }
                  }
                }
              }
            }

            .ant-card-actions {
              border-top: 1px solid #f0f0f0;

              li {
                margin: 8px 0;

                .ant-btn {
                  border: none;
                  box-shadow: none;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .ai-design-panel {
    .panel-content {
      .analysis-overview {
        .overview-stats {
          grid-template-columns: repeat(2, 1fr);
          gap: 12px;

          .stat-item {
            .stat-value {
              font-size: 20px;
            }
          }
        }
      }

      .ai-tabs {
        .ant-tabs-content {
          .patterns-grid {
            grid-template-columns: 1fr;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .ai-design-panel {
    .panel-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .header-title {
        justify-content: center;
      }

      .header-actions {
        justify-content: center;
      }
    }

    .panel-content {
      padding: 12px;

      .analysis-overview {
        .overview-stats {
          grid-template-columns: 1fr;
          gap: 8px;

          .stat-item {
            .stat-value {
              font-size: 18px;
            }
          }
        }

        .overview-progress {
          flex-direction: column;
          align-items: stretch;
          gap: 4px;
        }
      }

      .ai-tabs {
        .ant-tabs-content {
          .suggestions-list {
            gap: 8px;

            .suggestion-card {
              .ant-card-head {
                padding: 8px 12px;

                .suggestion-header {
                  flex-direction: column;
                  align-items: flex-start;
                  gap: 8px;
                }
              }

              .ant-card-body {
                padding: 12px;
              }
            }
          }
        }
      }
    }
  }
}

// 动画效果
@keyframes aiPanelAppear {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.ai-design-panel {
  animation: aiPanelAppear 0.3s ease;
}

@keyframes suggestionAppear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.suggestion-card {
  animation: suggestionAppear 0.3s ease;
}

// 加载状态
.ant-spin-container {
  min-height: 200px;
}

// 空状态
.ant-empty {
  padding: 40px 0;

  .ant-empty-image {
    height: 60px;
  }

  .ant-empty-description {
    color: #8c8c8c;
    font-size: 13px;
  }
}

// 评分组件样式
.ant-rate {
  .ant-rate-star {
    &.ant-rate-star-half,
    &.ant-rate-star-full {
      .ant-rate-star-first,
      .ant-rate-star-second {
        color: #faad14;
      }
    }
  }

  &.ant-rate-small {
    .ant-rate-star {
      margin-right: 1px;
    }
  }
}

// 进度条样式
.ant-progress {
  .ant-progress-bg {
    border-radius: 4px;
  }

  &.ant-progress-small {
    .ant-progress-outer {
      .ant-progress-inner {
        height: 6px;
        border-radius: 3px;

        .ant-progress-bg {
          border-radius: 3px;
        }
      }
    }
  }
}
