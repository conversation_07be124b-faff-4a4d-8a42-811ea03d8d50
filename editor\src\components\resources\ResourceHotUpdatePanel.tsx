/**
 * 资源热更新面板组件
 * 用于管理资源热更新功能
 */
import React, { useState, useEffect } from 'react';
import { Tabs, Card, Button, Space, Typography, Tooltip, Badge, notification } from 'antd';
import {
  SyncOutlined,
  SettingOutlined,
  HistoryOutlined,
  InfoCircleOutlined,
  CloudDownloadOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { resourceHotUpdateService, ResourceHotUpdateServiceEventType } from '../../services/ResourceHotUpdateService';
import {
  setShowUpdatePanel,
  ResourceUpdateStatus
} from '../../store/resources/resourceHotUpdateSlice';
import ResourceUpdateProgressMonitor from './ResourceUpdateProgressMonitor';
import ResourceUpdateConfigPanel from './ResourceUpdateConfigPanel';
import './ResourceHotUpdatePanel.less';

const { TabPane } = Tabs;
const { Title } = Typography;

/**
 * 资源热更新面板组件属性
 */
interface ResourceHotUpdatePanelProps {
  /** 关闭回调 */
  onClose?: () => void;
}

/**
 * 资源热更新面板组件
 */
const ResourceHotUpdatePanel: React.FC<ResourceHotUpdatePanelProps> = ({
  onClose
}) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  // 从Redux状态获取更新信息
  const {
    status,
    progress,
    updateHistory
  } = useSelector((state: RootState) => state.resourceHotUpdate);
  
  // 本地状态
  const [activeTab, setActiveTab] = useState<string>('updates');
  
  // 监听更新服务事件
  useEffect(() => {
    // 更新开始事件
    const handleUpdateStarted = (updateInfo: any) => {
      notification.info({
        message: t('resources.hotUpdate.updateAvailable'),
        description: updateInfo.description,
        icon: <InfoCircleOutlined style={{ color: '#1890ff' }} />,
        duration: 0,
        btn: (
          <Button type="primary" size="small" onClick={() => dispatch(setShowUpdatePanel(true))}>
            {t('resources.hotUpdate.viewDetails')}
          </Button>
        )
      });
    };
    
    // 更新完成事件
    const handleUpdateCompleted = () => {
      notification.success({
        message: t('resources.hotUpdate.updateComplete'),
        description: t('resources.hotUpdate.updateAppliedDesc'),
        icon: <CloudDownloadOutlined style={{ color: '#52c41a' }} />,
        duration: 4
      });
    };
    
    // 更新失败事件
    const handleUpdateFailed = (error: any) => {
      notification.error({
        message: t('resources.hotUpdate.updateFailed'),
        description: error.message || t('resources.hotUpdate.updateFailedDesc'),
        duration: 0
      });
    };
    
    // 添加事件监听器
    resourceHotUpdateService.on(ResourceHotUpdateServiceEventType.UPDATE_STARTED, handleUpdateStarted);
    resourceHotUpdateService.on(ResourceHotUpdateServiceEventType.UPDATE_COMPLETED, handleUpdateCompleted);
    resourceHotUpdateService.on(ResourceHotUpdateServiceEventType.UPDATE_FAILED, handleUpdateFailed);
    
    // 清理函数
    return () => {
      resourceHotUpdateService.off(ResourceHotUpdateServiceEventType.UPDATE_STARTED, handleUpdateStarted);
      resourceHotUpdateService.off(ResourceHotUpdateServiceEventType.UPDATE_COMPLETED, handleUpdateCompleted);
      resourceHotUpdateService.off(ResourceHotUpdateServiceEventType.UPDATE_FAILED, handleUpdateFailed);
    };
  }, [dispatch, t]);
  
  // 检查更新
  const handleCheckUpdate = async () => {
    await resourceHotUpdateService.checkForUpdates();
  };
  
  // 关闭面板
  const handleClose = () => {
    if (onClose) {
      onClose();
    }
    dispatch(setShowUpdatePanel(false));
  };
  
  // 获取状态徽章
  const getStatusBadge = () => {
    switch (status) {
      case ResourceUpdateStatus.AVAILABLE:
        return <Badge status="warning" text={t('resources.hotUpdate.updateAvailable')} />;
      case ResourceUpdateStatus.DOWNLOADING:
        return <Badge status="processing" text={`${t('resources.hotUpdate.downloading')} ${progress}%`} />;
      case ResourceUpdateStatus.DOWNLOADED:
        return <Badge status="success" text={t('resources.hotUpdate.readyToApply')} />;
      case ResourceUpdateStatus.APPLYING:
        return <Badge status="processing" text={t('resources.hotUpdate.applying')} />;
      case ResourceUpdateStatus.APPLIED:
        return <Badge status="success" text={t('resources.hotUpdate.applied')} />;
      case ResourceUpdateStatus.ERROR:
        return <Badge status="error" text={t('resources.hotUpdate.error')} />;
      case ResourceUpdateStatus.UP_TO_DATE:
        return <Badge status="success" text={t('resources.hotUpdate.upToDate')} />;
      default:
        return null;
    }
  };
  
  // 渲染更新标签页
  const renderUpdatesTab = () => {
    return (
      <div className="updates-tab">
        <ResourceUpdateProgressMonitor 
          showDetails={true} 
          showControls={true} 
          showHistory={true}
        />
      </div>
    );
  };
  
  // 渲染配置标签页
  const renderConfigTab = () => {
    return (
      <div className="config-tab">
        <ResourceUpdateConfigPanel />
      </div>
    );
  };
  
  return (
    <div className="resource-hot-update-panel">
      <Card
        title={
          <div className="panel-header">
            <Space>
              <Title level={4}>{t('resources.hotUpdate.title')}</Title>
              {getStatusBadge()}
            </Space>
            <Button 
              type="text" 
              icon={<CloseCircleOutlined />} 
              onClick={handleClose}
            />
          </div>
        }
        extra={
          <Space>
            <Tooltip title={t('resources.hotUpdate.checkForUpdates')}>
              <Button 
                icon={<SyncOutlined />} 
                onClick={handleCheckUpdate}
                loading={status === ResourceUpdateStatus.CHECKING}
              >
                {t('resources.hotUpdate.check')}
              </Button>
            </Tooltip>
          </Space>
        }
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane 
            tab={
              <span>
                <CloudDownloadOutlined />
                {t('resources.hotUpdate.updates')}
              </span>
            } 
            key="updates"
          >
            {renderUpdatesTab()}
          </TabPane>
          <TabPane 
            tab={
              <span>
                <SettingOutlined />
                {t('resources.hotUpdate.settings')}
              </span>
            } 
            key="config"
          >
            {renderConfigTab()}
          </TabPane>
          <TabPane 
            tab={
              <span>
                <HistoryOutlined />
                {t('resources.hotUpdate.history')}
                {updateHistory.length > 0 && (
                  <Badge 
                    count={updateHistory.length} 
                    size="small" 
                    style={{ marginLeft: 8 }}
                  />
                )}
              </span>
            } 
            key="history"
          >
            <ResourceUpdateProgressMonitor 
              showDetails={false} 
              showControls={false} 
              showHistory={true}
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default ResourceHotUpdatePanel;
