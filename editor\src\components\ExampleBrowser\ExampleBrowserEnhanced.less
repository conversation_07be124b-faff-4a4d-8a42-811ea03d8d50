/* 增强版示例项目浏览器样式 */

.example-browser-enhanced {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f0f2f5;

  .example-browser-header {
    background-color: #fff;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    z-index: 10;
    height: 64px;

    .header-title {
      display: flex;
      align-items: center;

      h2 {
        margin: 0;
        margin-right: 16px;
        font-size: 18px;
        font-weight: 600;
      }
    }

    .header-search {
      flex: 1;
      display: flex;
      justify-content: center;
      max-width: 500px;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 16px;
    }
  }

  .example-browser-sider {
    background-color: #fff;
    padding: 16px;
    border-right: 1px solid #f0f0f0;
    overflow-y: auto;
  }

  .example-browser-content {
    padding: 24px;
    overflow-y: auto;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    
    p {
      margin-top: 16px;
      color: #666;
    }
  }

  .example-grid {
    margin-top: 16px;
  }

  .example-list {
    margin-top: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;

    .example-list-item {
      cursor: pointer;
      transition: all 0.3s;
      border-radius: 8px;
      overflow: hidden;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
      }

      .example-list-content {
        display: flex;
        align-items: center;
      }

      .example-list-image {
        width: 120px;
        height: 80px;
        object-fit: cover;
        border-radius: 4px;
        margin-right: 16px;
      }

      .example-list-info {
        flex: 1;

        h3 {
          margin: 0 0 8px 0;
          font-size: 16px;
          font-weight: 600;
        }

        p {
          margin: 0 0 8px 0;
          color: #666;
          font-size: 14px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .example-list-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }
      }

      .example-list-actions {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
    }
  }

  /* 标签页样式 */
  .ant-tabs-nav {
    margin-bottom: 16px;
  }

  .ant-tabs-tab {
    padding: 8px 16px;
  }

  .ant-tabs-tab-active {
    font-weight: 600;
  }

  /* 空状态样式 */
  .ant-empty {
    margin: 48px 0;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .example-browser-header {
      flex-direction: column;
      height: auto;
      padding: 16px;
      gap: 16px;

      .header-title, .header-search, .header-actions {
        width: 100%;
      }
    }

    .example-browser-sider {
      width: 100% !important;
      max-width: 100% !important;
      flex: 0 0 100% !important;
    }

    .example-list-item {
      .example-list-content {
        flex-direction: column;
        align-items: flex-start;
      }

      .example-list-image {
        width: 100%;
        height: 160px;
        margin-right: 0;
        margin-bottom: 16px;
      }

      .example-list-actions {
        flex-direction: row;
        margin-top: 16px;
      }
    }
  }

  /* 卡片悬停效果 */
  .ant-card {
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }
  }

  /* 收藏图标样式 */
  .ant-btn-icon-only.ant-btn-sm > .anticon {
    font-size: 14px;
  }

  .favorite-icon {
    color: #faad14;
  }

  /* 标签样式 */
  .ant-tag {
    margin-right: 0;
  }

  /* 筛选器样式 */
  .filter-section {
    margin-bottom: 24px;

    h3 {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16px;
    }

    .ant-select {
      width: 100%;
    }

    .ant-checkbox-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .filter-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-top: 8px;
    }
  }

  /* 详情页样式 */
  .example-detail {
    .detail-header {
      display: flex;
      align-items: center;
      margin-bottom: 24px;

      .back-button {
        margin-right: 16px;
      }

      h2 {
        margin: 0;
        flex: 1;
      }

      .detail-actions {
        display: flex;
        gap: 8px;
      }
    }

    .detail-content {
      display: flex;
      gap: 24px;

      @media (max-width: 768px) {
        flex-direction: column;
      }

      .detail-main {
        flex: 1;
      }

      .detail-sidebar {
        width: 300px;

        @media (max-width: 768px) {
          width: 100%;
        }
      }
    }

    .detail-carousel {
      margin-bottom: 24px;
      border-radius: 8px;
      overflow: hidden;

      img {
        width: 100%;
        height: 400px;
        object-fit: cover;
      }
    }

    .detail-description {
      margin-bottom: 24px;
    }

    .detail-features {
      margin-bottom: 24px;

      .feature-item {
        margin-bottom: 16px;

        h4 {
          font-weight: 600;
          margin-bottom: 8px;
        }
      }
    }

    .detail-meta {
      background-color: #f9f9f9;
      padding: 16px;
      border-radius: 8px;
      margin-bottom: 24px;

      .meta-item {
        display: flex;
        margin-bottom: 8px;

        .meta-label {
          width: 100px;
          color: #666;
        }

        .meta-value {
          flex: 1;
        }
      }
    }
  }

  /* 导入对话框样式 */
  .import-dialog {
    .ant-form-item {
      margin-bottom: 16px;
    }
  }
}
