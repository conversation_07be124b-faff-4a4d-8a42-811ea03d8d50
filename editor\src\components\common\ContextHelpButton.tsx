/**
 * 上下文帮助按钮组件
 * 用于在界面元素旁边显示帮助按钮，点击后打开相关帮助主题
 */
import React from 'react';
import { Button, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import HelpService from '../../services/HelpService';
import './ContextHelpButton.less';

interface ContextHelpButtonProps {
  contextId: string;
  tooltip?: string;
  placement?: 'top' | 'left' | 'right' | 'bottom';
  size?: 'small' | 'middle' | 'large';
  className?: string;
  style?: React.CSSProperties;
}

const ContextHelpButton: React.FC<ContextHelpButtonProps> = ({
  contextId,
  tooltip,
  placement = 'top',
  size = 'small',
  className = '',
  style = {}}) => {
  const { t } = useTranslation();
  
  // 处理点击事件
  const handleClick = () => {
    HelpService.openContextHelp(contextId);
  };
  
  // 获取上下文相关的帮助主题
  const topics = HelpService.getTopicsByContextId(contextId);
  
  // 如果没有相关主题，不显示按钮
  if (topics.length === 0) {
    return null;
  }
  
  // 默认提示文本
  const defaultTooltip = t('help.clickForHelp');
  
  return (
    <Tooltip
      title={tooltip || defaultTooltip}
      placement={placement}
    >
      <Button
        type="text"
        icon={<QuestionCircleOutlined />}
        size={size}
        onClick={handleClick}
        className={`context-help-button ${className}`}
        style={style}
        aria-label={String(t('help.help'))}
      />
    </Tooltip>
  );
};

export default ContextHelpButton;
