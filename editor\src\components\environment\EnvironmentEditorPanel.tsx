/**
 * 环境编辑器面板
 * 
 * 该面板整合了环境感知、环境响应、环境预设和环境可视化等功能。
 */
import React, { useState, useEffect } from 'react';
import { Tabs, Card, Space, Button, message } from 'antd';
import { 
  EnvironmentOutlined, 
  ThunderboltOutlined, 
  AppstoreOutlined, 
  AreaChartOutlined,
  BugOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

import EnvironmentAwarenessPanel from './EnvironmentAwarenessPanel';
import EnvironmentResponseEditor from './EnvironmentResponseEditor';
import EnvironmentPresetPanel from './EnvironmentPresetPanel';
import EnvironmentVisualizationPanel from './EnvironmentVisualizationPanel';
import EnvironmentDebugPanel from './EnvironmentDebugPanel';

// 导入类型定义
import {
  EnvironmentType,
  WeatherType,
  TerrainType,
  LogLevel,
  type EnvironmentAwarenessData,
  type EnvironmentResponseRule,
  type LogItem
} from './types';

const { TabPane } = Tabs;

/**
 * 环境编辑器面板属性接口
 */
interface EnvironmentEditorPanelProps {
  entityId?: string;
}

/**
 * 环境编辑器面板组件
 */
const EnvironmentEditorPanel: React.FC<EnvironmentEditorPanelProps> = ({
  entityId
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<string>('awareness');
  const [environmentData, setEnvironmentData] = useState<EnvironmentAwarenessData | null>(null);
  const [responseRules, setResponseRules] = useState<EnvironmentResponseRule[]>([]);
  const [customPresets, setCustomPresets] = useState<EnvironmentResponseRule[]>([]);
  const [historicalData, setHistoricalData] = useState<EnvironmentAwarenessData[]>([]);
  const [logs, setLogs] = useState<LogItem[]>([]);
  const [activeResponses, setActiveResponses] = useState<Map<string, any[]>>(new Map());
  const [debugEnabled, setDebugEnabled] = useState<boolean>(false);
  const [visualizationEnabled, setVisualizationEnabled] = useState<boolean>(false);

  // 初始化数据
  useEffect(() => {
    if (entityId) {
      fetchEnvironmentData();
      fetchResponseRules();
      fetchCustomPresets();
    }
  }, [entityId]);

  /**
   * 获取环境数据
   */
  const fetchEnvironmentData = () => {
    // 这里应该从引擎获取实际数据
    // 现在使用模拟数据
    const mockData: EnvironmentAwarenessData = {
      environmentType: EnvironmentType.OUTDOOR,
      weatherType: WeatherType.CLEAR,
      terrainType: TerrainType.FLAT,
      lightIntensity: 0.8,
      temperature: 22,
      humidity: 0.5,
      windSpeed: 2,
      windDirection: { x: 1, y: 0, z: 0 },
      noiseLevel: 0.2,
      airQuality: 0.9,
      waterLevel: 0,
      visibility: 1000,
      timeOfDay: 12,
      customParameters: new Map(),
      lastEnvironmentChangeTime: Date.now(),
      awarenessRange: 50
    };
    
    setEnvironmentData(mockData);
    
    // 添加到历史数据
    setHistoricalData(prev => {
      const newData = [...prev, mockData];
      // 限制历史数据数量
      if (newData.length > 100) {
        return newData.slice(-100);
      }
      return newData;
    });
    
    // 添加日志
    addLog(LogLevel.INFO, '环境数据已更新', mockData);
  };

  /**
   * 获取响应规则
   */
  const fetchResponseRules = () => {
    // 这里应该从引擎获取实际数据
    // 现在使用空数组
    setResponseRules([]);
  };

  /**
   * 获取自定义预设
   */
  const fetchCustomPresets = () => {
    // 这里应该从存储获取实际数据
    // 现在使用空数组
    setCustomPresets([]);
  };

  /**
   * 添加日志
   * @param level 级别
   * @param message 消息
   * @param details 详情
   */
  const addLog = (level: LogLevel, message: string, details?: any) => {
    const newLog: LogItem = {
      id: `log_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      timestamp: Date.now(),
      level,
      message,
      details
    };
    
    setLogs(prev => [...prev, newLog]);
  };

  /**
   * 处理保存环境数据
   * @param data 环境数据
   */
  const handleSaveEnvironmentData = (data: Partial<EnvironmentAwarenessData>) => {
    if (!environmentData) return;
    
    const updatedData = {
      ...environmentData,
      ...data,
      lastEnvironmentChangeTime: Date.now()
    };
    
    setEnvironmentData(updatedData);
    
    // 添加到历史数据
    setHistoricalData(prev => {
      const newData = [...prev, updatedData];
      // 限制历史数据数量
      if (newData.length > 100) {
        return newData.slice(-100);
      }
      return newData;
    });
    
    // 添加日志
    addLog(LogLevel.INFO, '环境数据已保存', updatedData);
    
    // 这里应该将数据发送到引擎
    message.success(t('environment.dataSaved'));
  };

  /**
   * 处理保存响应规则
   * @param rules 响应规则
   */
  const handleSaveResponseRules = (rules: EnvironmentResponseRule[]) => {
    setResponseRules(rules);
    
    // 添加日志
    addLog(LogLevel.INFO, '响应规则已保存', { ruleCount: rules.length });
    
    // 这里应该将规则发送到引擎
    message.success(t('environment.rulesSaved'));
  };

  /**
   * 处理应用预设
   * @param preset 预设
   */
  const handleApplyPreset = (preset: EnvironmentResponseRule) => {
    // 检查是否已存在相同ID的规则
    const existingRuleIndex = responseRules.findIndex(r => r.id === preset.id);
    
    if (existingRuleIndex !== -1) {
      // 如果存在，更新规则
      const updatedRules = [...responseRules];
      updatedRules[existingRuleIndex] = preset;
      setResponseRules(updatedRules);
    } else {
      // 如果不存在，添加规则
      setResponseRules([...responseRules, preset]);
    }
    
    // 添加日志
    addLog(LogLevel.INFO, `预设已应用: ${preset.name}`, preset);
    
    // 这里应该将规则发送到引擎
    message.success(t('environment.presetApplied'));
  };

  /**
   * 处理保存自定义预设
   * @param preset 预设
   */
  const handleSaveCustomPreset = (preset: EnvironmentResponseRule) => {
    // 检查是否已存在相同ID的预设
    const existingPresetIndex = customPresets.findIndex(p => p.id === preset.id);
    
    if (existingPresetIndex !== -1) {
      // 如果存在，更新预设
      const updatedPresets = [...customPresets];
      updatedPresets[existingPresetIndex] = preset;
      setCustomPresets(updatedPresets);
    } else {
      // 如果不存在，添加预设
      setCustomPresets([...customPresets, preset]);
    }
    
    // 添加日志
    addLog(LogLevel.INFO, `自定义预设已保存: ${preset.name}`, preset);
    
    // 这里应该将预设保存到存储
    message.success(t('environment.presetSaved'));
  };

  /**
   * 处理删除自定义预设
   * @param presetId 预设ID
   */
  const handleDeleteCustomPreset = (presetId: string) => {
    setCustomPresets(customPresets.filter(p => p.id !== presetId));
    
    // 添加日志
    addLog(LogLevel.INFO, `自定义预设已删除: ${presetId}`);
    
    // 这里应该从存储中删除预设
    message.success(t('environment.presetDeleted'));
  };

  /**
   * 处理测试规则
   * @param rule 规则
   */
  const handleTestRule = (rule: EnvironmentResponseRule) => {
    // 模拟活动响应
    const newActiveResponses = new Map(activeResponses);
    
    if (newActiveResponses.has(rule.id)) {
      // 如果规则已经活动，停止响应
      newActiveResponses.delete(rule.id);
      addLog(LogLevel.INFO, `响应已停止: ${rule.name}`, rule);
    } else {
      // 如果规则未活动，启动响应
      newActiveResponses.set(rule.id, rule.actions);
      addLog(LogLevel.INFO, `响应已启动: ${rule.name}`, rule);
    }
    
    setActiveResponses(newActiveResponses);
    
    // 这里应该将测试命令发送到引擎
    message.success(t('environment.ruleTestStarted'));
  };

  /**
   * 处理刷新
   */
  const handleRefresh = () => {
    fetchEnvironmentData();
    fetchResponseRules();
    fetchCustomPresets();
    
    // 添加日志
    addLog(LogLevel.INFO, '数据已刷新');
    
    message.success(t('environment.dataRefreshed'));
  };

  /**
   * 处理清除日志
   */
  const handleClearLogs = () => {
    setLogs([]);
    message.success(t('environment.logsCleared'));
  };

  /**
   * 处理切换调试
   * @param enabled 是否启用
   */
  const handleToggleDebug = (enabled: boolean) => {
    setDebugEnabled(enabled);
    
    // 添加日志
    addLog(LogLevel.INFO, `调试模式已${enabled ? '启用' : '禁用'}`);
    
    // 这里应该将调试状态发送到引擎
    message.success(t(`environment.debug${enabled ? 'Enabled' : 'Disabled'}`));
  };

  /**
   * 处理切换可视化
   * @param enabled 是否启用
   */
  const handleToggleVisualization = (enabled: boolean) => {
    setVisualizationEnabled(enabled);
    
    // 添加日志
    addLog(LogLevel.INFO, `可视化已${enabled ? '启用' : '禁用'}`);
    
    // 这里应该将可视化状态发送到引擎
    message.success(t(`environment.visualization${enabled ? 'Enabled' : 'Disabled'}`));
  };

  return (
    <div className="environment-editor-panel">
      <Card
        title={
          <Space>
            <EnvironmentOutlined />
            <span>{t('environment.editorPanel')}</span>
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
            >
              {t('environment.refresh')}
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={() => {
                // 保存所有数据
                if (environmentData) {
                  handleSaveEnvironmentData(environmentData);
                }
                handleSaveResponseRules(responseRules);
                message.success(t('environment.allDataSaved'));
              }}
            >
              {t('environment.saveAll')}
            </Button>
          </Space>
        }
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <span>
                <EnvironmentOutlined />
                {t('environment.awareness')}
              </span>
            }
            key="awareness"
          >
            <EnvironmentAwarenessPanel
              entityId={entityId}
              environmentData={environmentData || undefined}
              onSave={handleSaveEnvironmentData}
              onRefresh={handleRefresh}
            />
          </TabPane>
          <TabPane
            tab={
              <span>
                <ThunderboltOutlined />
                {t('environment.response')}
              </span>
            }
            key="response"
          >
            <EnvironmentResponseEditor
              entityId={entityId}
              initialRules={responseRules}
              onSave={handleSaveResponseRules}
              onTest={handleTestRule}
            />
          </TabPane>
          <TabPane
            tab={
              <span>
                <AppstoreOutlined />
                {t('environment.presets')}
              </span>
            }
            key="presets"
          >
            <EnvironmentPresetPanel
              entityId={entityId}
              onApplyPreset={handleApplyPreset}
              onSaveCustomPreset={handleSaveCustomPreset}
              onDeleteCustomPreset={handleDeleteCustomPreset}
              customPresets={customPresets}
            />
          </TabPane>
          <TabPane
            tab={
              <span>
                <AreaChartOutlined />
                {t('environment.visualization')}
              </span>
            }
            key="visualization"
          >
            <EnvironmentVisualizationPanel
              entityId={entityId}
              environmentData={environmentData || undefined}
              historicalData={historicalData}
              onRefresh={handleRefresh}
              onToggleVisualization={handleToggleVisualization}
              visualizationEnabled={visualizationEnabled}
            />
          </TabPane>
          <TabPane
            tab={
              <span>
                <BugOutlined />
                {t('environment.debug')}
              </span>
            }
            key="debug"
          >
            <EnvironmentDebugPanel
              entityId={entityId}
              environmentData={environmentData ? {
                ...environmentData,
                windDirection: environmentData.windDirection || { x: 1, y: 0, z: 0 },
                customParameters: environmentData.customParameters || new Map(),
                lastEnvironmentChangeTime: environmentData.lastEnvironmentChangeTime || Date.now(),
                awarenessRange: environmentData.awarenessRange || 50
              } : undefined}
              activeResponses={activeResponses}
              logs={logs}
              onClearLogs={handleClearLogs}
              onRefresh={handleRefresh}
              onToggleDebug={handleToggleDebug}
              debugEnabled={debugEnabled}
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default EnvironmentEditorPanel;
