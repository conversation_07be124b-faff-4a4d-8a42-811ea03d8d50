/**
 * AnimationPropertyEditor.tsx
 * 
 * 动画属性编辑器组件
 */

import React, { useState, useCallback, useMemo } from 'react';
import {
  Card,
  Form,
  Input,
  InputNumber,
  Select,
  ColorPicker,
  Slider,
  // Switch, // 暂时注释掉未使用的组件
  Button,
  Space,
  Collapse,
  Tabs,
  Tooltip
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  CopyOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  LockOutlined,
  UnlockOutlined
} from '@ant-design/icons';
import type { Keyframe, AnimationTrack } from './TimelineEditor';
import './AnimationPropertyEditor.module.css';

const { Panel } = Collapse;
const { TabPane } = Tabs;

/**
 * 动画属性类型
 */
export enum AnimationPropertyType {
  TRANSFORM = 'transform',
  OPACITY = 'opacity',
  COLOR = 'color',
  SIZE = 'size',
  POSITION = 'position',
  ROTATION = 'rotation',
  SCALE = 'scale',
  CUSTOM = 'custom'
}

/**
 * 属性配置接口
 */
export interface PropertyConfig {
  type: AnimationPropertyType;
  name: string;
  label: string;
  defaultValue: any;
  min?: number;
  max?: number;
  step?: number;
  unit?: string;
  options?: Array<{ label: string; value: any }>;
}

/**
 * 预定义属性配置
 */
const PROPERTY_CONFIGS: PropertyConfig[] = [
  {
    type: AnimationPropertyType.POSITION,
    name: 'x',
    label: 'X位置',
    defaultValue: 0,
    unit: 'px'
  },
  {
    type: AnimationPropertyType.POSITION,
    name: 'y',
    label: 'Y位置',
    defaultValue: 0,
    unit: 'px'
  },
  {
    type: AnimationPropertyType.SIZE,
    name: 'width',
    label: '宽度',
    defaultValue: 100,
    min: 0,
    unit: 'px'
  },
  {
    type: AnimationPropertyType.SIZE,
    name: 'height',
    label: '高度',
    defaultValue: 100,
    min: 0,
    unit: 'px'
  },
  {
    type: AnimationPropertyType.ROTATION,
    name: 'rotation',
    label: '旋转',
    defaultValue: 0,
    min: -360,
    max: 360,
    unit: 'deg'
  },
  {
    type: AnimationPropertyType.SCALE,
    name: 'scaleX',
    label: 'X缩放',
    defaultValue: 1,
    min: 0,
    max: 5,
    step: 0.1
  },
  {
    type: AnimationPropertyType.SCALE,
    name: 'scaleY',
    label: 'Y缩放',
    defaultValue: 1,
    min: 0,
    max: 5,
    step: 0.1
  },
  {
    type: AnimationPropertyType.OPACITY,
    name: 'opacity',
    label: '透明度',
    defaultValue: 1,
    min: 0,
    max: 1,
    step: 0.01
  },
  {
    type: AnimationPropertyType.COLOR,
    name: 'backgroundColor',
    label: '背景色',
    defaultValue: '#ffffff'
  },
  {
    type: AnimationPropertyType.COLOR,
    name: 'borderColor',
    label: '边框色',
    defaultValue: '#000000'
  }
];

/**
 * 缓动函数选项
 */
const EASING_OPTIONS = [
  { label: 'Linear', value: 'linear' },
  { label: 'Ease', value: 'ease' },
  { label: 'Ease In', value: 'ease-in' },
  { label: 'Ease Out', value: 'ease-out' },
  { label: 'Ease In Out', value: 'ease-in-out' },
  { label: 'Ease In Sine', value: 'ease-in-sine' },
  { label: 'Ease Out Sine', value: 'ease-out-sine' },
  { label: 'Ease In Out Sine', value: 'ease-in-out-sine' },
  { label: 'Ease In Quad', value: 'ease-in-quad' },
  { label: 'Ease Out Quad', value: 'ease-out-quad' },
  { label: 'Ease In Out Quad', value: 'ease-in-out-quad' },
  { label: 'Ease In Cubic', value: 'ease-in-cubic' },
  { label: 'Ease Out Cubic', value: 'ease-out-cubic' },
  { label: 'Ease In Out Cubic', value: 'ease-in-out-cubic' },
  { label: 'Ease In Back', value: 'ease-in-back' },
  { label: 'Ease Out Back', value: 'ease-out-back' },
  { label: 'Ease In Out Back', value: 'ease-in-out-back' },
  { label: 'Ease In Bounce', value: 'ease-in-bounce' },
  { label: 'Ease Out Bounce', value: 'ease-out-bounce' },
  { label: 'Ease In Out Bounce', value: 'ease-in-out-bounce' }
];

/**
 * 动画属性编辑器属性
 */
export interface AnimationPropertyEditorProps {
  /** 选中的关键帧 */
  selectedKeyframes: Keyframe[];
  /** 动画轨道列表 */
  tracks: AnimationTrack[];
  /** 选中的组件ID */
  selectedComponentId?: string;
  /** 关键帧变化回调 */
  onKeyframeChange?: (keyframes: Keyframe[]) => void;
  /** 轨道变化回调 */
  onTrackChange?: (tracks: AnimationTrack[]) => void;
  /** 添加轨道回调 */
  onAddTrack?: (property: string, componentId: string) => void;
  /** 样式类名 */
  className?: string;
}

/**
 * 动画属性编辑器组件
 */
export const AnimationPropertyEditor: React.FC<AnimationPropertyEditorProps> = ({
  selectedKeyframes,
  tracks,
  selectedComponentId,
  onKeyframeChange,
  onTrackChange,
  onAddTrack,
  className
}) => {
  // const [form] = Form.useForm(); // 暂时注释掉未使用的变量
  const [activeTab, setActiveTab] = useState('keyframes');

  // 获取当前选中的轨道
  const selectedTracks = useMemo(() => {
    if (!selectedComponentId) return [];
    return tracks.filter(track => track.componentId === selectedComponentId);
  }, [tracks, selectedComponentId]);

  // 获取可用的属性配置
  const availableProperties = useMemo(() => {
    const usedProperties = selectedTracks.map(track => track.property);
    return PROPERTY_CONFIGS.filter(config => !usedProperties.includes(config.name));
  }, [selectedTracks]);

  // 处理关键帧值变化
  const handleKeyframeValueChange = useCallback((keyframeId: string, field: string, value: any) => {
    const updatedKeyframes = selectedKeyframes.map(kf => 
      kf.id === keyframeId 
        ? { ...kf, [field]: value }
        : kf
    );
    onKeyframeChange?.(updatedKeyframes);
  }, [selectedKeyframes, onKeyframeChange]);

  // 处理轨道属性变化
  const handleTrackChange = useCallback((trackId: string, field: string, value: any) => {
    const updatedTracks = tracks.map(track => 
      track.id === trackId 
        ? { ...track, [field]: value }
        : track
    );
    onTrackChange?.(updatedTracks);
  }, [tracks, onTrackChange]);

  // 添加新轨道
  const handleAddTrack = useCallback((property: string) => {
    if (selectedComponentId) {
      onAddTrack?.(property, selectedComponentId);
    }
  }, [selectedComponentId, onAddTrack]);

  // 渲染属性值编辑器
  const renderPropertyEditor = useCallback((config: PropertyConfig, value: any, onChange: (value: any) => void) => {
    switch (config.type) {
      case AnimationPropertyType.COLOR:
        return (
          <ColorPicker
            value={value}
            onChange={(color) => onChange(color.toHexString())}
            showText
          />
        );
      
      case AnimationPropertyType.OPACITY:
        return (
          <Slider
            value={value}
            min={config.min || 0}
            max={config.max || 1}
            step={config.step || 0.01}
            onChange={onChange}
            tooltip={{ formatter: (val) => `${val}` }}
          />
        );
      
      default:
        return (
          <InputNumber
            value={value}
            min={config.min}
            max={config.max}
            step={config.step || 1}
            onChange={onChange}
            addonAfter={config.unit}
            style={{ width: '100%' }}
          />
        );
    }
  }, []);

  // 渲染关键帧编辑器
  const renderKeyframeEditor = () => {
    if (selectedKeyframes.length === 0) {
      return (
        <div className="empty-state">
          <p>请选择关键帧进行编辑</p>
        </div>
      );
    }

    return (
      <div className="keyframe-editor">
        {selectedKeyframes.map(keyframe => {
          const track = tracks.find(t => t.keyframes.some(kf => kf.id === keyframe.id));
          const propertyConfig = PROPERTY_CONFIGS.find(config => config.name === track?.property);
          
          return (
            <Card
              key={keyframe.id}
              size="small"
              title={`关键帧 - ${track?.name || '未知'}`}
              className="keyframe-card"
            >
              <Form layout="vertical" size="small">
                <Form.Item label="时间">
                  <InputNumber
                    value={keyframe.time}
                    min={0}
                    step={0.01}
                    addonAfter="s"
                    onChange={(value) => handleKeyframeValueChange(keyframe.id, 'time', value || 0)}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
                
                <Form.Item label="值">
                  {propertyConfig ? (
                    renderPropertyEditor(
                      propertyConfig,
                      keyframe.value,
                      (value) => handleKeyframeValueChange(keyframe.id, 'value', value)
                    )
                  ) : (
                    <Input
                      value={keyframe.value}
                      onChange={(e) => handleKeyframeValueChange(keyframe.id, 'value', e.target.value)}
                    />
                  )}
                </Form.Item>
                
                <Form.Item label="缓动函数">
                  <Select
                    value={keyframe.easing}
                    onChange={(value) => handleKeyframeValueChange(keyframe.id, 'easing', value)}
                    options={EASING_OPTIONS}
                  />
                </Form.Item>
              </Form>
            </Card>
          );
        })}
      </div>
    );
  };

  // 渲染轨道编辑器
  const renderTrackEditor = () => {
    return (
      <div className="track-editor">
        {/* 现有轨道 */}
        <Collapse size="small" ghost>
          {selectedTracks.map(track => (
            <Panel
              key={track.id}
              header={
                <div className="track-header">
                  <span style={{ color: track.color }}>●</span>
                  <span>{track.name}</span>
                  <Space size="small" onClick={(e) => e.stopPropagation()}>
                    <Tooltip title={track.visible ? '隐藏' : '显示'}>
                      <Button
                        type="text"
                        size="small"
                        icon={track.visible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                        onClick={() => handleTrackChange(track.id, 'visible', !track.visible)}
                      />
                    </Tooltip>
                    <Tooltip title={track.locked ? '解锁' : '锁定'}>
                      <Button
                        type="text"
                        size="small"
                        icon={track.locked ? <LockOutlined /> : <UnlockOutlined />}
                        onClick={() => handleTrackChange(track.id, 'locked', !track.locked)}
                      />
                    </Tooltip>
                    <Tooltip title="复制轨道">
                      <Button type="text" size="small" icon={<CopyOutlined />} />
                    </Tooltip>
                    <Tooltip title="删除轨道">
                      <Button type="text" size="small" icon={<DeleteOutlined />} danger />
                    </Tooltip>
                  </Space>
                </div>
              }
            >
              <Form layout="vertical" size="small">
                <Form.Item label="轨道名称">
                  <Input
                    value={track.name}
                    onChange={(e) => handleTrackChange(track.id, 'name', e.target.value)}
                  />
                </Form.Item>
                
                <Form.Item label="轨道颜色">
                  <ColorPicker
                    value={track.color}
                    onChange={(color) => handleTrackChange(track.id, 'color', color.toHexString())}
                    showText
                  />
                </Form.Item>
                
                <Form.Item label="关键帧数量">
                  <span>{track.keyframes.length}</span>
                </Form.Item>
              </Form>
            </Panel>
          ))}
        </Collapse>

        {/* 添加新轨道 */}
        {availableProperties.length > 0 && (
          <Card size="small" title="添加动画属性" className="add-track-card">
            <div className="property-grid">
              {availableProperties.map(config => (
                <Button
                  key={config.name}
                  type="dashed"
                  size="small"
                  icon={<PlusOutlined />}
                  onClick={() => handleAddTrack(config.name)}
                  className="property-button"
                >
                  {config.label}
                </Button>
              ))}
            </div>
          </Card>
        )}
      </div>
    );
  };

  return (
    <div className={`animation-property-editor ${className || ''}`}>
      <Tabs activeKey={activeTab} onChange={setActiveTab} size="small">
        <TabPane tab="关键帧" key="keyframes">
          {renderKeyframeEditor()}
        </TabPane>
        <TabPane tab="轨道" key="tracks">
          {renderTrackEditor()}
        </TabPane>
      </Tabs>
    </div>
  );
};

export default AnimationPropertyEditor;
