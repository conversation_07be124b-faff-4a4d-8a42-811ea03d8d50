/**
 * RAG应用工作空间
 */
import React, { useState } from 'react';
import {
  Layout,
  Menu,
  Button,
  Space,
  Typography,
  Breadcrumb,
  Badge,
} from 'antd';
import {
  RobotOutlined,
  DatabaseOutlined,
  MessageOutlined,
  SoundOutlined,
  ApiOutlined,
  HomeOutlined,
  SettingOutlined,
  PlayCircleOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import RAGQuickStart from './RAGQuickStart';
import KnowledgeBasePanel from '../panels/KnowledgeBasePanel';
import AvatarConfigPanel from '../panels/AvatarConfigPanel';
import VoiceConfigPanel from '../panels/VoiceConfigPanel';
import RAGDialoguePanel from '../panels/RAGDialoguePanel';
import RAGApplicationsPanel from '../panels/RAGApplicationsPanel';

const { Sider, Content } = Layout;
const { Title } = Typography;

/**
 * RAG工作空间组件
 */
const RAGWorkspace: React.FC = () => {
  const { t: _ } = useTranslation();
  const [selectedKey, setSelectedKey] = useState('quickstart');
  const [collapsed, setCollapsed] = useState(false);

  /**
   * 菜单项配置
   */
  const menuItems = [
    {
      key: 'quickstart',
      icon: <HomeOutlined />,
      label: '快速启动',
      component: <RAGQuickStart />,
    },
    {
      key: 'knowledge',
      icon: <DatabaseOutlined />,
      label: '知识库管理',
      component: <KnowledgeBasePanel />,
      badge: 2, // 显示知识库数量
    },
    {
      key: 'avatar',
      icon: <RobotOutlined />,
      label: '数字人配置',
      component: <AvatarConfigPanel />,
      badge: 3, // 显示数字人数量
    },
    {
      key: 'voice',
      icon: <SoundOutlined />,
      label: '语音配置',
      component: <VoiceConfigPanel />,
    },
    {
      key: 'dialogue',
      icon: <MessageOutlined />,
      label: '对话测试',
      component: <RAGDialoguePanel />,
    },
    {
      key: 'applications',
      icon: <ApiOutlined />,
      label: '应用管理',
      component: <RAGApplicationsPanel />,
      badge: 2, // 显示应用数量
    },
  ];

  /**
   * 获取当前选中的菜单项
   */
  const getCurrentMenuItem = () => {
    return menuItems.find(item => item.key === selectedKey);
  };

  /**
   * 渲染菜单项
   */
  const renderMenuItem = (item: any) => {
    const label = (
      <Space>
        <span>{item.label}</span>
        {item.badge && (
          <Badge count={item.badge} size="small" />
        )}
      </Space>
    );

    return {
      key: item.key,
      icon: item.icon,
      label: collapsed ? item.label : label,
    };
  };

  /**
   * 渲染内容区域
   */
  const renderContent = () => {
    const currentItem = getCurrentMenuItem();
    if (!currentItem) return null;

    return (
      <div style={{ height: '100%' }}>
        {currentItem.component}
      </div>
    );
  };

  return (
    <Layout style={{ height: '100vh', background: '#f0f2f5' }}>
      {/* 侧边栏 */}
      <Sider
        collapsible
        collapsed={collapsed}
        onCollapse={setCollapsed}
        width={250}
        style={{
          background: '#fff',
          borderRight: '1px solid #e8e8e8',
        }}
      >
        {/* Logo区域 */}
        <div
          style={{
            height: '64px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderBottom: '1px solid #e8e8e8',
            background: '#001529',
          }}
        >
          <RobotOutlined
            style={{
              fontSize: collapsed ? '24px' : '32px',
              color: '#1890ff',
              marginRight: collapsed ? 0 : '8px',
            }}
          />
          {!collapsed && (
            <Title
              level={4}
              style={{
                color: '#fff',
                margin: 0,
                fontSize: '16px',
              }}
            >
              RAG应用
            </Title>
          )}
        </div>

        {/* 菜单 */}
        <Menu
          mode="inline"
          selectedKeys={[selectedKey]}
          style={{ borderRight: 0, height: 'calc(100% - 64px)' }}
          items={menuItems.map(renderMenuItem)}
          onClick={({ key }) => setSelectedKey(key)}
        />
      </Sider>

      {/* 主内容区域 */}
      <Layout>
        {/* 头部 */}
        <div
          style={{
            background: '#fff',
            padding: '0 24px',
            borderBottom: '1px solid #e8e8e8',
            height: '64px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <div>
            <Breadcrumb
              items={[
                {
                  title: (
                    <Space>
                      <RobotOutlined />
                      <span>RAG应用</span>
                    </Space>
                  ),
                },
                {
                  title: getCurrentMenuItem()?.label,
                },
              ]}
            />
          </div>
          
          <Space>
            <Button
              icon={<PlayCircleOutlined />}
              type="primary"
              ghost
            >
              预览应用
            </Button>
            <Button
              icon={<SettingOutlined />}
            >
              全局设置
            </Button>
          </Space>
        </div>

        {/* 内容区域 */}
        <Content
          style={{
            background: '#fff',
            margin: '16px',
            borderRadius: '6px',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          {renderContent()}
        </Content>
      </Layout>
    </Layout>
  );
};

export default RAGWorkspace;
