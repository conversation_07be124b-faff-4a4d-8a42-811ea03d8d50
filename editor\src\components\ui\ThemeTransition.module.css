/**
 * ThemeTransition.module.css
 * 
 * 主题切换动画样式
 */

/* 主题过渡容器 */
.theme-transition-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.theme-transition-container.transitioning {
  pointer-events: none;
}

/* 过渡遮罩基础样式 */
.theme-transition-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  pointer-events: none;
  will-change: transform, opacity;
}

/* 淡入淡出过渡 */
.fade-transition {
  opacity: 0;
  transition: opacity var(--theme-transition-duration, 800ms) var(--theme-transition-easing, ease-in-out);
}

.fade-transition.active {
  opacity: 1;
}

/* 滑动过渡 */
.slide-transition {
  transform: translateX(100%);
  transition: transform var(--theme-transition-duration, 800ms) var(--theme-transition-easing, cubic-bezier(0.4, 0, 0.2, 1));
}

.slide-transition.slide-left {
  transform: translateX(-100%);
}

.slide-transition.slide-right {
  transform: translateX(100%);
}

.slide-transition.slide-up {
  transform: translateY(-100%);
}

.slide-transition.slide-down {
  transform: translateY(100%);
}

.slide-transition.active {
  transform: translate(0, 0);
}

/* 波纹过渡 */
.ripple-transition {
  position: relative;
  overflow: hidden;
}

.ripple-transition::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: var(--theme-background, #ffffff);
  transform: translate(-50%, -50%);
  transition: width var(--theme-transition-duration, 800ms) var(--theme-transition-easing, ease-out),
              height var(--theme-transition-duration, 800ms) var(--theme-transition-easing, ease-out);
  z-index: 1;
}

.ripple-transition.active::before {
  width: 300vmax;
  height: 300vmax;
}

/* 变形过渡 */
.morph-transition {
  clip-path: circle(0% at 50% 50%);
  transition: clip-path var(--theme-transition-duration, 800ms) var(--theme-transition-easing, ease-in-out);
}

.morph-transition.active {
  clip-path: circle(150% at 50% 50%);
}

/* 波浪过渡 */
.wave-transition {
  position: relative;
  overflow: hidden;
}

.wave-transition::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 200%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--theme-background, #ffffff) 25%,
    var(--theme-background, #ffffff) 75%,
    transparent 100%
  );
  transform: skewX(-15deg);
  transition: left var(--theme-transition-duration, 800ms) var(--theme-transition-easing, ease-in-out);
  z-index: 1;
}

.wave-transition.active::before {
  left: 100%;
}

/* 粒子过渡效果 */
.particles-transition {
  position: relative;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--theme-primary, #1890ff);
  border-radius: 50%;
  opacity: 0;
  animation: particleFloat var(--theme-transition-duration, 800ms) ease-out forwards;
}

@keyframes particleFloat {
  0% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(180deg);
  }
  100% {
    opacity: 0;
    transform: scale(0) rotate(360deg);
  }
}

/* 主题变量过渡 */
:root {
  --theme-transition-duration: 800ms;
  --theme-transition-easing: cubic-bezier(0.4, 0, 0.2, 1);
  
  /* 颜色过渡 */
  --theme-primary: #1890ff;
  --theme-secondary: #722ed1;
  --theme-background: #ffffff;
  --theme-surface: #fafafa;
  --theme-text: #000000;
  --theme-text-secondary: #666666;
  --theme-border: #d9d9d9;
  --theme-shadow: rgba(0, 0, 0, 0.15);
  
  transition: 
    --theme-primary var(--theme-transition-duration) var(--theme-transition-easing),
    --theme-secondary var(--theme-transition-duration) var(--theme-transition-easing),
    --theme-background var(--theme-transition-duration) var(--theme-transition-easing),
    --theme-surface var(--theme-transition-duration) var(--theme-transition-easing),
    --theme-text var(--theme-transition-duration) var(--theme-transition-easing),
    --theme-text-secondary var(--theme-transition-duration) var(--theme-transition-easing),
    --theme-border var(--theme-transition-duration) var(--theme-transition-easing),
    --theme-shadow var(--theme-transition-duration) var(--theme-transition-easing);
}

/* 元素过渡样式 */
* {
  transition: 
    background-color var(--theme-transition-duration) var(--theme-transition-easing),
    color var(--theme-transition-duration) var(--theme-transition-easing),
    border-color var(--theme-transition-duration) var(--theme-transition-easing),
    box-shadow var(--theme-transition-duration) var(--theme-transition-easing);
}

/* 特殊元素过渡 */
.theme-aware {
  background-color: var(--theme-background);
  color: var(--theme-text);
  border-color: var(--theme-border);
}

.theme-surface {
  background-color: var(--theme-surface);
  color: var(--theme-text);
}

.theme-primary {
  background-color: var(--theme-primary);
  color: white;
}

.theme-secondary {
  background-color: var(--theme-secondary);
  color: white;
}

/* 暗色主题 */
[data-theme="dark"] {
  --theme-primary: #177ddc;
  --theme-secondary: #9254de;
  --theme-background: #141414;
  --theme-surface: #1f1f1f;
  --theme-text: #ffffff;
  --theme-text-secondary: #a6a6a6;
  --theme-border: #434343;
  --theme-shadow: rgba(255, 255, 255, 0.15);
}

/* 高对比度主题 */
[data-theme="high-contrast"] {
  --theme-primary: #0000ff;
  --theme-secondary: #800080;
  --theme-background: #000000;
  --theme-surface: #1a1a1a;
  --theme-text: #ffffff;
  --theme-text-secondary: #cccccc;
  --theme-border: #ffffff;
  --theme-shadow: rgba(255, 255, 255, 0.5);
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  :root {
    --theme-transition-duration: 0ms;
  }
  
  * {
    transition: none !important;
    animation: none !important;
  }
  
  .theme-transition-overlay {
    transition: none !important;
  }
}

/* 性能优化 */
.theme-transition-container {
  contain: layout style paint;
}

.theme-transition-overlay {
  contain: strict;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* GPU加速 */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform, opacity;
  backface-visibility: hidden;
}

/* 过渡状态指示器 */
.transition-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  border: 3px solid var(--theme-primary);
  border-top: 3px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 10000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.transition-indicator.visible {
  opacity: 1;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 过渡进度条 */
.transition-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background: linear-gradient(
    90deg,
    var(--theme-primary) 0%,
    var(--theme-secondary) 100%
  );
  z-index: 10001;
  transition: width var(--theme-transition-duration) var(--theme-transition-easing);
}

.transition-progress.active {
  width: 100%;
}

/* 主题预览 */
.theme-preview {
  position: relative;
  width: 100px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.theme-preview:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.theme-preview-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.theme-preview-accent {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 20px;
}

.theme-preview-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .theme-transition-overlay {
    will-change: auto;
  }
  
  .transition-indicator {
    width: 30px;
    height: 30px;
    top: 10px;
    right: 10px;
  }
  
  .theme-preview {
    width: 80px;
    height: 48px;
  }
}

/* 打印样式 */
@media print {
  .theme-transition-overlay,
  .transition-indicator,
  .transition-progress {
    display: none !important;
  }
}
