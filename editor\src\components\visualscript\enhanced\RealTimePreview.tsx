/**
 * RealTimePreview.tsx
 * 
 * 实时预览组件 - 提供脚本执行的实时预览和可视化反馈
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Card,
  Button,
  Space,
  Switch,
  Slider,
  Typography,
  Tag,
  Progress,
  Tooltip,
  Alert,
  Divider
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  SettingOutlined,
  ThunderboltOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;

/**
 * 节点状态枚举
 */
export enum NodeState {
  IDLE = 'idle',
  EXECUTING = 'executing',
  COMPLETED = 'completed',
  ERROR = 'error',
  WAITING = 'waiting'
}

/**
 * 数据流信息
 */
export interface DataFlow {
  connectionId: string;
  sourceNodeId: string;
  targetNodeId: string;
  data: any;
  timestamp: number;
  dataType: string;
}

/**
 * 执行统计信息
 */
export interface ExecutionStats {
  totalNodes: number;
  executedNodes: number;
  errorNodes: number;
  executionTime: number;
  averageNodeTime: number;
  memoryUsage: number;
}

/**
 * 可视化脚本数据接口
 */
export interface VisualScriptData {
  nodes: any[];
  connections: any[];
  variables: Record<string, any>;
}

/**
 * 实时预览组件属性
 */
export interface RealTimePreviewProps {
  /** 脚本数据 */
  scriptData: VisualScriptData;
  /** 是否启用实时预览 */
  enabled?: boolean;
  /** 预览更新频率（毫秒） */
  updateInterval?: number;
  /** 节点状态变化回调 */
  onNodeStateChange?: (nodeId: string, state: NodeState) => void;
  /** 数据流更新回调 */
  onDataFlowUpdate?: (dataFlow: DataFlow) => void;
  /** 执行统计更新回调 */
  onStatsUpdate?: (stats: ExecutionStats) => void;
  /** 错误回调 */
  onError?: (error: Error) => void;
}

/**
 * 实时预览组件
 */
const RealTimePreview: React.FC<RealTimePreviewProps> = ({
  scriptData,
  enabled = true,
  updateInterval = 100,
  onNodeStateChange,
  onDataFlowUpdate,
  onStatsUpdate,
  onError
}) => {
  const { t } = useTranslation();
  
  // 状态管理
  const [isRunning, setIsRunning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [showDataFlow, setShowDataFlow] = useState(true);
  const [showStats, setShowStats] = useState(true);
  const [playbackSpeed, setPlaybackSpeed] = useState(1);
  
  // 执行状态
  const [nodeStates, setNodeStates] = useState<Record<string, NodeState>>({});
  const [dataFlows, setDataFlows] = useState<DataFlow[]>([]);
  const [executionStats, setExecutionStats] = useState<ExecutionStats>({
    totalNodes: 0,
    executedNodes: 0,
    errorNodes: 0,
    executionTime: 0,
    averageNodeTime: 0,
    memoryUsage: 0
  });
  
  // 引用
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number>(0);

  /**
   * 初始化节点状态
   */
  const initializeNodeStates = useCallback(() => {
    const initialStates: Record<string, NodeState> = {};
    scriptData.nodes.forEach(node => {
      initialStates[node.id] = NodeState.IDLE;
    });
    setNodeStates(initialStates);
  }, [scriptData.nodes]);

  /**
   * 更新节点状态
   */
  const updateNodeState = useCallback((nodeId: string, state: NodeState) => {
    setNodeStates(prev => ({
      ...prev,
      [nodeId]: state
    }));
    
    if (onNodeStateChange) {
      onNodeStateChange(nodeId, state);
    }
  }, [onNodeStateChange]);

  /**
   * 添加数据流
   */
  const addDataFlow = useCallback((dataFlow: DataFlow) => {
    setDataFlows(prev => {
      const newFlows = [...prev, dataFlow];
      // 保持最近100个数据流
      return newFlows.slice(-100);
    });
    
    if (onDataFlowUpdate) {
      onDataFlowUpdate(dataFlow);
    }
  }, [onDataFlowUpdate]);

  /**
   * 更新执行统计
   */
  const updateStats = useCallback(() => {
    const totalNodes = scriptData.nodes.length;
    const executedNodes = Object.values(nodeStates).filter(
      state => state === NodeState.COMPLETED || state === NodeState.EXECUTING
    ).length;
    const errorNodes = Object.values(nodeStates).filter(
      state => state === NodeState.ERROR
    ).length;
    const executionTime = Date.now() - startTimeRef.current;
    const averageNodeTime = executedNodes > 0 ? executionTime / executedNodes : 0;
    
    const stats: ExecutionStats = {
      totalNodes,
      executedNodes,
      errorNodes,
      executionTime,
      averageNodeTime,
      memoryUsage: (performance as any).memory?.usedJSHeapSize || 0
    };
    
    setExecutionStats(stats);
    
    if (onStatsUpdate) {
      onStatsUpdate(stats);
    }
  }, [scriptData.nodes.length, nodeStates, onStatsUpdate]);

  /**
   * 模拟脚本执行
   */
  const simulateExecution = useCallback(() => {
    if (!isRunning || isPaused) return;

    // 模拟节点执行逻辑
    const nodeIds = scriptData.nodes.map(node => node.id);
    const randomNodeId = nodeIds[Math.floor(Math.random() * nodeIds.length)];
    
    if (nodeStates[randomNodeId] === NodeState.IDLE) {
      updateNodeState(randomNodeId, NodeState.EXECUTING);
      
      // 模拟执行时间
      setTimeout(() => {
        if (Math.random() > 0.9) {
          updateNodeState(randomNodeId, NodeState.ERROR);
          // 调用错误回调
          onError?.(new Error(`节点 ${randomNodeId} 执行失败`));
        } else {
          updateNodeState(randomNodeId, NodeState.COMPLETED);
          
          // 模拟数据流
          const connections = scriptData.connections.filter(
            conn => conn.sourceNodeId === randomNodeId
          );
          
          connections.forEach(conn => {
            const dataFlow: DataFlow = {
              connectionId: conn.id,
              sourceNodeId: conn.sourceNodeId,
              targetNodeId: conn.targetNodeId,
              data: Math.random() * 100,
              timestamp: Date.now(),
              dataType: 'number'
            };
            addDataFlow(dataFlow);
          });
        }
      }, Math.random() * 1000 / playbackSpeed);
    }
    
    updateStats();
  }, [isRunning, isPaused, scriptData, nodeStates, playbackSpeed, updateNodeState, addDataFlow, updateStats]);

  /**
   * 开始预览
   */
  const startPreview = useCallback(() => {
    setIsRunning(true);
    setIsPaused(false);
    startTimeRef.current = Date.now();
    initializeNodeStates();
    setDataFlows([]);
  }, [initializeNodeStates]);

  /**
   * 暂停预览
   */
  const pausePreview = useCallback(() => {
    setIsPaused(!isPaused);
  }, [isPaused]);

  /**
   * 停止预览
   */
  const stopPreview = useCallback(() => {
    setIsRunning(false);
    setIsPaused(false);
    initializeNodeStates();
    setDataFlows([]);
    setExecutionStats({
      totalNodes: 0,
      executedNodes: 0,
      errorNodes: 0,
      executionTime: 0,
      averageNodeTime: 0,
      memoryUsage: 0
    });
  }, [initializeNodeStates]);

  /**
   * 重置预览
   */
  const resetPreview = useCallback(() => {
    stopPreview();
    setTimeout(startPreview, 100);
  }, [stopPreview, startPreview]);

  // 设置定时器
  useEffect(() => {
    if (isRunning && !isPaused && enabled) {
      intervalRef.current = setInterval(simulateExecution, updateInterval);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning, isPaused, enabled, simulateExecution, updateInterval]);

  // 初始化
  useEffect(() => {
    initializeNodeStates();
  }, [initializeNodeStates]);

  /**
   * 渲染控制工具栏
   */
  const renderControlToolbar = () => (
    <Space>
      <Button
        type="primary"
        icon={isRunning && !isPaused ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
        onClick={isRunning ? pausePreview : startPreview}
        disabled={!enabled}
      >
        {isRunning && !isPaused ? t('实时预览.暂停') : t('实时预览.开始')}
      </Button>
      
      <Button
        icon={<StopOutlined />}
        onClick={stopPreview}
        disabled={!isRunning}
      >
        {t('实时预览.停止')}
      </Button>
      
      <Button
        icon={<ReloadOutlined />}
        onClick={resetPreview}
        disabled={!enabled}
      >
        {t('实时预览.重置')}
      </Button>
      
      <Divider type="vertical" />
      
      <Tooltip title={t('实时预览.播放速度')}>
        <Space>
          <ThunderboltOutlined />
          <Slider
            min={0.1}
            max={3}
            step={0.1}
            value={playbackSpeed}
            onChange={setPlaybackSpeed}
            style={{ width: 100 }}
          />
          <Text>{playbackSpeed}x</Text>
        </Space>
      </Tooltip>
    </Space>
  );

  /**
   * 渲染显示选项
   */
  const renderDisplayOptions = () => (
    <Space>
      <Switch
        checkedChildren={<EyeOutlined />}
        unCheckedChildren={<EyeInvisibleOutlined />}
        checked={showDataFlow}
        onChange={setShowDataFlow}
      />
      <Text>{t('实时预览.数据流')}</Text>
      
      <Switch
        checkedChildren={<EyeOutlined />}
        unCheckedChildren={<EyeInvisibleOutlined />}
        checked={showStats}
        onChange={setShowStats}
      />
      <Text>{t('实时预览.统计信息')}</Text>
    </Space>
  );

  /**
   * 渲染执行统计
   */
  const renderExecutionStats = () => {
    if (!showStats) return null;

    const progressPercent = executionStats.totalNodes > 0 
      ? (executionStats.executedNodes / executionStats.totalNodes) * 100 
      : 0;

    return (
      <Card size="small" title={t('实时预览.执行统计')}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text>{t('实时预览.执行进度')}: </Text>
            <Progress 
              percent={progressPercent} 
              size="small" 
              status={executionStats.errorNodes > 0 ? 'exception' : 'active'}
            />
          </div>
          
          <Space wrap>
            <Tag color="blue">
              {t('实时预览.总节点')}: {executionStats.totalNodes}
            </Tag>
            <Tag color="green">
              {t('实时预览.已执行')}: {executionStats.executedNodes}
            </Tag>
            <Tag color="red">
              {t('实时预览.错误')}: {executionStats.errorNodes}
            </Tag>
          </Space>
          
          <Space wrap>
            <Tag icon={<ClockCircleOutlined />}>
              {t('实时预览.执行时间')}: {executionStats.executionTime}ms
            </Tag>
            <Tag>
              {t('实时预览.平均时间')}: {executionStats.averageNodeTime.toFixed(2)}ms
            </Tag>
          </Space>
        </Space>
      </Card>
    );
  };

  /**
   * 渲染数据流信息
   */
  const renderDataFlowInfo = () => {
    if (!showDataFlow) return null;

    const recentFlows = dataFlows.slice(-10);

    return (
      <Card size="small" title={t('实时预览.数据流')}>
        <div style={{ maxHeight: 200, overflow: 'auto' }}>
          {recentFlows.length === 0 ? (
            <Text type="secondary">{t('实时预览.暂无数据流')}</Text>
          ) : (
            recentFlows.map((flow, index) => (
              <div key={index} style={{ marginBottom: 8 }}>
                <Space>
                  <Tag color="processing">{flow.dataType}</Tag>
                  <Text code>{flow.sourceNodeId}</Text>
                  <Text>→</Text>
                  <Text code>{flow.targetNodeId}</Text>
                  <Text type="secondary">
                    {typeof flow.data === 'number' ? flow.data.toFixed(2) : String(flow.data)}
                  </Text>
                </Space>
              </div>
            ))
          )}
        </div>
      </Card>
    );
  };

  if (!enabled) {
    return (
      <Alert
        message={t('实时预览.已禁用')}
        description={t('实时预览.禁用说明')}
        type="info"
        showIcon
      />
    );
  }

  return (
    <div className="real-time-preview">
      <Card
        title={
          <Space>
            <EyeOutlined />
            <Title level={5} style={{ margin: 0 }}>
              {t('实时预览.标题')}
            </Title>
          </Space>
        }
        extra={
          <Space>
            {renderDisplayOptions()}
            <Button icon={<SettingOutlined />} size="small">
              {t('实时预览.设置')}
            </Button>
          </Space>
        }
        size="small"
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          {renderControlToolbar()}
          {renderExecutionStats()}
          {renderDataFlowInfo()}
        </Space>
      </Card>
    </div>
  );
};

export default RealTimePreview;
