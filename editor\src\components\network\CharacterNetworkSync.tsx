/**
 * 角色网络同步组件
 * 用于管理多人场景中的角色同步
 */
import React, { useState } from 'react';
import {
  Form,
  Input,
  Button,
  Switch,
  Slider,
  Collapse,
  Divider,
  Space,
  Typography,
  Badge,
  Table,
  Tag,
  message
} from 'antd';
import {
  LinkOutlined,
  DisconnectOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import './CharacterNetworkSync.less';

const { Panel } = Collapse;
const { Title, Text } = Typography;

// 网络同步配置
interface NetworkSyncConfig {
  enabled: boolean;
  serverUrl: string;
  roomId: string;
  syncInterval: number;
  interpolation: boolean;
  extrapolation: boolean;
  compression: boolean;
  adaptiveSync: boolean;
  prioritySync: boolean;
  syncPosition: boolean;
  syncRotation: boolean;
  syncScale: boolean;
  syncAnimation: boolean;
  syncFacialAnimation: boolean;
  syncPhysics: boolean;
  bandwidth: number;
  latencySimulation: number;
  packetLossSimulation: number;
}

// 网络用户
interface NetworkUser {
  id: string;
  name: string;
  isLocal: boolean;
  connected: boolean;
  ping: number;
}

// 网络实体
interface NetworkEntity {
  id: string;
  ownerId: string;
  type: string;
  syncEnabled: boolean;
  lastSyncTime: number;
}

// 网络状态
interface NetworkStatus {
  connected: boolean;
  roomJoined: boolean;
  ping: number;
  packetLoss: number;
  bandwidth: {
    upload: number;
    download: number;
  };
  users: NetworkUser[];
  entities: NetworkEntity[];
}

interface CharacterNetworkSyncProps {
  entityId?: string;
  onConnect?: (config: NetworkSyncConfig) => void;
  onDisconnect?: () => void;
  onConfigChange?: (config: NetworkSyncConfig) => void;
  initialConfig?: Partial<NetworkSyncConfig>;
  networkStatus?: NetworkStatus;
}

const defaultConfig: NetworkSyncConfig = {
  enabled: false,
  serverUrl: 'wss://sync.example.com',
  roomId: 'room-1',
  syncInterval: 100,
  interpolation: true,
  extrapolation: true,
  compression: true,
  adaptiveSync: true,
  prioritySync: true,
  syncPosition: true,
  syncRotation: true,
  syncScale: false,
  syncAnimation: true,
  syncFacialAnimation: true,
  syncPhysics: false,
  bandwidth: 5000,
  latencySimulation: 0,
  packetLossSimulation: 0
};

const CharacterNetworkSync: React.FC<CharacterNetworkSyncProps> = ({
  onConnect,
  onDisconnect,
  onConfigChange,
  initialConfig,
  networkStatus
}) => {
  const { t } = useTranslation();
  const [config, setConfig] = useState<NetworkSyncConfig>({
    ...defaultConfig,
    ...initialConfig
  });
  const [connecting, setConnecting] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);

  // 处理配置变更
  const handleConfigChange = <K extends keyof NetworkSyncConfig>(key: K, value: NetworkSyncConfig[K]) => {
    const newConfig = { ...config, [key]: value };
    setConfig(newConfig);
    
    if (onConfigChange) {
      onConfigChange(newConfig);
    }
  };

  // 连接到服务器
  const handleConnect = () => {
    if (!config.serverUrl) {
      message.error(t('networkSync.serverUrlRequired'));
      return;
    }
    
    if (!config.roomId) {
      message.error(t('networkSync.roomIdRequired'));
      return;
    }
    
    setConnecting(true);
    
    if (onConnect) {
      onConnect(config);
    }
    
    // 模拟连接延迟
    setTimeout(() => {
      setConnecting(false);
    }, 1500);
  };

  // 断开连接
  const handleDisconnect = () => {
    if (onDisconnect) {
      onDisconnect();
    }
  };

  // 渲染连接状态
  const renderConnectionStatus = () => {
    if (!networkStatus) {
      return (
        <div className="connection-status">
          <Badge status="default" text={t('networkSync.notConnected')} />
        </div>
      );
    }
    
    return (
      <div className="connection-status">
        <div className="status-item">
          <Badge 
            status={networkStatus.connected ? 'success' : 'error'} 
            text={networkStatus.connected ? t('networkSync.connected') : t('networkSync.disconnected')} 
          />
        </div>
        
        {networkStatus.connected && (
          <>
            <div className="status-item">
              <Text>{t('networkSync.ping')}: {networkStatus.ping}ms</Text>
            </div>
            <div className="status-item">
              <Text>{t('networkSync.packetLoss')}: {networkStatus.packetLoss.toFixed(1)}%</Text>
            </div>
            <div className="status-item">
              <Text>
                {t('networkSync.bandwidth')}: ↑{formatBandwidth(networkStatus.bandwidth.upload)} / 
                ↓{formatBandwidth(networkStatus.bandwidth.download)}
              </Text>
            </div>
          </>
        )}
      </div>
    );
  };

  // 格式化带宽
  const formatBandwidth = (bytes: number): string => {
    if (bytes < 1024) {
      return `${bytes.toFixed(0)} B/s`;
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(1)} KB/s`;
    } else {
      return `${(bytes / (1024 * 1024)).toFixed(1)} MB/s`;
    }
  };

  // 渲染用户列表
  const renderUserList = () => {
    if (!networkStatus || !networkStatus.users || networkStatus.users.length === 0) {
      return (
        <div className="empty-list">
          <Text type="secondary">{t('networkSync.noUsers')}</Text>
        </div>
      );
    }
    
    return (
      <div className="user-list">
        {networkStatus.users.map(user => (
          <div key={user.id} className="user-item">
            <Badge 
              status={user.connected ? 'success' : 'error'} 
              text={
                <Space>
                  <Text strong={user.isLocal}>{user.name}</Text>
                  {user.isLocal && <Tag color="blue">{t('networkSync.you')}</Tag>}
                  <Text type="secondary">{user.ping}ms</Text>
                </Space>
              } 
            />
          </div>
        ))}
      </div>
    );
  };

  // 渲染实体列表
  const renderEntityList = () => {
    if (!networkStatus || !networkStatus.entities || networkStatus.entities.length === 0) {
      return (
        <div className="empty-list">
          <Text type="secondary">{t('networkSync.noEntities')}</Text>
        </div>
      );
    }
    
    const columns = [
      {
        title: t('networkSync.entityId'),
        dataIndex: 'id',
        key: 'id',
        ellipsis: true,
        width: 120
      },
      {
        title: t('networkSync.owner'),
        dataIndex: 'ownerId',
        key: 'ownerId',
        width: 100,
        render: (ownerId: string) => {
          const owner = networkStatus!.users.find(user => user.id === ownerId);
          return owner ? (
            <Text strong={owner.isLocal}>{owner.name}</Text>
          ) : (
            <Text type="secondary">{ownerId}</Text>
          );
        }
      },
      {
        title: t('networkSync.type'),
        dataIndex: 'type',
        key: 'type',
        width: 100
      },
      {
        title: t('networkSync.syncStatus'),
        key: 'syncStatus',
        width: 100,
        render: (_: any, entity: NetworkEntity) => (
          <Badge
            status={entity.syncEnabled ? 'success' : 'default'}
            text={entity.syncEnabled ? t('networkSync.syncing') : t('networkSync.notSyncing')}
          />
        )
      },
      {
        title: t('networkSync.lastSync'),
        key: 'lastSync',
        width: 100,
        render: (_: any, entity: NetworkEntity) => {
          if (!entity.lastSyncTime) {
            return <Text type="secondary">-</Text>;
          }

          const now = Date.now();
          const diff = now - entity.lastSyncTime;

          if (diff < 1000) {
            return <Text type="success">{diff}ms</Text>;
          } else if (diff < 5000) {
            return <Text>{Math.floor(diff / 1000)}s</Text>;
          } else {
            return <Text type="warning">{Math.floor(diff / 1000)}s</Text>;
          }
        }
      }
    ];
    
    return (
      <Table 
        dataSource={networkStatus.entities} 
        columns={columns} 
        rowKey="id"
        size="small"
        pagination={false}
        scroll={{ y: 200 }}
      />
    );
  };

  return (
    <div className="character-network-sync">
      <div className="sync-header">
        <Title level={4}>{t('networkSync.title')}</Title>
        {renderConnectionStatus()}
      </div>
      
      <div className="sync-content">
        <Form layout="vertical">
          <Form.Item label={t('networkSync.enabled')}>
            <Switch
              checked={config.enabled}
              onChange={value => handleConfigChange('enabled', value)}
            />
          </Form.Item>
          
          <Form.Item label={t('networkSync.serverUrl')}>
            <Input
              value={config.serverUrl}
              onChange={e => handleConfigChange('serverUrl', e.target.value)}
              placeholder="wss://sync.example.com"
              disabled={networkStatus?.connected}
            />
          </Form.Item>
          
          <Form.Item label={t('networkSync.roomId')}>
            <Input
              value={config.roomId}
              onChange={e => handleConfigChange('roomId', e.target.value)}
              placeholder="room-1"
              disabled={networkStatus?.connected}
            />
          </Form.Item>
          
          <div className="connection-actions">
            {!networkStatus?.connected ? (
              <Button
                type="primary"
                icon={<LinkOutlined />}
                onClick={handleConnect}
                loading={connecting}
                disabled={!config.enabled}
                block
              >
                {t('networkSync.connect')}
              </Button>
            ) : (
              <Button
                type="primary"
                danger
                icon={<DisconnectOutlined />}
                onClick={handleDisconnect}
                block
              >
                {t('networkSync.disconnect')}
              </Button>
            )}
          </div>
          
          <Divider />
          
          <Collapse 
            ghost 
            activeKey={showAdvanced ? ['advanced'] : []}
            onChange={() => setShowAdvanced(!showAdvanced)}
          >
            <Panel header={t('networkSync.advancedSettings')} key="advanced">
              <Form.Item label={t('networkSync.syncInterval')}>
                <Slider
                  min={10}
                  max={500}
                  step={10}
                  value={config.syncInterval}
                  onChange={value => handleConfigChange('syncInterval', value)}
                  marks={{
                    10: '10ms',
                    100: '100ms',
                    250: '250ms',
                    500: '500ms'
                  }}
                />
              </Form.Item>
              
              <Form.Item label={t('networkSync.bandwidth')}>
                <Slider
                  min={1000}
                  max={20000}
                  step={1000}
                  value={config.bandwidth}
                  onChange={value => handleConfigChange('bandwidth', value)}
                  marks={{
                    1000: '1KB/s',
                    5000: '5KB/s',
                    10000: '10KB/s',
                    20000: '20KB/s'
                  }}
                />
              </Form.Item>
              
              <div className="sync-options">
                <Form.Item label={t('networkSync.interpolation')}>
                  <Switch
                    checked={config.interpolation}
                    onChange={value => handleConfigChange('interpolation', value)}
                  />
                </Form.Item>
                
                <Form.Item label={t('networkSync.extrapolation')}>
                  <Switch
                    checked={config.extrapolation}
                    onChange={value => handleConfigChange('extrapolation', value)}
                  />
                </Form.Item>
                
                <Form.Item label={t('networkSync.compression')}>
                  <Switch
                    checked={config.compression}
                    onChange={value => handleConfigChange('compression', value)}
                  />
                </Form.Item>
                
                <Form.Item label={t('networkSync.adaptiveSync')}>
                  <Switch
                    checked={config.adaptiveSync}
                    onChange={value => handleConfigChange('adaptiveSync', value)}
                  />
                </Form.Item>
                
                <Form.Item label={t('networkSync.prioritySync')}>
                  <Switch
                    checked={config.prioritySync}
                    onChange={value => handleConfigChange('prioritySync', value)}
                  />
                </Form.Item>
              </div>
              
              <Divider />
              
              <Title level={5}>{t('networkSync.syncProperties')}</Title>
              
              <div className="sync-options">
                <Form.Item label={t('networkSync.syncPosition')}>
                  <Switch
                    checked={config.syncPosition}
                    onChange={value => handleConfigChange('syncPosition', value)}
                  />
                </Form.Item>
                
                <Form.Item label={t('networkSync.syncRotation')}>
                  <Switch
                    checked={config.syncRotation}
                    onChange={value => handleConfigChange('syncRotation', value)}
                  />
                </Form.Item>
                
                <Form.Item label={t('networkSync.syncScale')}>
                  <Switch
                    checked={config.syncScale}
                    onChange={value => handleConfigChange('syncScale', value)}
                  />
                </Form.Item>
                
                <Form.Item label={t('networkSync.syncAnimation')}>
                  <Switch
                    checked={config.syncAnimation}
                    onChange={value => handleConfigChange('syncAnimation', value)}
                  />
                </Form.Item>
                
                <Form.Item label={t('networkSync.syncFacialAnimation')}>
                  <Switch
                    checked={config.syncFacialAnimation}
                    onChange={value => handleConfigChange('syncFacialAnimation', value)}
                  />
                </Form.Item>
                
                <Form.Item label={t('networkSync.syncPhysics')}>
                  <Switch
                    checked={config.syncPhysics}
                    onChange={value => handleConfigChange('syncPhysics', value)}
                  />
                </Form.Item>
              </div>
              
              <Divider />
              
              <Title level={5}>{t('networkSync.debugSettings')}</Title>
              
              <Form.Item 
                label={t('networkSync.latencySimulation')}
                tooltip={t('networkSync.latencySimulationTooltip')}
              >
                <Slider
                  min={0}
                  max={1000}
                  step={10}
                  value={config.latencySimulation}
                  onChange={value => handleConfigChange('latencySimulation', value)}
                  marks={{
                    0: '0ms',
                    200: '200ms',
                    500: '500ms',
                    1000: '1000ms'
                  }}
                />
              </Form.Item>
              
              <Form.Item 
                label={t('networkSync.packetLossSimulation')}
                tooltip={t('networkSync.packetLossSimulationTooltip')}
              >
                <Slider
                  min={0}
                  max={100}
                  step={1}
                  value={config.packetLossSimulation}
                  onChange={value => handleConfigChange('packetLossSimulation', value)}
                  marks={{
                    0: '0%',
                    25: '25%',
                    50: '50%',
                    100: '100%'
                  }}
                />
              </Form.Item>
            </Panel>
          </Collapse>
          
          {networkStatus?.connected && (
            <>
              <Divider />
              
              <Title level={5}>{t('networkSync.connectedUsers')}</Title>
              {renderUserList()}
              
              <Divider />
              
              <Title level={5}>{t('networkSync.syncedEntities')}</Title>
              {renderEntityList()}
            </>
          )}
        </Form>
      </div>
    </div>
  );
};

export default CharacterNetworkSync;
