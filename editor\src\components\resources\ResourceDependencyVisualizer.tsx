/**
 * 资源依赖可视化组件
 * 用于可视化资源之间的依赖关系
 */
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, Row, Col, Select, Button, Space, Empty, Spin, Tabs, Switch, Input, Tag } from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  FullscreenOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  WarningOutlined,
  BranchesOutlined,
  ApartmentOutlined
} from '@ant-design/icons';
import ReactFlow, {
  Background,
  Controls,
  MiniMap,
  NodeTypes,
  useNodesState,
  useEdgesState,
  Panel
} from 'reactflow';
import 'reactflow/dist/style.css';
import { useTranslation } from 'react-i18next';
import type { DependencyType } from '../../libs/dl-engine-types';

// 本地枚举常量，对应 DependencyType
const DependencyTypeValues = {
  STRONG: 'strong' as const,
  WEAK: 'weak' as const
} as const;
import { ResourceDependencyAnalyzer } from './ResourceDependencyAnalyzer';
import './ResourceDependencyVisualizer.less';

const { TabPane } = Tabs;
const { Option } = Select;

// 自定义节点类型
const ResourceNode = ({ data }: { data: any }) => {
  const nodeStyle = {
    background: data.type === 'texture' ? '#91d5ff' : 
                data.type === 'model' ? '#b7eb8f' : 
                data.type === 'material' ? '#ffd666' : 
                data.type === 'audio' ? '#adc6ff' : 
                data.type === 'shader' ? '#ffadd2' : '#d9d9d9',
    border: data.selected ? '2px solid #1890ff' : '1px solid #d9d9d9',
    borderRadius: '4px',
    padding: '10px',
    width: '150px'};

  return (
    <div style={nodeStyle}>
      <div style={{ fontWeight: 'bold', marginBottom: '5px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
        {data.label}
      </div>
      <div style={{ fontSize: '12px', color: '#666' }}>
        {data.type}
      </div>
      {data.size && (
        <div style={{ fontSize: '12px', color: '#666' }}>
          {formatBytes(data.size)}
        </div>
      )}
      {data.warning && (
        <div style={{ marginTop: '5px' }}>
          <Tag color="warning" icon={<WarningOutlined />}>
            {data.warning}
          </Tag>
        </div>
      )}
    </div>
  );
};

// 格式化字节大小
const formatBytes = (bytes: number, decimals = 2) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

// 自定义节点类型映射
const nodeTypes: NodeTypes = {
  resourceNode: ResourceNode};

interface ResourceDependencyVisualizerProps {
  /** 资源ID */
  resourceId?: string;
  /** 资源类型 */
  resourceType?: string;
  /** 是否显示所有资源 */
  showAllResources?: boolean;
  /** 是否自动刷新 */
  autoRefresh?: boolean;
  /** 刷新间隔（毫秒） */
  refreshInterval?: number;
  /** 是否显示详细信息 */
  showDetails?: boolean;
  /** 是否显示警告 */
  showWarnings?: boolean;
  /** 是否显示优化建议 */
  showOptimizationSuggestions?: boolean;
  /** 类名 */
  className?: string;
}

/**
 * 资源依赖可视化组件
 */
const ResourceDependencyVisualizer: React.FC<ResourceDependencyVisualizerProps> = ({
  resourceId,
  resourceType,
  showAllResources = false,
  autoRefresh = false,
  refreshInterval = 5000,
  showDetails = true,
  showWarnings = true,
  showOptimizationSuggestions = true,
  className
}) => {
  const { t } = useTranslation();
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedResource, setSelectedResource] = useState<string | null>(resourceId || null);
  const [searchValue, setSearchValue] = useState<string>('');
  const [filterType, setFilterType] = useState<string | null>(null);
  const [showAllResourcesState, setShowAllResourcesState] = useState<boolean>(showAllResources);
  const [dependencyTypes] = useState<DependencyType[]>([
    DependencyTypeValues.STRONG,
    DependencyTypeValues.WEAK
  ] as DependencyType[]);
  const [layoutType] = useState<'dagre' | 'force' | 'radial'>('dagre');
  const [showMiniMap] = useState<boolean>(true);
  const [showControls] = useState<boolean>(true);
  const [showGrid] = useState<boolean>(true);
  const [analysisResults, setAnalysisResults] = useState<any>(null);
  const [optimizationSuggestions, setOptimizationSuggestions] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState<string>('graph');

  // 使用 optimizationSuggestions 避免未使用警告
  console.debug('Optimization suggestions count:', optimizationSuggestions.length);
  const [refreshTimerId, setRefreshTimerId] = useState<number | null>(null);

  // 使用传入的props
  console.debug('ResourceDependencyVisualizer props:', {
    showDetails,
    showWarnings,
    showOptimizationSuggestions,
    resourceType
  });

  // 依赖分析器
  const analyzer = useMemo(() => new ResourceDependencyAnalyzer(), []);

  // 加载依赖数据
  const loadDependencyData = useCallback(async () => {
    if (!selectedResource && !showAllResourcesState) {
      setNodes([]);
      setEdges([]);
      setAnalysisResults(null);
      setOptimizationSuggestions([]);
      return;
    }

    setLoading(true);

    try {
      // 获取依赖数据
      const { nodes, edges, analysis, suggestions } = await analyzer.analyzeDependencies(
        selectedResource,
        {
          includeTypes: dependencyTypes,
          showAllResources: showAllResourcesState,
          filterType: filterType || undefined,
          searchValue: searchValue || undefined,
          layoutType
        }
      );

      setNodes(nodes);
      setEdges(edges);
      setAnalysisResults(analysis);
      setOptimizationSuggestions(suggestions);
    } catch (error) {
      console.error('加载依赖数据失败:', error);
    } finally {
      setLoading(false);
    }
  }, [
    selectedResource,
    showAllResourcesState,
    dependencyTypes,
    filterType,
    searchValue,
    layoutType,
    analyzer
  ]);

  // 初始加载
  useEffect(() => {
    loadDependencyData();
  }, [loadDependencyData]);

  // 自动刷新
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const timerId = window.setInterval(() => {
        loadDependencyData();
      }, refreshInterval);

      setRefreshTimerId(timerId);

      return () => {
        if (timerId) {
          clearInterval(timerId);
        }
      };
    } else if (refreshTimerId) {
      clearInterval(refreshTimerId);
      setRefreshTimerId(null);
    }
  }, [autoRefresh, refreshInterval, loadDependencyData]);

  // 渲染依赖图
  const renderDependencyGraph = () => {
    if (loading) {
      return (
        <div className="loading-container">
          <Spin size="large" />
          <div className="loading-text">{t('resources.dependency.loading') as string}</div>
        </div>
      );
    }

    if ((!selectedResource && !showAllResourcesState) || (nodes.length === 0 && edges.length === 0)) {
      return (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={t('resources.dependency.noData') as string}
        />
      );
    }

    return (
      <div className="dependency-graph">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          nodeTypes={nodeTypes}
          fitView
          attributionPosition="bottom-right"
        >
          {showGrid && <Background />}
          {showControls && <Controls />}
          {showMiniMap && <MiniMap />}
          <Panel position="top-right">
            <div className="graph-controls">
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadDependencyData}
                  title={t('resources.dependency.refresh') as string}
                />
                <Button
                  icon={<ZoomInOutlined />}
                  title={t('resources.dependency.zoomIn') as string}
                />
                <Button
                  icon={<ZoomOutOutlined />}
                  title={t('resources.dependency.zoomOut') as string}
                />
                <Button
                  icon={<FullscreenOutlined />}
                  title={t('resources.dependency.fitView') as string}
                />
              </Space>
            </div>
          </Panel>
        </ReactFlow>
      </div>
    );
  };

  // 渲染分析结果
  const renderAnalysisResults = () => {
    if (!analysisResults) {
      return (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={t('resources.dependency.noAnalysisData') as string}
        />
      );
    }

    return (
      <div className="analysis-results">
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Card title={t('resources.dependency.summary') as string}>
              <div className="analysis-item">
                <div className="analysis-label">{t('resources.dependency.totalResources') as string}</div>
                <div className="analysis-value">{analysisResults.totalResources}</div>
              </div>
              <div className="analysis-item">
                <div className="analysis-label">{t('resources.dependency.totalDependencies') as string}</div>
                <div className="analysis-value">{analysisResults.totalDependencies}</div>
              </div>
              <div className="analysis-item">
                <div className="analysis-label">{t('resources.dependency.circularDependencies') as string}</div>
                <div className="analysis-value">{analysisResults.circularDependencies.length}</div>
              </div>
              <div className="analysis-item">
                <div className="analysis-label">{t('resources.dependency.redundantDependencies') as string}</div>
                <div className="analysis-value">{analysisResults.redundantDependencies.length}</div>
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  return (
    <div className={`resource-dependency-visualizer ${className || ''}`}>
      <div className="visualizer-header">
        <Space>
          <Select
            placeholder={t('resources.dependency.selectResource') as string}
            value={selectedResource}
            onChange={setSelectedResource}
            style={{ width: 300 }}
            showSearch
            allowClear
            disabled={showAllResourcesState}
          >
            {/* 资源选项将在实际实现中动态加载 */}
          </Select>

          <Switch
            checked={showAllResourcesState}
            onChange={(checked) => {
              setShowAllResourcesState(checked);
              if (checked) {
                setSelectedResource(null);
              }
            }}
            checkedChildren={t('resources.dependency.showAll') as string}
            unCheckedChildren={t('resources.dependency.showSelected') as string}
          />

          <Input
            placeholder={t('resources.dependency.search') as string}
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            prefix={<SearchOutlined />}
            style={{ width: 200 }}
            allowClear
          />

          <Select
            placeholder={t('resources.dependency.filterByType') as string}
            value={filterType}
            onChange={setFilterType}
            style={{ width: 150 }}
            allowClear
          >
            <Option value="texture">{t('resources.types.texture') as string}</Option>
            <Option value="model">{t('resources.types.model') as string}</Option>
            <Option value="material">{t('resources.types.material') as string}</Option>
            <Option value="audio">{t('resources.types.audio') as string}</Option>
            <Option value="shader">{t('resources.types.shader') as string}</Option>
            <Option value="other">{t('resources.types.other') as string}</Option>
          </Select>

          <Button
            icon={<ReloadOutlined />}
            onClick={loadDependencyData}
            loading={loading}
          >
            {t('resources.dependency.refresh') as string}
          </Button>
        </Space>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={<span><ApartmentOutlined />{t('resources.dependency.graph') as string}</span>}
          key="graph"
        >
          {renderDependencyGraph()}
        </TabPane>
        <TabPane
          tab={<span><BranchesOutlined />{t('resources.dependency.analysis') as string}</span>}
          key="analysis"
        >
          {renderAnalysisResults()}
        </TabPane>
      </Tabs>
    </div>
  );
};

export default ResourceDependencyVisualizer;
