/**
 * 后处理特效系统服务
 * 提供屏幕空间特效、色彩校正、景深、运动模糊、辉光等后处理效果
 */
import { EventEmitter } from '../utils/EventEmitter';

// 后处理效果类型枚举
export enum PostProcessingEffectType {
  BLOOM = 'bloom',
  DEPTH_OF_FIELD = 'depth_of_field',
  MOTION_BLUR = 'motion_blur',
  COLOR_GRADING = 'color_grading',
  TONE_MAPPING = 'tone_mapping',
  VIGNETTE = 'vignette',
  CHROMATIC_ABERRATION = 'chromatic_aberration',
  FILM_GRAIN = 'film_grain',
  SCREEN_SPACE_REFLECTION = 'screen_space_reflection',
  AMBIENT_OCCLUSION = 'ambient_occlusion',
  ANTI_ALIASING = 'anti_aliasing',
  SHARPEN = 'sharpen',
  BLUR = 'blur',
  DISTORTION = 'distortion',
  GLITCH = 'glitch'
}

// 后处理效果参数接口
export interface PostProcessingEffectParams {
  // 通用参数
  enabled: boolean;
  intensity: number;
  
  // 辉光效果参数
  bloomThreshold?: number;
  bloomRadius?: number;
  bloomIntensity?: number;
  
  // 景深效果参数
  focusDistance?: number;
  focalLength?: number;
  aperture?: number;
  bokehScale?: number;
  
  // 运动模糊参数
  motionBlurSamples?: number;
  motionBlurIntensity?: number;
  velocityScale?: number;
  
  // 色彩分级参数
  exposure?: number;
  brightness?: number;
  contrast?: number;
  saturation?: number;
  hue?: number;
  gamma?: number;
  shadows?: [number, number, number];
  midtones?: [number, number, number];
  highlights?: [number, number, number];
  
  // 色调映射参数
  toneMappingType?: 'linear' | 'reinhard' | 'cineon' | 'aces';
  whitePoint?: number;
  
  // 暗角效果参数
  vignetteOffset?: number;
  vignetteDarkness?: number;
  
  // 色差参数
  chromaticAberrationOffset?: number;
  
  // 胶片颗粒参数
  grainIntensity?: number;
  grainSize?: number;
  
  // 环境光遮蔽参数
  aoRadius?: number;
  aoIntensity?: number;
  aoBias?: number;
  
  // 抗锯齿参数
  aaType?: 'fxaa' | 'smaa' | 'taa';
  aaQuality?: 'low' | 'medium' | 'high' | 'ultra';
  
  // 锐化参数
  sharpenAmount?: number;
  
  // 模糊参数
  blurRadius?: number;
  blurSigma?: number;
  
  // 扭曲参数
  distortionStrength?: number;
  distortionCenter?: [number, number];
  
  // 故障效果参数
  glitchIntensity?: number;
  glitchSpeed?: number;
  
  // 自定义参数
  customParams?: Record<string, any>;
}

// 后处理效果定义接口
export interface PostProcessingEffect {
  id: string;
  type: PostProcessingEffectType;
  name: string;
  description: string;
  enabled: boolean;
  order: number;
  params: PostProcessingEffectParams;
  shaderCode?: {
    vertex?: string;
    fragment?: string;
  };
  uniforms?: Record<string, any>;
  textures?: string[];
  renderTargets?: string[];
  metadata: EffectMetadata;
}

// 效果元数据接口
export interface EffectMetadata {
  category: string;
  tags: string[];
  performance: number; // 0-100
  quality: number; // 0-100
  compatibility: string[];
  author: string;
  version: string;
  thumbnail?: string;
}

// 后处理链接口
export interface PostProcessingChain {
  id: string;
  name: string;
  description: string;
  effects: PostProcessingEffect[];
  enabled: boolean;
  renderOrder: number[];
  globalParams: {
    renderScale: number;
    temporalUpsampling: boolean;
    debugMode: boolean;
  };
  metadata: {
    author: string;
    tags: string[];
    category: string;
    thumbnail?: string;
  };
}

// 后处理预设接口
export interface PostProcessingPreset {
  id: string;
  name: string;
  description: string;
  category: string;
  thumbnail: string;
  effects: Partial<PostProcessingEffect>[];
  tags: string[];
  rating: number;
  downloads: number;
}

// 渲染目标接口
export interface RenderTarget {
  id: string;
  name: string;
  width: number;
  height: number;
  format: 'RGBA8' | 'RGBA16F' | 'RGBA32F' | 'RGB8' | 'RGB16F' | 'RGB32F' | 'R8' | 'R16F' | 'R32F';
  minFilter: 'NEAREST' | 'LINEAR';
  magFilter: 'NEAREST' | 'LINEAR';
  wrapS: 'REPEAT' | 'CLAMP_TO_EDGE' | 'MIRRORED_REPEAT';
  wrapT: 'REPEAT' | 'CLAMP_TO_EDGE' | 'MIRRORED_REPEAT';
  generateMipmaps: boolean;
  samples?: number; // for MSAA
}

/**
 * 后处理特效系统服务类
 */
export class PostProcessingService extends EventEmitter {
  private static instance: PostProcessingService;
  private effects: Map<string, PostProcessingEffect> = new Map();
  private chains: Map<string, PostProcessingChain> = new Map();
  private presets: Map<string, PostProcessingPreset> = new Map();
  private renderTargets: Map<string, RenderTarget> = new Map();
  private activeChain: PostProcessingChain | null = null;
  private isProcessing: boolean = false;

  private constructor() {
    super();
    this.initializeDefaultEffects();
    this.initializeDefaultPresets();
    this.initializeRenderTargets();
  }

  public static getInstance(): PostProcessingService {
    if (!PostProcessingService.instance) {
      PostProcessingService.instance = new PostProcessingService();
    }
    return PostProcessingService.instance;
  }

  /**
   * 初始化默认效果
   */
  private initializeDefaultEffects(): void {
    // 辉光效果
    const bloomEffect = this.createEffect({
      type: PostProcessingEffectType.BLOOM,
      name: 'Bloom',
      description: 'Adds a glowing effect to bright areas',
      params: {
        enabled: true,
        intensity: 1.0,
        bloomThreshold: 1.0,
        bloomRadius: 1.0,
        bloomIntensity: 1.0
      }
    });

    // 景深效果
    const dofEffect = this.createEffect({
      type: PostProcessingEffectType.DEPTH_OF_FIELD,
      name: 'Depth of Field',
      description: 'Simulates camera focus with blur effects',
      params: {
        enabled: false,
        intensity: 1.0,
        focusDistance: 10.0,
        focalLength: 50.0,
        aperture: 2.8,
        bokehScale: 1.0
      }
    });

    // 色彩分级效果
    const colorGradingEffect = this.createEffect({
      type: PostProcessingEffectType.COLOR_GRADING,
      name: 'Color Grading',
      description: 'Adjusts color balance and tone',
      params: {
        enabled: true,
        intensity: 1.0,
        exposure: 0.0,
        brightness: 0.0,
        contrast: 1.0,
        saturation: 1.0,
        hue: 0.0,
        gamma: 1.0,
        shadows: [1, 1, 1],
        midtones: [1, 1, 1],
        highlights: [1, 1, 1]
      }
    });

    // 抗锯齿效果
    const aaEffect = this.createEffect({
      type: PostProcessingEffectType.ANTI_ALIASING,
      name: 'Anti-Aliasing',
      description: 'Reduces jagged edges and aliasing',
      params: {
        enabled: true,
        intensity: 1.0,
        aaType: 'fxaa',
        aaQuality: 'high'
      }
    });

    this.effects.set(bloomEffect.id, bloomEffect);
    this.effects.set(dofEffect.id, dofEffect);
    this.effects.set(colorGradingEffect.id, colorGradingEffect);
    this.effects.set(aaEffect.id, aaEffect);
  }

  /**
   * 初始化默认预设
   */
  private initializeDefaultPresets(): void {
    const presets: PostProcessingPreset[] = [
      {
        id: 'preset_cinematic',
        name: 'Cinematic',
        description: 'Film-like color grading with subtle bloom',
        category: 'cinematic',
        thumbnail: '/presets/cinematic.jpg',
        effects: [
          {
            type: PostProcessingEffectType.COLOR_GRADING,
            params: {
              enabled: true,
              intensity: 1.0,
              contrast: 1.1,
              saturation: 0.9,
              shadows: [0.9, 0.95, 1.0],
              highlights: [1.0, 0.95, 0.9]
            }
          },
          {
            type: PostProcessingEffectType.BLOOM,
            params: {
              enabled: true,
              intensity: 0.3,
              bloomThreshold: 1.2
            }
          },
          {
            type: PostProcessingEffectType.VIGNETTE,
            params: {
              enabled: true,
              intensity: 0.2,
              vignetteOffset: 0.3,
              vignetteDarkness: 0.5
            }
          }
        ],
        tags: ['cinematic', 'film', 'moody'],
        rating: 4.8,
        downloads: 1250
      },
      {
        id: 'preset_vibrant',
        name: 'Vibrant',
        description: 'High contrast and saturated colors',
        category: 'stylized',
        thumbnail: '/presets/vibrant.jpg',
        effects: [
          {
            type: PostProcessingEffectType.COLOR_GRADING,
            params: {
              enabled: true,
              intensity: 1.0,
              contrast: 1.3,
              saturation: 1.4,
              brightness: 0.1
            }
          },
          {
            type: PostProcessingEffectType.BLOOM,
            params: {
              enabled: true,
              intensity: 0.8,
              bloomThreshold: 0.8
            }
          }
        ],
        tags: ['vibrant', 'colorful', 'high-contrast'],
        rating: 4.5,
        downloads: 980
      },
      {
        id: 'preset_retro',
        name: 'Retro',
        description: 'Vintage look with film grain and color shift',
        category: 'vintage',
        thumbnail: '/presets/retro.jpg',
        effects: [
          {
            type: PostProcessingEffectType.COLOR_GRADING,
            params: {
              enabled: true,
              intensity: 1.0,
              saturation: 0.8,
              hue: 0.05,
              shadows: [1.1, 0.9, 0.8],
              highlights: [0.9, 1.0, 1.1]
            }
          },
          {
            type: PostProcessingEffectType.FILM_GRAIN,
            params: {
              enabled: true,
              intensity: 0.3,
              grainIntensity: 0.5,
              grainSize: 1.0
            }
          },
          {
            type: PostProcessingEffectType.VIGNETTE,
            params: {
              enabled: true,
              intensity: 0.4,
              vignetteOffset: 0.2,
              vignetteDarkness: 0.7
            }
          }
        ],
        tags: ['retro', 'vintage', 'film', 'grain'],
        rating: 4.3,
        downloads: 750
      }
    ];

    presets.forEach(preset => {
      this.presets.set(preset.id, preset);
    });
  }

  /**
   * 初始化渲染目标
   */
  private initializeRenderTargets(): void {
    // 主渲染目标
    const mainTarget: RenderTarget = {
      id: 'main',
      name: 'Main Render Target',
      width: 1920,
      height: 1080,
      format: 'RGBA16F',
      minFilter: 'LINEAR',
      magFilter: 'LINEAR',
      wrapS: 'CLAMP_TO_EDGE',
      wrapT: 'CLAMP_TO_EDGE',
      generateMipmaps: false
    };

    // 深度缓冲
    const depthTarget: RenderTarget = {
      id: 'depth',
      name: 'Depth Buffer',
      width: 1920,
      height: 1080,
      format: 'R32F',
      minFilter: 'NEAREST',
      magFilter: 'NEAREST',
      wrapS: 'CLAMP_TO_EDGE',
      wrapT: 'CLAMP_TO_EDGE',
      generateMipmaps: false
    };

    // 临时缓冲
    const tempTarget: RenderTarget = {
      id: 'temp',
      name: 'Temporary Buffer',
      width: 1920,
      height: 1080,
      format: 'RGBA16F',
      minFilter: 'LINEAR',
      magFilter: 'LINEAR',
      wrapS: 'CLAMP_TO_EDGE',
      wrapT: 'CLAMP_TO_EDGE',
      generateMipmaps: false
    };

    this.renderTargets.set(mainTarget.id, mainTarget);
    this.renderTargets.set(depthTarget.id, depthTarget);
    this.renderTargets.set(tempTarget.id, tempTarget);
  }

  /**
   * 创建后处理效果
   */
  public createEffect(config: {
    type: PostProcessingEffectType;
    name: string;
    description: string;
    params: PostProcessingEffectParams;
    shaderCode?: { vertex?: string; fragment?: string };
  }): PostProcessingEffect {
    const effectId = `effect_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const effect: PostProcessingEffect = {
      id: effectId,
      type: config.type,
      name: config.name,
      description: config.description,
      enabled: config.params.enabled,
      order: this.effects.size,
      params: config.params,
      shaderCode: config.shaderCode || this.getDefaultShaderCode(config.type),
      uniforms: this.generateUniforms(config.params),
      textures: this.getRequiredTextures(config.type),
      renderTargets: this.getRequiredRenderTargets(config.type),
      metadata: {
        category: this.getCategoryForType(config.type),
        tags: this.getTagsForType(config.type),
        performance: this.calculatePerformance(config.type, config.params),
        quality: this.calculateQuality(config.type, config.params),
        compatibility: ['webgl2', 'webgpu'],
        author: 'System',
        version: '1.0.0'
      }
    };

    this.effects.set(effectId, effect);
    this.emit('effectCreated', effect);
    
    return effect;
  }

  /**
   * 更新效果参数
   */
  public updateEffect(effectId: string, params: Partial<PostProcessingEffectParams>): PostProcessingEffect {
    const effect = this.effects.get(effectId);
    if (!effect) {
      throw new Error('Effect not found');
    }

    effect.params = { ...effect.params, ...params };
    effect.uniforms = this.generateUniforms(effect.params);
    effect.metadata.performance = this.calculatePerformance(effect.type, effect.params);
    effect.metadata.quality = this.calculateQuality(effect.type, effect.params);

    this.emit('effectUpdated', effect);
    return effect;
  }

  /**
   * 创建后处理链
   */
  public createChain(name: string, description: string): PostProcessingChain {
    const chainId = `chain_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const chain: PostProcessingChain = {
      id: chainId,
      name,
      description,
      effects: [],
      enabled: true,
      renderOrder: [],
      globalParams: {
        renderScale: 1.0,
        temporalUpsampling: false,
        debugMode: false
      },
      metadata: {
        author: 'User',
        tags: [],
        category: 'custom'
      }
    };

    this.chains.set(chainId, chain);
    this.emit('chainCreated', chain);
    
    return chain;
  }

  /**
   * 添加效果到链
   */
  public addEffectToChain(chainId: string, effectId: string): void {
    const chain = this.chains.get(chainId);
    const effect = this.effects.get(effectId);
    
    if (!chain || !effect) {
      throw new Error('Chain or effect not found');
    }

    // 克隆效果以避免修改原始效果
    const clonedEffect = { ...effect, id: `${effect.id}_${Date.now()}` };
    chain.effects.push(clonedEffect);
    chain.renderOrder.push(chain.effects.length - 1);

    this.emit('effectAddedToChain', { chain, effect: clonedEffect });
  }

  /**
   * 从链中移除效果
   */
  public removeEffectFromChain(chainId: string, effectIndex: number): void {
    const chain = this.chains.get(chainId);
    if (!chain || effectIndex < 0 || effectIndex >= chain.effects.length) {
      throw new Error('Chain or effect index not found');
    }

    const removedEffect = chain.effects.splice(effectIndex, 1)[0];
    chain.renderOrder = chain.renderOrder
      .filter(index => index !== effectIndex)
      .map(index => index > effectIndex ? index - 1 : index);

    this.emit('effectRemovedFromChain', { chain, effect: removedEffect });
  }

  /**
   * 重新排序链中的效果
   */
  public reorderEffectsInChain(chainId: string, newOrder: number[]): void {
    const chain = this.chains.get(chainId);
    if (!chain) {
      throw new Error('Chain not found');
    }

    if (newOrder.length !== chain.effects.length) {
      throw new Error('Invalid order array length');
    }

    const reorderedEffects = newOrder.map(index => chain.effects[index]);
    chain.effects = reorderedEffects;
    chain.renderOrder = newOrder.map((_, index) => index);

    this.emit('chainReordered', chain);
  }

  /**
   * 应用预设到链
   */
  public applyPresetToChain(chainId: string, presetId: string): void {
    const chain = this.chains.get(chainId);
    const preset = this.presets.get(presetId);

    if (!chain || !preset) {
      throw new Error('Chain or preset not found');
    }

    // 清空现有效果
    chain.effects = [];
    chain.renderOrder = [];

    // 应用预设效果
    preset.effects.forEach((presetEffect, index) => {
      if (presetEffect.type) {
        const effect = this.createEffect({
          type: presetEffect.type,
          name: presetEffect.name || this.getDefaultNameForType(presetEffect.type),
          description: presetEffect.description || '',
          params: { ...this.getDefaultParamsForType(presetEffect.type), ...presetEffect.params },
          shaderCode: presetEffect.shaderCode
        });

        chain.effects.push(effect);
        chain.renderOrder.push(index);
      }
    });

    this.emit('presetAppliedToChain', { chain, preset });
  }

  /**
   * 设置活动链
   */
  public setActiveChain(chainId: string): void {
    const chain = this.chains.get(chainId);
    if (!chain) {
      throw new Error('Chain not found');
    }

    this.activeChain = chain;
    this.emit('activeChainChanged', chain);
  }

  /**
   * 处理后处理效果
   */
  public async processEffects(inputTexture: any, outputTexture: any): Promise<void> {
    if (!this.activeChain || !this.activeChain.enabled || this.isProcessing) {
      return;
    }

    this.isProcessing = true;
    this.emit('processingStarted');

    try {
      let currentInput = inputTexture;
      let currentOutput = outputTexture;

      // 按顺序处理每个效果
      for (const effectIndex of this.activeChain.renderOrder) {
        const effect = this.activeChain.effects[effectIndex];

        if (effect && effect.enabled) {
          await this.processEffect(effect, currentInput, currentOutput);

          // 交换输入输出缓冲区（ping-pong）
          [currentInput, currentOutput] = [currentOutput, currentInput];
        }
      }

      this.emit('processingCompleted');
    } catch (error) {
      this.emit('processingError', error);
      throw error;
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 处理单个效果
   */
  private async processEffect(effect: PostProcessingEffect, _input: any, _output: any): Promise<void> {
    // 模拟效果处理
    await new Promise(resolve => setTimeout(resolve, 10));

    // 这里应该实际执行着色器渲染
    this.emit('effectProcessed', effect);
  }

  /**
   * 获取默认着色器代码
   */
  private getDefaultShaderCode(type: PostProcessingEffectType): { vertex: string; fragment: string } {
    const vertexShader = `
      attribute vec2 position;
      attribute vec2 uv;
      varying vec2 vUv;

      void main() {
        vUv = uv;
        gl_Position = vec4(position, 0.0, 1.0);
      }
    `;

    let fragmentShader = '';

    switch (type) {
      case PostProcessingEffectType.BLOOM:
        fragmentShader = `
          precision mediump float;
          uniform sampler2D inputTexture;
          uniform float bloomThreshold;
          uniform float bloomIntensity;
          varying vec2 vUv;

          void main() {
            vec4 color = texture2D(inputTexture, vUv);
            float brightness = dot(color.rgb, vec3(0.299, 0.587, 0.114));

            if (brightness > bloomThreshold) {
              gl_FragColor = color * bloomIntensity;
            } else {
              gl_FragColor = vec4(0.0);
            }
          }
        `;
        break;

      case PostProcessingEffectType.COLOR_GRADING:
        fragmentShader = `
          precision mediump float;
          uniform sampler2D inputTexture;
          uniform float exposure;
          uniform float brightness;
          uniform float contrast;
          uniform float saturation;
          uniform float hue;
          uniform float gamma;
          varying vec2 vUv;

          vec3 rgb2hsv(vec3 c) {
            vec4 K = vec4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);
            vec4 p = mix(vec4(c.bg, K.wz), vec4(c.gb, K.xy), step(c.b, c.g));
            vec4 q = mix(vec4(p.xyw, c.r), vec4(c.r, p.yzx), step(p.x, c.r));
            float d = q.x - min(q.w, q.y);
            float e = 1.0e-10;
            return vec3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), d / (q.x + e), q.x);
          }

          vec3 hsv2rgb(vec3 c) {
            vec4 K = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
            vec3 p = abs(fract(c.xxx + K.xyz) * 6.0 - K.www);
            return c.z * mix(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y);
          }

          void main() {
            vec4 color = texture2D(inputTexture, vUv);

            // Exposure
            color.rgb *= pow(2.0, exposure);

            // Brightness
            color.rgb += brightness;

            // Contrast
            color.rgb = (color.rgb - 0.5) * contrast + 0.5;

            // Saturation and Hue
            vec3 hsv = rgb2hsv(color.rgb);
            hsv.x += hue;
            hsv.y *= saturation;
            color.rgb = hsv2rgb(hsv);

            // Gamma
            color.rgb = pow(color.rgb, vec3(1.0 / gamma));

            gl_FragColor = color;
          }
        `;
        break;

      case PostProcessingEffectType.VIGNETTE:
        fragmentShader = `
          precision mediump float;
          uniform sampler2D inputTexture;
          uniform float vignetteOffset;
          uniform float vignetteDarkness;
          varying vec2 vUv;

          void main() {
            vec4 color = texture2D(inputTexture, vUv);
            vec2 uv = vUv - 0.5;
            float vignette = smoothstep(vignetteOffset, vignetteOffset - vignetteDarkness, length(uv));
            color.rgb *= vignette;
            gl_FragColor = color;
          }
        `;
        break;

      default:
        fragmentShader = `
          precision mediump float;
          uniform sampler2D inputTexture;
          varying vec2 vUv;

          void main() {
            gl_FragColor = texture2D(inputTexture, vUv);
          }
        `;
    }

    return { vertex: vertexShader, fragment: fragmentShader };
  }

  /**
   * 生成uniform变量
   */
  private generateUniforms(params: PostProcessingEffectParams): Record<string, any> {
    const uniforms: Record<string, any> = {};

    Object.entries(params).forEach(([key, value]) => {
      if (key !== 'enabled' && key !== 'customParams') {
        uniforms[key] = { value };
      }
    });

    return uniforms;
  }

  /**
   * 获取所需纹理
   */
  private getRequiredTextures(type: PostProcessingEffectType): string[] {
    switch (type) {
      case PostProcessingEffectType.DEPTH_OF_FIELD:
        return ['inputTexture', 'depthTexture'];
      case PostProcessingEffectType.MOTION_BLUR:
        return ['inputTexture', 'velocityTexture'];
      case PostProcessingEffectType.SCREEN_SPACE_REFLECTION:
        return ['inputTexture', 'normalTexture', 'depthTexture'];
      default:
        return ['inputTexture'];
    }
  }

  /**
   * 获取所需渲染目标
   */
  private getRequiredRenderTargets(type: PostProcessingEffectType): string[] {
    switch (type) {
      case PostProcessingEffectType.BLOOM:
        return ['temp', 'bloom'];
      case PostProcessingEffectType.DEPTH_OF_FIELD:
        return ['temp', 'coc'];
      default:
        return ['temp'];
    }
  }

  /**
   * 获取效果类型的分类
   */
  private getCategoryForType(type: PostProcessingEffectType): string {
    switch (type) {
      case PostProcessingEffectType.BLOOM:
      case PostProcessingEffectType.VIGNETTE:
      case PostProcessingEffectType.FILM_GRAIN:
        return 'artistic';
      case PostProcessingEffectType.COLOR_GRADING:
      case PostProcessingEffectType.TONE_MAPPING:
        return 'color';
      case PostProcessingEffectType.DEPTH_OF_FIELD:
      case PostProcessingEffectType.MOTION_BLUR:
        return 'camera';
      case PostProcessingEffectType.ANTI_ALIASING:
      case PostProcessingEffectType.SHARPEN:
        return 'quality';
      default:
        return 'general';
    }
  }

  /**
   * 获取效果类型的标签
   */
  private getTagsForType(type: PostProcessingEffectType): string[] {
    switch (type) {
      case PostProcessingEffectType.BLOOM:
        return ['glow', 'bright', 'artistic'];
      case PostProcessingEffectType.DEPTH_OF_FIELD:
        return ['focus', 'blur', 'camera'];
      case PostProcessingEffectType.COLOR_GRADING:
        return ['color', 'tone', 'cinematic'];
      case PostProcessingEffectType.ANTI_ALIASING:
        return ['quality', 'smooth', 'edges'];
      default:
        return [type.replace('_', '-')];
    }
  }

  /**
   * 计算效果性能影响
   */
  private calculatePerformance(type: PostProcessingEffectType, params: PostProcessingEffectParams): number {
    let performance = 100;

    // 基于效果类型的性能影响
    switch (type) {
      case PostProcessingEffectType.SCREEN_SPACE_REFLECTION:
        performance -= 40;
        break;
      case PostProcessingEffectType.MOTION_BLUR:
        performance -= 30;
        break;
      case PostProcessingEffectType.DEPTH_OF_FIELD:
        performance -= 25;
        break;
      case PostProcessingEffectType.BLOOM:
        performance -= 20;
        break;
      case PostProcessingEffectType.AMBIENT_OCCLUSION:
        performance -= 35;
        break;
      default:
        performance -= 10;
    }

    // 基于参数强度的影响
    if (params.intensity > 0.5) {
      performance -= 10;
    }

    return Math.max(0, performance);
  }

  /**
   * 计算效果质量
   */
  private calculateQuality(type: PostProcessingEffectType, params: PostProcessingEffectParams): number {
    let quality = 70;

    // 基于效果类型的质量提升
    switch (type) {
      case PostProcessingEffectType.ANTI_ALIASING:
        quality += 20;
        break;
      case PostProcessingEffectType.COLOR_GRADING:
        quality += 15;
        break;
      case PostProcessingEffectType.BLOOM:
        quality += 10;
        break;
      case PostProcessingEffectType.DEPTH_OF_FIELD:
        quality += 15;
        break;
      default:
        quality += 5;
    }

    // 基于参数设置的质量影响
    if (params.intensity > 0 && params.intensity <= 1) {
      quality += 10;
    }

    return Math.min(100, quality);
  }

  /**
   * 获取效果类型的默认名称
   */
  private getDefaultNameForType(type: PostProcessingEffectType): string {
    return type.split('_').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');
  }

  /**
   * 获取效果类型的默认参数
   */
  private getDefaultParamsForType(type: PostProcessingEffectType): PostProcessingEffectParams {
    const baseParams: PostProcessingEffectParams = {
      enabled: true,
      intensity: 1.0
    };

    switch (type) {
      case PostProcessingEffectType.BLOOM:
        return {
          ...baseParams,
          bloomThreshold: 1.0,
          bloomRadius: 1.0,
          bloomIntensity: 1.0
        };
      case PostProcessingEffectType.COLOR_GRADING:
        return {
          ...baseParams,
          exposure: 0.0,
          brightness: 0.0,
          contrast: 1.0,
          saturation: 1.0,
          hue: 0.0,
          gamma: 1.0,
          shadows: [1, 1, 1],
          midtones: [1, 1, 1],
          highlights: [1, 1, 1]
        };
      case PostProcessingEffectType.VIGNETTE:
        return {
          ...baseParams,
          vignetteOffset: 0.3,
          vignetteDarkness: 0.5
        };
      default:
        return baseParams;
    }
  }

  /**
   * 获取所有效果
   */
  public getAllEffects(): PostProcessingEffect[] {
    return Array.from(this.effects.values());
  }

  /**
   * 获取所有链
   */
  public getAllChains(): PostProcessingChain[] {
    return Array.from(this.chains.values());
  }

  /**
   * 获取所有预设
   */
  public getAllPresets(): PostProcessingPreset[] {
    return Array.from(this.presets.values());
  }

  /**
   * 获取活动链
   */
  public getActiveChain(): PostProcessingChain | null {
    return this.activeChain;
  }

  /**
   * 删除效果
   */
  public deleteEffect(effectId: string): boolean {
    const effect = this.effects.get(effectId);
    if (!effect) {
      return false;
    }

    this.effects.delete(effectId);
    this.emit('effectDeleted', effect);
    return true;
  }

  /**
   * 删除链
   */
  public deleteChain(chainId: string): boolean {
    const chain = this.chains.get(chainId);
    if (!chain) {
      return false;
    }

    if (this.activeChain?.id === chainId) {
      this.activeChain = null;
    }

    this.chains.delete(chainId);
    this.emit('chainDeleted', chain);
    return true;
  }

  /**
   * 导出链配置
   */
  public exportChain(chainId: string): string {
    const chain = this.chains.get(chainId);
    if (!chain) {
      throw new Error('Chain not found');
    }

    return JSON.stringify(chain, null, 2);
  }

  /**
   * 导入链配置
   */
  public importChain(chainData: string): PostProcessingChain {
    try {
      const chain = JSON.parse(chainData) as PostProcessingChain;
      chain.id = `chain_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      this.chains.set(chain.id, chain);
      this.emit('chainImported', chain);

      return chain;
    } catch (error) {
      throw new Error('Invalid chain data');
    }
  }

  /**
   * 获取性能统计
   */
  public getPerformanceStats(): {
    totalEffects: number;
    activeEffects: number;
    averagePerformance: number;
    estimatedFrameTime: number;
  } {
    const activeEffects = this.activeChain?.effects.filter(e => e.enabled) || [];
    const totalPerformance = activeEffects.reduce((sum, effect) => sum + effect.metadata.performance, 0);
    const averagePerformance = activeEffects.length > 0 ? totalPerformance / activeEffects.length : 100;

    // 估算帧时间影响（毫秒）
    const estimatedFrameTime = activeEffects.length * (100 - averagePerformance) * 0.01;

    return {
      totalEffects: this.effects.size,
      activeEffects: activeEffects.length,
      averagePerformance,
      estimatedFrameTime
    };
  }

  /**
   * 检查是否正在处理
   */
  public isProcessingEffects(): boolean {
    return this.isProcessing;
  }
}

export default PostProcessingService;
