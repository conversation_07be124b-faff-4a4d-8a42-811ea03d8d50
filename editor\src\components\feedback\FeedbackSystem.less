.feedback-modal {
  .feedback-type-selection {
    margin-bottom: 24px;

    .type-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 12px;
      margin-top: 12px;

      .type-card {
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        &.selected {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .type-icon {
          font-size: 24px;
          margin-bottom: 8px;
        }

        .type-label {
          font-weight: 600;
          margin-bottom: 4px;
          color: #262626;
        }

        .type-description {
          font-size: 12px;
          color: #8c8c8c;
          line-height: 1.4;
        }
      }
    }
  }

  .ant-form-item {
    margin-bottom: 16px;
  }
}

.feedback-stats {
  .stats-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    margin-bottom: 16px;

    .ant-card {
      text-align: center;

      .stat-item {
        .stat-value {
          font-size: 24px;
          font-weight: 700;
          color: #1890ff;
          line-height: 1;
        }

        .stat-label {
          font-size: 12px;
          color: #8c8c8c;
          margin-top: 4px;
        }
      }
    }
  }

  .stats-breakdown {
    .breakdown-section {
      margin-bottom: 16px;

      .breakdown-item {
        display: flex;
        align-items: center;
        gap: 12px;
        margin: 8px 0;

        span:first-child {
          min-width: 80px;
          font-size: 12px;
          color: #595959;
        }

        .ant-progress {
          flex: 1;
        }

        span:last-child {
          min-width: 30px;
          text-align: right;
          font-size: 12px;
          font-weight: 600;
          color: #262626;
        }
      }
    }
  }
}

.feedback-title {
  display: flex;
  justify-content: space-between;
  align-items: center;

  span {
    font-weight: 600;
    color: #262626;
  }
}

.feedback-meta {
  margin-top: 8px;
}

.ant-list-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  .ant-list-item-meta {
    .ant-list-item-meta-avatar {
      margin-right: 16px;
    }

    .ant-list-item-meta-content {
      .ant-list-item-meta-title {
        margin-bottom: 8px;
      }

      .ant-list-item-meta-description {
        color: #595959;
        line-height: 1.5;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .feedback-modal {
    .feedback-type-selection {
      .type-cards {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;

        .type-card {
          .type-icon {
            font-size: 20px;
            margin-bottom: 6px;
          }

          .type-label {
            font-size: 12px;
          }

          .type-description {
            font-size: 10px;
          }
        }
      }
    }
  }

  .feedback-stats {
    .stats-cards {
      grid-template-columns: 1fr;
      gap: 8px;

      .ant-card {
        .stat-item {
          .stat-value {
            font-size: 20px;
          }
        }
      }
    }

    .stats-breakdown {
      .breakdown-section {
        .breakdown-item {
          flex-direction: column;
          align-items: stretch;
          gap: 4px;

          span:first-child {
            min-width: auto;
          }

          span:last-child {
            text-align: left;
            min-width: auto;
          }
        }
      }
    }
  }

  .feedback-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

// 动画效果
@keyframes feedbackAppear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feedback-modal,
.ant-drawer-content {
  animation: feedbackAppear 0.3s ease;
}

// 浮动按钮样式增强
.ant-float-btn {
  &.ant-float-btn-primary {
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);

    &:hover {
      background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(24, 144, 255, 0.5);
    }
  }
}

// 评分组件样式
.ant-rate {
  .ant-rate-star {
    margin-right: 4px;

    &.ant-rate-star-half,
    &.ant-rate-star-full {
      .ant-rate-star-first,
      .ant-rate-star-second {
        color: #faad14;
      }
    }
  }

  &.ant-rate-small {
    .ant-rate-star {
      margin-right: 2px;
    }
  }
}

// 标签样式增强
.ant-tag {
  border-radius: 12px;
  font-size: 11px;
  line-height: 1.2;
  padding: 2px 8px;
  margin: 0;
  font-weight: 500;

  &.ant-tag-blue {
    background: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
  }

  &.ant-tag-green {
    background: #f6ffed;
    border-color: #b7eb8f;
    color: #52c41a;
  }

  &.ant-tag-orange {
    background: #fff7e6;
    border-color: #ffd591;
    color: #fa8c16;
  }

  &.ant-tag-red {
    background: #fff2f0;
    border-color: #ffccc7;
    color: #ff4d4f;
  }
}

// 进度条样式
.ant-progress {
  .ant-progress-bg {
    border-radius: 4px;
  }

  &.ant-progress-small {
    .ant-progress-outer {
      .ant-progress-inner {
        height: 6px;
        border-radius: 3px;

        .ant-progress-bg {
          border-radius: 3px;
        }
      }
    }
  }
}

// 卡片样式增强
.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  }

  &.ant-card-small {
    .ant-card-body {
      padding: 12px;
    }
  }
}
