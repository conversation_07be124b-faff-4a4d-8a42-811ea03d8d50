/**
 * TransformEditor 集成测试
 * 测试与 ScenePanel、PropertiesPanel 和撤销系统的完整集成
 */
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import TransformEditor from '../TransformEditor';
import ScenePanel from '../../ScenePanel';
import PropertiesPanel from '../../PropertiesPanel';
import editorReducer from '../../../store/editor/editorSlice';
import sceneReducer from '../../../store/scene/sceneSlice';

// Mock EngineService
jest.mock('../../../services/EngineService', () => ({
  getActiveScene: jest.fn(() => ({
    getEntities: jest.fn(() => [])
  })),
  selectEntity: jest.fn(),
  clearSelection: jest.fn()
}));

// Mock lodash debounce
jest.mock('lodash', () => ({
  debounce: jest.fn((fn) => fn)
}));

// 创建测试store
const createTestStore = (initialState: any = {}) => {
  return configureStore({
    reducer: {
      editor: editorReducer,
      scene: sceneReducer
    },
    preloadedState: {
      editor: {
        isLoading: false,
        error: null,
        activeCamera: null,
        selectedObject: null,
        selectedObjects: [],
        transformMode: 'translate' as any,
        transformSpace: 'local' as any,
        snapMode: 'disabled' as any,
        gridSize: 1,
        showGrid: true,
        showAxes: true,
        showStats: false,
        undoStack: [],
        redoStack: [],
        isPlaying: false,
        viewportSize: { width: 0, height: 0 },
        sceneGraph: [],
        ...(initialState.editor || {})
      },
      scene: {
        entities: [],
        selectedEntityId: null,
        isLoading: false,
        error: null,
        ...(initialState.scene || {})
      }
    }
  });
};

// Mock实体和变换组件
const mockTransform = {
  getPosition: jest.fn(() => ({ x: 0, y: 0, z: 0 })),
  getRotation: jest.fn(() => ({ x: 0, y: 0, z: 0 })),
  getScale: jest.fn(() => ({ x: 1, y: 1, z: 1 })),
  getWorldPosition: jest.fn(() => ({ x: 0, y: 0, z: 0 })),
  setPosition: jest.fn(),
  setRotation: jest.fn(),
  setScale: jest.fn(),
  on: jest.fn(),
  off: jest.fn()
};

const mockEntity = {
  id: 'test-entity-1',
  name: 'Test Entity',
  type: 'mesh',
  parentId: null,
  visible: true,
  locked: false,
  transform: {
    position: [0, 0, 0] as [number, number, number],
    rotation: [0, 0, 0] as [number, number, number],
    scale: [1, 1, 1] as [number, number, number]
  },
  components: {},
  getTransform: jest.fn(() => mockTransform)
};

describe('TransformEditor 集成测试', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('与场景面板集成', () => {
    it('应该在场景面板选择实体时更新TransformEditor', async () => {
      const store = createTestStore({
        scene: {
          entities: [mockEntity],
          selectedEntityId: null
        }
      });

      const TestComponent = () => (
        <Provider store={store}>
          <div>
            <ScenePanel />
            <TransformEditor />
          </div>
        </Provider>
      );

      render(<TestComponent />);

      // 初始状态应该显示无选中实体的提示
      expect(screen.getByText('请选择一个实体来编辑变换')).toBeInTheDocument();

      // 模拟在场景面板中选择实体
      // 注意：这里需要根据实际的ScenePanel实现来调整测试方法
      act(() => {
        store.dispatch({ type: 'scene/selectEntity', payload: 'test-entity-1' });
        store.dispatch({ type: 'editor/setSelectedObject', payload: mockEntity });
      });

      // 等待组件更新
      await waitFor(() => {
        expect(screen.getByText('变换')).toBeInTheDocument();
        expect(screen.getByText('位置 (Position)')).toBeInTheDocument();
      });
    });
  });

  describe('与属性面板集成', () => {
    it('应该在属性面板中正确显示TransformEditor', () => {
      const store = createTestStore({
        scene: {
          entities: [mockEntity],
          selectedEntityId: 'test-entity-1'
        },
        editor: {
          selectedObject: mockEntity
        }
      });

      render(
        <Provider store={store}>
          <PropertiesPanel />
        </Provider>
      );

      // 应该显示变换标签页
      expect(screen.getByText('变换')).toBeInTheDocument();
    });
  });

  describe('撤销/重做集成', () => {
    it('应该正确处理变换操作的撤销和重做', async () => {
      const store = createTestStore({
        editor: {
          selectedObject: mockEntity,
          undoStack: [],
          redoStack: []
        }
      });

      const onChangeMock = jest.fn();

      render(
        <Provider store={store}>
          <TransformEditor onChange={onChangeMock} />
        </Provider>
      );

      // 模拟变换操作
      const positionXInput = screen.getAllByRole('spinbutton')[0];
      
      act(() => {
        fireEvent.change(positionXInput, { target: { value: '5' } });
      });

      await waitFor(() => {
        expect(onChangeMock).toHaveBeenCalled();
      });

      // 检查撤销栈是否有操作
      // 注意：这里需要根据实际的撤销系统实现来调整断言
      expect(onChangeMock).toHaveBeenCalled();
    });
  });

  describe('坐标空间切换', () => {
    it('应该正确切换本地和世界坐标空间', async () => {
      const store = createTestStore({
        editor: {
          selectedObject: mockEntity
        }
      });

      render(
        <Provider store={store}>
          <TransformEditor />
        </Provider>
      );

      // 查找坐标空间切换按钮
      const worldButton = screen.getByText('世界');
      
      act(() => {
        fireEvent.click(worldButton);
      });

      // 验证切换到世界坐标
      await waitFor(() => {
        expect(mockTransform.getWorldPosition).toHaveBeenCalled();
      });
    });
  });

  describe('链接缩放功能', () => {
    it('应该正确处理链接缩放', async () => {
      const store = createTestStore({
        editor: {
          selectedObject: mockEntity
        }
      });

      const onChangeMock = jest.fn();

      render(
        <Provider store={store}>
          <TransformEditor onChange={onChangeMock} />
        </Provider>
      );

      // 点击链接按钮
      const linkButton = screen.getByLabelText('链接缩放');
      
      act(() => {
        fireEvent.click(linkButton);
      });

      // 修改X轴缩放
      const scaleXInput = screen.getAllByRole('spinbutton')[6]; // 假设缩放输入框在第7个位置
      
      act(() => {
        fireEvent.change(scaleXInput, { target: { value: '2' } });
      });

      await waitFor(() => {
        expect(onChangeMock).toHaveBeenCalledWith(
          expect.objectContaining({
            scale: { x: 2, y: 2, z: 2 }
          })
        );
      });
    });
  });

  describe('复制粘贴功能', () => {
    it('应该正确处理变换数据的复制和粘贴', async () => {
      const store = createTestStore({
        editor: {
          selectedObject: mockEntity
        }
      });

      const onChangeMock = jest.fn();

      render(
        <Provider store={store}>
          <TransformEditor onChange={onChangeMock} />
        </Provider>
      );

      // 点击复制按钮
      const copyButton = screen.getByLabelText('复制变换');
      
      act(() => {
        fireEvent.click(copyButton);
      });

      // 修改一些值
      const positionXInput = screen.getAllByRole('spinbutton')[0];
      
      act(() => {
        fireEvent.change(positionXInput, { target: { value: '10' } });
      });

      // 点击粘贴按钮
      const pasteButton = screen.getByLabelText('粘贴变换');
      
      act(() => {
        fireEvent.click(pasteButton);
      });

      await waitFor(() => {
        // 应该恢复到复制时的值
        expect(onChangeMock).toHaveBeenCalled();
      });
    });
  });

  describe('性能测试', () => {
    it('应该在大量更新时保持良好性能', async () => {
      const store = createTestStore({
        editor: {
          selectedObject: mockEntity
        }
      });

      const onChangeMock = jest.fn();

      render(
        <Provider store={store}>
          <TransformEditor onChange={onChangeMock} />
        </Provider>
      );

      const startTime = performance.now();

      // 模拟大量快速更新
      const positionXInput = screen.getAllByRole('spinbutton')[0];
      
      for (let i = 0; i < 100; i++) {
        act(() => {
          fireEvent.change(positionXInput, { target: { value: i.toString() } });
        });
      }

      const endTime = performance.now();
      const duration = endTime - startTime;

      // 应该在合理时间内完成（这里设置为1秒）
      expect(duration).toBeLessThan(1000);
    });
  });

  describe('错误处理', () => {
    it('应该正确处理无效的变换数据', async () => {
      const store = createTestStore({
        editor: {
          selectedObject: mockEntity
        }
      });

      render(
        <Provider store={store}>
          <TransformEditor />
        </Provider>
      );

      // 输入无效值
      const positionXInput = screen.getAllByRole('spinbutton')[0];
      
      act(() => {
        fireEvent.change(positionXInput, { target: { value: 'invalid' } });
      });

      // 组件应该仍然正常工作，不应该崩溃
      expect(screen.getByText('变换')).toBeInTheDocument();
    });

    it('应该正确处理引擎连接失败', () => {
      // Mock引擎服务返回null
      const mockEngineService = require('../../../services/EngineService');
      mockEngineService.getActiveScene.mockReturnValue(null);

      const store = createTestStore({
        editor: {
          selectedObject: mockEntity
        }
      });

      render(
        <Provider store={store}>
          <TransformEditor />
        </Provider>
      );

      // 应该显示适当的错误状态或回退UI
      expect(screen.getByText('变换')).toBeInTheDocument();
    });
  });
});
