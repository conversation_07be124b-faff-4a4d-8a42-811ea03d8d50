/**
 * UI预设选择器组件
 */
import React, { useState, useEffect } from 'react';
import { 
  Modal, 
  Card, 
  Row, 
  Col, 
  Input, 
  Select, 
  Button, 
  Space, 
  Tag,
  Empty,
  Tooltip,
  message
} from 'antd';
import { SearchOutlined, EyeOutlined, CheckOutlined } from '@ant-design/icons';
import { UIElementType } from './UIElementEditor';
import UIPresetManager, { UIPreset, UIPresetCategory } from './UIPresetManager';

const { Search } = Input;
const { Option } = Select;

/**
 * UI预设选择器属性
 */
interface UIPresetSelectorProps {
  /** 是否显示 */
  visible: boolean;
  /** 关闭回调 */
  onClose: () => void;
  /** 选择预设回调 */
  onSelect: (preset: UIPreset) => void;
  /** 当前UI元素类型过滤 */
  filterType?: UIElementType;
  /** 标题 */
  title?: string;
}

/**
 * UI预设选择器组件
 */
const UIPresetSelector: React.FC<UIPresetSelectorProps> = ({
  visible,
  onClose,
  onSelect,
  filterType,
  title = '选择UI预设'
}) => {
  const [presets, setPresets] = useState<UIPreset[]>([]);
  const [filteredPresets, setFilteredPresets] = useState<UIPreset[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>(filterType || 'all');
  const [previewPreset, setPreviewPreset] = useState<UIPreset | null>(null);

  useEffect(() => {
    // 加载所有预设
    const allPresets = UIPresetManager.getAllPresets();
    setPresets(allPresets);
    setFilteredPresets(allPresets);
  }, []);

  useEffect(() => {
    // 应用过滤条件
    let filtered = presets;

    // 按类型过滤
    if (selectedType !== 'all') {
      filtered = filtered.filter(preset => preset.type === selectedType);
    }

    // 按类别过滤
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(preset => preset.category === selectedCategory);
    }

    // 按搜索查询过滤
    if (searchQuery) {
      filtered = UIPresetManager.searchPresets(searchQuery).filter((preset: UIPreset) =>
        (selectedType === 'all' || preset.type === selectedType) &&
        (selectedCategory === 'all' || preset.category === selectedCategory)
      );
    }

    setFilteredPresets(filtered);
  }, [presets, selectedType, selectedCategory, searchQuery]);

  // 处理预设选择
  const handleSelectPreset = (preset: UIPreset) => {
    onSelect(preset);
    message.success(`已应用预设: ${preset.name}`);
    onClose();
  };

  // 处理预设预览
  const handlePreviewPreset = (preset: UIPreset) => {
    setPreviewPreset(preset);
  };

  // 获取类别选项
  const getCategoryOptions = () => {
    const categories = Array.from(new Set(presets.map(preset => preset.category)));
    return [
      { label: '全部类别', value: 'all' },
      ...categories.map(category => ({
        label: getCategoryLabel(category),
        value: category
      }))
    ];
  };

  // 获取类型选项
  const getTypeOptions = () => {
    const types = Array.from(new Set(presets.map(preset => preset.type)));
    return [
      { label: '全部类型', value: 'all' },
      ...types.map(type => ({
        label: getTypeLabel(type),
        value: type
      }))
    ];
  };

  // 获取类别标签
  const getCategoryLabel = (category: string): string => {
    const labels: Record<string, string> = {
      [UIPresetCategory.BASIC]: '基础',
      [UIPresetCategory.MODERN]: '现代',
      [UIPresetCategory.CLASSIC]: '经典',
      [UIPresetCategory.GAMING]: '游戏',
      [UIPresetCategory.MOBILE]: '移动端',
      [UIPresetCategory.CUSTOM]: '自定义'
    };
    return labels[category] || category;
  };

  // 获取类型标签
  const getTypeLabel = (type: string): string => {
    const labels: Record<string, string> = {
      [UIElementType.BUTTON]: '按钮',
      [UIElementType.TEXT]: '文本',
      [UIElementType.IMAGE]: '图像',
      [UIElementType.INPUT]: '输入框',
      [UIElementType.PANEL]: '面板',
      [UIElementType.WINDOW]: '窗口',
      [UIElementType.SLIDER]: '滑块',
      [UIElementType.CHECKBOX]: '复选框',
      [UIElementType.DROPDOWN]: '下拉框'
    };
    return labels[type] || type;
  };

  // 获取类别颜色
  const getCategoryColor = (category: string): string => {
    const colors: Record<string, string> = {
      [UIPresetCategory.BASIC]: 'blue',
      [UIPresetCategory.MODERN]: 'green',
      [UIPresetCategory.CLASSIC]: 'orange',
      [UIPresetCategory.GAMING]: 'purple',
      [UIPresetCategory.MOBILE]: 'cyan',
      [UIPresetCategory.CUSTOM]: 'red'
    };
    return colors[category] || 'default';
  };

  // 渲染预设卡片
  const renderPresetCard = (preset: UIPreset) => (
    <Card
      key={preset.id}
      size="small"
      hoverable
      style={{ height: '100%' }}
      cover={
        <div 
          style={{ 
            height: 80, 
            backgroundColor: '#f5f5f5',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: 24,
            color: '#999'
          }}
        >
          {preset.thumbnail ? (
            <img src={preset.thumbnail} alt={preset.name} style={{ maxHeight: '100%' }} />
          ) : (
            getTypeLabel(preset.type)
          )}
        </div>
      }
      actions={[
        <Tooltip title="预览" key="preview">
          <EyeOutlined onClick={() => handlePreviewPreset(preset)} />
        </Tooltip>,
        <Tooltip title="选择" key="select">
          <CheckOutlined onClick={() => handleSelectPreset(preset)} />
        </Tooltip>
      ]}
    >
      <Card.Meta
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span style={{ fontSize: 14 }}>{preset.name}</span>
            <Tag color={getCategoryColor(preset.category)}>
              {getCategoryLabel(preset.category)}
            </Tag>
          </div>
        }
        description={
          <div>
            <div style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>
              {preset.description}
            </div>
            {preset.tags && (
              <div>
                {preset.tags.slice(0, 3).map(tag => (
                  <Tag key={tag} style={{ fontSize: 10 }}>
                    {tag}
                  </Tag>
                ))}
              </div>
            )}
          </div>
        }
      />
    </Card>
  );

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      style={{ top: 20 }}
    >
      <div style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Search
              placeholder="搜索预设..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              prefix={<SearchOutlined />}
            />
          </Col>
          <Col span={8}>
            <Select
              value={selectedCategory}
              onChange={setSelectedCategory}
              style={{ width: '100%' }}
              placeholder="选择类别"
            >
              {getCategoryOptions().map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={8}>
            <Select
              value={selectedType}
              onChange={setSelectedType}
              style={{ width: '100%' }}
              placeholder="选择类型"
              disabled={!!filterType}
            >
              {getTypeOptions().map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Col>
        </Row>
      </div>

      <div style={{ maxHeight: 400, overflowY: 'auto' }}>
        {filteredPresets.length > 0 ? (
          <Row gutter={[16, 16]}>
            {filteredPresets.map(preset => (
              <Col key={preset.id} span={8}>
                {renderPresetCard(preset)}
              </Col>
            ))}
          </Row>
        ) : (
          <Empty 
            description="没有找到匹配的预设"
            style={{ padding: 40 }}
          />
        )}
      </div>

      {/* 预设预览模态框 */}
      <Modal
        title={`预览: ${previewPreset?.name}`}
        open={!!previewPreset}
        onCancel={() => setPreviewPreset(null)}
        footer={[
          <Button key="cancel" onClick={() => setPreviewPreset(null)}>
            取消
          </Button>,
          <Button 
            key="select" 
            type="primary" 
            onClick={() => previewPreset && handleSelectPreset(previewPreset)}
          >
            选择此预设
          </Button>
        ]}
      >
        {previewPreset && (
          <div>
            <p><strong>描述:</strong> {previewPreset.description}</p>
            <p><strong>类型:</strong> {getTypeLabel(previewPreset.type)}</p>
            <p><strong>类别:</strong> {getCategoryLabel(previewPreset.category)}</p>
            {previewPreset.tags && (
              <p>
                <strong>标签:</strong> 
                <Space style={{ marginLeft: 8 }}>
                  {previewPreset.tags.map(tag => (
                    <Tag key={tag}>{tag}</Tag>
                  ))}
                </Space>
              </p>
            )}
            <div style={{ marginTop: 16 }}>
              <strong>预设数据:</strong>
              <pre style={{ 
                backgroundColor: '#f5f5f5', 
                padding: 12, 
                borderRadius: 4,
                fontSize: 12,
                maxHeight: 200,
                overflow: 'auto'
              }}>
                {JSON.stringify(previewPreset.data, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </Modal>
    </Modal>
  );
};

export default UIPresetSelector;
