/**
 * AI代码生成器面板样式
 */

.ai-code-generator-panel {
  .ant-modal-body {
    padding: 16px;
    max-height: 80vh;
    overflow-y: auto;
  }

  .code-generator-content {
    .ant-steps {
      margin-bottom: 24px;

      .ant-steps-item {
        .ant-steps-item-title {
          font-size: 14px;
        }
      }
    }

    .step-content {
      min-height: 500px;

      .step-card {
        .ant-card-head {
          .ant-card-head-title {
            font-size: 16px;
            font-weight: 600;
          }
        }

        .ant-card-body {
          padding: 24px;
        }
      }
    }
  }

  // 配置表单
  .config-form {
    .ant-form-item {
      margin-bottom: 16px;

      .ant-form-item-label {
        font-weight: 500;
      }
    }

    .ant-select,
    .ant-input {
      border-radius: 6px;
    }

    .ant-checkbox-wrapper {
      margin-bottom: 8px;
    }
  }

  // 属性配置
  .props-config {
    .prop-row {
      margin-bottom: 12px;
      padding: 12px;
      background: #f9f9f9;
      border-radius: 6px;
      border: 1px solid #f0f0f0;

      &:hover {
        border-color: #d9d9d9;
      }

      .ant-input,
      .ant-select {
        border-radius: 4px;
      }

      .ant-btn {
        border: none;
        box-shadow: none;
      }
    }

    .add-prop-btn {
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      height: 40px;
      color: #666666;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }
    }
  }

  // 生成按钮
  .generate-button {
    height: 48px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    border: none;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);

    &:hover {
      background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
      box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
    }

    &:disabled {
      background: #f5f5f5;
      box-shadow: none;
    }
  }

  // 结果列表
  .results-list {
    .ant-list-item {
      padding: 16px;
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      margin-bottom: 12px;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
      }

      .ant-list-item-meta {
        .ant-list-item-meta-avatar {
          margin-right: 16px;
        }

        .ant-list-item-meta-title {
          margin-bottom: 8px;
          font-size: 16px;
        }

        .ant-list-item-meta-description {
          .ant-progress {
            margin-left: 8px;
          }

          .ant-rate {
            margin-left: 8px;
          }
        }
      }

      .ant-list-item-action {
        margin-left: 16px;

        .ant-btn {
          margin-left: 4px;
          border: none;
          box-shadow: none;

          &:hover {
            background-color: #f0f0f0;
          }
        }
      }
    }
  }

  // 代码预览
  .code-preview {
    .ant-tabs {
      .ant-tabs-tab {
        font-size: 12px;
        padding: 8px 12px;
      }

      .ant-tabs-content {
        .ant-tabs-tabpane {
          position: relative;

          pre {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            line-height: 1.5;
            white-space: pre-wrap;
            word-wrap: break-word;
          }

          .copy-button {
            position: absolute;
            top: 8px;
            right: 8px;
            z-index: 10;
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid #d9d9d9;

            &:hover {
              background: #ffffff;
            }
          }
        }
      }
    }
  }

  // 空状态
  .empty-state {
    text-align: center;
    padding: 40px 0;

    .empty-icon {
      font-size: 48px;
      color: #d9d9d9;
      margin-bottom: 16px;
    }

    .ant-typography {
      margin-bottom: 8px;
    }
  }

  // 加载状态
  .generating-alert {
    .ant-alert-icon {
      animation: spin 2s linear infinite;
    }
  }
}

// 深色主题
.dark-theme {
  .ai-code-generator-panel {
    .ant-card {
      background: #2d2d2d;
      border-color: #404040;

      .ant-card-head {
        background: #2d2d2d;
        border-bottom-color: #404040;

        .ant-card-head-title {
          color: #ffffff;
        }
      }

      .ant-card-body {
        background: #2d2d2d;
        color: #cccccc;
      }
    }

    .props-config {
      .prop-row {
        background: #404040;
        border-color: #555555;
      }

      .add-prop-btn {
        border-color: #555555;
        color: #cccccc;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }
      }
    }

    .results-list {
      .ant-list-item {
        background: #2d2d2d;
        border-color: #404040;

        &:hover {
          border-color: #1890ff;
          background-color: #1f3a5f;
        }

        .ant-list-item-meta {
          .ant-list-item-meta-title {
            color: #ffffff;
          }

          .ant-list-item-meta-description {
            color: #cccccc;
          }
        }
      }
    }

    .code-preview {
      pre {
        background: #1e1e1e !important;
        color: #d4d4d4;
        border: 1px solid #404040;
      }

      .copy-button {
        background: rgba(45, 45, 45, 0.9);
        border-color: #404040;
        color: #cccccc;

        &:hover {
          background: #404040;
          color: #ffffff;
        }
      }
    }

    .empty-state {
      .empty-icon {
        color: #666666;
      }
    }
  }
}

// 紧凑模式
.compact-theme {
  .ai-code-generator-panel {
    .ant-modal-body {
      padding: 12px;
    }

    .code-generator-content {
      .step-content {
        min-height: 400px;

        .step-card {
          .ant-card-body {
            padding: 16px;
          }
        }
      }
    }

    .config-form {
      .ant-form-item {
        margin-bottom: 12px;
      }
    }

    .props-config {
      .prop-row {
        margin-bottom: 8px;
        padding: 8px;
      }
    }

    .results-list {
      .ant-list-item {
        padding: 12px;
        margin-bottom: 8px;

        .ant-list-item-meta {
          .ant-list-item-meta-title {
            font-size: 14px;
          }
        }
      }
    }

    .generate-button {
      height: 40px;
      font-size: 14px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .ai-code-generator-panel {
    .ant-modal {
      width: 95% !important;
      max-width: none;
    }

    .code-generator-content {
      .ant-steps {
        .ant-steps-item {
          .ant-steps-item-title {
            font-size: 12px;
          }
        }
      }

      .step-content {
        min-height: 350px;
      }
    }

    .config-form {
      .ant-row {
        .ant-col {
          margin-bottom: 12px;
        }
      }
    }

    .props-config {
      .prop-row {
        .ant-row {
          .ant-col {
            margin-bottom: 8px;
          }
        }
      }
    }

    .results-list {
      .ant-list-item {
        .ant-list-item-action {
          margin-left: 8px;

          .ant-btn {
            margin-left: 2px;
            padding: 4px 8px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .ai-code-generator-panel {
    .code-generator-content {
      .ant-steps {
        .ant-steps-item {
          .ant-steps-item-title {
            display: none;
          }
        }
      }
    }

    .props-config {
      .prop-row {
        .ant-row {
          .ant-col {
            flex: 0 0 100%;
            max-width: 100%;
            margin-bottom: 8px;
          }
        }
      }
    }

    .code-preview {
      .ant-tabs {
        .ant-tabs-tab {
          font-size: 11px;
          padding: 6px 8px;
        }
      }

      pre {
        font-size: 11px;
      }
    }
  }
}

// 动画效果
.ai-code-generator-panel {
  .results-list {
    .ant-list-item {
      transition: all 0.3s ease;
    }
  }

  .props-config {
    .prop-row {
      transition: border-color 0.2s ease;
    }
  }

  .ant-btn {
    transition: all 0.2s ease;
  }

  .ant-steps {
    .ant-steps-item {
      transition: all 0.3s ease;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
  .ai-code-generator-panel {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }

    .generating-alert {
      .ant-alert-icon {
        animation: none;
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .ai-code-generator-panel {
    .ant-card {
      border: 2px solid #000;
    }

    .results-list {
      .ant-list-item {
        border: 2px solid #000;
      }
    }

    .props-config {
      .prop-row {
        border: 2px solid #000;
      }
    }

    .ant-steps {
      .ant-steps-item {
        .ant-steps-item-icon {
          border: 2px solid #000;
        }
      }
    }

    .code-preview {
      pre {
        border: 2px solid #000;
      }
    }
  }
}

// 代码生成器特定样式
.ai-code-generator-panel {
  .framework-react {
    color: #61dafb;
  }

  .framework-vue {
    color: #4fc08d;
  }

  .framework-angular {
    color: #dd0031;
  }

  .framework-vanilla {
    color: #f7df1e;
  }

  .code-type-component {
    background: linear-gradient(45deg, #e6f7ff 0%, #f0f9ff 100%);
  }

  .code-type-style {
    background: linear-gradient(45deg, #f6ffed 0%, #fcffe6 100%);
  }

  .code-type-logic {
    background: linear-gradient(45deg, #fff2e6 0%, #fff7e6 100%);
  }

  .code-type-test {
    background: linear-gradient(45deg, #f9f0ff 0%, #efdbff 100%);
  }

  .quality-excellent {
    color: #52c41a;
  }

  .quality-good {
    color: #faad14;
  }

  .quality-poor {
    color: #ff4d4f;
  }
}
