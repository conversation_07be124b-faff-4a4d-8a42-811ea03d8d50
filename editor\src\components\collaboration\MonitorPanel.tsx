/**
 * 协作监控面板组件
 */
import React, { useEffect, useState } from 'react';
import {
  Card,
  Tabs,
  Table,
  Badge,
  Space,
  Typography,
  Button,
  Statistic,
  Alert,
  Row,
  Col,
  Divider,
  Empty,
  Tag
} from 'antd';
import {
  LineChartOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  HistoryOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  CloseCircleOutlined,
  ReloadOutlined,
  ClearOutlined
} from '@ant-design/icons';

import {
  CollaborationMonitorService,
  PerformanceMetrics,
  Alert as MonitorAlert,
  AlertLevel
} from '../../services/CollaborationMonitorService';
import { CollaborationStatus } from '../../services/CollaborationService';

const { Text } = Typography;
const { TabPane } = Tabs;

// 创建监控服务实例
const monitorService = new CollaborationMonitorService();

/**
 * 协作监控面板组件
 */
const MonitorPanel: React.FC = () => {

  // 状态
  const [currentMetrics, setCurrentMetrics] = useState<PerformanceMetrics | null>(null);
  const [historyMetrics, setHistoryMetrics] = useState<PerformanceMetrics[]>([]);
  const [activeAlerts, setActiveAlerts] = useState<MonitorAlert[]>([]);
  const [historyAlerts, setHistoryAlerts] = useState<MonitorAlert[]>([]);
  const [isEnabled, setIsEnabled] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('overview');

  // 初始化
  useEffect(() => {
    // 启用监控服务
    monitorService.enable();
    setIsEnabled(true);

    // 监听指标更新事件
    monitorService.on('metricsUpdated', handleMetricsUpdated);

    // 监听告警事件
    monitorService.on('alert', handleAlert);

    // 初始加载数据
    loadData();

    // 清理
    return () => {
      monitorService.off('metricsUpdated', handleMetricsUpdated);
      monitorService.off('alert', handleAlert);
    };
  }, []);

  // 加载数据
  const loadData = () => {
    setCurrentMetrics(monitorService.getCurrentMetrics());
    setHistoryMetrics(monitorService.getHistoryMetrics());
    setActiveAlerts(monitorService.getActiveAlerts());
    setHistoryAlerts(monitorService.getHistoryAlerts());
  };

  // 处理指标更新
  const handleMetricsUpdated = (metrics: PerformanceMetrics) => {
    setCurrentMetrics(metrics);
    setHistoryMetrics(monitorService.getHistoryMetrics());
  };

  // 处理告警
  const handleAlert = () => {
    setActiveAlerts(monitorService.getActiveAlerts());
    setHistoryAlerts(monitorService.getHistoryAlerts());
  };

  // 处理确认告警
  const handleAcknowledgeAlert = (alertId: string) => {
    monitorService.acknowledgeAlert(alertId);
    setActiveAlerts(monitorService.getActiveAlerts());
    setHistoryAlerts(monitorService.getHistoryAlerts());
  };

  // 处理清空历史数据
  const handleClearHistory = () => {
    monitorService.clearHistory();
    loadData();
  };

  // 处理刷新数据
  const handleRefresh = () => {
    loadData();
  };

  // 获取连接状态颜色
  const getConnectionStatusColor = (status: CollaborationStatus): string => {
    switch (status) {
      case CollaborationStatus.CONNECTED:
        return 'green';
      case CollaborationStatus.CONNECTING:
        return 'blue';
      case CollaborationStatus.ERROR:
        return 'red';
      default:
        return 'gray';
    }
  };

  // 获取连接状态文本
  const getConnectionStatusText = (status: CollaborationStatus): string => {
    switch (status) {
      case CollaborationStatus.CONNECTED:
        return '已连接';
      case CollaborationStatus.CONNECTING:
        return '连接中';
      case CollaborationStatus.ERROR:
        return '连接错误';
      default:
        return '未连接';
    }
  };

  // 获取告警级别颜色
  const getAlertLevelColor = (level: AlertLevel): string => {
    switch (level) {
      case AlertLevel.INFO:
        return 'blue';
      case AlertLevel.WARNING:
        return 'orange';
      case AlertLevel.ERROR:
        return 'red';
      case AlertLevel.CRITICAL:
        return 'purple';
      default:
        return 'default';
    }
  };

  // 获取告警级别图标
  const getAlertLevelIcon = (level: AlertLevel) => {
    switch (level) {
      case AlertLevel.INFO:
        return <InfoCircleOutlined />;
      case AlertLevel.WARNING:
        return <WarningOutlined />;
      case AlertLevel.ERROR:
        return <CloseCircleOutlined />;
      case AlertLevel.CRITICAL:
        return <ExclamationCircleOutlined />;
      default:
        return <InfoCircleOutlined />;
    }
  };

  // 格式化时间
  const formatTime = (timestamp: number): string => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString();
  };

  // 渲染概览标签页
  const renderOverviewTab = () => {
    if (!currentMetrics) {
      return (
        <Empty description="暂无监控数据" />
      );
    }

    return (
      <div className="monitor-overview">
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Card>
              <Statistic
                title="连接状态"
                value={getConnectionStatusText(currentMetrics.connectionStatus)}
                valueStyle={{ color: getConnectionStatusColor(currentMetrics.connectionStatus) }}
                prefix={<Badge status={getConnectionStatusColor(currentMetrics.connectionStatus) as any} />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="网络延迟"
                value={currentMetrics.latency}
                suffix="ms"
                valueStyle={{ color: currentMetrics.latency > 200 ? 'red' : 'green' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="在线用户"
                value={currentMetrics.userCount}
                prefix={<TeamOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="冲突率"
                value={Math.round(currentMetrics.conflictRate * 100)}
                suffix="%"
                valueStyle={{ color: currentMetrics.conflictRate > 0.1 ? 'red' : 'green' }}
              />
            </Card>
          </Col>
        </Row>

        <Divider />

        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Card title="活跃告警">
              {activeAlerts.length > 0 ? (
                <ul className="alert-list">
                  {activeAlerts.slice(0, 5).map(alert => (
                    <li key={alert.id}>
                      <Space>
                        {getAlertLevelIcon(alert.level)}
                        <Tag color={getAlertLevelColor(alert.level)}>{alert.level}</Tag>
                        <Text>{alert.message}</Text>
                        <Text type="secondary">{formatTime(alert.timestamp)}</Text>
                        <Button
                          size="small"
                          type="text"
                          icon={<CheckCircleOutlined />}
                          onClick={() => handleAcknowledgeAlert(alert.id)}
                        />
                      </Space>
                    </li>
                  ))}
                </ul>
              ) : (
                <Empty description="暂无活跃告警" />
              )}
            </Card>
          </Col>
          <Col span={12}>
            <Card title="操作统计">
              <Statistic
                title="总操作数"
                value={currentMetrics.operationCount}
                prefix={<HistoryOutlined />}
              />
              <Divider />
              <Statistic
                title="冲突数"
                value={currentMetrics.conflictCount}
                prefix={<WarningOutlined />}
              />
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染性能标签页
  const renderPerformanceTab = () => {
    if (historyMetrics.length === 0) {
      return (
        <Empty description="暂无性能数据" />
      );
    }

    // 表格列定义
    const columns = [
      {
        title: '时间',
        dataIndex: 'timestamp',
        key: 'timestamp',
        render: (timestamp: number) => formatTime(timestamp)
      },
      {
        title: '延迟',
        dataIndex: 'latency',
        key: 'latency',
        render: (latency: number) => `${latency} ms`,
        sorter: (a: PerformanceMetrics, b: PerformanceMetrics) => a.latency - b.latency
      },
      {
        title: '丢包率',
        dataIndex: 'packetLoss',
        key: 'packetLoss',
        render: (packetLoss: number) => `${(packetLoss * 100).toFixed(2)}%`,
        sorter: (a: PerformanceMetrics, b: PerformanceMetrics) => a.packetLoss - b.packetLoss
      },
      {
        title: '操作队列',
        dataIndex: 'operationQueueLength',
        key: 'operationQueueLength',
        sorter: (a: PerformanceMetrics, b: PerformanceMetrics) => a.operationQueueLength - b.operationQueueLength
      },
      {
        title: '内存使用',
        dataIndex: 'memoryUsage',
        key: 'memoryUsage',
        render: (memoryUsage: number) => `${memoryUsage.toFixed(2)} MB`,
        sorter: (a: PerformanceMetrics, b: PerformanceMetrics) => a.memoryUsage - b.memoryUsage
      },
      {
        title: '用户数',
        dataIndex: 'userCount',
        key: 'userCount',
        sorter: (a: PerformanceMetrics, b: PerformanceMetrics) => a.userCount - b.userCount
      }
    ];

    return (
      <div className="monitor-performance">
        <Card title="性能指标历史">
          <Table
            dataSource={historyMetrics.map((metric, index) => ({ ...metric, key: index }))}
            columns={columns}
            pagination={{ pageSize: 10 }}
            size="small"
          />
        </Card>
      </div>
    );
  };

  // 渲染告警标签页
  const renderAlertsTab = () => {
    // 活跃告警表格列定义
    const activeColumns = [
      {
        title: '级别',
        dataIndex: 'level',
        key: 'level',
        render: (level: AlertLevel) => (
          <Tag color={getAlertLevelColor(level)} icon={getAlertLevelIcon(level)}>
            {level}
          </Tag>
        ),
        filters: [
          { text: '信息', value: AlertLevel.INFO },
          { text: '警告', value: AlertLevel.WARNING },
          { text: '错误', value: AlertLevel.ERROR },
          { text: '严重', value: AlertLevel.CRITICAL },
        ],
        onFilter: (value: any, record: MonitorAlert) => record.level === value
      },
      {
        title: '消息',
        dataIndex: 'message',
        key: 'message'
      },
      {
        title: '时间',
        dataIndex: 'timestamp',
        key: 'timestamp',
        render: (timestamp: number) => formatTime(timestamp),
        sorter: (a: MonitorAlert, b: MonitorAlert) => a.timestamp - b.timestamp
      },
      {
        title: '指标',
        dataIndex: 'metric',
        key: 'metric',
        render: (metric: string) => metric || '-'
      },
      {
        title: '值',
        dataIndex: 'value',
        key: 'value',
        render: (value: number) => value?.toFixed(2) || '-'
      },
      {
        title: '阈值',
        dataIndex: 'threshold',
        key: 'threshold',
        render: (threshold: number) => threshold?.toFixed(2) || '-'
      },
      {
        title: '操作',
        key: 'action',
        render: (_: any, record: MonitorAlert) => (
          <Button
            type="primary"
            size="small"
            icon={<CheckCircleOutlined />}
            onClick={() => handleAcknowledgeAlert(record.id)}
          >
            确认
          </Button>
        )
      }
    ];

    // 历史告警表格列定义
    const historyColumns = [
      {
        title: '级别',
        dataIndex: 'level',
        key: 'level',
        render: (level: AlertLevel) => (
          <Tag color={getAlertLevelColor(level)} icon={getAlertLevelIcon(level)}>
            {level}
          </Tag>
        ),
        filters: [
          { text: '信息', value: AlertLevel.INFO },
          { text: '警告', value: AlertLevel.WARNING },
          { text: '错误', value: AlertLevel.ERROR },
          { text: '严重', value: AlertLevel.CRITICAL },
        ],
        onFilter: (value: any, record: MonitorAlert) => record.level === value
      },
      {
        title: '消息',
        dataIndex: 'message',
        key: 'message'
      },
      {
        title: '时间',
        dataIndex: 'timestamp',
        key: 'timestamp',
        render: (timestamp: number) => formatTime(timestamp),
        sorter: (a: MonitorAlert, b: MonitorAlert) => a.timestamp - b.timestamp
      },
      {
        title: '指标',
        dataIndex: 'metric',
        key: 'metric',
        render: (metric: string) => metric || '-'
      },
      {
        title: '值',
        dataIndex: 'value',
        key: 'value',
        render: (value: number) => value?.toFixed(2) || '-'
      },
      {
        title: '状态',
        dataIndex: 'acknowledged',
        key: 'acknowledged',
        render: (acknowledged: boolean) => (
          acknowledged ?
            <Tag color="green" icon={<CheckCircleOutlined />}>已确认</Tag> :
            <Tag color="orange" icon={<ClockCircleOutlined />}>未确认</Tag>
        ),
        filters: [
          { text: '已确认', value: true },
          { text: '未确认', value: false },
        ],
        onFilter: (value: any, record: MonitorAlert) => record.acknowledged === value
      }
    ];

    return (
      <div className="monitor-alerts">
        <Tabs defaultActiveKey="active">
          <TabPane tab="活跃告警" key="active">
            {activeAlerts.length > 0 ? (
              <Table
                dataSource={activeAlerts.map(alert => ({ ...alert, key: alert.id }))}
                columns={activeColumns}
                pagination={{ pageSize: 10 }}
                size="small"
              />
            ) : (
              <Empty description="暂无活跃告警" />
            )}
          </TabPane>
          <TabPane tab="历史告警" key="history">
            {historyAlerts.length > 0 ? (
              <Table
                dataSource={historyAlerts.map(alert => ({ ...alert, key: alert.id }))}
                columns={historyColumns}
                pagination={{ pageSize: 10 }}
                size="small"
              />
            ) : (
              <Empty description="暂无历史告警" />
            )}
          </TabPane>
        </Tabs>
      </div>
    );
  };

  // 渲染设置标签页
  const renderSettingsTab = () => {
    return (
      <div className="monitor-settings">
        <Card title="监控设置">
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Space>
                <Button
                  type={isEnabled ? "default" : "primary"}
                  onClick={() => {
                    if (isEnabled) {
                      monitorService.disable();
                    } else {
                      monitorService.enable();
                    }
                    setIsEnabled(!isEnabled);
                  }}
                >
                  {isEnabled ? "禁用监控" : "启用监控"}
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                >
                  刷新数据
                </Button>
                <Button
                  icon={<ClearOutlined />}
                  onClick={handleClearHistory}
                >
                  清空历史
                </Button>
              </Space>
            </Col>
          </Row>

          <Divider />

          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Alert
                message="监控服务信息"
                description={`
                  当前采样间隔: ${(monitorService as any).config?.sampleInterval || 5000}ms
                  历史数据保留时间: ${((monitorService as any).config?.historyDuration || 3600000) / 60000}分钟
                  告警阈值: 延迟>${(monitorService as any).config?.alertThresholds?.latency || 500}ms,
                  丢包率>${((monitorService as any).config?.alertThresholds?.packetLoss || 0.1) * 100}%,
                  冲突率>${((monitorService as any).config?.alertThresholds?.conflictRate || 0.2) * 100}%
                `}
                type="info"
                showIcon
              />
            </Col>
          </Row>
        </Card>
      </div>
    );
  };

  return (
    <div className="collaboration-monitor-panel">
      <Card
        title={
          <Space>
            <LineChartOutlined />
            <span>协作监控</span>
            {currentMetrics && (
              <Badge
                status={getConnectionStatusColor(currentMetrics.connectionStatus) as any}
                text={getConnectionStatusText(currentMetrics.connectionStatus)}
              />
            )}
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              size="small"
            />
            {activeAlerts.length > 0 && (
              <Badge count={activeAlerts.length} overflowCount={99}>
                <Button
                  icon={<WarningOutlined />}
                  onClick={() => setActiveTab('alerts')}
                  size="small"
                />
              </Badge>
            )}
          </Space>
        }
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="概览" key="overview">
            {renderOverviewTab()}
          </TabPane>
          <TabPane tab="性能" key="performance">
            {renderPerformanceTab()}
          </TabPane>
          <TabPane
            tab={
              <span>
                告警
                {activeAlerts.length > 0 && (
                  <Badge count={activeAlerts.length} style={{ marginLeft: 8 }} />
                )}
              </span>
            }
            key="alerts"
          >
            {renderAlertsTab()}
          </TabPane>
          <TabPane tab="设置" key="settings">
            {renderSettingsTab()}
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
}

// 导出监控面板组件
export default MonitorPanel;