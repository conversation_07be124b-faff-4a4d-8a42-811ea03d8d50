/**
 * 粒子系统面板样式
 */

.particle-system-panel {
  .ant-modal-body {
    padding: 16px;
    height: 70vh;
    overflow: hidden;
  }

  // 系统列表
  .systems-list {
    height: 100%;
    display: flex;
    flex-direction: column;

    .systems-header {
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;

      .ant-btn {
        margin-right: 8px;
        margin-bottom: 8px;
      }
    }

    .ant-list {
      flex: 1;
      overflow-y: auto;

      .system-item {
        padding: 12px;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          border-color: #d9d9d9;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        &.selected {
          border-color: #1890ff;
          background-color: #e6f7ff;
        }

        &.active {
          border-color: #52c41a;
          background-color: #f6ffed;
        }

        .system-icon {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          font-size: 18px;

          &.fire {
            background: linear-gradient(45deg, #ff4d4f, #faad14);
            color: #ffffff;
          }

          &.smoke {
            background: linear-gradient(45deg, #8c8c8c, #d9d9d9);
            color: #ffffff;
          }

          &.explosion {
            background: linear-gradient(45deg, #ff7a45, #ff4d4f);
            color: #ffffff;
          }

          &.rain {
            background: linear-gradient(45deg, #1890ff, #13c2c2);
            color: #ffffff;
          }

          &.snow {
            background: linear-gradient(45deg, #f0f0f0, #ffffff);
            color: #1890ff;
          }

          &.magic {
            background: linear-gradient(45deg, #722ed1, #eb2f96);
            color: #ffffff;
          }

          &.energy {
            background: linear-gradient(45deg, #52c41a, #13c2c2);
            color: #ffffff;
          }
        }

        .ant-list-item-meta {
          .ant-list-item-meta-title {
            margin-bottom: 4px;
            font-size: 14px;
          }

          .ant-list-item-meta-description {
            font-size: 12px;

            .ant-progress {
              margin-top: 4px;
            }
          }
        }

        .ant-list-item-action {
          margin-left: 8px;

          .ant-btn {
            margin-left: 4px;
          }
        }
      }
    }
  }

  // 属性编辑器
  .property-editor {
    height: 100%;
    overflow-y: auto;

    .editor-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      .ant-typography {
        margin-bottom: 0;
      }
    }

    .no-system-selected {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #d9d9d9;
    }

    .ant-collapse {
      .ant-collapse-item {
        .ant-collapse-header {
          font-size: 14px;
          font-weight: 600;
          padding: 12px 16px;
        }

        .ant-collapse-content {
          .ant-collapse-content-box {
            padding: 16px;
          }
        }
      }
    }

    .ant-form {
      .ant-form-item {
        margin-bottom: 16px;

        .ant-form-item-label {
          font-weight: 500;
        }
      }

      .ant-slider {
        margin: 8px 0;
      }

      .ant-color-picker {
        width: 100%;
      }
    }
  }

  // 预览和统计
  .preview-and-stats {
    height: 100%;
    overflow-y: auto;

    .ant-card {
      .ant-card-head {
        .ant-card-head-title {
          font-size: 14px;
          font-weight: 600;
        }
      }

      .ant-card-body {
        padding: 16px;
      }
    }

    .preview-viewport {
      position: relative;

      .simulation-indicator {
        position: absolute;
        top: 8px;
        right: 8px;
        animation: pulse 2s infinite;
      }
    }

    .preview-controls {
      .ant-btn {
        margin-right: 8px;
      }
    }

    .performance-indicator {
      .ant-progress {
        margin-top: 8px;
      }
    }
  }
}

// 预设选择对话框
.preset-modal {
  .ant-card {
    .ant-card-cover {
      height: 120px;
      overflow: hidden;

      img {
        transition: transform 0.3s ease;
      }
    }

    &:hover {
      .ant-card-cover img {
        transform: scale(1.05);
      }
    }

    .ant-card-meta {
      .ant-card-meta-title {
        font-size: 14px;
      }

      .ant-card-meta-description {
        font-size: 12px;

        .ant-tag {
          margin-bottom: 2px;
        }
      }
    }
  }
}

// 深色主题
.dark-theme {
  .particle-system-panel {
    .ant-modal-content {
      background: #2d2d2d;
      color: #cccccc;
    }

    .systems-list {
      .systems-header {
        border-bottom-color: #404040;
      }

      .ant-list {
        .system-item {
          background: #2d2d2d;
          border-color: #404040;

          &:hover {
            border-color: #555555;
            background-color: #404040;
          }

          &.selected {
            border-color: #1890ff;
            background-color: #1f3a5f;
          }

          &.active {
            border-color: #52c41a;
            background-color: #2a4a2a;
          }

          .ant-list-item-meta {
            .ant-list-item-meta-title {
              color: #ffffff;
            }

            .ant-list-item-meta-description {
              color: #cccccc;
            }
          }
        }
      }
    }

    .property-editor {
      .editor-header {
        .ant-typography {
          color: #ffffff;
        }
      }

      .no-system-selected {
        color: #666666;
      }

      .ant-collapse {
        .ant-collapse-item {
          background: #2d2d2d;
          border-color: #404040;

          .ant-collapse-header {
            background: #2d2d2d;
            color: #ffffff;
          }

          .ant-collapse-content {
            background: #2d2d2d;
            border-top-color: #404040;

            .ant-collapse-content-box {
              color: #cccccc;
            }
          }
        }
      }
    }

    .preview-and-stats {
      .ant-card {
        background: #2d2d2d;
        border-color: #404040;

        .ant-card-head {
          background: #2d2d2d;
          border-bottom-color: #404040;

          .ant-card-head-title {
            color: #ffffff;
          }
        }

        .ant-card-body {
          background: #2d2d2d;
          color: #cccccc;
        }
      }
    }
  }
}

// 紧凑模式
.compact-theme {
  .particle-system-panel {
    .ant-modal-body {
      padding: 12px;
    }

    .systems-list {
      .systems-header {
        margin-bottom: 12px;
        padding-bottom: 8px;
      }

      .ant-list {
        .system-item {
          padding: 8px;
          margin-bottom: 6px;

          .system-icon {
            width: 32px;
            height: 32px;
            font-size: 14px;
          }

          .ant-list-item-meta {
            .ant-list-item-meta-title {
              font-size: 13px;
            }

            .ant-list-item-meta-description {
              font-size: 11px;
            }
          }
        }
      }
    }

    .property-editor {
      .editor-header {
        margin-bottom: 12px;
      }

      .ant-collapse {
        .ant-collapse-item {
          .ant-collapse-header {
            padding: 8px 12px;
          }

          .ant-collapse-content {
            .ant-collapse-content-box {
              padding: 12px;
            }
          }
        }
      }

      .ant-form {
        .ant-form-item {
          margin-bottom: 12px;
        }
      }
    }

    .preview-and-stats {
      .ant-card {
        .ant-card-body {
          padding: 12px;
        }
      }

      .preview-viewport {
        height: 150px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .particle-system-panel {
    .ant-modal {
      width: 95% !important;
      max-width: none;
    }
  }
}

@media (max-width: 768px) {
  .particle-system-panel {
    .ant-row {
      .ant-col {
        margin-bottom: 16px;
      }
    }

    .systems-list {
      .systems-header {
        .ant-space {
          flex-direction: column;
          align-items: stretch;
          width: 100%;
        }
      }
    }

    .property-editor {
      .ant-form {
        .ant-row {
          .ant-col {
            flex: 0 0 100%;
            max-width: 100%;
            margin-bottom: 12px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .particle-system-panel {
    .preview-and-stats {
      .preview-viewport {
        height: 120px;
      }

      .ant-statistic {
        .ant-statistic-title {
          font-size: 12px;
        }

        .ant-statistic-content {
          font-size: 16px;
        }
      }
    }
  }
}

// 动画效果
.particle-system-panel {
  .systems-list {
    .system-item {
      transition: all 0.2s ease;
    }
  }

  .preset-modal {
    .ant-card {
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .ant-btn {
    transition: all 0.2s ease;
  }

  .ant-slider {
    .ant-slider-handle {
      transition: all 0.2s ease;
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
  .particle-system-panel {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }

    .preset-modal {
      .ant-card {
        &:hover {
          transform: none;
        }
      }
    }

    .simulation-indicator {
      animation: none !important;
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .particle-system-panel {
    .systems-list {
      .system-item {
        border: 2px solid #000;

        &.selected {
          border-color: #0066cc;
        }

        &.active {
          border-color: #00aa00;
        }
      }
    }

    .property-editor {
      .ant-collapse {
        .ant-collapse-item {
          border: 2px solid #000;
        }
      }
    }

    .preview-and-stats {
      .ant-card {
        border: 2px solid #000;
      }

      .preview-viewport {
        border: 2px solid #000;
      }
    }
  }
}

// 粒子系统特定样式
.particle-system-panel {
  .particle-type-fire {
    border-left: 4px solid #ff4d4f;
  }

  .particle-type-smoke {
    border-left: 4px solid #8c8c8c;
  }

  .particle-type-explosion {
    border-left: 4px solid #ff7a45;
  }

  .particle-type-rain {
    border-left: 4px solid #1890ff;
  }

  .particle-type-snow {
    border-left: 4px solid #f0f0f0;
  }

  .particle-type-magic {
    border-left: 4px solid #722ed1;
  }

  .particle-type-energy {
    border-left: 4px solid #52c41a;
  }

  .performance-excellent {
    color: #52c41a;
  }

  .performance-good {
    color: #faad14;
  }

  .performance-poor {
    color: #ff4d4f;
  }

  .simulation-active {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 4px;
      right: 4px;
      width: 8px;
      height: 8px;
      background: #52c41a;
      border-radius: 50%;
      animation: pulse 2s infinite;
    }
  }

  .emitter-shape-indicator {
    &.point {
      background: radial-gradient(circle, #1890ff 30%, transparent 70%);
    }

    &.sphere {
      background: radial-gradient(circle, #52c41a 50%, transparent 70%);
    }

    &.box {
      background: linear-gradient(45deg, #faad14 25%, transparent 25%, transparent 75%, #faad14 75%);
    }

    &.cone {
      background: conic-gradient(from 0deg, #722ed1, transparent, #722ed1);
    }
  }

  .blend-mode-preview {
    &.additive {
      background: linear-gradient(45deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.2));
    }

    &.multiply {
      background: linear-gradient(45deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.2));
    }

    &.screen {
      background: linear-gradient(45deg, rgba(255, 255, 255, 0.9), rgba(128, 128, 128, 0.5));
    }
  }
}
