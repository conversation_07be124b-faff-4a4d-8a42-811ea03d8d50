/**
 * 交互预览组件
 * 用于实时预览交互效果
 */
import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Card, Button, Space, Tag, Alert, Spin, Typography } from 'antd';
import {
  PlayCircleOutlined,
  PauseOutlined,
  ReloadOutlined,
  FullscreenOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { interactionService } from '../../services/InteractionService';
import type { InteractionEvent } from '../../services/InteractionService';
import './InteractionPreview.less';

const { Text, Title } = Typography;

interface InteractionPreviewProps {
  /** 预览配置 */
  config?: any;
  /** 是否自动播放 */
  autoPlay?: boolean;
  /** 预览尺寸 */
  size?: 'small' | 'medium' | 'large';
  /** 是否显示控制栏 */
  showControls?: boolean;
  /** 是否显示统计信息 */
  showStats?: boolean;
}

/**
 * 交互预览组件
 */
const InteractionPreview: React.FC<InteractionPreviewProps> = ({
  config: _config,
  autoPlay = false,
  size = 'medium',
  showControls = true,
  showStats = true
}) => {
  const { t } = useTranslation();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<any>(null);
  const [recentEvents, setRecentEvents] = useState<InteractionEvent[]>([]);

  // 预览尺寸映射
  const sizeMap = {
    small: { width: 300, height: 200 },
    medium: { width: 500, height: 350 },
    large: { width: 800, height: 600 }
  };

  const dimensions = sizeMap[size];

  // 初始化预览
  useEffect(() => {
    initializePreview();
    
    // 监听交互事件
    const handleInteraction = (event: InteractionEvent) => {
      setRecentEvents(prev => [...prev.slice(-9), event]);
    };

    interactionService.on('interaction', handleInteraction);

    return () => {
      interactionService.off('interaction', handleInteraction);
      cleanup();
    };
  }, []);

  // 更新统计信息
  useEffect(() => {
    if (showStats && interactionService.isReady()) {
      const updateStats = () => {
        setStats(interactionService.getStats());
      };

      updateStats();
      const interval = setInterval(updateStats, 1000);

      return () => clearInterval(interval);
    }
  }, [showStats]);

  /**
   * 初始化预览
   */
  const initializePreview = useCallback(async () => {
    if (!canvasRef.current) return;

    setIsLoading(true);
    setError(null);

    try {
      // 这里应该初始化3D场景和交互系统
      // 由于我们没有完整的3D引擎集成，这里只是模拟
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (autoPlay) {
        setIsPlaying(true);
      }
    } catch (err) {
      setError(t('interaction.previewInitError'));
      console.error('Preview initialization error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [autoPlay, t]);

  /**
   * 开始预览
   */
  const startPreview = useCallback(() => {
    setIsPlaying(true);
    // 这里应该启动3D场景渲染循环
  }, []);

  /**
   * 停止预览
   */
  const stopPreview = useCallback(() => {
    setIsPlaying(false);
    // 这里应该停止3D场景渲染循环
  }, []);

  /**
   * 重新加载预览
   */
  const reloadPreview = useCallback(() => {
    setRecentEvents([]);
    setStats(null);
    initializePreview();
  }, [initializePreview]);

  /**
   * 全屏预览
   */
  const toggleFullscreen = useCallback(() => {
    if (!canvasRef.current) return;

    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      canvasRef.current.requestFullscreen();
    }
  }, []);

  /**
   * 清理资源
   */
  const cleanup = useCallback(() => {
    // 清理3D场景资源
    setIsPlaying(false);
  }, []);

  /**
   * 渲染控制栏
   */
  const renderControls = () => {
    if (!showControls) return null;

    return (
      <div className="preview-controls">
        <Space>
          <Button
            type={isPlaying ? 'primary' : 'default'}
            icon={isPlaying ? <PauseOutlined /> : <PlayCircleOutlined />}
            onClick={isPlaying ? stopPreview : startPreview}
            disabled={isLoading}
          >
            {isPlaying ? t('common.pause') : t('common.play')}
          </Button>
          
          <Button
            icon={<ReloadOutlined />}
            onClick={reloadPreview}
            disabled={isLoading}
          >
            {t('common.reload')}
          </Button>
          
          <Button
            icon={<FullscreenOutlined />}
            onClick={toggleFullscreen}
            disabled={isLoading}
          >
            {t('common.fullscreen')}
          </Button>
          
          <Button
            icon={<SettingOutlined />}
            disabled={isLoading}
          >
            {t('common.settings')}
          </Button>
        </Space>

        <div className="preview-status">
          <Space>
            <Tag color={isPlaying ? 'green' : 'default'}>
              {isPlaying ? t('interaction.playing') : t('interaction.stopped')}
            </Tag>
            {stats && (
              <Text type="secondary">
                {t('interaction.totalInteractions')}: {stats.totalInteractions}
              </Text>
            )}
          </Space>
        </div>
      </div>
    );
  };

  /**
   * 渲染统计信息
   */
  const renderStats = () => {
    if (!showStats || !stats) return null;

    return (
      <div className="preview-stats">
        <Title level={5}>{t('interaction.statistics')}</Title>
        <div className="stats-grid">
          <div className="stat-item">
            <Text strong>{stats.totalInteractions}</Text>
            <Text type="secondary">{t('interaction.totalInteractions')}</Text>
          </div>
          <div className="stat-item">
            <Text strong>{Object.keys(stats.interactionsByType).length}</Text>
            <Text type="secondary">{t('interaction.interactionTypes')}</Text>
          </div>
          <div className="stat-item">
            <Text strong>{stats.mostInteractedEntity || 'N/A'}</Text>
            <Text type="secondary">{t('interaction.mostInteracted')}</Text>
          </div>
        </div>

        {recentEvents.length > 0 && (
          <div className="recent-events">
            <Text strong>{t('interaction.recentEvents')}</Text>
            <div className="events-list">
              {recentEvents.slice(-5).map((event, index) => (
                <div key={index} className="event-item">
                  <Tag color="blue">{event.interactionType}</Tag>
                  <Text type="secondary">
                    {new Date(event.timestamp).toLocaleTimeString()}
                  </Text>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <Card
      className={`interaction-preview interaction-preview-${size}`}
      title={t('interaction.preview')}
      extra={renderControls()}
    >
      <div className="preview-container">
        <div className="preview-viewport">
          {isLoading && (
            <div className="preview-loading">
              <Spin size="large" />
              <Text>{t('interaction.loadingPreview')}</Text>
            </div>
          )}

          {error && (
            <Alert
              message={t('interaction.previewError')}
              description={error}
              type="error"
              showIcon
              action={
                <Button size="small" onClick={reloadPreview}>
                  {t('common.retry')}
                </Button>
              }
            />
          )}

          <canvas
            ref={canvasRef}
            width={dimensions.width}
            height={dimensions.height}
            className="preview-canvas"
            style={{
              display: isLoading || error ? 'none' : 'block'
            }}
          />

          {!isLoading && !error && !isPlaying && (
            <div className="preview-placeholder">
              <PlayCircleOutlined style={{ fontSize: 48, color: '#ccc' }} />
              <Text type="secondary">{t('interaction.clickToStart')}</Text>
            </div>
          )}
        </div>

        {renderStats()}
      </div>
    </Card>
  );
};

export default InteractionPreview;
