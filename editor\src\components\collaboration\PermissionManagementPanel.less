/**
 * 权限管理面板样式
 */

.permission-management-panel {
  .ant-modal-body {
    padding: 16px;
  }

  .ant-tabs {
    .ant-tabs-tab {
      .ant-badge {
        .ant-badge-count {
          font-size: 10px;
          min-width: 16px;
          height: 16px;
          line-height: 16px;
        }
      }
    }
  }

  .ant-card {
    .ant-card-body {
      padding: 16px;
    }
  }

  .ant-table {
    .ant-table-tbody {
      .ant-table-row {
        &:hover {
          background-color: #f5f5f5;
        }
      }
    }

    .ant-table-cell {
      padding: 8px 12px;

      .ant-space {
        .ant-tag {
          margin-bottom: 2px;
        }
      }
    }
  }

  .permission-change-item {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }

  .ant-timeline {
    .ant-timeline-item {
      .ant-timeline-item-content {
        margin-left: 20px;
      }
    }
  }

  .ant-form {
    .ant-form-item {
      margin-bottom: 16px;

      .ant-checkbox-group {
        max-height: 200px;
        overflow-y: auto;
        padding: 8px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;

        .ant-space {
          width: 100%;

          .ant-checkbox-wrapper {
            margin-bottom: 8px;
            width: 100%;
          }
        }
      }
    }
  }
}

// 深色主题
.dark-theme {
  .permission-management-panel {
    .ant-card {
      background: #2d2d2d;
      border-color: #404040;

      .ant-card-body {
        background: #2d2d2d;
      }
    }

    .ant-table {
      background: #2d2d2d;

      .ant-table-thead {
        .ant-table-cell {
          background: #404040;
          color: #ffffff;
          border-bottom-color: #555555;
        }
      }

      .ant-table-tbody {
        .ant-table-row {
          background: #2d2d2d;
          color: #cccccc;

          &:hover {
            background-color: #404040;
          }

          .ant-table-cell {
            border-bottom-color: #404040;
          }
        }
      }
    }

    .permission-change-item {
      border-bottom-color: #404040;
    }

    .ant-timeline {
      .ant-timeline-item {
        .ant-timeline-item-tail {
          border-left-color: #404040;
        }

        .ant-timeline-item-head {
          background: #2d2d2d;
          border-color: #404040;
        }
      }
    }

    .ant-form {
      .ant-form-item {
        .ant-checkbox-group {
          background: #2d2d2d;
          border-color: #404040;

          .ant-checkbox-wrapper {
            color: #cccccc;

            .ant-checkbox {
              .ant-checkbox-inner {
                background: #2d2d2d;
                border-color: #404040;
              }

              &.ant-checkbox-checked {
                .ant-checkbox-inner {
                  background: #1890ff;
                  border-color: #1890ff;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 紧凑模式
.compact-theme {
  .permission-management-panel {
    .ant-modal-body {
      padding: 12px;
    }

    .ant-card {
      .ant-card-body {
        padding: 12px;
      }
    }

    .ant-table {
      .ant-table-cell {
        padding: 6px 8px;
        font-size: 12px;
      }
    }

    .permission-change-item {
      padding: 6px 0;
    }

    .ant-timeline {
      .ant-timeline-item {
        .ant-timeline-item-content {
          margin-left: 16px;
          font-size: 12px;
        }
      }
    }

    .ant-form {
      .ant-form-item {
        margin-bottom: 12px;

        .ant-form-item-label {
          padding-bottom: 2px;

          label {
            font-size: 12px;
          }
        }

        .ant-checkbox-group {
          max-height: 150px;
          padding: 6px;

          .ant-checkbox-wrapper {
            margin-bottom: 6px;
            font-size: 12px;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .permission-management-panel {
    .ant-modal {
      width: 95% !important;
      max-width: none;
    }

    .ant-table {
      .ant-table-cell {
        padding: 4px 6px;
        font-size: 12px;
      }

      .ant-space {
        flex-direction: column;
        align-items: flex-start;
      }
    }

    .ant-tabs {
      .ant-tabs-tab {
        padding: 8px 12px;
        font-size: 12px;
      }
    }
  }
}

@media (max-width: 480px) {
  .permission-management-panel {
    .ant-table {
      .ant-table-thead {
        display: none;
      }

      .ant-table-tbody {
        .ant-table-row {
          display: block;
          border: 1px solid #f0f0f0;
          margin-bottom: 8px;
          border-radius: 4px;

          .ant-table-cell {
            display: block;
            border: none;
            padding: 8px;

            &:before {
              content: attr(data-label) ': ';
              font-weight: bold;
              display: inline-block;
              width: 80px;
            }
          }
        }
      }
    }

    .ant-form {
      .ant-form-item {
        .ant-checkbox-group {
          max-height: 120px;

          .ant-checkbox-wrapper {
            font-size: 11px;
          }
        }
      }
    }
  }
}

// 动画效果
.permission-management-panel {
  .ant-table {
    .ant-table-row {
      transition: background-color 0.2s ease;
    }
  }

  .ant-btn {
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
    }
  }

  .ant-tag {
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  .permission-change-item {
    transition: all 0.2s ease;

    &:hover {
      background-color: #f9f9f9;
      padding-left: 8px;
    }
  }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
  .permission-management-panel {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .permission-management-panel {
    .ant-table {
      border: 2px solid #000;

      .ant-table-cell {
        border: 1px solid #000;
      }
    }

    .ant-card {
      border: 2px solid #000;
    }

    .ant-tag {
      border: 1px solid #000;
    }

    .permission-change-item {
      border-bottom: 2px solid #000;
    }
  }
}
