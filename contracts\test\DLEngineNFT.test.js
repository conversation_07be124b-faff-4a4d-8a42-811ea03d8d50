/**
 * DL引擎NFT合约测试套件
 */

const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("DLEngineNFT", function () {
  let nftContract;
  let marketplace;
  let owner, creator, user1, user2;
  let dlMetadata;

  beforeEach(async function () {
    [owner, creator, user1, user2] = await ethers.getSigners();

    // 部署NFT合约
    const DLEngineNFT = await ethers.getContractFactory("DLEngineNFT");
    nftContract = await DLEngineNFT.deploy("DL Engine NFT", "DLNFT");
    await nftContract.deployed();

    // 部署市场合约
    const DLEngineMarketplace = await ethers.getContractFactory("DLEngineMarketplace");
    marketplace = await DLEngineMarketplace.deploy(owner.address);
    await marketplace.deployed();

    // 配置合约
    await marketplace.approveNFTContract(nftContract.address);
    await nftContract.authorizeMinter(creator.address);

    // 准备测试元数据
    dlMetadata = {
      assetType: "model",
      licenseType: "CC0",
      subject: "数学",
      gradeLevel: "小学",
      difficulty: 5,
      isEducational: true,
      creator: creator.address,
      createdAt: Math.floor(Date.now() / 1000)
    };
  });

  describe("部署", function () {
    it("应该正确设置合约名称和符号", async function () {
      expect(await nftContract.name()).to.equal("DL Engine NFT");
      expect(await nftContract.symbol()).to.equal("DLNFT");
    });

    it("应该设置正确的所有者", async function () {
      expect(await nftContract.owner()).to.equal(owner.address);
    });

    it("应该授权部署者为铸造者", async function () {
      expect(await nftContract.authorizedMinters(owner.address)).to.be.true;
    });
  });

  describe("铸造功能", function () {
    it("应该能够铸造NFT", async function () {
      const tokenURI = "ipfs://QmTest123";
      
      await nftContract.connect(creator).mintNFT(
        user1.address,
        tokenURI,
        creator.address,
        500, // 5% 版税
        dlMetadata
      );

      expect(await nftContract.ownerOf(0)).to.equal(user1.address);
      expect(await nftContract.tokenURI(0)).to.equal(tokenURI);
      expect(await nftContract.totalSupply()).to.equal(1);
    });

    it("应该正确设置DL引擎元数据", async function () {
      const tokenURI = "ipfs://QmTest123";
      
      await nftContract.connect(creator).mintNFT(
        user1.address,
        tokenURI,
        creator.address,
        500,
        dlMetadata
      );

      const metadata = await nftContract.getDLMetadata(0);
      expect(metadata.assetType).to.equal("model");
      expect(metadata.licenseType).to.equal("CC0");
      expect(metadata.subject).to.equal("数学");
      expect(metadata.gradeLevel).to.equal("小学");
      expect(metadata.difficulty).to.equal(5);
      expect(metadata.isEducational).to.be.true;
      expect(metadata.creator).to.equal(creator.address);
    });

    it("应该正确设置版税信息", async function () {
      const tokenURI = "ipfs://QmTest123";
      
      await nftContract.connect(creator).mintNFT(
        user1.address,
        tokenURI,
        creator.address,
        500, // 5% 版税
        dlMetadata
      );

      const [recipient, amount] = await nftContract.royaltyInfo(0, 10000);
      expect(recipient).to.equal(creator.address);
      expect(amount).to.equal(500); // 5% of 10000
    });

    it("应该防止未授权用户铸造", async function () {
      const tokenURI = "ipfs://QmTest123";
      
      await expect(
        nftContract.connect(user1).mintNFT(
          user1.address,
          tokenURI,
          creator.address,
          500,
          dlMetadata
        )
      ).to.be.revertedWith("Not authorized to mint");
    });

    it("应该防止重复使用相同的tokenURI", async function () {
      const tokenURI = "ipfs://QmTest123";
      
      await nftContract.connect(creator).mintNFT(
        user1.address,
        tokenURI,
        creator.address,
        500,
        dlMetadata
      );

      await expect(
        nftContract.connect(creator).mintNFT(
          user2.address,
          tokenURI,
          creator.address,
          500,
          dlMetadata
        )
      ).to.be.revertedWith("Token URI already used");
    });

    it("应该防止设置过高的版税", async function () {
      const tokenURI = "ipfs://QmTest123";
      
      await expect(
        nftContract.connect(creator).mintNFT(
          user1.address,
          tokenURI,
          creator.address,
          1500, // 15% 版税，超过最大值
          dlMetadata
        )
      ).to.be.revertedWith("Royalty too high");
    });
  });

  describe("批量铸造功能", function () {
    it("应该能够批量铸造NFT", async function () {
      const tokenURIs = [
        "ipfs://QmTest1",
        "ipfs://QmTest2",
        "ipfs://QmTest3"
      ];
      const metadataArray = [dlMetadata, dlMetadata, dlMetadata];

      const tx = await nftContract.connect(creator).batchMintNFT(
        user1.address,
        tokenURIs,
        creator.address,
        500,
        metadataArray
      );

      const receipt = await tx.wait();
      const batchMintedEvent = receipt.events.find(e => e.event === 'BatchMinted');
      
      expect(batchMintedEvent.args.to).to.equal(user1.address);
      expect(batchMintedEvent.args.count).to.equal(3);
      expect(await nftContract.totalSupply()).to.equal(3);
      
      // 验证每个NFT的所有者
      for (let i = 0; i < 3; i++) {
        expect(await nftContract.ownerOf(i)).to.equal(user1.address);
        expect(await nftContract.tokenURI(i)).to.equal(tokenURIs[i]);
      }
    });

    it("应该防止批量铸造数量过大", async function () {
      const tokenURIs = new Array(51).fill().map((_, i) => `ipfs://QmTest${i}`);
      const metadataArray = new Array(51).fill(dlMetadata);

      await expect(
        nftContract.connect(creator).batchMintNFT(
          user1.address,
          tokenURIs,
          creator.address,
          500,
          metadataArray
        )
      ).to.be.revertedWith("Batch size too large");
    });

    it("应该防止数组长度不匹配", async function () {
      const tokenURIs = ["ipfs://QmTest1", "ipfs://QmTest2"];
      const metadataArray = [dlMetadata]; // 长度不匹配

      await expect(
        nftContract.connect(creator).batchMintNFT(
          user1.address,
          tokenURIs,
          creator.address,
          500,
          metadataArray
        )
      ).to.be.revertedWith("Array length mismatch");
    });
  });

  describe("元数据管理", function () {
    beforeEach(async function () {
      const tokenURI = "ipfs://QmTest123";
      await nftContract.connect(creator).mintNFT(
        user1.address,
        tokenURI,
        creator.address,
        500,
        dlMetadata
      );
    });

    it("应该允许创建者更新元数据", async function () {
      const newMetadata = {
        ...dlMetadata,
        subject: "物理",
        difficulty: 7
      };

      await nftContract.connect(creator).updateDLMetadata(0, newMetadata);
      
      const metadata = await nftContract.getDLMetadata(0);
      expect(metadata.subject).to.equal("物理");
      expect(metadata.difficulty).to.equal(7);
      // 不可变字段应该保持不变
      expect(metadata.creator).to.equal(creator.address);
    });

    it("应该防止非创建者更新元数据", async function () {
      const newMetadata = {
        ...dlMetadata,
        subject: "物理"
      };

      await expect(
        nftContract.connect(user1).updateDLMetadata(0, newMetadata)
      ).to.be.revertedWith("Not the creator");
    });
  });

  describe("授权管理", function () {
    it("应该允许所有者授权新的铸造者", async function () {
      await nftContract.authorizeMinter(user1.address);
      expect(await nftContract.authorizedMinters(user1.address)).to.be.true;
    });

    it("应该允许所有者撤销铸造者授权", async function () {
      await nftContract.authorizeMinter(user1.address);
      await nftContract.revokeMinter(user1.address);
      expect(await nftContract.authorizedMinters(user1.address)).to.be.false;
    });

    it("应该防止非所有者授权铸造者", async function () {
      await expect(
        nftContract.connect(user1).authorizeMinter(user2.address)
      ).to.be.revertedWith("Ownable: caller is not the owner");
    });
  });

  describe("暂停功能", function () {
    it("应该允许所有者暂停合约", async function () {
      await nftContract.pause();
      expect(await nftContract.paused()).to.be.true;
    });

    it("应该在暂停时阻止铸造", async function () {
      await nftContract.pause();
      
      await expect(
        nftContract.connect(creator).mintNFT(
          user1.address,
          "ipfs://QmTest123",
          creator.address,
          500,
          dlMetadata
        )
      ).to.be.revertedWith("Pausable: paused");
    });

    it("应该允许所有者恢复合约", async function () {
      await nftContract.pause();
      await nftContract.unpause();
      expect(await nftContract.paused()).to.be.false;
    });
  });

  describe("销毁功能", function () {
    beforeEach(async function () {
      const tokenURI = "ipfs://QmTest123";
      await nftContract.connect(creator).mintNFT(
        user1.address,
        tokenURI,
        creator.address,
        500,
        dlMetadata
      );
    });

    it("应该允许所有者销毁NFT", async function () {
      await nftContract.connect(user1).burn(0);
      
      await expect(nftContract.ownerOf(0)).to.be.revertedWith(
        "ERC721: invalid token ID"
      );
    });

    it("应该在销毁时清理相关数据", async function () {
      const tokenURI = await nftContract.tokenURI(0);
      expect(await nftContract.isTokenURIUsed(tokenURI)).to.be.true;
      
      await nftContract.connect(user1).burn(0);
      
      // URI应该可以重新使用
      expect(await nftContract.isTokenURIUsed(tokenURI)).to.be.false;
    });
  });

  describe("统计功能", function () {
    beforeEach(async function () {
      // 铸造不同类型的NFT
      const tokenURIs = ["ipfs://QmTest1", "ipfs://QmTest2", "ipfs://QmTest3"];
      const metadataArray = [
        { ...dlMetadata, assetType: "model" },
        { ...dlMetadata, assetType: "texture" },
        { ...dlMetadata, assetType: "model" }
      ];

      for (let i = 0; i < 3; i++) {
        await nftContract.connect(creator).mintNFT(
          user1.address,
          tokenURIs[i],
          creator.address,
          500,
          metadataArray[i]
        );
      }
    });

    it("应该正确统计按资产类型的Token数量", async function () {
      const modelCount = await nftContract.getTokenCountByAssetType("model");
      const textureCount = await nftContract.getTokenCountByAssetType("texture");
      
      expect(modelCount).to.equal(2);
      expect(textureCount).to.equal(1);
    });

    it("应该正确统计按创建者的Token数量", async function () {
      const creatorCount = await nftContract.getTokenCountByCreator(creator.address);
      expect(creatorCount).to.equal(3);
    });
  });

  describe("接口支持", function () {
    it("应该支持ERC721接口", async function () {
      expect(await nftContract.supportsInterface("0x80ac58cd")).to.be.true;
    });

    it("应该支持ERC2981版税接口", async function () {
      expect(await nftContract.supportsInterface("0x2a55205a")).to.be.true;
    });

    it("应该支持ERC165接口", async function () {
      expect(await nftContract.supportsInterface("0x01ffc9a7")).to.be.true;
    });
  });

  describe("Gas优化测试", function () {
    it("批量铸造应该比单独铸造更节省Gas", async function () {
      const tokenURIs = ["ipfs://QmTest1", "ipfs://QmTest2", "ipfs://QmTest3"];
      const metadataArray = [dlMetadata, dlMetadata, dlMetadata];

      // 单独铸造的Gas消耗
      let individualGas = 0;
      for (let i = 0; i < 3; i++) {
        const tx = await nftContract.connect(creator).mintNFT(
          user1.address,
          `ipfs://QmSingle${i}`,
          creator.address,
          500,
          dlMetadata
        );
        const receipt = await tx.wait();
        individualGas += receipt.gasUsed.toNumber();
      }

      // 批量铸造的Gas消耗
      const batchTx = await nftContract.connect(creator).batchMintNFT(
        user2.address,
        tokenURIs,
        creator.address,
        500,
        metadataArray
      );
      const batchReceipt = await batchTx.wait();
      const batchGas = batchReceipt.gasUsed.toNumber();

      // 批量铸造应该更节省Gas
      expect(batchGas).to.be.lessThan(individualGas);
      console.log(`单独铸造Gas: ${individualGas}, 批量铸造Gas: ${batchGas}, 节省: ${((individualGas - batchGas) / individualGas * 100).toFixed(2)}%`);
    });
  });
});
