/**
 * 版本控制面板样式
 */

.version-control-panel {
  .ant-modal-body {
    padding: 16px;
    max-height: 70vh;
    overflow-y: auto;
  }

  // 通用区域样式
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;

    .ant-typography {
      margin-bottom: 0;
    }
  }

  // 分支列表
  .branches-section {
    .branch-item {
      padding: 12px 0;
      border-bottom: 1px solid #f5f5f5;
      transition: all 0.2s ease;

      &:hover {
        background-color: #f5f5f5;
      }

      &.active {
        background-color: #e6f7ff;
        border-left: 3px solid #1890ff;
        padding-left: 9px;
      }

      &:last-child {
        border-bottom: none;
      }

      .branch-icon {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 14px;

        &.main {
          background: linear-gradient(45deg, #1890ff, #52c41a);
          color: #ffffff;
        }

        &.feature {
          background: linear-gradient(45deg, #52c41a, #13c2c2);
          color: #ffffff;
        }

        &.hotfix {
          background: linear-gradient(45deg, #ff4d4f, #ff7a45);
          color: #ffffff;
        }

        &.release {
          background: linear-gradient(45deg, #722ed1, #eb2f96);
          color: #ffffff;
        }

        &.develop {
          background: linear-gradient(45deg, #faad14, #fa8c16);
          color: #ffffff;
        }

        &.custom {
          background: linear-gradient(45deg, #8c8c8c, #d9d9d9);
          color: #ffffff;
        }
      }

      .branch-info {
        display: flex;
        align-items: center;
      }

      .ant-list-item-meta {
        .ant-list-item-meta-title {
          margin-bottom: 4px;
          font-size: 14px;
        }

        .ant-list-item-meta-description {
          font-size: 12px;
        }
      }
    }
  }

  // 提交历史
  .commits-section {
    .ant-timeline {
      .ant-timeline-item {
        padding-bottom: 16px;

        .commit-item {
          .commit-header {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
          }

          .commit-meta {
            margin-bottom: 4px;
          }

          .commit-stats {
            font-size: 11px;
            color: #8c8c8c;
          }
        }
      }
    }
  }

  // 工作目录
  .working-directory {
    .ant-card {
      .ant-card-head {
        .ant-card-head-title {
          font-size: 14px;
          font-weight: 600;
        }
      }

      .ant-card-body {
        padding: 12px;
      }

      .ant-list {
        .ant-list-item {
          padding: 8px 0;
          border-bottom: 1px solid #f5f5f5;

          &:last-child {
            border-bottom: none;
          }

          .ant-list-item-meta {
            .ant-list-item-meta-title {
              font-size: 13px;
            }

            .ant-list-item-meta-description {
              margin-top: 4px;
            }
          }
        }
      }
    }
  }

  // 文件差异
  .file-diff {
    pre {
      background: #f5f5f5;
      padding: 16px;
      border-radius: 4px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      line-height: 1.5;
      overflow-x: auto;
      white-space: pre-wrap;
      word-wrap: break-word;

      // 差异高亮
      .diff-added {
        background-color: #d4edda;
        color: #155724;
      }

      .diff-removed {
        background-color: #f8d7da;
        color: #721c24;
      }

      .diff-context {
        color: #6c757d;
      }
    }
  }
}

// 文件状态颜色
.version-control-panel {
  .file-status-added {
    color: #52c41a;
  }

  .file-status-modified {
    color: #1890ff;
  }

  .file-status-deleted {
    color: #ff4d4f;
  }

  .file-status-renamed {
    color: #faad14;
  }

  .file-status-untracked {
    color: #8c8c8c;
  }

  .file-status-ignored {
    color: #d9d9d9;
  }
}

// 深色主题
.dark-theme {
  .version-control-panel {
    .ant-modal-content {
      background: #2d2d2d;
      color: #cccccc;
    }

    .section-header {
      border-bottom-color: #404040;

      .ant-typography {
        color: #ffffff;
      }
    }

    .branches-section {
      .branch-item {
        border-bottom-color: #404040;

        &:hover {
          background-color: #404040;
        }

        &.active {
          background-color: #1f3a5f;
        }

        .ant-list-item-meta {
          .ant-list-item-meta-title {
            color: #ffffff;
          }

          .ant-list-item-meta-description {
            color: #cccccc;
          }
        }
      }
    }

    .commits-section {
      .ant-timeline {
        .ant-timeline-item {
          .commit-item {
            .commit-header {
              color: #ffffff;
            }

            .commit-meta {
              color: #cccccc;
            }

            .commit-stats {
              color: #8c8c8c;
            }
          }
        }
      }
    }

    .working-directory {
      .ant-card {
        background: #2d2d2d;
        border-color: #404040;

        .ant-card-head {
          background: #2d2d2d;
          border-bottom-color: #404040;

          .ant-card-head-title {
            color: #ffffff;
          }
        }

        .ant-card-body {
          background: #2d2d2d;
          color: #cccccc;
        }

        .ant-list {
          .ant-list-item {
            border-bottom-color: #404040;

            .ant-list-item-meta {
              .ant-list-item-meta-title {
                color: #ffffff;
              }
            }
          }
        }
      }
    }

    .file-diff {
      pre {
        background: #404040;
        color: #cccccc;
      }
    }
  }
}

// 紧凑模式
.compact-theme {
  .version-control-panel {
    .ant-modal-body {
      padding: 12px;
    }

    .section-header {
      margin-bottom: 12px;
      padding-bottom: 6px;
    }

    .branches-section {
      .branch-item {
        padding: 8px 0;

        .branch-icon {
          width: 28px;
          height: 28px;
          font-size: 12px;
        }

        .ant-list-item-meta {
          .ant-list-item-meta-title {
            font-size: 13px;
          }

          .ant-list-item-meta-description {
            font-size: 11px;
          }
        }
      }
    }

    .commits-section {
      .ant-timeline {
        .ant-timeline-item {
          padding-bottom: 12px;
        }
      }
    }

    .working-directory {
      .ant-card {
        .ant-card-body {
          padding: 8px;
        }

        .ant-list {
          .ant-list-item {
            padding: 6px 0;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .version-control-panel {
    .ant-modal {
      width: 95% !important;
      max-width: none;
    }

    .section-header {
      flex-direction: column;
      align-items: stretch;
      gap: 8px;
    }

    .branches-section {
      .branch-item {
        .ant-list-item-action {
          flex-direction: column;
          gap: 4px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .version-control-panel {
    .file-diff {
      pre {
        font-size: 10px;
        padding: 8px;
      }
    }
  }
}

// 动画效果
.version-control-panel {
  .branch-item {
    transition: all 0.2s ease;
  }

  .commit-item {
    transition: all 0.2s ease;

    &:hover {
      background-color: #f5f5f5;
      padding: 8px;
      border-radius: 4px;
      margin: -8px;
    }
  }

  .ant-btn {
    transition: all 0.2s ease;
  }

  .ant-timeline-item {
    transition: all 0.2s ease;
  }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
  .version-control-panel {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .version-control-panel {
    .branch-item {
      border: 1px solid #000;
      margin-bottom: 4px;

      &.active {
        border-color: #0066cc;
        border-width: 2px;
      }
    }

    .working-directory {
      .ant-card {
        border: 2px solid #000;
      }
    }

    .file-diff {
      pre {
        border: 2px solid #000;
      }
    }
  }
}

// 版本控制特定样式
.version-control-panel {
  .merge-indicator {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, transparent, rgba(24, 144, 255, 0.3), transparent);
      animation: merging 2s infinite;
    }
  }

  .conflict-indicator {
    background: rgba(255, 77, 79, 0.2);
    border: 1px solid #ff4d4f;
    border-radius: 4px;
    padding: 4px 8px;
  }

  .commit-hash {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 11px;
    background: #f5f5f5;
    padding: 2px 4px;
    border-radius: 2px;
  }

  .branch-ahead {
    color: #52c41a;
  }

  .branch-behind {
    color: #ff4d4f;
  }

  .protected-branch {
    position: relative;

    &::before {
      content: '🔒';
      position: absolute;
      top: 2px;
      right: 2px;
      font-size: 10px;
    }
  }
}

@keyframes merging {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
