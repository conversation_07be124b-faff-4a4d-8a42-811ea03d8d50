/**
 * 编辑区域面板组件
 */
import React, { useEffect, useState } from 'react';
import {
  Card,
  List,
  Button,
  Space,
  Typography,
  Tag,
  Empty,
  Tooltip,
  Badge,
  Switch,
  Avatar
} from 'antd';
import {
  UserOutlined,
  EditOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  HighlightOutlined
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { EditingZone, EditingZoneType } from '../../services/ConflictPreventionService';
import { 
  setShowEditingZones,
  setHighlightEditingZones
} from '../../store/collaboration/editingZonesSlice';

const { Text } = Typography;

/**
 * 编辑区域面板组件
 */
const EditingZonesPanel: React.FC = () => {
  const dispatch = useDispatch();
  const zones = useSelector((state: RootState) => state.editingZones.zones);
  const showEditingZones = useSelector((state: RootState) => state.editingZones.showEditingZones);
  const highlightEditingZones = useSelector((state: RootState) => state.editingZones.highlightEditingZones);
  
  // 按用户分组的编辑区域
  const [groupedZones, setGroupedZones] = useState<Record<string, EditingZone[]>>({});
  
  // 更新分组
  useEffect(() => {
    const grouped: Record<string, EditingZone[]> = {};
    
    zones.forEach(zone => {
      if (!grouped[zone.userId]) {
        grouped[zone.userId] = [];
      }
      grouped[zone.userId].push(zone);
    });
    
    setGroupedZones(grouped);
  }, [zones]);
  
  // 处理显示/隐藏编辑区域
  const handleToggleShowZones = (checked: boolean) => {
    dispatch(setShowEditingZones(checked));
  };
  
  // 处理高亮/不高亮编辑区域
  const handleToggleHighlightZones = (checked: boolean) => {
    dispatch(setHighlightEditingZones(checked));
  };
  
  // 处理关闭面板
  const handleClose = () => {
    // 关闭面板的逻辑
  };
  
  // 格式化时间
  const formatTime = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    
    if (diff < 60000) {
      return '刚刚';
    } else if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) {
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      return new Date(timestamp).toLocaleString();
    }
  };
  
  // 获取编辑区域类型名称
  const getZoneTypeName = (type: EditingZoneType) => {
    switch (type) {
      case EditingZoneType.ENTITY:
        return '实体';
      case EditingZoneType.COMPONENT:
        return '组件';
      case EditingZoneType.PROPERTY:
        return '属性';
      case EditingZoneType.SCENE:
        return '场景';
      case EditingZoneType.RESOURCE:
        return '资源';
      default:
        return '未知';
    }
  };
  
  // 获取编辑区域描述
  const getZoneDescription = (zone: EditingZone) => {
    switch (zone.type) {
      case EditingZoneType.ENTITY:
        return `实体: ${zone.entityId}`;
      case EditingZoneType.COMPONENT:
        return `组件: ${zone.entityId}/${zone.componentId}`;
      case EditingZoneType.PROPERTY:
        return `属性: ${zone.entityId}/${zone.propertyPath?.join('.')}`;
      case EditingZoneType.SCENE:
        return '场景设置';
      case EditingZoneType.RESOURCE:
        return `资源: ${zone.resourceId}`;
      default:
        return '未知区域';
    }
  };
  
  // 渲染用户编辑区域
  const renderUserZones = (userId: string, userZones: EditingZone[]) => {
    if (userZones.length === 0) return null;
    
    const userName = userZones[0].userName;
    
    return (
      <div key={userId} className="user-zones">
        <div className="user-header">
          <Space>
            <Avatar 
              icon={<UserOutlined />} 
              style={{ backgroundColor: userZones[0].color }}
            />
            <Text strong>{userName}</Text>
            <Badge count={userZones.length} />
          </Space>
        </div>
        
        <List
          className="zones-list"
          dataSource={userZones}
          renderItem={zone => (
            <List.Item key={zone.id}>
              <List.Item.Meta
                avatar={
                  <div className="zone-icon" style={{ color: zone.color }}>
                    <EditOutlined />
                  </div>
                }
                title={
                  <Space>
                    <Text>{getZoneTypeName(zone.type)}</Text>
                    <Tag color={zone.color}>正在编辑</Tag>
                  </Space>
                }
                description={
                  <div>
                    <div>{getZoneDescription(zone)}</div>
                    <div>
                      <ClockCircleOutlined /> {formatTime(zone.lastUpdateTime)}
                    </div>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </div>
    );
  };
  
  return (
    <div className="editing-zones-panel">
      <Card
        title={
          <Space>
            <EditOutlined />
            <span>实时编辑区域</span>
            <Badge count={zones.length} style={{ backgroundColor: '#52c41a' }} />
          </Space>
        }
        extra={
          <Space>
            <Tooltip title={showEditingZones ? '隐藏编辑区域' : '显示编辑区域'}>
              <Switch
                checkedChildren={<EyeOutlined />}
                unCheckedChildren={<EyeInvisibleOutlined />}
                checked={showEditingZones}
                onChange={handleToggleShowZones}
              />
            </Tooltip>
            <Tooltip title={highlightEditingZones ? '关闭高亮' : '开启高亮'}>
              <Switch
                checkedChildren={<HighlightOutlined />}
                unCheckedChildren={<HighlightOutlined />}
                checked={highlightEditingZones}
                onChange={handleToggleHighlightZones}
              />
            </Tooltip>
            <Button 
              type="text" 
              icon={<CloseCircleOutlined />} 
              onClick={handleClose}
              title="关闭"
            />
          </Space>
        }
        className="zones-card"
      >
        {Object.keys(groupedZones).length > 0 ? (
          <div className="zones-container">
            {Object.entries(groupedZones).map(([userId, userZones]) => 
              renderUserZones(userId, userZones)
            )}
          </div>
        ) : (
          <Empty description="当前没有其他用户正在编辑" />
        )}
      </Card>
    </div>
  );
};

export default EditingZonesPanel;
