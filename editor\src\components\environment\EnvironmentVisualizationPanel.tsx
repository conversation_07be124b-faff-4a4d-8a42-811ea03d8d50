/**
 * 环境可视化面板
 * 
 * 该面板提供环境状态的可视化展示，包括环境参数的图表、热图等。
 */
import React, { useState, useEffect, useRef } from 'react';
import { 
  Card, 
  Tabs, 
  Button, 
  Space, 
  Select, 
  Switch, 
  Slider, 
  Radio, 
  Tooltip, 
  Row, 
  Col,
  Divider,
  message
} from 'antd';
import { 
  AreaChartOutlined, 
  BarChartOutlined, 
  LineChartOutlined, 
  HeatMapOutlined, 
  EyeOutlined,
  EyeInvisibleOutlined,
  ReloadOutlined,
  DownloadOutlined,
  FullscreenOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { EnvironmentAwarenessData } from './types';

// 模拟引入图表库
// 在实际项目中，应该使用 echarts、recharts 或其他图表库
const MockChart = ({ type, data, title }: { type: string, data: any, title: string }) => {
  return (
    <div className="mock-chart" style={{ height: 300, border: '1px solid #eee', padding: 10 }}>
      <h3>{title}</h3>
      <div>{type} 图表 - 数据点: {data.length}</div>
    </div>
  );
};

const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 环境可视化面板属性接口
 */
interface EnvironmentVisualizationPanelProps {
  entityId?: string;
  environmentData?: EnvironmentAwarenessData;
  historicalData?: EnvironmentAwarenessData[];
  onRefresh?: () => void;
  onToggleVisualization?: (visible: boolean) => void;
  visualizationEnabled?: boolean;
}

/**
 * 环境可视化面板组件
 */
const EnvironmentVisualizationPanel: React.FC<EnvironmentVisualizationPanelProps> = ({
  entityId,
  environmentData,
  historicalData = [],
  onRefresh,
  onToggleVisualization,
  visualizationEnabled = false
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [chartType, setChartType] = useState<string>('line');
  const [selectedParameter, setSelectedParameter] = useState<string>('temperature');
  const [timeRange, setTimeRange] = useState<number>(60); // 分钟
  const [updateInterval, setUpdateInterval] = useState<number>(5); // 秒
  const [isAutoUpdate, setIsAutoUpdate] = useState<boolean>(true);
  const [showLegend, setShowLegend] = useState<boolean>(true);
  const [is3D, setIs3D] = useState<boolean>(false);
  const updateTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 参数选项
  const parameterOptions = [
    { value: 'temperature', label: t('environment.temperature') },
    { value: 'humidity', label: t('environment.humidity') },
    { value: 'lightIntensity', label: t('environment.lightIntensity') },
    { value: 'windSpeed', label: t('environment.windSpeed') },
    { value: 'noiseLevel', label: t('environment.noiseLevel') },
    { value: 'airQuality', label: t('environment.airQuality') },
    { value: 'waterLevel', label: t('environment.waterLevel') },
    { value: 'visibility', label: t('environment.visibility') }
  ];

  // 设置自动更新
  useEffect(() => {
    if (isAutoUpdate && onRefresh) {
      updateTimerRef.current = setInterval(() => {
        onRefresh();
      }, updateInterval * 1000);
    }

    return () => {
      if (updateTimerRef.current) {
        clearInterval(updateTimerRef.current);
        updateTimerRef.current = null;
      }
    };
  }, [isAutoUpdate, updateInterval, onRefresh]);

  /**
   * 处理刷新
   */
  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
      message.success(t('environment.dataRefreshed'));
    }
  };

  /**
   * 处理切换可视化
   */
  const handleToggleVisualization = () => {
    if (onToggleVisualization) {
      onToggleVisualization(!visualizationEnabled);
    }
  };

  /**
   * 处理下载数据
   */
  const handleDownloadData = () => {
    if (!environmentData) {
      message.error(t('environment.noDataToDownload'));
      return;
    }

    try {
      // 创建要下载的数据
      const dataToDownload = {
        current: environmentData,
        historical: historicalData,
        timestamp: new Date().toISOString(),
        entityId
      };

      // 转换为JSON字符串
      const jsonString = JSON.stringify(dataToDownload, null, 2);
      
      // 创建Blob对象
      const blob = new Blob([jsonString], { type: 'application/json' });
      
      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `environment_data_${new Date().toISOString()}.json`;
      
      // 触发下载
      document.body.appendChild(a);
      a.click();
      
      // 清理
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      message.success(t('environment.dataDownloaded'));
    } catch (error) {
      console.error('下载数据失败:', error);
      message.error(t('environment.downloadFailed'));
    }
  };

  /**
   * 获取图表数据
   */
  const getChartData = () => {
    if (!historicalData || historicalData.length === 0) {
      return [];
    }

    // 根据时间范围过滤数据
    const now = Date.now();
    const filteredData = historicalData.filter(data => {
      const changeTime = data.lastEnvironmentChangeTime || now;
      return (now - changeTime) <= timeRange * 60 * 1000;
    });

    // 提取选定参数的数据
    return filteredData.map(data => {
      return {
        value: (data as any)[selectedParameter],
        timestamp: data.lastEnvironmentChangeTime || Date.now()
      };
    });
  };

  /**
   * 渲染概览标签页
   */
  const renderOverviewTab = () => {
    if (!environmentData) {
      return (
        <div className="no-data">
          <p>{t('environment.noData')}</p>
          <Button type="primary" onClick={handleRefresh}>
            {t('environment.refresh')}
          </Button>
        </div>
      );
    }

    return (
      <div className="overview-tab">
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <MockChart 
              type="line" 
              data={getChartData()} 
              title={t('environment.temperatureOverTime')}
            />
          </Col>
          <Col span={12}>
            <MockChart 
              type="area" 
              data={getChartData()} 
              title={t('environment.humidityOverTime')}
            />
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={12}>
            <MockChart 
              type="bar" 
              data={getChartData()} 
              title={t('environment.lightIntensityOverTime')}
            />
          </Col>
          <Col span={12}>
            <MockChart 
              type="line" 
              data={getChartData()} 
              title={t('environment.windSpeedOverTime')}
            />
          </Col>
        </Row>
      </div>
    );
  };

  /**
   * 渲染详细图表标签页
   */
  const renderDetailedChartTab = () => {
    return (
      <div className="detailed-chart-tab">
        <div className="chart-controls">
          <Row gutter={16}>
            <Col span={8}>
              <div className="control-group">
                <label>{t('environment.parameter')}:</label>
                <Select
                  value={selectedParameter}
                  onChange={setSelectedParameter}
                  style={{ width: '100%' }}
                >
                  {parameterOptions.map(option => (
                    <Option key={option.value} value={option.value}>{option.label}</Option>
                  ))}
                </Select>
              </div>
            </Col>
            <Col span={8}>
              <div className="control-group">
                <label>{t('environment.chartType')}:</label>
                <Radio.Group value={chartType} onChange={e => setChartType(e.target.value)}>
                  <Radio.Button value="line"><LineChartOutlined /></Radio.Button>
                  <Radio.Button value="area"><AreaChartOutlined /></Radio.Button>
                  <Radio.Button value="bar"><BarChartOutlined /></Radio.Button>
                </Radio.Group>
              </div>
            </Col>
            <Col span={8}>
              <div className="control-group">
                <label>{t('environment.timeRange')}:</label>
                <Select
                  value={timeRange}
                  onChange={setTimeRange}
                  style={{ width: '100%' }}
                >
                  <Option value={5}>5 {t('environment.minutes')}</Option>
                  <Option value={15}>15 {t('environment.minutes')}</Option>
                  <Option value={30}>30 {t('environment.minutes')}</Option>
                  <Option value={60}>1 {t('environment.hour')}</Option>
                  <Option value={360}>6 {t('environment.hours')}</Option>
                  <Option value={1440}>24 {t('environment.hours')}</Option>
                </Select>
              </div>
            </Col>
          </Row>

          <Row gutter={16} style={{ marginTop: 16 }}>
            <Col span={8}>
              <div className="control-group">
                <label>{t('environment.updateInterval')}:</label>
                <Slider
                  min={1}
                  max={60}
                  value={updateInterval}
                  onChange={setUpdateInterval}
                  marks={{ 1: '1s', 15: '15s', 30: '30s', 60: '60s' }}
                />
              </div>
            </Col>
            <Col span={8}>
              <div className="control-group">
                <Space>
                  <label>{t('environment.autoUpdate')}:</label>
                  <Switch checked={isAutoUpdate} onChange={setIsAutoUpdate} />
                </Space>
              </div>
            </Col>
            <Col span={8}>
              <div className="control-group">
                <Space>
                  <label>{t('environment.showLegend')}:</label>
                  <Switch checked={showLegend} onChange={setShowLegend} />
                  <label>{t('environment.3dView')}:</label>
                  <Switch checked={is3D} onChange={setIs3D} />
                </Space>
              </div>
            </Col>
          </Row>
        </div>

        <Divider />

        <div className="chart-container">
          <MockChart 
            type={chartType} 
            data={getChartData()} 
            title={`${parameterOptions.find(p => p.value === selectedParameter)?.label} ${t('environment.overTime')}`}
          />
        </div>
      </div>
    );
  };

  /**
   * 渲染热图标签页
   */
  const renderHeatmapTab = () => {
    return (
      <div className="heatmap-tab">
        <div className="heatmap-controls">
          <Row gutter={16}>
            <Col span={12}>
              <div className="control-group">
                <label>{t('environment.parameter')}:</label>
                <Select
                  value={selectedParameter}
                  onChange={setSelectedParameter}
                  style={{ width: '100%' }}
                >
                  {parameterOptions.map(option => (
                    <Option key={option.value} value={option.value}>{option.label}</Option>
                  ))}
                </Select>
              </div>
            </Col>
            <Col span={12}>
              <div className="control-group">
                <Space>
                  <label>{t('environment.3dView')}:</label>
                  <Switch checked={is3D} onChange={setIs3D} />
                </Space>
              </div>
            </Col>
          </Row>
        </div>

        <Divider />

        <div className="heatmap-container">
          <div className="mock-heatmap" style={{ height: 400, border: '1px solid #eee', padding: 10 }}>
            <h3>{t('environment.environmentHeatmap')}</h3>
            <p>{t('environment.heatmapDescription')}</p>
            <div style={{ 
              height: 300, 
              background: 'linear-gradient(to right, blue, green, yellow, red)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: 'bold'
            }}>
              {t('environment.heatmapPlaceholder')}
            </div>
          </div>
        </div>
      </div>
    );
  };

  /**
   * 渲染3D可视化标签页
   */
  const render3DVisualizationTab = () => {
    return (
      <div className="visualization-3d-tab">
        <div className="visualization-controls">
          <Row gutter={16}>
            <Col span={12}>
              <div className="control-group">
                <label>{t('environment.visualizationType')}:</label>
                <Select
                  value="particles"
                  style={{ width: '100%' }}
                >
                  <Option value="particles">{t('environment.particles')}</Option>
                  <Option value="colorMap">{t('environment.colorMap')}</Option>
                  <Option value="icons">{t('environment.icons')}</Option>
                </Select>
              </div>
            </Col>
            <Col span={12}>
              <div className="control-group">
                <label>{t('environment.visualizationDensity')}:</label>
                <Slider
                  min={1}
                  max={10}
                  defaultValue={5}
                  marks={{ 1: t('environment.low'), 5: t('environment.medium'), 10: t('environment.high') }}
                />
              </div>
            </Col>
          </Row>
        </div>

        <Divider />

        <div className="visualization-container">
          <div className="mock-3d-visualization" style={{ height: 400, border: '1px solid #eee', padding: 10 }}>
            <h3>{t('environment.3dVisualization')}</h3>
            <p>{t('environment.3dVisualizationDescription')}</p>
            <div style={{ 
              height: 300, 
              background: 'radial-gradient(circle, #1a1a2e, #16213e, #0f3460)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: 'bold'
            }}>
              {t('environment.3dVisualizationPlaceholder')}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="environment-visualization-panel">
      <Card
        title={
          <Space>
            <AreaChartOutlined />
            <span>{t('environment.visualizationPanel')}</span>
          </Space>
        }
        extra={
          <Space>
            <Tooltip title={t('environment.toggleVisualization')}>
              <Button
                type="text"
                icon={visualizationEnabled ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                onClick={handleToggleVisualization}
              />
            </Tooltip>
            <Tooltip title={t('environment.refresh')}>
              <Button
                type="text"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              />
            </Tooltip>
            <Tooltip title={t('environment.downloadData')}>
              <Button
                type="text"
                icon={<DownloadOutlined />}
                onClick={handleDownloadData}
              />
            </Tooltip>
            <Tooltip title={t('environment.fullscreen')}>
              <Button
                type="text"
                icon={<FullscreenOutlined />}
              />
            </Tooltip>
          </Space>
        }
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <span>
                <AreaChartOutlined />
                {t('environment.overview')}
              </span>
            }
            key="overview"
          >
            {renderOverviewTab()}
          </TabPane>
          <TabPane
            tab={
              <span>
                <LineChartOutlined />
                {t('environment.detailedChart')}
              </span>
            }
            key="detailedChart"
          >
            {renderDetailedChartTab()}
          </TabPane>
          <TabPane
            tab={
              <span>
                <HeatMapOutlined />
                {t('environment.heatmap')}
              </span>
            }
            key="heatmap"
          >
            {renderHeatmapTab()}
          </TabPane>
          <TabPane
            tab={
              <span>
                <EyeOutlined />
                {t('environment.3dVisualization')}
              </span>
            }
            key="3dVisualization"
          >
            {render3DVisualizationTab()}
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default EnvironmentVisualizationPanel;
