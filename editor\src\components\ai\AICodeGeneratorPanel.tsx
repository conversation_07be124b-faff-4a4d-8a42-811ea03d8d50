/**
 * AI代码生成器面板组件
 * 提供智能代码生成功能
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Space,
  Typography,
  Steps,
  List,
  Tag,
  Progress,
  Modal,
  Tabs,
  Checkbox,
  Row,
  Col,
  Divider,
  Alert,
  Tooltip,
  Collapse,
  Rate
} from 'antd';
import {
  CodeOutlined,
  RobotOutlined,
  DownloadOutlined,
  EyeOutlined,
  CopyOutlined,
  LoadingOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import AICodeGeneratorService, {
  CodeGenerationRequest,
  GeneratedCode,
  CodeType,
  FrameworkType,
  StyleType,
  PropertyDefinition,
  StateDefinition,
  EventDefinition
} from '../../services/AICodeGeneratorService';
import './AICodeGeneratorPanel.less';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { Step } = Steps;
const { TabPane } = Tabs;
const { Panel } = Collapse;

interface AICodeGeneratorPanelProps {
  visible: boolean;
  onClose: () => void;
}

const AICodeGeneratorPanel: React.FC<AICodeGeneratorPanelProps> = ({
  visible,
  onClose
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [currentStep, setCurrentStep] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedCodes, setGeneratedCodes] = useState<GeneratedCode[]>([]);
  const [selectedCode, setSelectedCode] = useState<GeneratedCode | null>(null);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [props, setProps] = useState<PropertyDefinition[]>([]);
  const [states, setStates] = useState<StateDefinition[]>([]);
  const [events] = useState<EventDefinition[]>([]);

  const codeGeneratorService = AICodeGeneratorService.getInstance();

  useEffect(() => {
    if (visible) {
      loadData();
      setupEventListeners();
    }

    return () => {
      cleanupEventListeners();
    };
  }, [visible]);

  const setupEventListeners = () => {
    codeGeneratorService.on('codeGenerationStarted', handleGenerationStarted);
    codeGeneratorService.on('codeGenerationCompleted', handleGenerationCompleted);
    codeGeneratorService.on('codeGenerationError', handleGenerationError);
  };

  const cleanupEventListeners = () => {
    codeGeneratorService.off('codeGenerationStarted', handleGenerationStarted);
    codeGeneratorService.off('codeGenerationCompleted', handleGenerationCompleted);
    codeGeneratorService.off('codeGenerationError', handleGenerationError);
  };

  const loadData = () => {
    setGeneratedCodes(codeGeneratorService.getGeneratedCodes());
    setIsGenerating(codeGeneratorService.isGenerationInProgress());
  };

  const handleGenerationStarted = () => {
    setIsGenerating(true);
  };

  const handleGenerationCompleted = (code: GeneratedCode) => {
    setGeneratedCodes(prev => [...prev, code]);
    setIsGenerating(false);
    setCurrentStep(2);
  };

  const handleGenerationError = (error: any) => {
    setIsGenerating(false);
    console.error('Code generation failed:', error);
  };

  const handleGenerateCode = async () => {
    try {
      const values = await form.validateFields();
      
      const request: CodeGenerationRequest = {
        id: `code_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: values.type,
        framework: values.framework,
        styleType: values.styleType,
        description: values.description,
        requirements: {
          functionality: values.functionality?.split(',').map((f: string) => f.trim()) || [],
          props,
          state: states,
          events,
          styling: {
            theme: values.theme,
            colors: values.colors?.split(',').map((c: string) => c.trim()) || [],
            layout: values.layout,
            animations: values.animations,
            responsive: values.responsive
          },
          accessibility: values.accessibility,
          responsive: values.responsive,
          testing: values.testing
        },
        options: {
          includeComments: values.includeComments,
          includeTypes: values.includeTypes,
          includeTests: values.includeTests,
          includeStorybook: values.includeStorybook,
          optimizeForPerformance: values.optimizeForPerformance,
          followBestPractices: values.followBestPractices
        }
      };

      await codeGeneratorService.generateCode(request);
    } catch (error) {
      console.error('Failed to generate code:', error);
    }
  };

  const handlePreviewCode = (code: GeneratedCode) => {
    setSelectedCode(code);
    setPreviewModalVisible(true);
  };

  const handleDownloadCode = (code: GeneratedCode) => {
    try {
      const files = codeGeneratorService.exportCode(code.id, 'files') as any[];
      
      // 创建ZIP文件并下载
      files.forEach(file => {
        const blob = new Blob([file.content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = file.name;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      });
    } catch (error) {
      console.error('Failed to download code:', error);
    }
  };

  const handleCopyCode = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  // 添加属性
  const addProp = () => {
    setProps(prev => [...prev, {
      name: '',
      type: 'string',
      required: true,
      description: ''
    }]);
  };

  // 添加状态
  const addState = () => {
    setStates(prev => [...prev, {
      name: '',
      type: 'string',
      initialValue: '',
      description: ''
    }]);
  };

  // 添加事件功能已移除（未使用）

  // 渲染配置步骤
  const renderConfigStep = () => (
    <Card title={t('ai.codeGen.configuration')} className="step-card">
      <Form form={form} layout="vertical">
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Form.Item
              name="type"
              label={t('ai.codeGen.codeType')}
              rules={[{ required: true }]}
            >
              <Select placeholder={t('ai.codeGen.selectCodeType') || ''}>
                <Option value={CodeType.COMPONENT}>{t('ai.codeGen.component')}</Option>
                <Option value={CodeType.STYLE}>{t('ai.codeGen.style')}</Option>
                <Option value={CodeType.LOGIC}>{t('ai.codeGen.logic')}</Option>
                <Option value={CodeType.TEST}>{t('ai.codeGen.test')}</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="framework"
              label={t('ai.codeGen.framework')}
              rules={[{ required: true }]}
            >
              <Select placeholder={t('ai.codeGen.selectFramework') || ''}>
                <Option value={FrameworkType.REACT}>React</Option>
                <Option value={FrameworkType.VUE}>Vue</Option>
                <Option value={FrameworkType.ANGULAR}>Angular</Option>
                <Option value={FrameworkType.VANILLA}>Vanilla JS</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="description"
          label={t('ai.codeGen.description')}
          rules={[{ required: true }]}
        >
          <TextArea
            rows={3}
            placeholder={t('ai.codeGen.descriptionPlaceholder') || ''}
          />
        </Form.Item>

        <Form.Item
          name="functionality"
          label={t('ai.codeGen.functionality')}
        >
          <TextArea
            rows={2}
            placeholder={t('ai.codeGen.functionalityPlaceholder') || ''}
          />
        </Form.Item>

        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Form.Item
              name="styleType"
              label={t('ai.codeGen.styleType')}
            >
              <Select placeholder={t('ai.codeGen.selectStyleType') || ''}>
                <Option value={StyleType.CSS}>CSS</Option>
                <Option value={StyleType.SCSS}>SCSS</Option>
                <Option value={StyleType.LESS}>LESS</Option>
                <Option value={StyleType.STYLED_COMPONENTS}>Styled Components</Option>
                <Option value={StyleType.TAILWIND}>Tailwind CSS</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="theme"
              label={t('ai.codeGen.theme')}
            >
              <Select placeholder={t('ai.codeGen.selectTheme') || ''}>
                <Option value="light">{t('ai.codeGen.light')}</Option>
                <Option value="dark">{t('ai.codeGen.dark')}</Option>
                <Option value="auto">{t('ai.codeGen.auto')}</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Divider>{t('ai.codeGen.options')}</Divider>

        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Form.Item name="includeComments" valuePropName="checked">
              <Checkbox>{t('ai.codeGen.includeComments')}</Checkbox>
            </Form.Item>
            <Form.Item name="includeTypes" valuePropName="checked">
              <Checkbox>{t('ai.codeGen.includeTypes')}</Checkbox>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="includeTests" valuePropName="checked">
              <Checkbox>{t('ai.codeGen.includeTests')}</Checkbox>
            </Form.Item>
            <Form.Item name="includeStorybook" valuePropName="checked">
              <Checkbox>{t('ai.codeGen.includeStorybook')}</Checkbox>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="accessibility" valuePropName="checked">
              <Checkbox>{t('ai.codeGen.accessibility')}</Checkbox>
            </Form.Item>
            <Form.Item name="responsive" valuePropName="checked">
              <Checkbox>{t('ai.codeGen.responsive')}</Checkbox>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Card>
  );

  // 渲染详细配置步骤
  const renderDetailConfigStep = () => (
    <Card title={t('ai.codeGen.detailConfiguration')} className="step-card">
      <Collapse>
        <Panel header={t('ai.codeGen.properties')} key="props">
          <Space direction="vertical" style={{ width: '100%' }}>
            {props.map((prop, index) => (
              <Row key={index} gutter={[8, 8]} align="middle">
                <Col span={6}>
                  <Input
                    placeholder={t('ai.codeGen.propName') || ''}
                    value={prop.name}
                    onChange={(e) => {
                      const newProps = [...props];
                      newProps[index].name = e.target.value;
                      setProps(newProps);
                    }}
                  />
                </Col>
                <Col span={4}>
                  <Select
                    value={prop.type}
                    onChange={(value) => {
                      const newProps = [...props];
                      newProps[index].type = value;
                      setProps(newProps);
                    }}
                  >
                    <Option value="string">string</Option>
                    <Option value="number">number</Option>
                    <Option value="boolean">boolean</Option>
                    <Option value="object">object</Option>
                    <Option value="array">array</Option>
                  </Select>
                </Col>
                <Col span={3}>
                  <Checkbox
                    checked={prop.required}
                    onChange={(e) => {
                      const newProps = [...props];
                      newProps[index].required = e.target.checked;
                      setProps(newProps);
                    }}
                  >
                    {t('ai.codeGen.required')}
                  </Checkbox>
                </Col>
                <Col span={8}>
                  <Input
                    placeholder={t('ai.codeGen.description') || ''}
                    value={prop.description}
                    onChange={(e) => {
                      const newProps = [...props];
                      newProps[index].description = e.target.value;
                      setProps(newProps);
                    }}
                  />
                </Col>
                <Col span={3}>
                  <Button
                    type="text"
                    danger
                    onClick={() => setProps(prev => prev.filter((_, i) => i !== index))}
                  >
                    {t('common.delete')}
                  </Button>
                </Col>
              </Row>
            ))}
            <Button type="dashed" onClick={addProp} block>
              {t('ai.codeGen.addProperty')}
            </Button>
          </Space>
        </Panel>

        <Panel header={t('ai.codeGen.states')} key="states">
          <Space direction="vertical" style={{ width: '100%' }}>
            {states.map((state, index) => (
              <Row key={index} gutter={[8, 8]} align="middle">
                <Col span={6}>
                  <Input
                    placeholder={t('ai.codeGen.stateName') || ''}
                    value={state.name}
                    onChange={(e) => {
                      const newStates = [...states];
                      newStates[index].name = e.target.value;
                      setStates(newStates);
                    }}
                  />
                </Col>
                <Col span={4}>
                  <Select
                    value={state.type}
                    onChange={(value) => {
                      const newStates = [...states];
                      newStates[index].type = value;
                      setStates(newStates);
                    }}
                  >
                    <Option value="string">string</Option>
                    <Option value="number">number</Option>
                    <Option value="boolean">boolean</Option>
                    <Option value="object">object</Option>
                    <Option value="array">array</Option>
                  </Select>
                </Col>
                <Col span={6}>
                  <Input
                    placeholder={t('ai.codeGen.initialValue') || ''}
                    value={state.initialValue}
                    onChange={(e) => {
                      const newStates = [...states];
                      newStates[index].initialValue = e.target.value;
                      setStates(newStates);
                    }}
                  />
                </Col>
                <Col span={5}>
                  <Input
                    placeholder={t('ai.codeGen.description') || ''}
                    value={state.description}
                    onChange={(e) => {
                      const newStates = [...states];
                      newStates[index].description = e.target.value;
                      setStates(newStates);
                    }}
                  />
                </Col>
                <Col span={3}>
                  <Button
                    type="text"
                    danger
                    onClick={() => setStates(prev => prev.filter((_, i) => i !== index))}
                  >
                    {t('common.delete')}
                  </Button>
                </Col>
              </Row>
            ))}
            <Button type="dashed" onClick={addState} block>
              {t('ai.codeGen.addState')}
            </Button>
          </Space>
        </Panel>
      </Collapse>

      <Divider />

      <Button
        type="primary"
        icon={<ThunderboltOutlined />}
        onClick={handleGenerateCode}
        loading={isGenerating}
        size="large"
        block
      >
        {t('ai.codeGen.generateCode')}
      </Button>
    </Card>
  );

  // 渲染结果步骤
  const renderResultStep = () => (
    <Card title={t('ai.codeGen.generatedCode')} className="step-card">
      {generatedCodes.length > 0 ? (
        <List
          dataSource={generatedCodes}
          renderItem={(code) => (
            <List.Item
              actions={[
                <Tooltip title={t('ai.codeGen.preview')}>
                  <Button
                    type="text"
                    icon={<EyeOutlined />}
                    onClick={() => handlePreviewCode(code)}
                  />
                </Tooltip>,
                <Tooltip title={t('ai.codeGen.download')}>
                  <Button
                    type="text"
                    icon={<DownloadOutlined />}
                    onClick={() => handleDownloadCode(code)}
                  />
                </Tooltip>
              ]}
            >
              <List.Item.Meta
                avatar={<CodeOutlined style={{ fontSize: '24px', color: '#1890ff' }} />}
                title={
                  <Space>
                    <Text strong>{code.request.description}</Text>
                    <Tag color="blue">{code.request.framework}</Tag>
                    <Tag color="green">{code.request.type}</Tag>
                  </Space>
                }
                description={
                  <div>
                    <Text type="secondary">
                      {t('ai.codeGen.filesGenerated', { count: code.files.length })}
                    </Text>
                    <br />
                    <Space style={{ marginTop: 8 }}>
                      <Text type="secondary">
                        {t('ai.codeGen.quality')}: 
                      </Text>
                      <Progress
                        percent={code.quality.overall}
                        size="small"
                        strokeColor="#52c41a"
                        style={{ width: 100 }}
                      />
                      <Rate
                        disabled
                        count={5}
                        value={Math.round(code.quality.overall / 20)}
                        style={{ fontSize: '12px' }}
                      />
                    </Space>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      ) : (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <CodeOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
          <Title level={4} type="secondary">
            {t('ai.codeGen.noCodeGenerated')}
          </Title>
        </div>
      )}
    </Card>
  );

  const steps = [
    {
      title: t('ai.codeGen.configuration'),
      content: renderConfigStep()
    },
    {
      title: t('ai.codeGen.detailConfiguration'),
      content: renderDetailConfigStep()
    },
    {
      title: t('ai.codeGen.results'),
      content: renderResultStep()
    }
  ];

  return (
    <Modal
      title={
        <Space>
          <RobotOutlined />
          {t('ai.codeGen.aiCodeGenerator')}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1200}
      footer={null}
      className="ai-code-generator-panel"
    >
      <div className="code-generator-content">
        <Steps current={currentStep} onChange={setCurrentStep} style={{ marginBottom: 24 }}>
          {steps.map(item => (
            <Step key={item.title} title={item.title} />
          ))}
        </Steps>

        <div className="step-content">
          {steps[currentStep].content}
        </div>

        {isGenerating && (
          <Alert
            message={t('ai.codeGen.generating')}
            description={t('ai.codeGen.generatingDescription')}
            type="info"
            showIcon
            icon={<LoadingOutlined />}
            style={{ marginTop: 16 }}
          />
        )}
      </div>

      {/* 代码预览对话框 */}
      <Modal
        title={t('ai.codeGen.codePreview')}
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        width={1000}
        footer={[
          <Button key="close" onClick={() => setPreviewModalVisible(false)}>
            {t('common.close')}
          </Button>
        ]}
      >
        {selectedCode && (
          <Tabs defaultActiveKey="0">
            {selectedCode.files.map((file, index) => (
              <TabPane tab={file.name} key={index.toString()}>
                <div style={{ position: 'relative' }}>
                  <Button
                    type="text"
                    icon={<CopyOutlined />}
                    onClick={() => handleCopyCode(file.content)}
                    style={{ position: 'absolute', top: 8, right: 8, zIndex: 1 }}
                  >
                    {t('ai.codeGen.copy')}
                  </Button>
                  <pre style={{ 
                    maxHeight: 500, 
                    overflow: 'auto', 
                    backgroundColor: '#f6f8fa', 
                    padding: 16,
                    borderRadius: 4
                  }}>
                    {file.content}
                  </pre>
                </div>
              </TabPane>
            ))}
          </Tabs>
        )}
      </Modal>
    </Modal>
  );
};

export default AICodeGeneratorPanel;
