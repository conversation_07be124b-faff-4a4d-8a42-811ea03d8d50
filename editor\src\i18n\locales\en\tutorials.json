{"tutorials": {"title": "Tutorials", "start": "Start Tutorial", "next": "Next", "previous": "Previous", "skip": "<PERSON><PERSON>", "finish": "Finish", "restart": "<PERSON><PERSON>", "close": "Close", "progress": "Progress", "step": "Step", "of": "of", "completed": "Completed", "inProgress": "In Progress", "notStarted": "Not Started", "difficulty": {"beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced", "expert": "Expert"}, "categories": {"basics": "Basics", "modeling": "Modeling", "animation": "Animation", "materials": "Materials", "lighting": "Lighting", "physics": "Physics", "scripting": "Scripting", "ui": "User Interface", "optimization": "Optimization", "collaboration": "Collaboration"}, "basicTutorials": {"gettingStarted": {"title": "Getting Started", "description": "Learn the basic operations and interface of the editor", "steps": {"welcome": "Welcome to DL Engine Editor", "interface": "Understanding the Editor Interface", "navigation": "Learning Scene Navigation", "selection": "Selecting and Manipulating Objects", "properties": "Editing Object Properties"}}, "sceneCreation": {"title": "Creating Scenes", "description": "Learn how to create and manage scenes", "steps": {"newScene": "Creating a New Scene", "addObjects": "Adding Objects to Scene", "hierarchy": "Understanding Scene Hierarchy", "saveScene": "Saving the Scene"}}, "objectManipulation": {"title": "Object Manipulation", "description": "Learn how to manipulate 3D objects", "steps": {"transform": "Transforming Objects", "duplicate": "Duplicating Objects", "group": "Organizing Objects", "delete": "Deleting Objects"}}}, "animationTutorials": {"basicAnimation": {"title": "Basic Animation", "description": "Learn to create simple animations", "steps": {"timeline": "Understanding the Timeline", "keyframes": "Setting Keyframes", "interpolation": "Understanding Interpolation", "playback": "Playing Animations"}}, "characterAnimation": {"title": "Character Animation", "description": "Learn character animation creation", "steps": {"rigging": "Character Rigging", "skinning": "Skinning Setup", "posing": "Pose Adjustment", "walking": "Creating Walk Animation"}}}, "materialTutorials": {"basicMaterials": {"title": "Basic Materials", "description": "Learn the basic concepts of materials", "steps": {"create": "Creating Materials", "properties": "Setting Material Properties", "textures": "Applying Textures", "preview": "Previewing Material Effects"}}, "advancedMaterials": {"title": "Advanced Materials", "description": "Learn advanced material techniques", "steps": {"pbr": "PBR Materials", "nodes": "Node Editor", "shaders": "Custom Shaders", "optimization": "Material Optimization"}}}, "physicsTutorials": {"basicPhysics": {"title": "Basic Physics", "description": "Learn basic usage of the physics system", "steps": {"rigidbody": "Rigidbody Component", "colliders": "Colliders", "forces": "Applying Forces", "simulation": "Physics Simulation"}}, "characterController": {"title": "Character Controller", "description": "Learn character physics control", "steps": {"setup": "Setting up Character Controller", "movement": "Movement Control", "jumping": "Jumping Mechanism", "collision": "Collision Detection"}}}, "scriptingTutorials": {"introduction": {"title": "Scripting Introduction", "description": "Learn scripting programming basics", "steps": {"basics": "Scripting Basics", "components": "Component System", "events": "Event Handling", "debugging": "Debugging Techniques"}}, "gameLogic": {"title": "Game Logic", "description": "Implementing game logic", "steps": {"input": "Input Handling", "state": "State Management", "ai": "AI Behavior", "ui": "UI Interaction"}}}, "tips": {"title": "Tips", "hotkeys": "Hotkeys", "workflow": "Workflow", "bestPractices": "Best Practices", "troubleshooting": "Troubleshooting"}, "feedback": {"helpful": "Was this tutorial helpful?", "yes": "Yes", "no": "No", "improve": "How can we improve this tutorial?", "submit": "Submit <PERSON>", "thanks": "Thank you for your feedback!"}, "errors": {"loadFailed": "Failed to load tutorial", "stepFailed": "Failed to execute step", "validationFailed": "Validation failed", "timeout": "Operation timeout"}, "messages": {"tutorialStarted": "Tutorial started", "tutorialCompleted": "Tutorial completed", "stepCompleted": "Step completed", "allTutorialsCompleted": "All tutorials completed", "progressSaved": "Progress saved"}, "materialEditing": {"title": "Material Editing Tutorial", "description": "Learn how to create and edit materials", "steps": {"introduction": {"title": "Material Editing Introduction", "description": "Welcome to the Material Editing Tutorial! In this tutorial, you will learn how to create and edit various types of materials. We'll start with basic material concepts and gradually dive into advanced material techniques."}, "createMaterial": {"title": "Create New Material", "description": "First, we need to create a new material. Right-click in the project panel and select 'Create' > 'Material'. This will create a new material asset that you can edit."}, "selectMaterialType": {"title": "Select Material Type", "description": "Choose the material type that suits your needs. Common types include Standard Material, PBR Material, Unlit Material, etc. Each type has different properties and uses."}, "setBasicProperties": {"title": "Set Basic Properties", "description": "Set the basic properties of the material, such as color, metallic, roughness, etc. These properties determine the basic appearance and lighting response of the material."}, "addTextures": {"title": "Add Textures", "description": "Add texture maps to the material. You can drag texture files to the corresponding texture slots, such as diffuse map, normal map, roughness map, etc."}, "adjustTextureSettings": {"title": "Adjust Texture Settings", "description": "Adjust texture settings such as tiling, offset, rotation, etc. These settings can help you achieve the desired texture effect."}, "setShaderVariants": {"title": "<PERSON> Shader Variants", "description": "Enable or disable shader variants as needed, such as transparency, double-sided rendering, vertex colors, etc. These options can change the rendering behavior of the material."}, "previewMaterial": {"title": "Preview Material", "description": "View the material effect in the material preview window. You can use different preview models and lighting environments to test the material's appearance."}, "applyToObject": {"title": "Apply to Object", "description": "Apply the created material to objects in the scene. You can drag the material onto objects or select the material in the object's material properties."}, "saveMaterial": {"title": "Save Material", "description": "Save your material settings. Click the save button or use the shortcut Ctrl+S to save the material asset, ensuring your work is not lost."}, "completion": {"title": "Tutorial Complete", "description": "Congratulations on completing the Material Editing Tutorial! You now have mastered core skills like creating materials, setting properties, and adding textures. Keep practicing and exploring, and you'll be able to create more beautiful and complex material effects."}}}, "visualScripting": {"title": "Visual Scripting Tutorial", "description": "Learn how to use the visual scripting system", "steps": {"introduction": {"title": "Visual Scripting Introduction", "description": "Welcome to the Visual Scripting Tutorial! In this tutorial, you will learn how to use the editor's visual scripting system to create game logic. We'll start with basic node concepts and gradually dive into complex script writing."}, "openEditor": {"title": "Open Visual Script Editor", "description": "First, we need to open the visual script editor. Click on 'Window' in the menu bar, then select 'Visual Script Editor'. This will open a workspace specifically designed for creating and editing scripts."}, "createNewScript": {"title": "Create <PERSON>", "description": "Click the 'New Script' button to create a new visual script. Name your script, which will help you manage multiple scripts in your project."}, "understandInterface": {"title": "Understand Interface", "description": "Familiarize yourself with the visual script editor interface. Main areas include: Node Canvas (center), Node Library (left), Properties Panel (right), and Variables Panel (bottom)."}, "addEventNode": {"title": "Add Event Node", "description": "Drag an event node from the node library to the canvas. Event nodes are the starting points of scripts, such as 'Start', 'Update', or 'Collision' events."}, "addVariableNode": {"title": "Add Variable Node", "description": "Create a new variable in the variables panel, then drag it to the canvas. Variables are used to store and pass data."}, "addLogicNode": {"title": "Add Logic Node", "description": "Add a logic node from the logic category in the node library, such as 'Condition' or 'Loop' nodes. These nodes control the execution flow of scripts."}, "connectNodes": {"title": "Connect Nodes", "description": "Connect nodes by dragging from an output port of one node to an input port of another. Connection lines represent data or execution flow transfer."}, "addActionNode": {"title": "Add Action Node", "description": "Add an action node from the actions category, such as 'Move Object' or 'Play Sound'. Action nodes perform specific game operations."}, "configureNodeProperties": {"title": "Configure Node Properties", "description": "Select a node and configure its parameters in the properties panel. Different nodes have different properties, such as movement speed, color values, etc."}, "createSubgraph": {"title": "Create Subgraph", "description": "Organize a group of related nodes into a subgraph, which helps keep scripts clean and reusable. Select multiple nodes, then click the 'Create Subgraph' button."}, "addComments": {"title": "Add Comments", "description": "Add comments to your script to explain complex logic. Click the comment button, then drag on the canvas to create a comment area."}, "debugScript": {"title": "Debug Script", "description": "Use debugging features to test your script. Click the debug button, and the script will run in debug mode where you can see data flow and execution paths."}, "saveScript": {"title": "<PERSON>", "description": "After completing script writing, remember to save your work. Click the save button or use the shortcut Ctrl+S to save the script file."}, "attachToObject": {"title": "Attach to Object", "description": "Attach the script to an object in the scene. Select an object in the hierarchy panel, then drag the script onto that object, or add a script component in the object's component panel."}, "completion": {"title": "Tutorial Complete", "description": "Congratulations on completing the Visual Scripting Tutorial! You now have mastered core skills like creating nodes, connecting logic, and debugging scripts. Keep practicing and exploring, and you'll be able to create more complex and interesting game logic."}}}, "animationSystem": {"title": "Animation System Tutorial", "description": "Learn how to create and edit animations", "steps": {"introduction": {"title": "Animation System Introduction", "description": "Welcome to the Animation System Tutorial! In this tutorial, you will learn how to use the editor's animation system to create beautiful animations. We'll start with basic animation concepts and gradually dive into advanced animation techniques."}, "openAnimationEditor": {"title": "Open Animation Editor", "description": "First, we need to open the animation editor. Click on 'Window' in the menu bar, then select 'Animation Editor'. This will open a workspace specifically designed for creating and editing animations."}, "importAnimationModel": {"title": "Import Animation Model", "description": "To create animations, we first need a 3D model. Click the 'Import' button and select a 3D model file with a skeleton. Make sure the model has been properly rigged with a bone system."}, "checkSkeleton": {"title": "Check Skeleton Structure", "description": "After importing the model, we need to check if the skeleton structure is correct. In the skeleton view, you can see the model's bone hierarchy. Ensure all bones are properly connected and clearly named."}, "createAnimationClip": {"title": "Create Animation Clip", "description": "Now let's create our first animation clip. Click the 'Create Clip' button and name your animation clip. Animation clips are the basic units of animation, containing a series of keyframe data."}, "setKeyframes": {"title": "Set Keyframes", "description": "Keyframes define the state of the animation at specific time points. Select a time point on the timeline, then adjust the model's pose, and the system will automatically create keyframes. Repeat this process to create an animation sequence."}, "editCurves": {"title": "Edit Animation Curves", "description": "Animation curves control the interpolation between keyframes. In the curve editor, you can adjust the curve shape to change the feel of the animation, making movements smoother or more bouncy."}, "previewAnimation": {"title": "Preview Animation", "description": "After creating basic animation, click the preview button to see the animation effect. You can adjust playback speed, loop playback, or view the animation frame by frame."}, "createStateMachine": {"title": "Create Animation State Machine", "description": "State machines allow you to manage transitions between multiple animation clips. Click the 'State Machine' tab, where you can create different animation states like idle, walk, run, etc."}, "addStates": {"title": "Add Animation States", "description": "Add new states to the state machine. Each state represents a specific animation clip or behavior. Click the 'Add State' button, then assign the corresponding animation clip to the state."}, "createTransitions": {"title": "Create State Transitions", "description": "Transitions between states are needed to achieve smooth animation switching. In the state machine canvas, drag from one state to another to create transition connections."}, "setTransitionConditions": {"title": "Set Transition Conditions", "description": "Set trigger conditions for transitions. In the transition inspector, you can set parameter conditions, such as transitioning from idle to walk when speed is greater than a certain value."}, "createBlendTree": {"title": "Create Blend Tree", "description": "Blend trees allow you to blend multiple animations based on parameter values. This is very useful for creating directional animations (like 8-directional movement) or blending different animations based on speed."}, "addBlendParameters": {"title": "Add Blend Parameters", "description": "Add blend parameters in the parameters panel, such as speed, direction, etc. These parameters will control the blend weights of animations, achieving more natural animation effects."}, "testAnimationSystem": {"title": "Test Animation System", "description": "Now let's test the entire animation system. Click the test button, try changing different parameter values, and observe the changes and transition effects of animation states."}, "saveAnimationAssets": {"title": "Save Animation Assets", "description": "After completing the animation, remember to save your work. Click the save button to save animation clips, state machines, and related settings as asset files for use in your project."}, "completion": {"title": "Tutorial Complete", "description": "Congratulations on completing the Animation System Tutorial! You now have mastered core skills like creating animation clips, setting up state machines, and using blend trees. Keep practicing and exploring, and you'll be able to create more complex and beautiful animation effects."}}}}}