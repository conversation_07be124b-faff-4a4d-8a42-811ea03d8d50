/**
 * 环境响应编辑器组件
 * 
 * 该组件允许用户编辑角色对环境的响应规则，包括条件和动作。
 */
import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Select, 
  Button, 
  Switch, 
  Space, 
  Collapse, 
  Divider, 
  InputNumber, 
  Tooltip, 
  Tag, 
  message 
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  CopyOutlined,
  PlayCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

// 导入类型定义
import {
  EnvironmentType,
  WeatherType,
  ResponseType,
  ResponsePriority,
  type EnvironmentResponseRule,
  type EnvironmentCondition,
  type EnvironmentAction
} from './types';

const { Option } = Select;
const { Panel } = Collapse;

/**
 * 环境响应编辑器属性接口
 */
interface EnvironmentResponseEditorProps {
  entityId?: string;
  onSave?: (rules: EnvironmentResponseRule[]) => void;
  onTest?: (rule: EnvironmentResponseRule) => void;
  initialRules?: EnvironmentResponseRule[];
}

/**
 * 环境响应编辑器组件
 */
const EnvironmentResponseEditor: React.FC<EnvironmentResponseEditorProps> = ({
  onSave,
  onTest,
  initialRules = []
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  // 翻译辅助函数
  const translate = (key: string, defaultValue: string = ''): string => {
    const result = t(key);
    return typeof result === 'string' ? result : defaultValue;
  };
  const [rules, setRules] = useState<EnvironmentResponseRule[]>(initialRules);
  const [editingRule, setEditingRule] = useState<EnvironmentResponseRule | null>(null);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [presets, setPresets] = useState<EnvironmentResponseRule[]>([]);

  // 加载预设
  useEffect(() => {
    // 创建模拟的预设数据
    const allPresets: EnvironmentResponseRule[] = [
      {
        id: 'rainy_weather_response',
        name: '雨天响应',
        description: '角色在雨天的响应行为',
        responseType: ResponseType.ANIMATION,
        priority: ResponsePriority.MEDIUM,
        conditions: [
          {
            type: 'weather',
            params: { weatherType: WeatherType.RAINY },
            evaluate: (data) => data.weatherType === WeatherType.RAINY
          }
        ],
        actions: [
          {
            type: ResponseType.ANIMATION,
            params: { animationName: 'umbrella', blendTime: 0.5 },
            execute: (entity) => {
              console.log('执行雨天动画', entity);
            }
          }
        ],
        enabled: true
      },
      {
        id: 'hot_weather_response',
        name: '炎热天气响应',
        description: '角色在炎热天气的响应行为',
        responseType: ResponseType.BEHAVIOR,
        priority: ResponsePriority.HIGH,
        conditions: [
          {
            type: 'temperature',
            params: { minTemperature: 30, maxTemperature: 50 },
            evaluate: (data) => data.temperature >= 30 && data.temperature <= 50
          }
        ],
        actions: [
          {
            type: ResponseType.ANIMATION,
            params: { animationName: 'wipe_sweat', blendTime: 0.3 },
            execute: (entity) => {
              console.log('执行擦汗动画', entity);
            }
          }
        ],
        enabled: true
      }
    ];
    setPresets(allPresets);
  }, []);

  // 初始化规则
  useEffect(() => {
    setRules(initialRules);
  }, [initialRules]);

  /**
   * 创建新规则
   */
  const handleCreateRule = () => {
    const newRule: EnvironmentResponseRule = {
      id: `rule_${Date.now()}`,
      name: translate('environment.newRule', '新规则'),
      description: '',
      responseType: ResponseType.ANIMATION,
      priority: ResponsePriority.MEDIUM,
      conditions: [],
      actions: [],
      enabled: true
    };
    
    setEditingRule(newRule);
    setIsEditing(true);
    form.resetFields();
  };

  /**
   * 编辑规则
   * @param rule 规则
   */
  const handleEditRule = (rule: EnvironmentResponseRule) => {
    setEditingRule({ ...rule });
    setIsEditing(true);
    form.setFieldsValue({
      name: rule.name,
      description: rule.description,
      responseType: rule.responseType,
      priority: rule.priority,
      cooldown: rule.cooldown,
      enabled: rule.enabled
    });
  };

  /**
   * 删除规则
   * @param ruleId 规则ID
   */
  const handleDeleteRule = (ruleId: string) => {
    setRules(rules.filter(rule => rule.id !== ruleId));
    if (editingRule && editingRule.id === ruleId) {
      setEditingRule(null);
      setIsEditing(false);
    }
  };

  /**
   * 复制规则
   * @param rule 规则
   */
  const handleDuplicateRule = (rule: EnvironmentResponseRule) => {
    const newRule: EnvironmentResponseRule = {
      ...rule,
      id: `rule_${Date.now()}`,
      name: `${rule.name} (${translate('environment.copy', '副本')})`
    };
    setRules([...rules, newRule]);
    message.success(translate('environment.ruleDuplicated', '规则已复制'));
  };

  /**
   * 测试规则
   * @param rule 规则
   */
  const handleTestRule = (rule: EnvironmentResponseRule) => {
    if (onTest) {
      onTest(rule);
    } else {
      message.info(translate('environment.testNotAvailable', '测试功能不可用'));
    }
  };

  /**
   * 保存规则
   */
  const handleSaveRule = () => {
    form.validateFields().then(values => {
      if (!editingRule) return;
      
      const updatedRule: EnvironmentResponseRule = {
        ...editingRule,
        name: values.name,
        description: values.description,
        responseType: values.responseType,
        priority: values.priority,
        cooldown: values.cooldown,
        enabled: values.enabled
      };
      
      // 更新或添加规则
      const ruleIndex = rules.findIndex(r => r.id === updatedRule.id);
      if (ruleIndex !== -1) {
        const updatedRules = [...rules];
        updatedRules[ruleIndex] = updatedRule;
        setRules(updatedRules);
      } else {
        setRules([...rules, updatedRule]);
      }
      
      setEditingRule(null);
      setIsEditing(false);
      message.success(translate('environment.ruleSaved', '规则已保存'));
    });
  };

  /**
   * 取消编辑
   */
  const handleCancelEdit = () => {
    setEditingRule(null);
    setIsEditing(false);
  };

  /**
   * 添加条件
   * @param type 条件类型
   */
  const handleAddCondition = (type: string) => {
    if (!editingRule) return;
    
    let newCondition: EnvironmentCondition;
    
    switch (type) {
      case 'weather':
        newCondition = {
          type: 'weather',
          params: { weatherType: WeatherType.CLEAR },
          evaluate: (data) => data.weatherType === WeatherType.CLEAR
        };
        break;
      case 'environment':
        newCondition = {
          type: 'environment',
          params: { environmentType: EnvironmentType.OUTDOOR },
          evaluate: (data) => data.environmentType === EnvironmentType.OUTDOOR
        };
        break;
      case 'temperature':
        newCondition = {
          type: 'temperature',
          params: { minTemperature: 0, maxTemperature: 30 },
          evaluate: (data) => data.temperature >= 0 && data.temperature <= 30
        };
        break;
      case 'light':
        newCondition = {
          type: 'light',
          params: { minIntensity: 0.3, maxIntensity: 1.0 },
          evaluate: (data) => data.lightIntensity >= 0.3 && data.lightIntensity <= 1.0
        };
        break;
      case 'timeOfDay':
        newCondition = {
          type: 'timeOfDay',
          params: { minTime: 8, maxTime: 18 },
          evaluate: (data) => data.timeOfDay >= 8 && data.timeOfDay <= 18
        };
        break;
      default:
        newCondition = {
          type: 'custom',
          params: {},
          evaluate: () => true
        };
    }
    
    setEditingRule({
      ...editingRule,
      conditions: [...editingRule.conditions, newCondition]
    });
  };

  /**
   * 删除条件
   * @param index 条件索引
   */
  const handleDeleteCondition = (index: number) => {
    if (!editingRule) return;
    
    const updatedConditions = [...editingRule.conditions];
    updatedConditions.splice(index, 1);
    
    setEditingRule({
      ...editingRule,
      conditions: updatedConditions
    });
  };

  /**
   * 添加动作
   * @param type 动作类型
   */
  const handleAddAction = (type: string) => {
    if (!editingRule) return;
    
    let newAction: EnvironmentAction;
    
    switch (type) {
      case ResponseType.ANIMATION:
        newAction = {
          type: ResponseType.ANIMATION,
          params: { animationName: 'idle', blendTime: 0.5 },
          execute: (entity) => {
            const animator = entity.getAnimator();
            if (animator) {
              animator.playAnimation('idle', { blendTime: 0.5, loop: true });
            }
          },
          stop: (entity) => {
            const animator = entity.getAnimator();
            if (animator) {
              animator.stopAnimation('idle', { blendTime: 0.5 });
            }
          }
        };
        break;
      case ResponseType.SOUND:
        newAction = {
          type: ResponseType.SOUND,
          params: { soundName: 'ambient', volume: 1.0 },
          execute: (entity) => {
            const audioSource = entity.getAudioSource();
            if (audioSource) {
              audioSource.play('ambient', { volume: 1.0, loop: false });
            }
          },
          stop: (entity) => {
            const audioSource = entity.getAudioSource();
            if (audioSource) {
              audioSource.stop('ambient');
            }
          }
        };
        break;
      case ResponseType.EFFECT:
        newAction = {
          type: ResponseType.EFFECT,
          params: { effectName: 'particle', duration: 5.0 },
          execute: (entity) => {
            const effectSystem = entity.getEffectSystem();
            if (effectSystem) {
              effectSystem.playEffect('particle', { duration: 5.0 });
            }
          },
          stop: (entity) => {
            const effectSystem = entity.getEffectSystem();
            if (effectSystem) {
              effectSystem.stopEffect('particle');
            }
          }
        };
        break;
      default:
        newAction = {
          type: ResponseType.CUSTOM,
          params: {},
          execute: (entity) => {
            console.log('执行自定义动作', entity);
          }
        };
    }
    
    setEditingRule({
      ...editingRule,
      actions: [...editingRule.actions, newAction]
    });
  };

  /**
   * 删除动作
   * @param index 动作索引
   */
  const handleDeleteAction = (index: number) => {
    if (!editingRule) return;
    
    const updatedActions = [...editingRule.actions];
    updatedActions.splice(index, 1);
    
    setEditingRule({
      ...editingRule,
      actions: updatedActions
    });
  };

  /**
   * 应用预设
   * @param presetId 预设ID
   */
  const handleApplyPreset = (presetId: string) => {
    const preset = presets.find(p => p.id === presetId);
    if (!preset) return;
    
    // 检查是否已存在相同ID的规则
    const existingRuleIndex = rules.findIndex(r => r.id === preset.id);
    if (existingRuleIndex !== -1) {
      // 如果存在，创建一个新ID的副本
      const newPreset = {
        ...preset,
        id: `${preset.id}_${Date.now()}`,
        name: `${preset.name} (${translate('environment.copy', '副本')})`
      };
      setRules([...rules, newPreset]);
    } else {
      setRules([...rules, preset]);
    }

    message.success(translate('environment.presetApplied', '预设已应用'));
  };

  /**
   * 保存所有规则
   */
  const handleSaveAllRules = () => {
    if (onSave) {
      onSave(rules);
      message.success(translate('environment.allRulesSaved', '所有规则已保存'));
    }
  };

  /**
   * 渲染规则列表
   */
  const renderRulesList = () => {
    return (
      <div className="rules-list">
        <div className="rules-header">
          <h3>{translate('environment.rules', '规则')}</h3>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreateRule}
          >
            {translate('environment.addRule', '添加规则')}
          </Button>
        </div>
        
        <Collapse>
          {rules.map(rule => (
            <Panel 
              key={rule.id} 
              header={
                <div className="rule-header">
                  <span>{rule.name}</span>
                  <Tag color={rule.enabled ? 'green' : 'red'}>
                    {rule.enabled ? translate('environment.enabled', '启用') : translate('environment.disabled', '禁用')}
                  </Tag>
                </div>
              }
              extra={
                <Space>
                  <Tooltip title={translate('environment.edit', '编辑')}>
                    <Button
                      type="text"
                      icon={<SettingOutlined />}
                      onClick={(e) => { e.stopPropagation(); handleEditRule(rule); }}
                    />
                  </Tooltip>
                  <Tooltip title={translate('environment.test', '测试')}>
                    <Button
                      type="text"
                      icon={<PlayCircleOutlined />}
                      onClick={(e) => { e.stopPropagation(); handleTestRule(rule); }}
                    />
                  </Tooltip>
                  <Tooltip title={translate('environment.duplicate', '复制')}>
                    <Button
                      type="text"
                      icon={<CopyOutlined />}
                      onClick={(e) => { e.stopPropagation(); handleDuplicateRule(rule); }}
                    />
                  </Tooltip>
                  <Tooltip title={translate('environment.delete', '删除')}>
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={(e) => { e.stopPropagation(); handleDeleteRule(rule.id); }}
                    />
                  </Tooltip>
                </Space>
              }
            >
              <div className="rule-details">
                {rule.description && (
                  <p className="rule-description">{rule.description}</p>
                )}
                
                <div className="rule-info">
                  <div>
                    <strong>{translate('environment.type', '类型')}:</strong> {rule.responseType}
                  </div>
                  <div>
                    <strong>{translate('environment.priority', '优先级')}:</strong> {rule.priority}
                  </div>
                  {rule.cooldown && (
                    <div>
                      <strong>{translate('environment.cooldown', '冷却时间')}:</strong> {rule.cooldown / 1000}s
                    </div>
                  )}
                </div>

                <Divider>{translate('environment.conditions', '条件')}</Divider>
                {rule.conditions.length > 0 ? (
                  <ul className="conditions-list">
                    {rule.conditions.map((condition, index) => (
                      <li key={index}>
                        <Tag color="blue">{condition.type}</Tag>
                        <span>{JSON.stringify(condition.params)}</span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p>{translate('environment.noConditions', '无条件')}</p>
                )}

                <Divider>{translate('environment.actions', '动作')}</Divider>
                {rule.actions.length > 0 ? (
                  <ul className="actions-list">
                    {rule.actions.map((action, index) => (
                      <li key={index}>
                        <Tag color="green">{action.type}</Tag>
                        <span>{JSON.stringify(action.params)}</span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p>{translate('environment.noActions', '无动作')}</p>
                )}
              </div>
            </Panel>
          ))}
        </Collapse>
        
        {rules.length === 0 && (
          <div className="empty-rules">
            <p>{translate('environment.noRules', '暂无规则')}</p>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateRule}
            >
              {translate('environment.addRule', '添加规则')}
            </Button>
          </div>
        )}

        <div className="rules-footer">
          <Button
            type="primary"
            onClick={handleSaveAllRules}
          >
            {translate('environment.saveAllRules', '保存所有规则')}
          </Button>
        </div>
      </div>
    );
  };

  /**
   * 渲染规则编辑器
   */
  const renderRuleEditor = () => {
    if (!editingRule) return null;
    
    return (
      <div className="rule-editor">
        <h3>{isEditing ? translate('environment.editRule', '编辑规则') : translate('environment.newRule', '新规则')}</h3>
        
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            name: editingRule.name,
            description: editingRule.description,
            responseType: editingRule.responseType,
            priority: editingRule.priority,
            cooldown: editingRule.cooldown,
            enabled: editingRule.enabled
          }}
        >
          <Form.Item
            name="name"
            label={translate('environment.ruleName', '规则名称')}
            rules={[{ required: true, message: translate('environment.ruleNameRequired', '请输入规则名称') }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="description"
            label={translate('environment.ruleDescription', '规则描述')}
          >
            <Input.TextArea rows={2} />
          </Form.Item>

          <Form.Item
            name="responseType"
            label={translate('environment.responseType', '响应类型')}
            rules={[{ required: true, message: translate('environment.responseTypeRequired', '请选择响应类型') }]}
          >
            <Select>
              <Option value={ResponseType.ANIMATION}>{translate('environment.responseTypes.animation', '动画')}</Option>
              <Option value={ResponseType.EFFECT}>{translate('environment.responseTypes.effect', '特效')}</Option>
              <Option value={ResponseType.SOUND}>{translate('environment.responseTypes.sound', '声音')}</Option>
              <Option value={ResponseType.BEHAVIOR}>{translate('environment.responseTypes.behavior', '行为')}</Option>
              <Option value={ResponseType.CUSTOM}>{translate('environment.responseTypes.custom', '自定义')}</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="priority"
            label={translate('environment.priority', '优先级')}
            rules={[{ required: true, message: translate('environment.priorityRequired', '请选择优先级') }]}
          >
            <Select>
              <Option value={ResponsePriority.LOW}>{translate('environment.priorities.low', '低')}</Option>
              <Option value={ResponsePriority.MEDIUM}>{translate('environment.priorities.medium', '中')}</Option>
              <Option value={ResponsePriority.HIGH}>{translate('environment.priorities.high', '高')}</Option>
              <Option value={ResponsePriority.CRITICAL}>{translate('environment.priorities.critical', '紧急')}</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="cooldown"
            label={translate('environment.cooldown', '冷却时间')}
            tooltip={translate('environment.cooldownTooltip', '规则触发后的冷却时间（毫秒）')}
          >
            <InputNumber min={0} step={1000} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="enabled"
            label={translate('environment.enabled', '启用')}
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          
          <Divider>{translate('environment.conditions', '条件')}</Divider>

          <div className="conditions-section">
            <div className="conditions-header">
              <h4>{translate('environment.conditions', '条件')}</h4>
              <Select
                placeholder={translate('environment.addCondition', '添加条件')}
                style={{ width: 200 }}
                onChange={handleAddCondition}
                value={undefined}
              >
                <Option value="weather">{translate('environment.conditionTypes.weather', '天气')}</Option>
                <Option value="environment">{translate('environment.conditionTypes.environment', '环境')}</Option>
                <Option value="temperature">{translate('environment.conditionTypes.temperature', '温度')}</Option>
                <Option value="light">{translate('environment.conditionTypes.light', '光照')}</Option>
                <Option value="timeOfDay">{translate('environment.conditionTypes.timeOfDay', '时间')}</Option>
                <Option value="custom">{translate('environment.conditionTypes.custom', '自定义')}</Option>
              </Select>
            </div>
            
            {editingRule.conditions.length > 0 ? (
              <ul className="conditions-list">
                {editingRule.conditions.map((condition, index) => (
                  <li key={index} className="condition-item">
                    <div className="condition-content">
                      <Tag color="blue">{condition.type}</Tag>
                      <span>{JSON.stringify(condition.params)}</span>
                    </div>
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => handleDeleteCondition(index)}
                    />
                  </li>
                ))}
              </ul>
            ) : (
              <p>{translate('environment.noConditions', '无条件')}</p>
            )}
          </div>

          <Divider>{translate('environment.actions', '动作')}</Divider>

          <div className="actions-section">
            <div className="actions-header">
              <h4>{translate('environment.actions', '动作')}</h4>
              <Select
                placeholder={translate('environment.addAction', '添加动作')}
                style={{ width: 200 }}
                onChange={handleAddAction}
                value={undefined}
              >
                <Option value={ResponseType.ANIMATION}>{translate('environment.responseTypes.animation', '动画')}</Option>
                <Option value={ResponseType.EFFECT}>{translate('environment.responseTypes.effect', '特效')}</Option>
                <Option value={ResponseType.SOUND}>{translate('environment.responseTypes.sound', '声音')}</Option>
                <Option value={ResponseType.BEHAVIOR}>{translate('environment.responseTypes.behavior', '行为')}</Option>
                <Option value={ResponseType.CUSTOM}>{translate('environment.responseTypes.custom', '自定义')}</Option>
              </Select>
            </div>
            
            {editingRule.actions.length > 0 ? (
              <ul className="actions-list">
                {editingRule.actions.map((action, index) => (
                  <li key={index} className="action-item">
                    <div className="action-content">
                      <Tag color="green">{action.type}</Tag>
                      <span>{JSON.stringify(action.params)}</span>
                    </div>
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => handleDeleteAction(index)}
                    />
                  </li>
                ))}
              </ul>
            ) : (
              <p>{translate('environment.noActions', '无动作')}</p>
            )}
          </div>

          <div className="form-actions">
            <Button onClick={handleCancelEdit}>{translate('environment.cancel', '取消')}</Button>
            <Button type="primary" onClick={handleSaveRule}>{translate('environment.save', '保存')}</Button>
          </div>
        </Form>
      </div>
    );
  };

  /**
   * 渲染预设面板
   */
  const renderPresets = () => {
    return (
      <div className="presets-panel">
        <h3>{translate('environment.presets', '预设')}</h3>

        <div className="presets-list">
          {presets.map(preset => (
            <Card
              key={preset.id}
              size="small"
              title={preset.name}
              extra={
                <Button
                  type="primary"
                  size="small"
                  onClick={() => handleApplyPreset(preset.id)}
                >
                  {translate('environment.apply', '应用')}
                </Button>
              }
              style={{ marginBottom: 10 }}
            >
              <p>{preset.description}</p>
              <div>
                <Tag color="blue">{translate('environment.type', '类型')}: {preset.responseType}</Tag>
                <Tag color="green">{translate('environment.priority', '优先级')}: {preset.priority}</Tag>
                <Tag color="orange">{translate('environment.conditions', '条件')}: {preset.conditions.length}</Tag>
                <Tag color="purple">{translate('environment.actions', '动作')}: {preset.actions.length}</Tag>
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="environment-response-editor">
      {isEditing ? (
        renderRuleEditor()
      ) : (
        <div className="editor-content">
          <div className="rules-section">
            {renderRulesList()}
          </div>
          <div className="presets-section">
            {renderPresets()}
          </div>
        </div>
      )}
    </div>
  );
};

export default EnvironmentResponseEditor;
