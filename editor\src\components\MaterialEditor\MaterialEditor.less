/**
 * 材质编辑器样式
 */
.material-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .material-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #e8e8e8;
    
    h2 {
      margin: 0;
    }
    
    .material-editor-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .material-editor-content {
    display: flex;
    flex: 1;
    overflow: hidden;
    
    .material-editor-preview {
      width: 40%;
      padding: 16px;
      display: flex;
      flex-direction: column;
      border-right: 1px solid #e8e8e8;

      .preview-header {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 8px;
      }

      .preview-canvas {
        flex: 1;
        width: 100%;
        min-height: 300px;
        background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                    linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                    linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
        background-size: 20px 20px;
        background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        border-radius: 8px;
        border: 1px solid #d9d9d9;
        box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .preview-controls {
        margin-top: 16px;
        padding: 12px;
        background-color: #fafafa;
        border-radius: 6px;
        border: 1px solid #e8e8e8;

        .preview-geometry,
        .preview-environment {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .control-label {
            font-size: 12px;
            color: #666;
            margin-right: 8px;
            min-width: 50px;
          }
        }
      }
    }
    
    .material-editor-form {
      width: 60%;
      padding: 16px;
      overflow-y: auto;
      
      .ant-form-item {
        margin-bottom: 16px;
      }
      
      .color-picker-field {
        display: flex;
        align-items: center;
        gap: 8px;
        position: relative;
        
        .color-preview {
          width: 32px;
          height: 32px;
          border-radius: 4px;
          border: 1px solid #d9d9d9;
          cursor: pointer;
        }
        
        .color-picker-popover {
          position: absolute;
          z-index: 2;
          top: 40px;
          left: 0;
          
          .color-picker-cover {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
          }
        }
      }
      
      .texture-item {
        margin-bottom: 16px;

        .ant-card-body {
          padding: 16px;
        }

        .ant-form-item {
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      // 滑块样式增强
      .ant-slider {
        .ant-slider-track {
          background: linear-gradient(90deg, #1890ff, #52c41a);
        }

        .ant-slider-handle {
          border: 2px solid #1890ff;
          box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
        }
      }

      // 颜色选择器增强
      .color-picker-field {
        .color-preview {
          transition: all 0.3s ease;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          &:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
          }
        }
      }

      // 标签页增强
      .ant-tabs {
        .ant-tabs-tab {
          font-weight: 500;

          &.ant-tabs-tab-active {
            .ant-tabs-tab-btn {
              color: #1890ff;
              font-weight: 600;
            }
          }
        }
      }

      // 开关样式
      .ant-switch {
        &.ant-switch-checked {
          background-color: #52c41a;
        }
      }

      // 按钮组样式
      .ant-space-compact {
        .ant-btn {
          &:not(:last-child) {
            border-right: none;
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .material-editor-content {
      flex-direction: column;

      .material-editor-preview {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #e8e8e8;

        .preview-canvas {
          min-height: 250px;
        }
      }

      .material-editor-form {
        width: 100%;
      }
    }
  }

  @media (max-width: 768px) {
    .material-editor-header {
      flex-direction: column;
      gap: 12px;

      .material-editor-actions {
        justify-content: center;
      }
    }

    .material-editor-preview {
      .preview-controls {
        .preview-geometry,
        .preview-environment {
          flex-direction: column;
          align-items: flex-start;

          .control-label {
            margin-bottom: 4px;
          }
        }
      }
    }
  }
}

// 暗色主题支持
[data-theme='dark'] .material-editor {
  .material-editor-header {
    border-bottom-color: #303030;
    background-color: #1f1f1f;
  }

  .material-editor-preview {
    border-right-color: #303030;

    .preview-canvas {
      background: linear-gradient(45deg, #2a2a2a 25%, transparent 25%),
                  linear-gradient(-45deg, #2a2a2a 25%, transparent 25%),
                  linear-gradient(45deg, transparent 75%, #2a2a2a 75%),
                  linear-gradient(-45deg, transparent 75%, #2a2a2a 75%);
      border-color: #434343;
    }

    .preview-controls {
      background-color: #262626;
      border-color: #434343;

      .control-label {
        color: #a6a6a6;
      }
    }
  }

  .material-editor-form {
    .texture-item {
      .ant-card {
        background-color: #262626;
        border-color: #434343;
      }
    }

    .color-picker-field {
      .color-preview {
        border-color: #434343;
      }
    }
  }
}
