# AI服务集群部署配置
# 包含推荐服务、行为分析服务、内容分析服务等

apiVersion: v1
kind: Namespace
metadata:
  name: ai-services
  labels:
    name: ai-services

---
# 推荐服务部署
apiVersion: apps/v1
kind: Deployment
metadata:
  name: recommendation-service
  namespace: ai-services
  labels:
    app: recommendation-service
    tier: ai-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: recommendation-service
  template:
    metadata:
      labels:
        app: recommendation-service
    spec:
      containers:
      - name: recommendation-service
        image: dl-engine/recommendation-service:latest
        ports:
        - containerPort: 3009
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: ai-services-secrets
              key: database-url
        - name: REDIS_URL
          value: "redis://redis-service:6379"
        - name: MODEL_STORAGE_PATH
          value: "/models"
        - name: TENSORFLOW_BACKEND
          value: "cpu"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        volumeMounts:
        - name: model-storage
          mountPath: /models
        - name: cache-storage
          mountPath: /cache
        livenessProbe:
          httpGet:
            path: /health
            port: 3009
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /ready
            port: 3009
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: model-storage
        persistentVolumeClaim:
          claimName: ai-models-pvc
      - name: cache-storage
        emptyDir: {}

---
# 推荐服务Service
apiVersion: v1
kind: Service
metadata:
  name: recommendation-service
  namespace: ai-services
spec:
  selector:
    app: recommendation-service
  ports:
  - port: 3009
    targetPort: 3009
  type: ClusterIP

---
# 行为分析服务部署
apiVersion: apps/v1
kind: Deployment
metadata:
  name: behavior-analysis-service
  namespace: ai-services
  labels:
    app: behavior-analysis-service
    tier: ai-backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: behavior-analysis-service
  template:
    metadata:
      labels:
        app: behavior-analysis-service
    spec:
      containers:
      - name: behavior-analysis-service
        image: dl-engine/behavior-analysis-service:latest
        ports:
        - containerPort: 3010
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: ai-services-secrets
              key: database-url
        - name: KAFKA_BROKERS
          value: "kafka-service:9092"
        - name: ELASTICSEARCH_URL
          value: "http://elasticsearch-service:9200"
        resources:
          requests:
            memory: "1.5Gi"
            cpu: "500m"
          limits:
            memory: "3Gi"
            cpu: "1500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3010
          initialDelaySeconds: 45
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /ready
            port: 3010
          initialDelaySeconds: 20
          periodSeconds: 10

---
# 行为分析服务Service
apiVersion: v1
kind: Service
metadata:
  name: behavior-analysis-service
  namespace: ai-services
spec:
  selector:
    app: behavior-analysis-service
  ports:
  - port: 3010
    targetPort: 3010
  type: ClusterIP

---
# 内容分析服务部署
apiVersion: apps/v1
kind: Deployment
metadata:
  name: content-analysis-service
  namespace: ai-services
  labels:
    app: content-analysis-service
    tier: ai-backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: content-analysis-service
  template:
    metadata:
      labels:
        app: content-analysis-service
    spec:
      containers:
      - name: content-analysis-service
        image: dl-engine/content-analysis-service:latest
        ports:
        - containerPort: 3011
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: ai-services-secrets
              key: database-url
        - name: MINIO_ENDPOINT
          value: "minio-service:9000"
        - name: MINIO_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: ai-services-secrets
              key: minio-access-key
        - name: MINIO_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: ai-services-secrets
              key: minio-secret-key
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        volumeMounts:
        - name: temp-storage
          mountPath: /tmp/analysis
        livenessProbe:
          httpGet:
            path: /health
            port: 3011
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /ready
            port: 3011
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: temp-storage
        emptyDir:
          sizeLimit: 10Gi

---
# 内容分析服务Service
apiVersion: v1
kind: Service
metadata:
  name: content-analysis-service
  namespace: ai-services
spec:
  selector:
    app: content-analysis-service
  ports:
  - port: 3011
    targetPort: 3011
  type: ClusterIP

---
# AI模型存储PVC
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ai-models-pvc
  namespace: ai-services
spec:
  accessModes:
  - ReadWriteMany
  resources:
    requests:
      storage: 100Gi
  storageClassName: nfs-storage

---
# AI服务配置ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-services-config
  namespace: ai-services
data:
  recommendation.yml: |
    algorithms:
      neural_collaborative_filtering:
        enabled: true
        model_path: "/models/ncf/model.json"
        embedding_dim: 64
        hidden_layers: [128, 64, 32]
        dropout_rate: 0.2
        learning_rate: 0.001
      content_based:
        enabled: true
        similarity_threshold: 0.7
        max_candidates: 1000
      collaborative_filtering:
        enabled: true
        min_interactions: 5
        similarity_method: "cosine"
    cache:
      ttl: 300
      max_size: 10000
    diversity:
      weight: 0.3
      max_similarity: 0.8
  
  behavior_analysis.yml: |
    patterns:
      time_window: 3600  # 1 hour
      min_events: 10
      anomaly_threshold: 2.0
    prediction:
      model_type: "lstm"
      sequence_length: 50
      prediction_horizon: 24  # hours
    engagement:
      metrics:
        - session_duration
        - click_through_rate
        - task_completion_rate
        - feature_usage_frequency
  
  content_analysis.yml: |
    quality_dimensions:
      - technical_quality
      - aesthetic_quality
      - usability
      - performance
      - innovativeness
    scoring:
      weights:
        technical: 0.3
        aesthetic: 0.25
        usability: 0.25
        performance: 0.15
        innovation: 0.05
    benchmarks:
      update_frequency: "daily"
      sample_size: 1000

---
# AI服务密钥Secret
apiVersion: v1
kind: Secret
metadata:
  name: ai-services-secrets
  namespace: ai-services
type: Opaque
data:
  database-url: <base64-encoded-database-url>
  minio-access-key: <base64-encoded-minio-access-key>
  minio-secret-key: <base64-encoded-minio-secret-key>
  openai-api-key: <base64-encoded-openai-api-key>

---
# AI服务网络策略
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: ai-services-network-policy
  namespace: ai-services
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: api-gateway
    - namespaceSelector:
        matchLabels:
          name: default
    ports:
    - protocol: TCP
      port: 3009
    - protocol: TCP
      port: 3010
    - protocol: TCP
      port: 3011
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: database
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - namespaceSelector:
        matchLabels:
          name: cache
    ports:
    - protocol: TCP
      port: 6379
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53

---
# AI服务HPA
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: recommendation-service-hpa
  namespace: ai-services
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: recommendation-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
