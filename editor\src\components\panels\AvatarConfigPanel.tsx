/**
 * 数字人配置面板
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  Slider,
  Switch,
  Button,
  Space,
  Row,
  Col,
  Avatar,
  Upload,
  message,
  Tabs,
  Typography,
  Collapse,
  InputNumber,
} from 'antd';
import {
  UploadOutlined,
  PlayCircleOutlined,
  StopOutlined,
  EyeOutlined,
} from '@ant-design/icons';
// import { useTranslation } from 'react-i18next'; // 暂时注释掉未使用的导入

const { Title } = Typography;
const { Option } = Select;
const { Panel } = Collapse;

/**
 * 数字人配置接口
 */
interface AvatarConfig {
  // 基本信息
  id: string;
  name: string;
  description: string;
  avatar: string; // 头像URL
  
  // 外观配置
  appearance: {
    model: string; // 3D模型路径
    texture: string; // 贴图路径
    scale: number;
    position: { x: number; y: number; z: number };
    rotation: { x: number; y: number; z: number };
  };
  
  // 语音配置
  voice: {
    provider: 'azure' | 'google' | 'baidu' | 'openai';
    voice: string;
    language: string;
    rate: number;
    pitch: number;
    volume: number;
    style?: string;
    emotion?: string;
  };
  
  // 动画配置
  animation: {
    idleAnimation: string;
    talkingAnimation: string;
    gestureAnimations: string[];
    enableLipSync: boolean;
    enableGestures: boolean;
    enableEyeTracking: boolean;
  };
  
  // 表情配置
  expression: {
    defaultExpression: string;
    enableEmotionExpression: boolean;
    emotionIntensity: number;
    blinkFrequency: number;
  };
  
  // 个性配置
  personality: {
    type: 'friendly' | 'professional' | 'casual' | 'formal';
    responseStyle: string;
    greeting: string;
    farewell: string;
  };
  
  // 交互配置
  interaction: {
    enableVoiceInteraction: boolean;
    enableTextInteraction: boolean;
    responseDelay: number;
    maxConversationLength: number;
  };
}

/**
 * 数字人配置面板组件
 */
const AvatarConfigPanel: React.FC = () => {
  // const { t } = useTranslation(); // 暂时注释掉未使用的翻译
  const [form] = Form.useForm();
  const [config, setConfig] = useState<AvatarConfig | null>(null);
  // const [loading, setLoading] = useState(false); // 暂时注释掉未使用的加载状态
  const [previewMode, setPreviewMode] = useState(false);
  const [voicePreviewPlaying, setVoicePreviewPlaying] = useState(false);

  /**
   * 加载数字人配置
   */
  const loadAvatarConfig = async () => {
    // setLoading(true); // 暂时注释掉
    try {
      // 这里调用API获取数字人配置
      const mockConfig: AvatarConfig = {
        id: 'avatar_1',
        name: '小雅助手',
        description: '专业的医疗咨询数字人助手',
        avatar: '/avatars/xiaoya.jpg',
        appearance: {
          model: '/models/female_doctor.glb',
          texture: '/textures/doctor_uniform.jpg',
          scale: 1.0,
          position: { x: 0, y: 0, z: 0 },
          rotation: { x: 0, y: 0, z: 0 },
        },
        voice: {
          provider: 'azure',
          voice: 'zh-CN-XiaoxiaoNeural',
          language: 'zh-CN',
          rate: 1.0,
          pitch: 1.0,
          volume: 1.0,
          style: 'friendly',
          emotion: 'calm',
        },
        animation: {
          idleAnimation: 'idle_breathing',
          talkingAnimation: 'talking_gestures',
          gestureAnimations: ['wave', 'point', 'nod', 'shake_head'],
          enableLipSync: true,
          enableGestures: true,
          enableEyeTracking: true,
        },
        expression: {
          defaultExpression: 'neutral',
          enableEmotionExpression: true,
          emotionIntensity: 0.7,
          blinkFrequency: 3.0,
        },
        personality: {
          type: 'professional',
          responseStyle: 'warm_professional',
          greeting: '您好，我是小雅助手，很高兴为您服务。',
          farewell: '感谢您的咨询，祝您身体健康！',
        },
        interaction: {
          enableVoiceInteraction: true,
          enableTextInteraction: true,
          responseDelay: 500,
          maxConversationLength: 20,
        },
      };
      setConfig(mockConfig);
      form.setFieldsValue(mockConfig);
    } catch (error) {
      message.error('加载数字人配置失败');
    } finally {
      // setLoading(false); // 暂时注释掉
    }
  };

  /**
   * 保存配置
   */
  const handleSaveConfig = async (values: any) => {
    try {
      // 这里调用API保存配置
      console.log('保存数字人配置:', values);
      message.success('配置保存成功');
      setConfig(values);
    } catch (error) {
      message.error('保存配置失败');
    }
  };

  /**
   * 语音预览
   */
  const handleVoicePreview = async () => {
    if (voicePreviewPlaying) {
      setVoicePreviewPlaying(false);
      return;
    }

    try {
      setVoicePreviewPlaying(true);
      // 这里调用语音合成API进行预览
      const previewText = config?.personality.greeting || '您好，这是语音预览。';
      console.log('语音预览:', previewText);
      
      // 模拟播放时间
      setTimeout(() => {
        setVoicePreviewPlaying(false);
      }, 3000);
    } catch (error) {
      message.error('语音预览失败');
      setVoicePreviewPlaying(false);
    }
  };

  /**
   * 3D模型预览
   */
  const handleModelPreview = () => {
    setPreviewMode(!previewMode);
    // 这里可以打开3D预览窗口
  };

  useEffect(() => {
    loadAvatarConfig();
  }, []);

  if (!config) {
    return <div>加载中...</div>;
  }

  return (
    <div style={{ padding: '16px', height: '100%', overflow: 'auto' }}>
      <div style={{ marginBottom: '16px' }}>
        <Title level={4}>数字人配置</Title>
        <Space>
          <Button type="primary" onClick={() => form.submit()}>
            保存配置
          </Button>
          <Button icon={<EyeOutlined />} onClick={handleModelPreview}>
            {previewMode ? '关闭预览' : '3D预览'}
          </Button>
          <Button
            icon={voicePreviewPlaying ? <StopOutlined /> : <PlayCircleOutlined />}
            onClick={handleVoicePreview}
            loading={voicePreviewPlaying}
          >
            语音预览
          </Button>
        </Space>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSaveConfig}
        initialValues={config}
      >
        <Tabs
          items={[
            {
              key: 'basic',
              label: '基本信息',
              children: (
                <Card>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="name"
                        label="数字人名称"
                        rules={[{ required: true, message: '请输入数字人名称' }]}
                      >
                        <Input placeholder="请输入数字人名称" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="description" label="描述">
                        <Input placeholder="请输入描述" />
                      </Form.Item>
                    </Col>
                  </Row>
                  
                  <Form.Item label="头像">
                    <Upload
                      listType="picture-card"
                      showUploadList={false}
                      beforeUpload={() => false}
                    >
                      <div>
                        {config.avatar ? (
                          <Avatar size={64} src={config.avatar} />
                        ) : (
                          <div>
                            <UploadOutlined />
                            <div>上传头像</div>
                          </div>
                        )}
                      </div>
                    </Upload>
                  </Form.Item>
                </Card>
              ),
            },
            {
              key: 'appearance',
              label: '外观设置',
              children: (
                <Card>
                  <Collapse>
                    <Panel header="3D模型" key="model">
                      <Form.Item name={['appearance', 'model']} label="模型文件">
                        <Input placeholder="3D模型文件路径" />
                      </Form.Item>
                      <Form.Item name={['appearance', 'texture']} label="贴图文件">
                        <Input placeholder="贴图文件路径" />
                      </Form.Item>
                      <Form.Item name={['appearance', 'scale']} label="缩放比例">
                        <Slider min={0.1} max={3.0} step={0.1} marks={{ 0.5: '0.5x', 1: '1x', 2: '2x' }} />
                      </Form.Item>
                    </Panel>
                    
                    <Panel header="位置和旋转" key="transform">
                      <Row gutter={16}>
                        <Col span={8}>
                          <Form.Item name={['appearance', 'position', 'x']} label="X位置">
                            <InputNumber style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item name={['appearance', 'position', 'y']} label="Y位置">
                            <InputNumber style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item name={['appearance', 'position', 'z']} label="Z位置">
                            <InputNumber style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                      </Row>
                      
                      <Row gutter={16}>
                        <Col span={8}>
                          <Form.Item name={['appearance', 'rotation', 'x']} label="X旋转">
                            <Slider min={-180} max={180} />
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item name={['appearance', 'rotation', 'y']} label="Y旋转">
                            <Slider min={-180} max={180} />
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item name={['appearance', 'rotation', 'z']} label="Z旋转">
                            <Slider min={-180} max={180} />
                          </Form.Item>
                        </Col>
                      </Row>
                    </Panel>
                  </Collapse>
                </Card>
              ),
            },
            {
              key: 'voice',
              label: '语音设置',
              children: (
                <Card>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item name={['voice', 'provider']} label="语音提供商">
                        <Select>
                          <Option value="azure">Azure</Option>
                          <Option value="google">Google</Option>
                          <Option value="baidu">百度</Option>
                          <Option value="openai">OpenAI</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name={['voice', 'voice']} label="语音角色">
                        <Select>
                          <Option value="zh-CN-XiaoxiaoNeural">晓晓 (女性)</Option>
                          <Option value="zh-CN-YunxiNeural">云希 (男性)</Option>
                          <Option value="zh-CN-YunyangNeural">云扬 (男性)</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                  
                  <Row gutter={16}>
                    <Col span={8}>
                      <Form.Item name={['voice', 'rate']} label="语速">
                        <Slider min={0.5} max={2.0} step={0.1} marks={{ 0.5: '慢', 1: '正常', 2: '快' }} />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item name={['voice', 'pitch']} label="音调">
                        <Slider min={0.5} max={2.0} step={0.1} marks={{ 0.5: '低', 1: '正常', 2: '高' }} />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item name={['voice', 'volume']} label="音量">
                        <Slider min={0.1} max={1.0} step={0.1} marks={{ 0.1: '小', 0.5: '中', 1: '大' }} />
                      </Form.Item>
                    </Col>
                  </Row>
                  
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item name={['voice', 'style']} label="语音风格">
                        <Select>
                          <Option value="friendly">友好</Option>
                          <Option value="professional">专业</Option>
                          <Option value="warm">温暖</Option>
                          <Option value="calm">平静</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name={['voice', 'emotion']} label="情感">
                        <Select>
                          <Option value="neutral">中性</Option>
                          <Option value="happy">开心</Option>
                          <Option value="calm">平静</Option>
                          <Option value="gentle">温和</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>
              ),
            },
            {
              key: 'animation',
              label: '动画设置',
              children: (
                <Card>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item name={['animation', 'idleAnimation']} label="待机动画">
                        <Select>
                          <Option value="idle_breathing">自然呼吸</Option>
                          <Option value="idle_looking">环顾四周</Option>
                          <Option value="idle_thinking">思考状态</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name={['animation', 'talkingAnimation']} label="说话动画">
                        <Select>
                          <Option value="talking_gestures">手势说话</Option>
                          <Option value="talking_simple">简单说话</Option>
                          <Option value="talking_expressive">表情丰富</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                  
                  <Row gutter={16}>
                    <Col span={8}>
                      <Form.Item name={['animation', 'enableLipSync']} label="嘴形同步" valuePropName="checked">
                        <Switch />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item name={['animation', 'enableGestures']} label="手势动作" valuePropName="checked">
                        <Switch />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item name={['animation', 'enableEyeTracking']} label="眼部追踪" valuePropName="checked">
                        <Switch />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>
              ),
            },
            {
              key: 'personality',
              label: '个性设置',
              children: (
                <Card>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item name={['personality', 'type']} label="个性类型">
                        <Select>
                          <Option value="friendly">友好型</Option>
                          <Option value="professional">专业型</Option>
                          <Option value="casual">随和型</Option>
                          <Option value="formal">正式型</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name={['personality', 'responseStyle']} label="回应风格">
                        <Select>
                          <Option value="warm_professional">温暖专业</Option>
                          <Option value="efficient_direct">高效直接</Option>
                          <Option value="patient_detailed">耐心详细</Option>
                          <Option value="encouraging_supportive">鼓励支持</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                  
                  <Form.Item name={['personality', 'greeting']} label="问候语">
                    <Input.TextArea rows={2} placeholder="设置数字人的问候语" />
                  </Form.Item>
                  
                  <Form.Item name={['personality', 'farewell']} label="告别语">
                    <Input.TextArea rows={2} placeholder="设置数字人的告别语" />
                  </Form.Item>
                </Card>
              ),
            },
            {
              key: 'interaction',
              label: '交互设置',
              children: (
                <Card>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item name={['interaction', 'enableVoiceInteraction']} label="语音交互" valuePropName="checked">
                        <Switch />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name={['interaction', 'enableTextInteraction']} label="文本交互" valuePropName="checked">
                        <Switch />
                      </Form.Item>
                    </Col>
                  </Row>
                  
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item name={['interaction', 'responseDelay']} label="回应延迟 (毫秒)">
                        <InputNumber min={0} max={5000} style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name={['interaction', 'maxConversationLength']} label="最大对话轮数">
                        <InputNumber min={1} max={100} style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>
              ),
            },
          ]}
        />
      </Form>
    </div>
  );
};

export default AvatarConfigPanel;
