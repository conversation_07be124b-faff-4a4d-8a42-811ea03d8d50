/**
 * 协作历史面板组件
 * 显示版本历史、变更记录和协作统计
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Timeline,
  Table,
  Tabs,
  Button,
  Space,
  Tag,
  Avatar,
  Typography,
  Statistic,
  Row,
  Col,
  Modal,
  Input,
  Select,
  Tooltip,
  Popconfirm,
  message,
  Badge,
  Progress
} from 'antd';
import {
  HistoryOutlined,
  BranchesOutlined,
  MergeOutlined,
  UserOutlined,
  ClockCircleOutlined,
  RollbackOutlined,
  TagOutlined,
  TeamOutlined,
  TrophyOutlined,
  FileTextOutlined,
  GitlabOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import CollaborationHistoryService, {
  Version,
  ChangeRecord,
  Branch,
  MergeRequest,
  CollaborationSession,
  ChangeType,
  MergeStatus
} from '../../services/CollaborationHistoryService';
import './CollaborationHistoryPanel.less';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;

interface CollaborationHistoryPanelProps {
  visible: boolean;
  onClose: () => void;
  currentUserId: string;
}

const CollaborationHistoryPanel: React.FC<CollaborationHistoryPanelProps> = ({
  visible,
  onClose,
  currentUserId
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('versions');
  const [versions, setVersions] = useState<Version[]>([]);
  const [changes, setChanges] = useState<ChangeRecord[]>([]);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [mergeRequests, setMergeRequests] = useState<MergeRequest[]>([]);
  const [sessions, setSessions] = useState<CollaborationSession[]>([]);
  const [stats, setStats] = useState<any>({});
  const [revertModalVisible, setRevertModalVisible] = useState(false);
  const [selectedVersion, setSelectedVersion] = useState<Version | null>(null);

  const historyService = CollaborationHistoryService.getInstance();

  useEffect(() => {
    if (visible) {
      loadData();
    }
  }, [visible]);

  const loadData = () => {
    setVersions(historyService.getVersionHistory());
    setChanges(historyService.getChangeHistory(undefined, 50));
    setBranches(historyService.getAllBranches());
    setMergeRequests(historyService.getMergeRequests());
    setSessions(historyService.getCollaborationSessions());
    setStats(historyService.getCollaborationStats());
  };

  // 获取变更类型图标
  const getChangeTypeIcon = (type: ChangeType) => {
    switch (type) {
      case ChangeType.CREATE:
        return <span style={{ color: '#52c41a' }}>+</span>;
      case ChangeType.DELETE:
        return <span style={{ color: '#ff4d4f' }}>-</span>;
      case ChangeType.UPDATE:
        return <span style={{ color: '#1890ff' }}>~</span>;
      case ChangeType.MOVE:
        return <span style={{ color: '#722ed1' }}>→</span>;
      default:
        return <span style={{ color: '#666' }}>•</span>;
    }
  };

  // 获取合并状态颜色
  const getMergeStatusColor = (status: MergeStatus) => {
    switch (status) {
      case MergeStatus.OPEN:
        return 'blue';
      case MergeStatus.APPROVED:
        return 'green';
      case MergeStatus.MERGED:
        return 'success';
      case MergeStatus.CLOSED:
        return 'default';
      case MergeStatus.CONFLICTED:
        return 'red';
      default:
        return 'default';
    }
  };

  // 处理版本回滚
  const handleRevertVersion = async () => {
    if (!selectedVersion) return;

    try {
      await historyService.revertToVersion(
        selectedVersion.id,
        currentUserId,
        'Current User'
      );
      loadData();
      setRevertModalVisible(false);
      message.success(t('collaboration.history.versionReverted'));
    } catch (error) {
      message.error(t('collaboration.history.revertFailed'));
    }
  };

  // 版本历史表格列定义
  const versionColumns = [
    {
      title: t('collaboration.history.version'),
      dataIndex: 'number',
      key: 'number',
      render: (number: number) => <Tag color="blue">v{number}</Tag>
    },
    {
      title: t('collaboration.history.author'),
      dataIndex: 'authorName',
      key: 'authorName',
      render: (name: string) => (
        <Space>
          <Avatar size="small" icon={<UserOutlined />} />
          <Text>{name}</Text>
        </Space>
      )
    },
    {
      title: t('collaboration.history.message'),
      dataIndex: 'message',
      key: 'message',
      ellipsis: true
    },
    {
      title: t('collaboration.history.timestamp'),
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (timestamp: number) => new Date(timestamp).toLocaleString()
    },
    {
      title: t('collaboration.history.operations'),
      dataIndex: 'operations',
      key: 'operations',
      render: (operations: any[]) => (
        <Tag>{operations.length} {t('collaboration.history.changes')}</Tag>
      )
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (record: Version) => (
        <Space>
          <Tooltip title={t('collaboration.history.revertToVersion')}>
            <Button
              type="text"
              icon={<RollbackOutlined />}
              onClick={() => {
                setSelectedVersion(record);
                setRevertModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t('collaboration.history.viewDetails')}>
            <Button
              type="text"
              icon={<FileTextOutlined />}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  // 渲染版本时间线
  const renderVersionTimeline = () => (
    <Timeline>
      {versions.slice(0, 20).map(version => (
        <Timeline.Item
          key={version.id}
          dot={<Avatar size="small" icon={<UserOutlined />} />}
        >
          <div className="version-timeline-item">
            <div className="version-header">
              <Space>
                <Tag color="blue">v{version.number}</Tag>
                <Text strong>{version.authorName}</Text>
                <Text type="secondary">
                  {new Date(version.timestamp).toLocaleString()}
                </Text>
              </Space>
            </div>
            <Paragraph ellipsis={{ rows: 2 }}>{version.message}</Paragraph>
            <div className="version-meta">
              <Space>
                <Tag>{version.operations.length} changes</Tag>
                {version.tags.map(tag => (
                  <Tag key={tag} icon={<TagOutlined />} color="orange">
                    {tag}
                  </Tag>
                ))}
              </Space>
            </div>
          </div>
        </Timeline.Item>
      ))}
    </Timeline>
  );

  // 渲染变更历史
  const renderChangeHistory = () => (
    <Timeline>
      {changes.slice(0, 30).map(change => (
        <Timeline.Item
          key={change.id}
          dot={getChangeTypeIcon(change.changeType)}
        >
          <div className="change-timeline-item">
            <Space>
              <Text strong>{change.authorName}</Text>
              <Text>{change.description}</Text>
            </Space>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {new Date(change.timestamp).toLocaleString()}
            </Text>
            {change.affectedEntities.length > 0 && (
              <div className="affected-entities">
                <Space wrap>
                  {change.affectedEntities.map(entityId => (
                    <Tag key={entityId} size="small">{entityId}</Tag>
                  ))}
                </Space>
              </div>
            )}
          </div>
        </Timeline.Item>
      ))}
    </Timeline>
  );

  // 渲染分支信息
  const renderBranches = () => (
    <div className="branches-container">
      {branches.map(branch => (
        <Card key={branch.id} size="small" className="branch-card">
          <div className="branch-header">
            <Space>
              <BranchesOutlined />
              <Text strong>{branch.name}</Text>
              {branch.isProtected && <Tag color="red">Protected</Tag>}
              {branch.isActive && <Tag color="green">Active</Tag>}
            </Space>
          </div>
          <Paragraph ellipsis={{ rows: 2 }}>{branch.description}</Paragraph>
          <div className="branch-meta">
            <Text type="secondary">
              Created by {branch.createdBy} on {new Date(branch.createdAt).toLocaleDateString()}
            </Text>
          </div>
        </Card>
      ))}
    </div>
  );

  // 渲染合并请求
  const renderMergeRequests = () => (
    <div className="merge-requests-container">
      {mergeRequests.map(mr => (
        <Card key={mr.id} size="small" className="merge-request-card">
          <div className="mr-header">
            <Space>
              <MergeOutlined />
              <Text strong>{mr.title}</Text>
              <Tag color={getMergeStatusColor(mr.status)}>{mr.status}</Tag>
            </Space>
          </div>
          <Paragraph ellipsis={{ rows: 2 }}>{mr.description}</Paragraph>
          <div className="mr-meta">
            <Space>
              <Text type="secondary">
                {mr.sourceBranchId} → {mr.targetBranchId}
              </Text>
              <Text type="secondary">
                by {mr.authorId} on {new Date(mr.createdAt).toLocaleDateString()}
              </Text>
            </Space>
          </div>
          {mr.conflicts.length > 0 && (
            <div className="mr-conflicts">
              <Text type="danger">
                {mr.conflicts.length} conflicts detected
              </Text>
            </div>
          )}
        </Card>
      ))}
    </div>
  );

  // 渲染协作统计
  const renderStats = () => (
    <div className="collaboration-stats">
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('collaboration.history.totalVersions')}
              value={stats.totalVersions}
              prefix={<HistoryOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('collaboration.history.totalChanges')}
              value={stats.totalChanges}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('collaboration.history.totalBranches')}
              value={stats.totalBranches}
              prefix={<BranchesOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('collaboration.history.activeSessions')}
              value={stats.activeSessions}
              prefix={<TeamOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Card title={t('collaboration.history.topContributors')} style={{ marginTop: 16 }}>
        <div className="contributors-list">
          {stats.topContributors?.slice(0, 10).map((contributor: any, index: number) => (
            <div key={contributor.userId} className="contributor-item">
              <Space>
                <Badge count={index + 1} style={{ backgroundColor: '#52c41a' }}>
                  <Avatar icon={<UserOutlined />} />
                </Badge>
                <div className="contributor-info">
                  <Text strong>{contributor.userId}</Text>
                  <br />
                  <Text type="secondary">{contributor.contributions} contributions</Text>
                </div>
                <Progress
                  percent={Math.round((contributor.contributions / stats.totalVersions) * 100)}
                  size="small"
                  style={{ width: 100 }}
                />
              </Space>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );

  return (
    <Modal
      title={
        <Space>
          <HistoryOutlined />
          {t('collaboration.history.collaborationHistory')}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1200}
      footer={null}
      className="collaboration-history-panel"
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <Space>
              <HistoryOutlined />
              {t('collaboration.history.versions')}
            </Space>
          }
          key="versions"
        >
          <div className="versions-content">
            <div className="versions-table">
              <Table
                columns={versionColumns}
                dataSource={versions}
                rowKey="id"
                pagination={{ pageSize: 10 }}
                size="small"
              />
            </div>
            <div className="versions-timeline">
              {renderVersionTimeline()}
            </div>
          </div>
        </TabPane>

        <TabPane
          tab={
            <Space>
              <FileTextOutlined />
              {t('collaboration.history.changes')}
            </Space>
          }
          key="changes"
        >
          {renderChangeHistory()}
        </TabPane>

        <TabPane
          tab={
            <Space>
              <BranchesOutlined />
              {t('collaboration.history.branches')}
            </Space>
          }
          key="branches"
        >
          {renderBranches()}
        </TabPane>

        <TabPane
          tab={
            <Space>
              <MergeOutlined />
              {t('collaboration.history.mergeRequests')}
            </Space>
          }
          key="mergeRequests"
        >
          {renderMergeRequests()}
        </TabPane>

        <TabPane
          tab={
            <Space>
              <TrophyOutlined />
              {t('collaboration.history.statistics')}
            </Space>
          }
          key="stats"
        >
          {renderStats()}
        </TabPane>
      </Tabs>

      {/* 版本回滚确认对话框 */}
      <Modal
        title={t('collaboration.history.confirmRevert')}
        open={revertModalVisible}
        onOk={handleRevertVersion}
        onCancel={() => setRevertModalVisible(false)}
        okText={t('collaboration.history.revert')}
        cancelText={t('common.cancel')}
        okButtonProps={{ danger: true }}
      >
        <p>
          {t('collaboration.history.revertWarning', {
            version: selectedVersion?.number
          })}
        </p>
        <p>
          <Text type="secondary">
            {t('collaboration.history.revertDescription')}
          </Text>
        </p>
      </Modal>
    </Modal>
  );
};

export default CollaborationHistoryPanel;
