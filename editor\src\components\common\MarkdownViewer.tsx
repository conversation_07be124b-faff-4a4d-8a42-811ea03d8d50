/**
 * Markdown查看器组件
 * 用于渲染Markdown内容
 */
import React, { useEffect, useState } from 'react';
import './MarkdownViewer.less';

interface MarkdownViewerProps {
  content: string;
  baseUrl?: string;
}

const MarkdownViewer: React.FC<MarkdownViewerProps> = ({ content, baseUrl = '' }) => {
  const [processedContent, setProcessedContent] = useState(content);

  // 处理图片路径
  useEffect(() => {
    if (!content) return;

    // 替换相对路径的图片链接为绝对路径
    let processed = content;
    if (baseUrl) {
      const imgRegex = /!\[(.*?)\]\((\.\.\/.*?)\)/g;
      processed = processed.replace(imgRegex, (_, alt, path) => {
        const absolutePath = path.replace(/^\.\.\//, baseUrl);
        return `![${alt}](${absolutePath})`;
      });
    }

    setProcessedContent(processed);
  }, [content, baseUrl]);

  // 简单的 Markdown 转 HTML 处理
  const convertMarkdownToHtml = (markdown: string): string => {
    return markdown
      // 标题
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      // 粗体
      .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
      // 斜体
      .replace(/\*(.*)\*/gim, '<em>$1</em>')
      // 代码块
      .replace(/```([\s\S]*?)```/gim, '<pre><code>$1</code></pre>')
      // 行内代码
      .replace(/`([^`]*)`/gim, '<code>$1</code>')
      // 链接
      .replace(/\[([^\]]*)\]\(([^)]*)\)/gim, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')
      // 图片
      .replace(/!\[([^\]]*)\]\(([^)]*)\)/gim, '<img alt="$1" src="$2" style="max-width: 100%; height: auto;" />')
      // 换行
      .replace(/\n/gim, '<br>');
  };

  return (
    <div className="markdown-viewer">
      <div
        dangerouslySetInnerHTML={{
          __html: convertMarkdownToHtml(processedContent)
        }}
      />
    </div>
  );
};

export default MarkdownViewer;
