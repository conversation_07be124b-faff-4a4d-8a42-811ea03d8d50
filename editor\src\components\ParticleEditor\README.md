# 粒子系统编辑器

粒子系统编辑器是一个用于创建和编辑粒子效果的可视化工具，基于底层的 DL Engine 构建。

## 功能特性

### 🎨 可视化编辑
- 实时预览粒子效果
- 直观的参数调节界面
- 多种预设效果模板

### ⚙️ 丰富的参数控制
- **发射器设置**: 最大粒子数、发射率、生命周期
- **外观控制**: 起始/结束大小、颜色、混合模式
- **运动参数**: 起始速度、重力、发射器类型
- **渲染选项**: 3D/广告牌/拉伸渲染模式

### 🎯 预设系统
- 火焰效果
- 烟雾效果  
- 雪花效果
- 支持自定义预设

## 使用方法

### 基本用法

```tsx
import ParticleEditor from './components/ParticleEditor';

function App() {
  const handleSave = (particleSystem) => {
    console.log('保存粒子系统:', particleSystem);
  };

  const handleCancel = () => {
    console.log('取消编辑');
  };

  return (
    <ParticleEditor
      onSave={handleSave}
      onCancel={handleCancel}
    />
  );
}
```

### 编辑现有粒子系统

```tsx
<ParticleEditor
  particleSystemId="existing-particle-system-id"
  onSave={handleSave}
  onCancel={handleCancel}
/>
```

## 组件属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `particleSystemId` | `string` | - | 要编辑的粒子系统ID |
| `onSave` | `(particleSystem: any) => void` | - | 保存回调函数 |
| `onCancel` | `() => void` | - | 取消回调函数 |

## 参数说明

### 发射器参数
- **最大粒子数**: 同时存在的最大粒子数量
- **发射率**: 每秒发射的粒子数量
- **生命周期**: 粒子的存活时间范围（最小值-最大值）
- **循环**: 是否循环播放
- **预热**: 是否在开始时预先生成粒子

### 形状参数
- **粒子形状**: 圆形、方形、三角形、自定义纹理
- **发射器类型**: 点、圆形、矩形、球体
- **发射器大小**: 发射器的尺寸参数
- **发射器半径**: 圆形/球体发射器的半径

### 外观参数
- **起始/结束大小**: 粒子在生命周期中的大小变化
- **起始/结束颜色**: 粒子在生命周期中的颜色变化
- **混合模式**: 粒子的渲染混合方式
- **渲染模式**: 3D、广告牌、拉伸等渲染方式

### 运动参数
- **起始速度**: 粒子初始速度范围
- **重力**: 影响粒子运动的重力向量

## 预设效果

### 火焰效果
- 橙红色粒子
- 向上运动
- 逐渐变小和透明

### 烟雾效果
- 灰色粒子
- 缓慢向上飘散
- 逐渐变大和透明

### 雪花效果
- 白色小粒子
- 向下飘落
- 大范围发射

## 技术实现

### 架构设计
- 基于 React + TypeScript 构建
- 使用 Ant Design 组件库
- 集成 DL Engine 底层引擎
- 支持国际化

### 核心功能
- 实时参数调节和预览
- 引擎集成和粒子系统管理
- 表单验证和数据持久化
- 响应式布局设计

### 性能优化
- 使用 useRef 避免不必要的重渲染
- 防抖处理参数变化
- 内存管理和资源清理

## 开发指南

### 添加新预设
1. 在 `handlePresetSelect` 函数中添加新的 case
2. 定义预设参数对象
3. 更新预设按钮UI

### 扩展参数控制
1. 在表单中添加新的 Form.Item
2. 在 `updateParticleSystem` 函数中处理新参数
3. 更新类型定义

### 自定义渲染
1. 修改 `setupPreviewScene` 函数
2. 集成自定义渲染逻辑
3. 处理渲染状态管理

## 注意事项

1. **引擎依赖**: 确保 DL Engine 正确初始化
2. **Canvas 支持**: 需要浏览器支持 WebGL
3. **性能考虑**: 大量粒子可能影响性能
4. **内存管理**: 及时清理不用的粒子系统

## 故障排除

### 常见问题
1. **预览黑屏**: 检查 Canvas 初始化和引擎状态
2. **参数不生效**: 确认表单验证通过
3. **性能问题**: 降低最大粒子数或发射率

### 调试技巧
1. 开启浏览器开发者工具
2. 查看控制台日志输出
3. 检查引擎状态和错误信息
