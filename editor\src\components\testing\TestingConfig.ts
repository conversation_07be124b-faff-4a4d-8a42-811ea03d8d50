/**
 * 用户测试配置
 */

export interface TestingConfig {
  // 基础配置
  autoSaveInterval: number; // 自动保存间隔（毫秒）
  recordingInterval: number; // 录制间隔（毫秒）
  maxSessionsHistory: number; // 最大会话历史记录数
  
  // 功能开关
  enableAutoReporting: boolean; // 是否启用自动报告生成
  enableBehaviorAnalysis: boolean; // 是否启用行为分析
  enableScreenshots: boolean; // 是否启用截图功能
  enableVideoRecording: boolean; // 是否启用视频录制
  
  // 任务配置
  defaultTasks: TestTaskTemplate[];
  
  // UI配置
  theme: 'light' | 'dark';
  language: 'zh-CN' | 'en-US';
  showAdvancedFeatures: boolean;
}

export interface TestTaskTemplate {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'easy' | 'medium' | 'hard';
  estimatedTime: number; // 预估完成时间（秒）
  tags?: string[];
  dependencies?: string[];
  subtasks?: TestTaskTemplate[];
  weight?: number;
}

/**
 * 默认测试配置
 */
export const defaultTestingConfig: TestingConfig = {
  // 基础配置
  autoSaveInterval: 60000, // 1分钟
  recordingInterval: 1000, // 1秒
  maxSessionsHistory: 10,
  
  // 功能开关
  enableAutoReporting: true,
  enableBehaviorAnalysis: true,
  enableScreenshots: true,
  enableVideoRecording: false,
  
  // 任务配置
  defaultTasks: [
    {
      id: 'basic_navigation',
      title: '基础导航',
      description: '学习编辑器的基本导航功能',
      category: 'basics',
      difficulty: 'easy',
      estimatedTime: 120,
      tags: ['导航', '基础'],
      subtasks: [
        {
          id: 'basic_navigation_1',
          title: '打开场景视图',
          description: '找到并打开场景视图窗口',
          category: 'basics',
          difficulty: 'easy',
          estimatedTime: 30
        },
        {
          id: 'basic_navigation_2',
          title: '使用鼠标控制视角',
          description: '使用鼠标旋转、缩放和平移视角',
          category: 'basics',
          difficulty: 'easy',
          estimatedTime: 60
        },
        {
          id: 'basic_navigation_3',
          title: '重置视角',
          description: '将视角重置到默认位置',
          category: 'basics',
          difficulty: 'easy',
          estimatedTime: 30
        }
      ]
    },
    {
      id: 'object_creation',
      title: '对象创建',
      description: '学习如何在场景中创建3D对象',
      category: 'modeling',
      difficulty: 'easy',
      estimatedTime: 180,
      tags: ['建模', '对象'],
      dependencies: ['basic_navigation'],
      subtasks: [
        {
          id: 'object_creation_1',
          title: '创建立方体',
          description: '在场景中添加一个立方体对象',
          category: 'modeling',
          difficulty: 'easy',
          estimatedTime: 60
        },
        {
          id: 'object_creation_2',
          title: '创建球体',
          description: '在场景中添加一个球体对象',
          category: 'modeling',
          difficulty: 'easy',
          estimatedTime: 60
        },
        {
          id: 'object_creation_3',
          title: '调整对象位置',
          description: '移动创建的对象到不同位置',
          category: 'modeling',
          difficulty: 'easy',
          estimatedTime: 60
        }
      ]
    },
    {
      id: 'collaboration_basics',
      title: '协作基础',
      description: '学习多用户协作编辑功能',
      category: 'collaboration',
      difficulty: 'medium',
      estimatedTime: 300,
      tags: ['协作', '多用户'],
      dependencies: ['object_creation'],
      subtasks: [
        {
          id: 'collaboration_1',
          title: '邀请协作者',
          description: '邀请另一个用户加入当前项目',
          category: 'collaboration',
          difficulty: 'medium',
          estimatedTime: 120
        },
        {
          id: 'collaboration_2',
          title: '实时编辑',
          description: '与协作者同时编辑场景对象',
          category: 'collaboration',
          difficulty: 'medium',
          estimatedTime: 120
        },
        {
          id: 'collaboration_3',
          title: '冲突解决',
          description: '处理编辑冲突并解决',
          category: 'collaboration',
          difficulty: 'medium',
          estimatedTime: 60
        }
      ]
    },
    {
      id: 'advanced_features',
      title: '高级功能',
      description: '探索编辑器的高级功能',
      category: 'advanced',
      difficulty: 'hard',
      estimatedTime: 600,
      tags: ['高级', '功能'],
      dependencies: ['collaboration_basics'],
      subtasks: [
        {
          id: 'advanced_1',
          title: '材质编辑',
          description: '为对象应用和编辑材质',
          category: 'advanced',
          difficulty: 'hard',
          estimatedTime: 180
        },
        {
          id: 'advanced_2',
          title: '光照设置',
          description: '配置场景光照效果',
          category: 'advanced',
          difficulty: 'hard',
          estimatedTime: 180
        },
        {
          id: 'advanced_3',
          title: '动画制作',
          description: '创建简单的对象动画',
          category: 'advanced',
          difficulty: 'hard',
          estimatedTime: 240
        }
      ]
    }
  ],
  
  // UI配置
  theme: 'light',
  language: 'zh-CN',
  showAdvancedFeatures: false
};

/**
 * 测试任务类别配置
 */
export const taskCategories = {
  basics: {
    name: '基础操作',
    color: '#52c41a',
    icon: 'UserOutlined'
  },
  modeling: {
    name: '建模',
    color: '#1890ff',
    icon: 'BuildOutlined'
  },
  collaboration: {
    name: '协作',
    color: '#722ed1',
    icon: 'TeamOutlined'
  },
  advanced: {
    name: '高级功能',
    color: '#fa541c',
    icon: 'ExperimentOutlined'
  }
};

/**
 * 测试难度配置
 */
export const difficultyLevels = {
  easy: {
    name: '简单',
    color: '#52c41a',
    points: 1
  },
  medium: {
    name: '中等',
    color: '#faad14',
    points: 2
  },
  hard: {
    name: '困难',
    color: '#f5222d',
    points: 3
  }
};
