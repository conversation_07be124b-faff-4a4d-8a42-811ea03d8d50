/**
 * 肌肉编辑器样式
 */

.muscle-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.muscle-editor-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.muscle-editor-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.muscle-list {
  flex: 1;
  padding: 16px;
  border-right: 1px solid #f0f0f0;
  overflow-y: auto;
}

.muscle-properties {
  width: 300px;
  padding: 16px;
  overflow-y: auto;
  background: #fafafa;
}

.muscle-properties h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.property-group {
  margin-bottom: 16px;
}

.property-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
  font-weight: 500;
  color: #595959;
}

.selected-row {
  background-color: #e6f7ff !important;
}

.selected-row:hover {
  background-color: #bae7ff !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .muscle-editor-content {
    flex-direction: column;
  }
  
  .muscle-list {
    border-right: none;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .muscle-properties {
    width: 100%;
  }
}
