/**
 * 性能监控组件
 * 监控 TransformEditor 和编辑器的性能指标
 */
import React, { useState, useEffect, useRef } from 'react';
import { Card, Statistic, Row, Col, Progress, Switch, Typography, Divider, List, Tag } from 'antd';
import {
  ThunderboltOutlined,
  ClockCircleOutlined,
  DatabaseOutlined,
  LineChartOutlined,
  BugOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;

interface PerformanceMetrics {
  renderCount: number;
  lastRenderTime: number;
  averageRenderTime: number;
  memoryUsage: number;
  transformUpdates: number;
  undoOperations: number;
  redoOperations: number;
}

interface PerformanceLog {
  timestamp: number;
  type: 'render' | 'transform' | 'undo' | 'redo' | 'memory';
  duration?: number;
  details: string;
}

/**
 * 性能监控组件
 */
const PerformanceMonitor: React.FC = () => {
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderCount: 0,
    lastRenderTime: 0,
    averageRenderTime: 0,
    memoryUsage: 0,
    transformUpdates: 0,
    undoOperations: 0,
    redoOperations: 0
  });
  const [performanceLogs, setPerformanceLogs] = useState<PerformanceLog[]>([]);
  const [fps, setFps] = useState(60);
  
  const renderTimes = useRef<number[]>([]);
  const lastFrameTime = useRef<number>(performance.now());
  const frameCount = useRef<number>(0);

  // 监控渲染性能
  useEffect(() => {
    if (!isMonitoring) return;

    const measureRenderPerformance = () => {
      const now = performance.now();
      const deltaTime = now - lastFrameTime.current;
      
      if (deltaTime >= 16) { // 约60fps
        frameCount.current++;
        const currentFps = 1000 / deltaTime;
        setFps(Math.round(currentFps));
        
        renderTimes.current.push(deltaTime);
        if (renderTimes.current.length > 100) {
          renderTimes.current.shift();
        }
        
        const averageTime = renderTimes.current.reduce((a, b) => a + b, 0) / renderTimes.current.length;
        
        setMetrics(prev => ({
          ...prev,
          renderCount: frameCount.current,
          lastRenderTime: deltaTime,
          averageRenderTime: averageTime
        }));
        
        lastFrameTime.current = now;
      }
      
      if (isMonitoring) {
        requestAnimationFrame(measureRenderPerformance);
      }
    };

    requestAnimationFrame(measureRenderPerformance);
  }, [isMonitoring]);

  // 监控内存使用
  useEffect(() => {
    if (!isMonitoring) return;

    const monitorMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const memoryUsage = memory.usedJSHeapSize / 1024 / 1024; // MB
        
        setMetrics(prev => ({
          ...prev,
          memoryUsage: Math.round(memoryUsage * 100) / 100
        }));
        
        addPerformanceLog({
          type: 'memory',
          details: `内存使用: ${memoryUsage.toFixed(2)} MB`
        });
      }
    };

    const interval = setInterval(monitorMemory, 5000);
    return () => clearInterval(interval);
  }, [isMonitoring]);

  // 添加性能日志
  const addPerformanceLog = (log: Omit<PerformanceLog, 'timestamp'>) => {
    const newLog: PerformanceLog = {
      ...log,
      timestamp: Date.now()
    };
    
    setPerformanceLogs(prev => {
      const updated = [newLog, ...prev].slice(0, 50); // 保留最近50条
      return updated;
    });
  };

  // 模拟变换更新监控
  const simulateTransformUpdate = () => {
    const startTime = performance.now();
    
    // 模拟变换计算
    setTimeout(() => {
      const duration = performance.now() - startTime;
      
      setMetrics(prev => ({
        ...prev,
        transformUpdates: prev.transformUpdates + 1
      }));
      
      addPerformanceLog({
        type: 'transform',
        duration,
        details: `变换更新耗时: ${duration.toFixed(2)}ms`
      });
    }, Math.random() * 10);
  };

  // 模拟撤销操作监控
  const simulateUndoOperation = () => {
    const startTime = performance.now();
    
    setTimeout(() => {
      const duration = performance.now() - startTime;
      
      setMetrics(prev => ({
        ...prev,
        undoOperations: prev.undoOperations + 1
      }));
      
      addPerformanceLog({
        type: 'undo',
        duration,
        details: `撤销操作耗时: ${duration.toFixed(2)}ms`
      });
    }, Math.random() * 5);
  };

  // 重置监控数据
  const resetMetrics = () => {
    setMetrics({
      renderCount: 0,
      lastRenderTime: 0,
      averageRenderTime: 0,
      memoryUsage: 0,
      transformUpdates: 0,
      undoOperations: 0,
      redoOperations: 0
    });
    setPerformanceLogs([]);
    renderTimes.current = [];
    frameCount.current = 0;
  };

  // 获取性能等级
  const getPerformanceLevel = (value: number, thresholds: number[]) => {
    if (value <= thresholds[0]) return { level: 'excellent', color: '#52c41a' };
    if (value <= thresholds[1]) return { level: 'good', color: '#1890ff' };
    if (value <= thresholds[2]) return { level: 'warning', color: '#faad14' };
    return { level: 'poor', color: '#f5222d' };
  };

  const fpsLevel = getPerformanceLevel(60 - fps, [5, 10, 20]);
  const memoryLevel = getPerformanceLevel(metrics.memoryUsage, [50, 100, 200]);
  const renderLevel = getPerformanceLevel(metrics.averageRenderTime, [16, 33, 50]);

  return (
    <div style={{ padding: '20px' }}>
      <Card 
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <span>
              <BugOutlined style={{ marginRight: '8px' }} />
              性能监控
            </span>
            <Switch
              checked={isMonitoring}
              onChange={setIsMonitoring}
              checkedChildren="监控中"
              unCheckedChildren="已停止"
            />
          </div>
        }
        extra={
          <Text 
            type="secondary" 
            style={{ cursor: 'pointer' }}
            onClick={resetMetrics}
          >
            重置数据
          </Text>
        }
      >
        {/* 核心性能指标 */}
        <Row gutter={16} style={{ marginBottom: '20px' }}>
          <Col span={6}>
            <Statistic
              title="帧率 (FPS)"
              value={fps}
              precision={0}
              valueStyle={{ color: fpsLevel.color }}
              prefix={<ThunderboltOutlined />}
              suffix="fps"
            />
            <Progress 
              percent={Math.min(fps / 60 * 100, 100)} 
              strokeColor={fpsLevel.color}
              size="small"
              showInfo={false}
            />
          </Col>
          
          <Col span={6}>
            <Statistic
              title="平均渲染时间"
              value={metrics.averageRenderTime}
              precision={2}
              valueStyle={{ color: renderLevel.color }}
              prefix={<ClockCircleOutlined />}
              suffix="ms"
            />
            <Progress 
              percent={Math.max(0, 100 - (metrics.averageRenderTime / 50 * 100))} 
              strokeColor={renderLevel.color}
              size="small"
              showInfo={false}
            />
          </Col>
          
          <Col span={6}>
            <Statistic
              title="内存使用"
              value={metrics.memoryUsage}
              precision={2}
              valueStyle={{ color: memoryLevel.color }}
              prefix={<DatabaseOutlined />}
              suffix="MB"
            />
            <Progress 
              percent={Math.min(metrics.memoryUsage / 200 * 100, 100)} 
              strokeColor={memoryLevel.color}
              size="small"
              showInfo={false}
            />
          </Col>
          
          <Col span={6}>
            <Statistic
              title="渲染次数"
              value={metrics.renderCount}
              prefix={<LineChartOutlined />}
            />
          </Col>
        </Row>

        <Divider />

        {/* 操作统计 */}
        <Row gutter={16} style={{ marginBottom: '20px' }}>
          <Col span={8}>
            <Card size="small">
              <Statistic
                title="变换更新"
                value={metrics.transformUpdates}
                valueStyle={{ fontSize: '18px' }}
              />
              <div style={{ marginTop: '8px' }}>
                <Text
                  style={{ fontSize: '12px', color: '#1890ff', cursor: 'pointer' }}
                  onClick={simulateTransformUpdate}
                >
                  模拟更新
                </Text>
              </div>
            </Card>
          </Col>
          
          <Col span={8}>
            <Card size="small">
              <Statistic
                title="撤销操作"
                value={metrics.undoOperations}
                valueStyle={{ fontSize: '18px' }}
              />
              <div style={{ marginTop: '8px' }}>
                <Text
                  style={{ fontSize: '12px', color: '#1890ff', cursor: 'pointer' }}
                  onClick={simulateUndoOperation}
                >
                  模拟撤销
                </Text>
              </div>
            </Card>
          </Col>
          
          <Col span={8}>
            <Card size="small">
              <Statistic
                title="重做操作"
                value={metrics.redoOperations}
                valueStyle={{ fontSize: '18px' }}
              />
            </Card>
          </Col>
        </Row>

        <Divider />

        {/* 性能日志 */}
        <Title level={5}>性能日志</Title>
        <List
          size="small"
          dataSource={performanceLogs.slice(0, 10)}
          renderItem={(log) => (
            <List.Item>
              <div style={{ width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <Tag color={
                    log.type === 'render' ? 'blue' :
                    log.type === 'transform' ? 'green' :
                    log.type === 'undo' ? 'orange' :
                    log.type === 'redo' ? 'purple' : 'default'
                  }>
                    {log.type.toUpperCase()}
                  </Tag>
                  <Text>{log.details}</Text>
                </div>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {new Date(log.timestamp).toLocaleTimeString()}
                </Text>
              </div>
            </List.Item>
          )}
          locale={{ emptyText: '暂无性能日志' }}
        />
      </Card>
    </div>
  );
};

export default PerformanceMonitor;
