/**
 * 聊天消息组件
 * 显示单条聊天消息，支持不同类型的消息展示和交互
 */
import React, { useState, useCallback } from 'react';
import {
  Avatar,
  Button,
  Tag,
  Tooltip,
  Space,
  Typography,
  Popover
} from 'antd';
import {
  UserOutlined,
  RobotOutlined,
  InfoCircleOutlined,
  CopyOutlined,
  LikeOutlined,
  DislikeOutlined,
  ReloadOutlined,
  PlayCircleOutlined,
  CodeOutlined,
  SettingOutlined,
  BulbOutlined
} from '@ant-design/icons';
import { ChatMessage, AIAction } from './AIChatPanel';
import './ChatMessageComponent.scss';

const { Text, Paragraph } = Typography;

// 组件属性
export interface ChatMessageComponentProps {
  message: ChatMessage;
  showMetadata?: boolean;
  onActionClick?: (action: AIAction) => void;
  onSuggestionClick?: (suggestion: string) => void;
  onFeedback?: (messageId: string, feedback: 'positive' | 'negative') => void;
  onCopy?: (content: string) => void;
  onRegenerate?: (messageId: string) => void;
}

/**
 * 聊天消息组件
 */
export const ChatMessageComponent: React.FC<ChatMessageComponentProps> = ({
  message,
  showMetadata = false,
  onActionClick,
  onSuggestionClick,
  onFeedback,
  onCopy,
  onRegenerate
}) => {
  const [feedbackGiven, setFeedbackGiven] = useState<'positive' | 'negative' | null>(null);

  /**
   * 获取消息图标
   */
  const getMessageIcon = useCallback(() => {
    switch (message.type) {
      case 'user':
        return <UserOutlined />;
      case 'assistant':
        return <RobotOutlined />;
      case 'system':
        return <InfoCircleOutlined />;
      default:
        return <InfoCircleOutlined />;
    }
  }, [message.type]);

  /**
   * 获取消息样式类名
   */
  const getMessageClassName = useCallback(() => {
    const baseClass = 'chat-message';
    const typeClass = `${baseClass}--${message.type}`;
    const statusClass = message.status ? `${baseClass}--${message.status}` : '';
    
    return `${baseClass} ${typeClass} ${statusClass}`.trim();
  }, [message.type, message.status]);

  /**
   * 格式化时间戳
   */
  const formatTimestamp = useCallback((timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) {
      return '刚刚';
    } else if (diffMins < 60) {
      return `${diffMins}分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  }, []);

  /**
   * 处理反馈
   */
  const handleFeedback = useCallback((feedback: 'positive' | 'negative') => {
    if (feedbackGiven === feedback) return;
    
    setFeedbackGiven(feedback);
    onFeedback?.(message.id, feedback);
  }, [feedbackGiven, message.id, onFeedback]);

  /**
   * 处理复制
   */
  const handleCopy = useCallback(() => {
    onCopy?.(message.content);
  }, [message.content, onCopy]);

  /**
   * 处理重新生成
   */
  const handleRegenerate = useCallback(() => {
    onRegenerate?.(message.id);
  }, [message.id, onRegenerate]);

  /**
   * 渲染操作按钮
   */
  const renderActionButtons = useCallback(() => {
    if (!message.actions || message.actions.length === 0) {
      return null;
    }

    return (
      <div className="message-actions">
        <Text type="secondary" className="actions-label">建议操作:</Text>
        <Space wrap>
          {message.actions.map((action, index) => (
            <Button
              key={index}
              type={action.primary ? 'primary' : 'default'}
              size="small"
              icon={getActionIcon(action.type)}
              onClick={() => onActionClick?.(action)}
              className="action-button"
            >
              {action.label}
            </Button>
          ))}
        </Space>
      </div>
    );
  }, [message.actions, onActionClick]);

  /**
   * 渲染建议
   */
  const renderSuggestions = useCallback(() => {
    if (!message.suggestions || message.suggestions.length === 0) {
      return null;
    }

    return (
      <div className="message-suggestions">
        <Text type="secondary" className="suggestions-label">
          <BulbOutlined /> 你可能想问:
        </Text>
        <div className="suggestions-list">
          {message.suggestions.map((suggestion, index) => (
            <Tag
              key={index}
              className="suggestion-tag"
              onClick={() => onSuggestionClick?.(suggestion)}
            >
              {suggestion}
            </Tag>
          ))}
        </div>
      </div>
    );
  }, [message.suggestions, onSuggestionClick]);

  /**
   * 渲染元数据
   */
  const renderMetadata = useCallback(() => {
    if (!showMetadata || !message.metadata) {
      return null;
    }

    const metadata = message.metadata;
    
    const metadataContent = (
      <div className="metadata-content">
        {metadata.model && (
          <div className="metadata-item">
            <Text strong>模型:</Text> <Text code>{metadata.model}</Text>
          </div>
        )}
        {metadata.confidence !== undefined && (
          <div className="metadata-item">
            <Text strong>置信度:</Text> 
            <Text>{(metadata.confidence * 100).toFixed(1)}%</Text>
          </div>
        )}
        {metadata.processingTime !== undefined && (
          <div className="metadata-item">
            <Text strong>处理时间:</Text> 
            <Text>{metadata.processingTime}ms</Text>
          </div>
        )}
        {metadata.tokens !== undefined && (
          <div className="metadata-item">
            <Text strong>Token数:</Text> 
            <Text>{metadata.tokens}</Text>
          </div>
        )}
        {metadata.context && metadata.context.length > 0 && (
          <div className="metadata-item">
            <Text strong>上下文:</Text>
            <div className="context-tags">
              {metadata.context.map((ctx, index) => (
                <Tag key={index}>{ctx}</Tag>
              ))}
            </div>
          </div>
        )}
      </div>
    );

    return (
      <Popover content={metadataContent} title="消息元数据" trigger="hover">
        <Button 
          type="text" 
          size="small" 
          icon={<InfoCircleOutlined />}
          className="metadata-button"
        />
      </Popover>
    );
  }, [showMetadata, message.metadata]);

  /**
   * 渲染消息工具栏
   */
  const renderMessageToolbar = useCallback(() => {
    if (message.type === 'system') {
      return null;
    }

    return (
      <div className="message-toolbar">
        <div className="toolbar-left">
          <Text type="secondary" className="message-time">
            {formatTimestamp(message.timestamp)}
          </Text>
          {renderMetadata()}
        </div>
        
        <div className="toolbar-right">
          <Space size="small">
            {/* 复制按钮 */}
            <Tooltip title="复制">
              <Button
                type="text"
                size="small"
                icon={<CopyOutlined />}
                onClick={handleCopy}
              />
            </Tooltip>

            {/* AI消息的反馈按钮 */}
            {message.type === 'assistant' && (
              <>
                <Tooltip title="有用">
                  <Button
                    type="text"
                    size="small"
                    icon={<LikeOutlined />}
                    className={feedbackGiven === 'positive' ? 'feedback-active' : ''}
                    onClick={() => handleFeedback('positive')}
                  />
                </Tooltip>
                <Tooltip title="无用">
                  <Button
                    type="text"
                    size="small"
                    icon={<DislikeOutlined />}
                    className={feedbackGiven === 'negative' ? 'feedback-active' : ''}
                    onClick={() => handleFeedback('negative')}
                  />
                </Tooltip>
                <Tooltip title="重新生成">
                  <Button
                    type="text"
                    size="small"
                    icon={<ReloadOutlined />}
                    onClick={handleRegenerate}
                  />
                </Tooltip>
              </>
            )}
          </Space>
        </div>
      </div>
    );
  }, [
    message.type, 
    message.timestamp, 
    feedbackGiven,
    formatTimestamp,
    renderMetadata,
    handleCopy,
    handleFeedback,
    handleRegenerate
  ]);

  return (
    <div className={getMessageClassName()}>
      <div className="message-header">
        <Avatar 
          icon={getMessageIcon()} 
          className={`message-avatar message-avatar--${message.type}`}
          size="small"
        />
        <div className="message-sender">
          {message.type === 'user' ? '你' : 
           message.type === 'assistant' ? 'AI助手' : '系统'}
        </div>
        {message.status && (
          <div className={`message-status message-status--${message.status}`}>
            {getStatusText(message.status)}
          </div>
        )}
      </div>

      <div className="message-body">
        <div className="message-content">
          <Paragraph className="message-text">
            {message.content}
          </Paragraph>
        </div>

        {/* 渲染操作按钮 */}
        {renderActionButtons()}

        {/* 渲染建议 */}
        {renderSuggestions()}
      </div>

      {/* 渲染工具栏 */}
      {renderMessageToolbar()}
    </div>
  );
};

/**
 * 获取操作图标
 */
function getActionIcon(actionType: string): React.ReactNode {
  switch (actionType) {
    case 'create_object':
      return <PlayCircleOutlined />;
    case 'generate_code':
      return <CodeOutlined />;
    case 'optimize_scene':
      return <SettingOutlined />;
    case 'analyze_performance':
      return <InfoCircleOutlined />;
    default:
      return <PlayCircleOutlined />;
  }
}

/**
 * 获取状态文本
 */
function getStatusText(status: string): string {
  switch (status) {
    case 'sending':
      return '发送中...';
    case 'sent':
      return '已发送';
    case 'error':
      return '发送失败';
    default:
      return '';
  }
}
