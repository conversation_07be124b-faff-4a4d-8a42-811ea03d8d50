/**
 * 环境感知编辑器组件
 * 用于编辑角色的环境感知和响应
 */
import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, InputNumber, Select, Switch, Button, Table, Space, Row, Col, message } from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined, ImportOutlined, ExportOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import { Entity } from '../../store/scene/sceneSlice';
const { Option } = Select;

interface EnvironmentAwarenessEditorProps {
  visible: boolean;
  entityId: string;
  onClose: () => void;
}

/**
 * 环境类型
 */
enum EnvironmentType {
  /** 地面 */
  GROUND = 'ground',
  /** 水面 */
  WATER = 'water',
  /** 墙壁 */
  WALL = 'wall',
  /** 斜坡 */
  SLOPE = 'slope',
  /** 台阶 */
  STAIRS = 'stairs',
  /** 障碍物 */
  OBSTACLE = 'obstacle',
  /** 交互物体 */
  INTERACTIVE = 'interactive',
  /** 危险区域 */
  DANGER = 'danger',
  /** 安全区域 */
  SAFE = 'safe',
  /** 自定义 */
  CUSTOM = 'custom'
}

/**
 * 响应类型
 */
enum ResponseType {
  /** 动画 */
  ANIMATION = 'animation',
  /** 物理 */
  PHYSICS = 'physics',
  /** 声音 */
  SOUND = 'sound',
  /** 粒子 */
  PARTICLE = 'particle',
  /** 状态变化 */
  STATE = 'state',
  /** 自定义 */
  CUSTOM = 'custom'
}

/**
 * 环境感知规则
 */
interface EnvironmentRule {
  /** 规则ID */
  id: string;
  /** 规则名称 */
  name: string;
  /** 环境类型 */
  environmentType: EnvironmentType;
  /** 响应类型 */
  responseType: ResponseType;
  /** 检测距离 */
  detectionDistance: number;
  /** 检测角度 */
  detectionAngle: number;
  /** 是否启用 */
  enabled: boolean;
  /** 优先级 */
  priority: number;
  /** 冷却时间（秒） */
  cooldown: number;
  /** 响应参数 */
  responseParams: Record<string, any>;
  /** 标签 */
  tags: string[];
}

/**
 * 环境感知编辑器组件
 */
const EnvironmentAwarenessEditor: React.FC<EnvironmentAwarenessEditorProps> = ({ visible, entityId, onClose }) => {
  const { t } = useTranslation();

  // 获取实体数据
  const entity = useSelector((state: RootState) =>
    state.scene.entities.find((entity: Entity) => entity.id === entityId)
  );
  
  // 环境规则列表
  const [rules, setRules] = useState<EnvironmentRule[]>([]);
  
  // 当前编辑的规则
  const [currentRule, setCurrentRule] = useState<EnvironmentRule | null>(null);
  
  // 是否显示规则编辑对话框
  const [showRuleModal, setShowRuleModal] = useState(false);
  
  // 规则表单
  const [ruleForm] = Form.useForm();
  
  // 加载规则
  useEffect(() => {
    if (visible && entity) {
      // 模拟从实体加载规则
      // 实际实现应该从实体或环境感知系统获取规则数据
      const mockRules: EnvironmentRule[] = [
        {
          id: 'water_detection',
          name: '水面检测',
          environmentType: EnvironmentType.WATER,
          responseType: ResponseType.ANIMATION,
          detectionDistance: 0.5,
          detectionAngle: 360,
          enabled: true,
          priority: 2,
          cooldown: 0,
          responseParams: {
            animation: 'swim',
            transitionTime: 0.3
          },
          tags: ['水', '游泳']
        },
        {
          id: 'slope_detection',
          name: '斜坡检测',
          environmentType: EnvironmentType.SLOPE,
          responseType: ResponseType.PHYSICS,
          detectionDistance: 0.5,
          detectionAngle: 180,
          enabled: true,
          priority: 1,
          cooldown: 0,
          responseParams: {
            slopeAngleThreshold: 30,
            slideSpeedMultiplier: 1.5
          },
          tags: ['斜坡', '滑动']
        },
        {
          id: 'obstacle_detection',
          name: '障碍物检测',
          environmentType: EnvironmentType.OBSTACLE,
          responseType: ResponseType.ANIMATION,
          detectionDistance: 1.0,
          detectionAngle: 120,
          enabled: true,
          priority: 2,
          cooldown: 1.0,
          responseParams: {
            animation: 'obstacle_avoid',
            transitionTime: 0.2
          },
          tags: ['障碍物', '避让']
        }
      ];
      
      setRules(mockRules);
    }
  }, [visible, entity]);
  
  // 添加规则
  const handleAddRule = () => {
    // 创建新规则
    const newRule: EnvironmentRule = {
      id: `rule_${Date.now()}`,
      name: '新规则',
      environmentType: EnvironmentType.GROUND,
      responseType: ResponseType.ANIMATION,
      detectionDistance: 1.0,
      detectionAngle: 360,
      enabled: true,
      priority: 1,
      cooldown: 0,
      responseParams: {},
      tags: []
    };
    
    setCurrentRule(newRule);
    
    // 设置表单值
    ruleForm.setFieldsValue({
      name: newRule.name,
      environmentType: newRule.environmentType,
      responseType: newRule.responseType,
      detectionDistance: newRule.detectionDistance,
      detectionAngle: newRule.detectionAngle,
      enabled: newRule.enabled,
      priority: newRule.priority,
      cooldown: newRule.cooldown,
      responseParams: JSON.stringify(newRule.responseParams, null, 2),
      tags: newRule.tags.join(', ')
    });
    
    setShowRuleModal(true);
  };
  
  // 编辑规则
  const handleEditRule = (rule: EnvironmentRule) => {
    setCurrentRule(rule);
    
    // 设置表单值
    ruleForm.setFieldsValue({
      name: rule.name,
      environmentType: rule.environmentType,
      responseType: rule.responseType,
      detectionDistance: rule.detectionDistance,
      detectionAngle: rule.detectionAngle,
      enabled: rule.enabled,
      priority: rule.priority,
      cooldown: rule.cooldown,
      responseParams: JSON.stringify(rule.responseParams, null, 2),
      tags: rule.tags.join(', ')
    });
    
    setShowRuleModal(true);
  };
  
  // 删除规则
  const handleDeleteRule = (ruleId: string) => {
    setRules(rules.filter(rule => rule.id !== ruleId));
  };
  
  // 保存规则
  const handleSaveRule = () => {
    ruleForm.validateFields().then(values => {
      if (!currentRule) return;
      
      let responseParams = {};
      try {
        responseParams = JSON.parse(values.responseParams);
      } catch (error) {
        message.error(t('editor.avatar.invalidParamsJson') || '无效的JSON格式');
        return;
      }
      
      // 解析标签
      const tags = values.tags ? values.tags.split(',').map((tag: string) => tag.trim()) : [];
      
      // 更新规则
      const updatedRule: EnvironmentRule = {
        ...currentRule,
        name: values.name,
        environmentType: values.environmentType,
        responseType: values.responseType,
        detectionDistance: values.detectionDistance,
        detectionAngle: values.detectionAngle,
        enabled: values.enabled,
        priority: values.priority,
        cooldown: values.cooldown,
        responseParams,
        tags
      };
      
      // 更新规则列表
      const newRules = rules.map(rule => 
        rule.id === updatedRule.id ? updatedRule : rule
      );
      
      if (!newRules.includes(updatedRule)) {
        newRules.push(updatedRule);
      }
      
      setRules(newRules);
      setShowRuleModal(false);
      setCurrentRule(null);
    });
  };
  
  // 导出规则
  const handleExportRules = () => {
    // 创建JSON
    const json = JSON.stringify(rules, null, 2);
    
    // 创建下载链接
    const blob = new Blob([json], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `environment_rules_${entityId}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    message.success(t('editor.common.exportSuccess') || '导出成功');
  };
  
  // 导入规则
  const handleImportRules = () => {
    // 创建文件输入元素
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'application/json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;
      
      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const importedRules = JSON.parse(event.target?.result as string);
          
          // 验证导入的规则
          if (!Array.isArray(importedRules)) {
            throw new Error('Invalid rules format');
          }
          
          setRules(importedRules);
          message.success(t('editor.common.importSuccess') || '导入成功');
        } catch (error) {
          message.error(t('editor.common.importError') || '导入失败');
          console.error('导入规则失败:', error);
        }
      };
      reader.readAsText(file);
    };
    input.click();
  };
  
  // 规则表格列
  const ruleColumns = [
    {
      title: t('editor.common.name') || '名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: t('editor.avatar.environmentType') || '环境类型',
      dataIndex: 'environmentType',
      key: 'environmentType',
      render: (type: EnvironmentType) => t(`editor.avatar.environmentType_${type}`) || type
    },
    {
      title: t('editor.avatar.responseType') || '响应类型',
      dataIndex: 'responseType',
      key: 'responseType',
      render: (type: ResponseType) => t(`editor.avatar.responseType_${type}`) || type
    },
    {
      title: t('editor.avatar.detectionDistance') || '检测距离',
      dataIndex: 'detectionDistance',
      key: 'detectionDistance'
    },
    {
      title: t('editor.avatar.priority') || '优先级',
      dataIndex: 'priority',
      key: 'priority'
    },
    {
      title: t('editor.common.enabled') || '启用',
      dataIndex: 'enabled',
      key: 'enabled',
      render: (enabled: boolean) => (
        <Switch checked={enabled} disabled />
      )
    },
    {
      title: t('editor.common.actions') || '操作',
      key: 'actions',
      render: (_: any, record: EnvironmentRule) => (
        <Space>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditRule(record)}
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteRule(record.id)}
          />
        </Space>
      )
    }
  ];
  
  return (
    <Modal
      title={t('editor.avatar.environmentAwarenessEditor') || '环境感知编辑器'}
      open={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="import" icon={<ImportOutlined />} onClick={handleImportRules}>
          {t('editor.common.import') || '导入'}
        </Button>,
        <Button key="export" icon={<ExportOutlined />} onClick={handleExportRules}>
          {t('editor.common.export') || '导出'}
        </Button>,
        <Button key="close" onClick={onClose}>
          {t('editor.common.close') || '关闭'}
        </Button>
      ]}
    >
      <div className="environment-editor-container">
        <div style={{ marginBottom: 16 }}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddRule}
          >
            {t('editor.avatar.addRule') || '添加规则'}
          </Button>
        </div>
        
        <Table 
          dataSource={rules} 
          columns={ruleColumns} 
          rowKey="id"
          pagination={false}
          onRow={(record) => ({
            onClick: () => handleEditRule(record)
          })}
        />
      </div>
      
      {/* 规则编辑对话框 */}
      <Modal
        title={currentRule?.id ? (t('editor.avatar.editRule') || '编辑规则') : (t('editor.avatar.addRule') || '添加规则')}
        open={showRuleModal}
        onCancel={() => setShowRuleModal(false)}
        onOk={handleSaveRule}
        width={600}
      >
        <Form
          form={ruleForm}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label={t('editor.common.name') || '名称'}
            rules={[{ required: true, message: t('editor.common.nameRequired') || '请输入名称' }]}
          >
            <Input />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="environmentType"
                label={t('editor.avatar.environmentType') || '环境类型'}
                rules={[{ required: true, message: t('editor.avatar.environmentTypeRequired') || '请选择环境类型' }]}
              >
                <Select>
                  <Option value={EnvironmentType.GROUND}>{t('editor.avatar.environmentType_ground') || '地面'}</Option>
                  <Option value={EnvironmentType.WATER}>{t('editor.avatar.environmentType_water') || '水面'}</Option>
                  <Option value={EnvironmentType.WALL}>{t('editor.avatar.environmentType_wall') || '墙壁'}</Option>
                  <Option value={EnvironmentType.SLOPE}>{t('editor.avatar.environmentType_slope') || '斜坡'}</Option>
                  <Option value={EnvironmentType.STAIRS}>{t('editor.avatar.environmentType_stairs') || '台阶'}</Option>
                  <Option value={EnvironmentType.OBSTACLE}>{t('editor.avatar.environmentType_obstacle') || '障碍物'}</Option>
                  <Option value={EnvironmentType.INTERACTIVE}>{t('editor.avatar.environmentType_interactive') || '交互物体'}</Option>
                  <Option value={EnvironmentType.DANGER}>{t('editor.avatar.environmentType_danger') || '危险区域'}</Option>
                  <Option value={EnvironmentType.SAFE}>{t('editor.avatar.environmentType_safe') || '安全区域'}</Option>
                  <Option value={EnvironmentType.CUSTOM}>{t('editor.avatar.environmentType_custom') || '自定义'}</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="responseType"
                label={t('editor.avatar.responseType') || '响应类型'}
                rules={[{ required: true, message: t('editor.avatar.responseTypeRequired') || '请选择响应类型' }]}
              >
                <Select>
                  <Option value={ResponseType.ANIMATION}>{t('editor.avatar.responseType_animation') || '动画'}</Option>
                  <Option value={ResponseType.PHYSICS}>{t('editor.avatar.responseType_physics') || '物理'}</Option>
                  <Option value={ResponseType.SOUND}>{t('editor.avatar.responseType_sound') || '声音'}</Option>
                  <Option value={ResponseType.PARTICLE}>{t('editor.avatar.responseType_particle') || '粒子'}</Option>
                  <Option value={ResponseType.STATE}>{t('editor.avatar.responseType_state') || '状态变化'}</Option>
                  <Option value={ResponseType.CUSTOM}>{t('editor.avatar.responseType_custom') || '自定义'}</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="detectionDistance"
                label={t('editor.avatar.detectionDistance') || '检测距离'}
                rules={[{ required: true, type: 'number', min: 0, message: t('editor.avatar.detectionDistanceRequired') || '请输入检测距离' }]}
              >
                <InputNumber min={0} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="detectionAngle"
                label={t('editor.avatar.detectionAngle') || '检测角度'}
                rules={[{ required: true, type: 'number', min: 0, max: 360, message: t('editor.avatar.detectionAngleRequired') || '请输入检测角度' }]}
              >
                <InputNumber min={0} max={360} step={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="enabled"
                label={t('editor.common.enabled') || '启用'}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="priority"
                label={t('editor.avatar.priority') || '优先级'}
                rules={[{ required: true, type: 'number', min: 0, message: t('editor.avatar.priorityRequired') || '请输入优先级' }]}
              >
                <InputNumber min={0} step={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="cooldown"
                label={t('editor.avatar.cooldown') || '冷却时间'}
                rules={[{ required: true, type: 'number', min: 0, message: t('editor.avatar.cooldownRequired') || '请输入冷却时间' }]}
              >
                <InputNumber min={0} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="responseParams"
            label={t('editor.avatar.responseParams') || '响应参数'}
            tooltip={t('editor.avatar.responseParamsTooltip') || '响应参数的JSON配置'}
          >
            <Input.TextArea rows={6} />
          </Form.Item>

          <Form.Item
            name="tags"
            label={t('editor.common.tags') || '标签'}
            tooltip={t('editor.common.tagsTooltip') || '用逗号分隔的标签'}
          >
            <Input placeholder={t('editor.common.tagsPlaceholder') || '请输入标签，用逗号分隔'} />
          </Form.Item>
        </Form>
      </Modal>
    </Modal>
  );
};

export default EnvironmentAwarenessEditor;
