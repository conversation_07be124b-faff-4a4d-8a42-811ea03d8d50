/**
 * 云端存储面板样式
 */

.cloud-storage-panel {
  .ant-modal-body {
    padding: 16px;
    height: 70vh;
    overflow: hidden;
  }

  // 项目列表
  .projects-list {
    height: 100%;
    display: flex;
    flex-direction: column;

    .projects-header {
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;

      .ant-btn {
        margin-right: 8px;
        margin-bottom: 8px;
      }
    }

    .ant-list {
      flex: 1;
      overflow-y: auto;

      .project-item {
        padding: 12px;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          border-color: #d9d9d9;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        &.active {
          border-color: #1890ff;
          background-color: #e6f7ff;
        }

        .project-icon {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          background: linear-gradient(45deg, #1890ff, #52c41a);
          color: #ffffff;
          font-size: 18px;
        }

        .ant-list-item-meta {
          .ant-list-item-meta-title {
            margin-bottom: 4px;
            font-size: 14px;
          }

          .ant-list-item-meta-description {
            font-size: 12px;

            .ant-tag {
              margin-top: 4px;
            }
          }
        }

        .ant-list-item-action {
          margin-left: 8px;

          .ant-btn {
            margin-left: 4px;
          }
        }
      }
    }
  }

  // 文件列表
  .files-list {
    height: 100%;
    display: flex;
    flex-direction: column;

    .files-header {
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;

      .ant-upload-drag {
        padding: 8px;
        height: auto;
        background: transparent;
        border: 1px dashed #d9d9d9;

        .ant-upload-drag-container {
          padding: 0;
        }

        .ant-btn {
          border: none;
          box-shadow: none;
        }
      }
    }

    .no-project-selected {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #d9d9d9;
    }

    .ant-table {
      flex: 1;

      .ant-table-tbody {
        .ant-table-row {
          &:hover {
            background-color: #f5f5f5;
          }
        }
      }
    }

    .ant-alert {
      .ant-progress {
        margin-top: 4px;
      }
    }
  }

  // 存储统计
  .storage-stats {
    height: 100%;
    overflow-y: auto;

    .ant-statistic {
      .ant-statistic-title {
        font-size: 12px;
        color: #8c8c8c;
      }

      .ant-statistic-content {
        font-size: 18px;
        font-weight: 600;
      }
    }

    .storage-usage {
      .ant-progress {
        margin: 8px 0;
      }
    }

    .sync-status {
      .ant-badge {
        margin-bottom: 4px;
      }

      .ant-tag {
        margin-left: 8px;
      }
    }
  }
}

// 文件状态指示器
.cloud-storage-panel {
  .file-status-uploading {
    color: #1890ff;
  }

  .file-status-uploaded {
    color: #52c41a;
  }

  .file-status-downloading {
    color: #722ed1;
  }

  .file-status-syncing {
    color: #faad14;
  }

  .file-status-conflict {
    color: #ff4d4f;
  }

  .file-status-error {
    color: #ff4d4f;
  }
}

// 存储提供商图标
.cloud-storage-panel {
  .provider-aws {
    color: #ff9900;
  }

  .provider-azure {
    color: #0078d4;
  }

  .provider-google {
    color: #4285f4;
  }

  .provider-aliyun {
    color: #ff6a00;
  }

  .provider-tencent {
    color: #006eff;
  }

  .provider-custom {
    color: #8c8c8c;
  }
}

// 深色主题
.dark-theme {
  .cloud-storage-panel {
    .ant-modal-content {
      background: #2d2d2d;
      color: #cccccc;
    }

    .projects-list {
      .projects-header {
        border-bottom-color: #404040;
      }

      .ant-list {
        .project-item {
          background: #2d2d2d;
          border-color: #404040;

          &:hover {
            border-color: #555555;
            background-color: #404040;
          }

          &.active {
            border-color: #1890ff;
            background-color: #1f3a5f;
          }

          .ant-list-item-meta {
            .ant-list-item-meta-title {
              color: #ffffff;
            }

            .ant-list-item-meta-description {
              color: #cccccc;
            }
          }
        }
      }
    }

    .files-list {
      .files-header {
        border-bottom-color: #404040;
      }

      .no-project-selected {
        color: #666666;
      }

      .ant-table {
        background: #2d2d2d;

        .ant-table-thead {
          background: #2d2d2d;

          .ant-table-cell {
            background: #2d2d2d;
            border-bottom-color: #404040;
            color: #ffffff;
          }
        }

        .ant-table-tbody {
          .ant-table-cell {
            background: #2d2d2d;
            border-bottom-color: #404040;
            color: #cccccc;
          }

          .ant-table-row {
            &:hover {
              background-color: #404040;
            }
          }
        }
      }
    }

    .storage-stats {
      .ant-statistic {
        .ant-statistic-title {
          color: #8c8c8c;
        }

        .ant-statistic-content {
          color: #ffffff;
        }
      }
    }
  }
}

// 紧凑模式
.compact-theme {
  .cloud-storage-panel {
    .ant-modal-body {
      padding: 12px;
    }

    .projects-list {
      .projects-header {
        margin-bottom: 12px;
        padding-bottom: 8px;
      }

      .ant-list {
        .project-item {
          padding: 8px;
          margin-bottom: 6px;

          .project-icon {
            width: 32px;
            height: 32px;
            font-size: 14px;
          }

          .ant-list-item-meta {
            .ant-list-item-meta-title {
              font-size: 13px;
            }

            .ant-list-item-meta-description {
              font-size: 11px;
            }
          }
        }
      }
    }

    .files-list {
      .files-header {
        margin-bottom: 12px;
        padding-bottom: 8px;
      }
    }

    .storage-stats {
      .ant-statistic {
        .ant-statistic-content {
          font-size: 16px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .cloud-storage-panel {
    .ant-modal {
      width: 95% !important;
      max-width: none;
    }
  }
}

@media (max-width: 768px) {
  .cloud-storage-panel {
    .ant-row {
      .ant-col {
        margin-bottom: 16px;
      }
    }

    .projects-list {
      .projects-header {
        .ant-space {
          flex-direction: column;
          align-items: stretch;
          width: 100%;
        }
      }
    }

    .files-list {
      .files-header {
        .ant-space {
          flex-direction: column;
          align-items: stretch;
          width: 100%;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .cloud-storage-panel {
    .storage-stats {
      .ant-row {
        .ant-col {
          flex: 0 0 100%;
          max-width: 100%;
          margin-bottom: 12px;
        }
      }
    }
  }
}

// 动画效果
.cloud-storage-panel {
  .projects-list {
    .project-item {
      transition: all 0.2s ease;
    }
  }

  .files-list {
    .ant-table-row {
      transition: all 0.2s ease;
    }
  }

  .ant-btn {
    transition: all 0.2s ease;
  }

  .ant-progress {
    .ant-progress-bg {
      transition: all 0.3s ease;
    }
  }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
  .cloud-storage-panel {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .cloud-storage-panel {
    .projects-list {
      .project-item {
        border: 2px solid #000;

        &.active {
          border-color: #0066cc;
        }
      }
    }

    .files-list {
      .ant-table {
        border: 2px solid #000;
      }
    }

    .storage-stats {
      border: 2px solid #000;
      padding: 12px;
      border-radius: 4px;
    }
  }
}

// 云端存储特定样式
.cloud-storage-panel {
  .sync-indicator {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, transparent, rgba(24, 144, 255, 0.3), transparent);
      animation: syncing 2s infinite;
    }
  }

  .upload-progress {
    .ant-progress-line {
      .ant-progress-bg {
        background: linear-gradient(90deg, #1890ff, #52c41a);
      }
    }
  }

  .download-progress {
    .ant-progress-line {
      .ant-progress-bg {
        background: linear-gradient(90deg, #722ed1, #eb2f96);
      }
    }
  }

  .network-status {
    &.online {
      color: #52c41a;
    }

    &.offline {
      color: #ff4d4f;
    }
  }

  .compression-indicator {
    &.gzip {
      color: #1890ff;
    }

    &.brotli {
      color: #52c41a;
    }

    &.lz4 {
      color: #faad14;
    }

    &.zstd {
      color: #722ed1;
    }
  }

  .encryption-indicator {
    &.enabled {
      color: #52c41a;
    }

    &.disabled {
      color: #ff4d4f;
    }
  }
}

@keyframes syncing {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
