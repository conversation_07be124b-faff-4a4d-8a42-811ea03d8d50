/**
 * 移动端行为编辑器
 *
 * 专为移动设备优化的行为树编辑器，具有以下特性：
 * - 触摸友好的界面设计
 * - 手势操作支持
 * - 响应式布局
 * - 离线编辑支持
 * - 云同步功能
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Button,
  Modal,
  Space,
  FloatButton,
  Badge,
  message
} from 'antd';
import {
  PlusOutlined,
  SettingOutlined,
  CloudSyncOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  DeleteOutlined
} from '@ant-design/icons';
// 临时定义，实际应该从引擎库导入
enum BehaviorNodeType {
  SEQUENCE = 'sequence',
  SELECTOR = 'selector',
  PARALLEL = 'parallel',
  INVERTER = 'inverter',
  REPEATER = 'repeater',
  ACTION = 'action',
  CONDITION = 'condition',
  WAIT = 'wait'
}

// 临时的移动端引擎类
class MobileBehaviorEngine {
  constructor(_config: any) {
    // 临时实现
  }

  updateDeviceInfo(_info: any) {
    // 临时实现
  }

  dispose() {
    // 临时实现
  }
}

/**
 * 移动端节点配置
 */
interface MobileNodeConfig {
  id: string;
  name: string;
  type: BehaviorNodeType;
  position: { x: number; y: number };
  size: { width: number; height: number };
  properties: { [key: string]: any };
  children: string[];
  parent?: string;
  isSelected: boolean;
  isCollapsed: boolean;
}

/**
 * 手势状态
 */
interface GestureState {
  scale: number;
  translateX: number;
  translateY: number;
  isDragging: boolean;
  lastMouseX: number;
  lastMouseY: number;
}

/**
 * 编辑器状态
 */
interface EditorState {
  nodes: { [key: string]: MobileNodeConfig };
  selectedNodeId: string | null;
  isEditing: boolean;
  isOnline: boolean;
  lastSyncTime: number;
  isDirty: boolean;
}

/**
 * 移动端行为编辑器组件
 */
const MobileBehaviorEditor: React.FC<{
  entityId?: string;
  onSave?: (config: any) => void;
  onLoad?: () => any;
}> = ({ entityId, onSave, onLoad: _onLoad }) => {
  // 屏幕尺寸
  const [screenDimensions, setScreenDimensions] = useState({
    width: window.innerWidth,
    height: window.innerHeight
  });
  
  // 编辑器状态
  const [editorState, setEditorState] = useState<EditorState>({
    nodes: {},
    selectedNodeId: null,
    isEditing: false,
    isOnline: true,
    lastSyncTime: 0,
    isDirty: false
  });
  
  // 手势状态
  const [gestureState, setGestureState] = useState<GestureState>({
    scale: 1,
    translateX: 0,
    translateY: 0,
    isDragging: false,
    lastMouseX: 0,
    lastMouseY: 0
  });
  
  // 界面状态
  const [showNodePalette, setShowNodePalette] = useState(false);
  const [showProperties, setShowProperties] = useState(false);
  
  // 引用
  const canvasRef = useRef<HTMLDivElement>(null);
  
  // 移动端引擎
  const [mobileEngine] = useState(() => new MobileBehaviorEngine({
    platform: 'ios', // 或 'android'
    screenSize: screenDimensions,
    pixelRatio: 2,
    memoryLimit: 2048,
    cpuCores: 4,
    batteryLevel: 1.0,
    networkType: 'wifi',
    orientation: 'portrait',
    isLowPowerMode: false
  }));

  /**
   * 初始化组件
   */
  useEffect(() => {
    initializeEditor();
    setupNetworkListener();
    setupDimensionListener();
    
    return () => {
      cleanup();
    };
  }, []);

  /**
   * 初始化编辑器
   */
  const initializeEditor = async () => {
    try {
      // 加载本地数据
      const savedData = await loadLocalData();
      if (savedData) {
        setEditorState(prev => ({
          ...prev,
          nodes: savedData.nodes || {},
          lastSyncTime: savedData.lastSyncTime || 0
        }));
      }
      
      // 创建默认节点
      if (Object.keys(savedData?.nodes || {}).length === 0) {
        createDefaultTree();
      }
      
    } catch (error) {
      console.error('编辑器初始化失败:', error);
    }
  };

  /**
   * 设置网络监听
   */
  const setupNetworkListener = () => {
    const handleOnline = () => {
      setEditorState(prev => ({
        ...prev,
        isOnline: true
      }));

      // 网络恢复时自动同步
      if (editorState.isDirty) {
        syncToCloud();
      }
    };

    const handleOffline = () => {
      setEditorState(prev => ({
        ...prev,
        isOnline: false
      }));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  };

  /**
   * 设置屏幕尺寸监听
   */
  const setupDimensionListener = () => {
    const handleResize = () => {
      const newDimensions = {
        width: window.innerWidth,
        height: window.innerHeight
      };
      setScreenDimensions(newDimensions);

      // 更新移动端引擎设备信息
      mobileEngine.updateDeviceInfo({
        screenSize: newDimensions,
        orientation: newDimensions.width > newDimensions.height ? 'landscape' : 'portrait'
      });
    };

    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  };

  /**
   * 创建默认行为树
   */
  const createDefaultTree = () => {
    const rootNode: MobileNodeConfig = {
      id: 'root',
      name: '根节点',
      type: BehaviorNodeType.SELECTOR,
      position: { x: screenDimensions.width / 2, y: 100 },
      size: { width: 120, height: 60 },
      properties: {},
      children: [],
      isSelected: false,
      isCollapsed: false
    };
    
    setEditorState(prev => ({
      ...prev,
      nodes: { root: rootNode },
      isDirty: true
    }));
  };

  /**
   * 处理鼠标按下事件
   */
  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    setGestureState(prev => ({
      ...prev,
      isDragging: true,
      lastMouseX: event.clientX,
      lastMouseY: event.clientY
    }));
  }, []);

  /**
   * 处理鼠标移动事件
   */
  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (!gestureState.isDragging) return;

    const deltaX = event.clientX - gestureState.lastMouseX;
    const deltaY = event.clientY - gestureState.lastMouseY;

    setGestureState(prev => ({
      ...prev,
      translateX: prev.translateX + deltaX,
      translateY: prev.translateY + deltaY,
      lastMouseX: event.clientX,
      lastMouseY: event.clientY
    }));
  }, [gestureState.isDragging, gestureState.lastMouseX, gestureState.lastMouseY]);

  /**
   * 处理鼠标释放事件
   */
  const handleMouseUp = useCallback(() => {
    setGestureState(prev => ({
      ...prev,
      isDragging: false
    }));
  }, []);

  /**
   * 处理滚轮缩放事件
   */
  const handleWheel = useCallback((event: React.WheelEvent) => {
    event.preventDefault();
    const delta = event.deltaY > 0 ? 0.9 : 1.1;

    setGestureState(prev => ({
      ...prev,
      scale: Math.max(0.5, Math.min(3, prev.scale * delta))
    }));
  }, []);

  /**
   * 处理节点点击
   */
  const handleNodeTap = useCallback((nodeId: string) => {
    setEditorState(prev => ({
      ...prev,
      selectedNodeId: prev.selectedNodeId === nodeId ? null : nodeId,
      nodes: Object.fromEntries(
        Object.entries(prev.nodes).map(([id, node]) => [
          id,
          { ...node, isSelected: id === nodeId }
        ])
      )
    }));
  }, []);

  /**
   * 添加节点
   */
  const addNode = useCallback((type: BehaviorNodeType, position: { x: number; y: number }) => {
    const nodeId = `node_${Date.now()}`;
    const newNode: MobileNodeConfig = {
      id: nodeId,
      name: getNodeTypeName(type),
      type,
      position,
      size: { width: 100, height: 50 },
      properties: {},
      children: [],
      isSelected: false,
      isCollapsed: false
    };
    
    setEditorState(prev => ({
      ...prev,
      nodes: {
        ...prev.nodes,
        [nodeId]: newNode
      },
      isDirty: true
    }));
    
    // 自动保存到本地
    saveLocalData();
  }, []);

  /**
   * 删除节点
   */
  const deleteNode = useCallback((nodeId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个节点吗？',
      okText: '删除',
      cancelText: '取消',
      okType: 'danger',
      onOk: () => {
        setEditorState(prev => {
          const newNodes = { ...prev.nodes };
          delete newNodes[nodeId];

          // 移除父子关系
          Object.values(newNodes).forEach(node => {
            node.children = node.children.filter(id => id !== nodeId);
          });

          return {
            ...prev,
            nodes: newNodes,
            selectedNodeId: prev.selectedNodeId === nodeId ? null : prev.selectedNodeId,
            isDirty: true
          };
        });

        saveLocalData();
        message.success('节点已删除');
      }
    });
  }, []);

  /**
   * 保存到本地存储
   */
  const saveLocalData = async () => {
    try {
      const dataToSave = {
        nodes: editorState.nodes,
        lastSyncTime: Date.now(),
        version: '1.0'
      };

      localStorage.setItem(
        `behavior_tree_${entityId || 'default'}`,
        JSON.stringify(dataToSave)
      );

    } catch (error) {
      console.error('本地保存失败:', error);
    }
  };

  /**
   * 从本地存储加载
   */
  const loadLocalData = async () => {
    try {
      const data = localStorage.getItem(`behavior_tree_${entityId || 'default'}`);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('本地加载失败:', error);
      return null;
    }
  };

  /**
   * 同步到云端
   */
  const syncToCloud = async () => {
    if (!editorState.isOnline || !editorState.isDirty) return;
    
    try {
      if (onSave) {
        onSave({
          nodes: editorState.nodes,
          timestamp: Date.now()
        });
        
        setEditorState(prev => ({
          ...prev,
          isDirty: false,
          lastSyncTime: Date.now()
        }));
      }
    } catch (error) {
      console.error('云同步失败:', error);
    }
  };

  /**
   * 获取节点类型名称
   */
  const getNodeTypeName = (type: BehaviorNodeType): string => {
    const names = {
      [BehaviorNodeType.SEQUENCE]: '顺序',
      [BehaviorNodeType.SELECTOR]: '选择',
      [BehaviorNodeType.PARALLEL]: '并行',
      [BehaviorNodeType.INVERTER]: '反转',
      [BehaviorNodeType.REPEATER]: '重复',
      [BehaviorNodeType.ACTION]: '动作',
      [BehaviorNodeType.CONDITION]: '条件',
      [BehaviorNodeType.WAIT]: '等待'
    };
    return names[type] || '未知';
  };

  /**
   * 获取节点颜色
   */
  const getNodeColor = (type: BehaviorNodeType): string => {
    const colors = {
      [BehaviorNodeType.SEQUENCE]: '#52c41a',
      [BehaviorNodeType.SELECTOR]: '#1890ff',
      [BehaviorNodeType.PARALLEL]: '#722ed1',
      [BehaviorNodeType.INVERTER]: '#fa8c16',
      [BehaviorNodeType.REPEATER]: '#eb2f96',
      [BehaviorNodeType.ACTION]: '#f5222d',
      [BehaviorNodeType.CONDITION]: '#faad14',
      [BehaviorNodeType.WAIT]: '#13c2c2'
    };
    return colors[type] || '#d9d9d9';
  };

  /**
   * 渲染节点
   */
  const renderNode = (node: MobileNodeConfig) => {
    const { position, size, isSelected } = node;
    const color = getNodeColor(node.type);

    return (
      <div
        key={node.id}
        onClick={() => handleNodeTap(node.id)}
        style={{
          position: 'absolute',
          left: position.x - size.width / 2,
          top: position.y - size.height / 2,
          width: size.width,
          height: size.height,
          backgroundColor: color,
          border: isSelected ? '2px solid #000' : 'none',
          borderRadius: 8,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          userSelect: 'none'
        }}
      >
        <span
          style={{
            color: '#fff',
            fontSize: 12,
            fontWeight: 'bold',
            textAlign: 'center'
          }}
        >
          {node.name}
        </span>
      </div>
    );
  };

  /**
   * 渲染连接线
   */
  const renderConnections = () => {
    const connections: JSX.Element[] = [];

    Object.values(editorState.nodes).forEach(node => {
      node.children.forEach(childId => {
        const childNode = editorState.nodes[childId];
        if (childNode) {
          const x1 = node.position.x;
          const y1 = node.position.y + node.size.height / 2;
          const x2 = childNode.position.x;
          const y2 = childNode.position.y - childNode.size.height / 2;

          connections.push(
            <svg
              key={`${node.id}-${childId}`}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                pointerEvents: 'none',
                zIndex: 1
              }}
            >
              <line
                x1={x1}
                y1={y1}
                x2={x2}
                y2={y2}
                stroke="#666"
                strokeWidth={2}
              />
            </svg>
          );
        }
      });
    });

    return connections;
  };

  /**
   * 渲染节点面板
   */
  const renderNodePalette = () => {
    const nodeTypes = [
      BehaviorNodeType.SEQUENCE,
      BehaviorNodeType.SELECTOR,
      BehaviorNodeType.PARALLEL,
      BehaviorNodeType.ACTION,
      BehaviorNodeType.CONDITION,
      BehaviorNodeType.WAIT
    ];

    return (
      <Modal
        title="添加节点"
        open={showNodePalette}
        onCancel={() => setShowNodePalette(false)}
        footer={null}
        width={400}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          {nodeTypes.map(type => (
            <Button
              key={type}
              block
              style={{
                backgroundColor: getNodeColor(type),
                borderColor: getNodeColor(type),
                color: '#fff',
                height: 48
              }}
              onClick={() => {
                addNode(type, {
                  x: screenDimensions.width / 2,
                  y: screenDimensions.height / 2
                });
                setShowNodePalette(false);
                message.success(`已添加${getNodeTypeName(type)}节点`);
              }}
            >
              {getNodeTypeName(type)}
            </Button>
          ))}
        </Space>
      </Modal>
    );
  };

  /**
   * 渲染工具栏
   */
  const renderToolbar = () => (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      padding: '10px',
      backgroundColor: '#fff',
      borderBottom: '1px solid #e8e8e8',
      gap: '10px'
    }}>
      <Button
        type="primary"
        icon={<PlusOutlined />}
        onClick={() => setShowNodePalette(true)}
      >
        添加节点
      </Button>

      <Button
        icon={<SettingOutlined />}
        onClick={() => setShowProperties(true)}
      >
        属性
      </Button>

      <Button
        icon={<DeleteOutlined />}
        onClick={() => {
          const selectedNodeId = editorState.selectedNodeId;
          if (selectedNodeId) {
            deleteNode(selectedNodeId);
          } else {
            message.warning('请先选择一个节点');
          }
        }}
        disabled={!editorState.selectedNodeId}
        danger
      >
        删除
      </Button>

      <Button
        icon={<CloudSyncOutlined />}
        onClick={syncToCloud}
        disabled={!editorState.isOnline}
        style={{ opacity: editorState.isOnline ? 1 : 0.5 }}
      >
        同步
      </Button>

      <div style={{ marginLeft: 'auto', display: 'flex', alignItems: 'center', gap: '8px' }}>
        <Badge
          status={editorState.isOnline ? 'success' : 'error'}
          text={editorState.isOnline ? '在线' : '离线'}
        />
      </div>
    </div>
  );

  /**
   * 清理资源
   */
  const cleanup = () => {
    mobileEngine.dispose();
  };

  return (
    <div style={{
      width: '100%',
      height: '100vh',
      display: 'flex',
      flexDirection: 'column',
      backgroundColor: '#f5f5f5'
    }}>
      {renderToolbar()}

      <div
        ref={canvasRef}
        style={{
          flex: 1,
          position: 'relative',
          overflow: 'hidden',
          backgroundColor: '#fafafa',
          transform: `scale(${gestureState.scale}) translate(${gestureState.translateX}px, ${gestureState.translateY}px)`,
          transformOrigin: 'center center',
          cursor: gestureState.isDragging ? 'grabbing' : 'grab'
        }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onWheel={handleWheel}
      >
        {renderConnections()}
        {Object.values(editorState.nodes).map(renderNode)}
      </div>

      {renderNodePalette()}

      {/* 浮动按钮 */}
      <FloatButton.Group
        trigger="hover"
        type="primary"
        style={{ right: 24 }}
        icon={<PlusOutlined />}
      >
        <FloatButton
          icon={<ZoomInOutlined />}
          tooltip="放大"
          onClick={() => setGestureState(prev => ({
            ...prev,
            scale: Math.min(3, prev.scale * 1.2)
          }))}
        />
        <FloatButton
          icon={<ZoomOutOutlined />}
          tooltip="缩小"
          onClick={() => setGestureState(prev => ({
            ...prev,
            scale: Math.max(0.5, prev.scale * 0.8)
          }))}
        />
      </FloatButton.Group>

      {/* 属性面板 */}
      <Modal
        title="节点属性"
        open={showProperties}
        onCancel={() => setShowProperties(false)}
        footer={[
          <Button key="cancel" onClick={() => setShowProperties(false)}>
            取消
          </Button>,
          <Button key="ok" type="primary" onClick={() => setShowProperties(false)}>
            确定
          </Button>
        ]}
      >
        <div>
          {editorState.selectedNodeId ? (
            <p>节点ID: {editorState.selectedNodeId}</p>
          ) : (
            <p>请先选择一个节点</p>
          )}
        </div>
      </Modal>
    </div>
  );
};



export default MobileBehaviorEditor;
