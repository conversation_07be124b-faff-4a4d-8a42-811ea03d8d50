/**
 * 智慧工厂编辑器
 * 提供工厂场景编辑、设备配置、监控面板等功能
 */
import React, { useState, useEffect } from 'react';
import {
  Layout,
  Tabs,
  Card,
  Button,
  Space,
  Drawer,
  Tree,
  Table,
  Form,
  // Input,
  // Select,
  // InputNumber,
  // Switch,
  Modal,
  message,
  Tooltip,
  Badge,
  Progress,
  Statistic,
  Row,
  Col,
  Tag,
  Alert,
  Divider
} from 'antd';
import {
  SettingOutlined,
  // MonitorOutlined,
  DatabaseOutlined,
  // ApiOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  ExportOutlined,
  ImportOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
// import { useTranslation } from 'react-i18next';

const { Header, Content } = Layout;
const { TabPane } = Tabs;
// const { Option } = Select;
// const { TreeNode } = Tree;

/**
 * 设备类型枚举
 */
enum DeviceType {
  CNC_MACHINE = 'cnc_machine',
  ROBOT_ARM = 'robot_arm',
  CONVEYOR = 'conveyor',
  SENSOR = 'sensor',
  PLC = 'plc'
}

/**
 * 设备状态枚举
 */
enum DeviceStatus {
  OFFLINE = 'offline',
  ONLINE = 'online',
  RUNNING = 'running',
  ERROR = 'error',
  MAINTENANCE = 'maintenance'
}

/**
 * 设备接口
 */
interface Device {
  id: string;
  name: string;
  type: DeviceType;
  status: DeviceStatus;
  position: { x: number; y: number; z: number };
  protocol: string;
  address: string;
  port: number;
  tags: DeviceTag[];
  lastUpdate: Date;
  performance?: {
    availability: number;
    performance: number;
    quality: number;
    oee: number;
  };
}

/**
 * 设备标签接口
 */
interface DeviceTag {
  id: string;
  name: string;
  address: string;
  dataType: string;
  unit?: string;
  value?: any;
  quality?: string;
  timestamp?: Date;
}

/**
 * 工厂配置接口
 */
interface FactoryConfig {
  id: string;
  name: string;
  description: string;
  devices: Device[];
  layout: {
    width: number;
    height: number;
    depth: number;
  };
}

/**
 * 智慧工厂编辑器组件
 */
export const SmartFactoryEditor: React.FC = () => {
  // const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('scene');
  const [devices, setDevices] = useState<Device[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);
  const [factoryConfig, setFactoryConfig] = useState<FactoryConfig | null>(null);
  const [deviceDrawerVisible, setDeviceDrawerVisible] = useState(false);
  const [monitoringActive, setMonitoringActive] = useState(false);
  const [realTimeData, setRealTimeData] = useState<Map<string, any>>(new Map());
  const [form] = Form.useForm();

  // 避免未使用变量警告
  console.log('Factory config:', factoryConfig);

  // 模拟设备数据
  useEffect(() => {
    const mockDevices: Device[] = [
      {
        id: 'cnc-001',
        name: 'CNC机床-001',
        type: DeviceType.CNC_MACHINE,
        status: DeviceStatus.RUNNING,
        position: { x: 10, y: 5, z: 0 },
        protocol: 'modbus_tcp',
        address: '*************',
        port: 502,
        tags: [
          { id: 'spindle_speed', name: '主轴转速', address: '40001', dataType: 'float', unit: 'RPM' },
          { id: 'feed_rate', name: '进给速度', address: '40002', dataType: 'float', unit: 'mm/min' },
          { id: 'tool_number', name: '刀具号', address: '40003', dataType: 'integer' }
        ],
        lastUpdate: new Date(),
        performance: {
          availability: 95.5,
          performance: 88.2,
          quality: 99.1,
          oee: 83.4
        }
      },
      {
        id: 'robot-001',
        name: '机械臂-001',
        type: DeviceType.ROBOT_ARM,
        status: DeviceStatus.ONLINE,
        position: { x: 20, y: 10, z: 0 },
        protocol: 'ethernet_ip',
        address: '*************',
        port: 44818,
        tags: [
          { id: 'joint1_angle', name: '关节1角度', address: 'Joint1.Angle', dataType: 'float', unit: '°' },
          { id: 'joint2_angle', name: '关节2角度', address: 'Joint2.Angle', dataType: 'float', unit: '°' },
          { id: 'gripper_status', name: '夹爪状态', address: 'Gripper.Status', dataType: 'boolean' }
        ],
        lastUpdate: new Date(),
        performance: {
          availability: 92.3,
          performance: 91.7,
          quality: 98.5,
          oee: 83.2
        }
      },
      {
        id: 'sensor-001',
        name: '温度传感器-001',
        type: DeviceType.SENSOR,
        status: DeviceStatus.ONLINE,
        position: { x: 15, y: 8, z: 2 },
        protocol: 'mqtt',
        address: '*************',
        port: 1883,
        tags: [
          { id: 'temperature', name: '温度', address: 'sensors/temp001', dataType: 'float', unit: '°C' },
          { id: 'humidity', name: '湿度', address: 'sensors/hum001', dataType: 'float', unit: '%' }
        ],
        lastUpdate: new Date()
      }
    ];

    setDevices(mockDevices);

    const mockFactoryConfig: FactoryConfig = {
      id: 'factory-001',
      name: '智能制造工厂',
      description: '基于DL引擎的智能制造工厂示例',
      devices: mockDevices,
      layout: {
        width: 50,
        height: 30,
        depth: 10
      }
    };

    setFactoryConfig(mockFactoryConfig);
  }, []);

  // 模拟实时数据更新
  useEffect(() => {
    if (!monitoringActive) return;

    const interval = setInterval(() => {
      const newData = new Map();
      
      devices.forEach(device => {
        const deviceData: any = {};
        device.tags.forEach(tag => {
          switch (tag.dataType) {
            case 'float':
              deviceData[tag.id] = {
                value: Math.random() * 100,
                timestamp: new Date(),
                quality: 'good'
              };
              break;
            case 'integer':
              deviceData[tag.id] = {
                value: Math.floor(Math.random() * 10) + 1,
                timestamp: new Date(),
                quality: 'good'
              };
              break;
            case 'boolean':
              deviceData[tag.id] = {
                value: Math.random() > 0.5,
                timestamp: new Date(),
                quality: 'good'
              };
              break;
          }
        });
        newData.set(device.id, deviceData);
      });

      setRealTimeData(newData);
    }, 2000);

    return () => clearInterval(interval);
  }, [monitoringActive, devices]);

  /**
   * 获取设备状态颜色
   */
  // const getStatusColor = (status: DeviceStatus): string => {
  //   switch (status) {
  //     case DeviceStatus.RUNNING:
  //       return '#52c41a';
  //     case DeviceStatus.ONLINE:
  //       return '#1890ff';
  //     case DeviceStatus.ERROR:
  //       return '#ff4d4f';
  //     case DeviceStatus.MAINTENANCE:
  //       return '#faad14';
  //     default:
  //       return '#d9d9d9';
  //   }
  // };

  /**
   * 获取设备状态图标
   */
  const getStatusIcon = (status: DeviceStatus) => {
    switch (status) {
      case DeviceStatus.RUNNING:
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case DeviceStatus.ONLINE:
        return <PlayCircleOutlined style={{ color: '#1890ff' }} />;
      case DeviceStatus.ERROR:
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      case DeviceStatus.MAINTENANCE:
        return <WarningOutlined style={{ color: '#faad14' }} />;
      default:
        return <CloseCircleOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  /**
   * 设备表格列定义
   */
  const deviceColumns = [
    {
      title: '设备名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Device) => (
        <Space>
          {getStatusIcon(record.status)}
          <span>{text}</span>
        </Space>
      )
    },
    {
      title: '设备类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: DeviceType) => {
        const typeMap = {
          [DeviceType.CNC_MACHINE]: 'CNC机床',
          [DeviceType.ROBOT_ARM]: '机械臂',
          [DeviceType.CONVEYOR]: '传送带',
          [DeviceType.SENSOR]: '传感器',
          [DeviceType.PLC]: 'PLC'
        };
        return <Tag>{typeMap[type]}</Tag>;
      }
    },
    {
      title: '通信协议',
      dataIndex: 'protocol',
      key: 'protocol',
      render: (protocol: string) => <Tag color="blue">{protocol.toUpperCase()}</Tag>
    },
    {
      title: '设备地址',
      key: 'address',
      render: (record: Device) => `${record.address}:${record.port}`
    },
    {
      title: 'OEE',
      key: 'oee',
      render: (record: Device) => {
        if (record.performance) {
          return (
            <Progress
              percent={record.performance.oee}
              size="small"
              status={record.performance.oee > 80 ? 'success' : record.performance.oee > 60 ? 'normal' : 'exception'}
            />
          );
        }
        return '-';
      }
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: Device) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => {
                setSelectedDevice(record);
                setDeviceDrawerVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="编辑设备">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => {
                setSelectedDevice(record);
                form.setFieldsValue(record);
                setDeviceDrawerVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="删除设备">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => {
                Modal.confirm({
                  title: '确认删除',
                  content: `确定要删除设备 ${record.name} 吗？`,
                  onOk: () => {
                    setDevices(devices.filter(d => d.id !== record.id));
                    message.success('设备删除成功');
                  }
                });
              }}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  /**
   * 渲染场景编辑器
   */
  const renderSceneEditor = () => (
    <div style={{ height: '100%', background: '#f0f2f5', padding: '16px' }}>
      <Row gutter={16} style={{ height: '100%' }}>
        <Col span={18}>
          <Card title="3D工厂场景" style={{ height: '100%' }}>
            <div
              style={{
                height: '500px',
                background: '#001529',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: '18px'
              }}
            >
              3D工厂场景视图 (集成DL引擎3D渲染)
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card title="设备树" style={{ height: '100%' }}>
            <Tree
              showIcon
              defaultExpandAll
              onSelect={(_, info) => {
                if (info.node.key && typeof info.node.key === 'string') {
                  const device = devices.find(d => d.id === info.node.key);
                  if (device) {
                    setSelectedDevice(device);
                  }
                }
              }}
              treeData={[
                {
                  title: '工厂设备',
                  key: 'factory',
                  icon: <DatabaseOutlined />,
                  children: devices.map(device => ({
                    title: device.name,
                    key: device.id,
                    icon: getStatusIcon(device.status),
                  }))
                }
              ]}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );

  /**
   * 渲染设备管理
   */
  const renderDeviceManagement = () => (
    <div style={{ padding: '16px' }}>
      <div style={{ marginBottom: '16px' }}>
        <Space>
          <Button type="primary" icon={<PlusOutlined />}>
            添加设备
          </Button>
          <Button icon={<ImportOutlined />}>
            导入配置
          </Button>
          <Button icon={<ExportOutlined />}>
            导出配置
          </Button>
          <Button icon={<ReloadOutlined />}>
            刷新状态
          </Button>
        </Space>
      </div>
      
      <Table
        columns={deviceColumns}
        dataSource={devices}
        rowKey="id"
        pagination={{ pageSize: 10 }}
      />
    </div>
  );

  /**
   * 渲染实时监控
   */
  const renderRealTimeMonitoring = () => (
    <div style={{ padding: '16px' }}>
      <div style={{ marginBottom: '16px' }}>
        <Space>
          <Button
            type={monitoringActive ? 'default' : 'primary'}
            icon={monitoringActive ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
            onClick={() => setMonitoringActive(!monitoringActive)}
          >
            {monitoringActive ? '暂停监控' : '开始监控'}
          </Button>
          <Button icon={<StopOutlined />} onClick={() => setMonitoringActive(false)}>
            停止监控
          </Button>
          <Badge status={monitoringActive ? 'processing' : 'default'} text={monitoringActive ? '监控中' : '已停止'} />
        </Space>
      </div>

      <Row gutter={16}>
        {devices.map(device => (
          <Col span={8} key={device.id} style={{ marginBottom: '16px' }}>
            <Card
              title={device.name}
              extra={<Badge status={monitoringActive ? 'processing' : 'default'} />}
              size="small"
            >
              {device.performance && (
                <Row gutter={8} style={{ marginBottom: '12px' }}>
                  <Col span={12}>
                    <Statistic
                      title="可用性"
                      value={device.performance.availability}
                      precision={1}
                      suffix="%"
                      valueStyle={{ fontSize: '14px' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="OEE"
                      value={device.performance.oee}
                      precision={1}
                      suffix="%"
                      valueStyle={{ fontSize: '14px' }}
                    />
                  </Col>
                </Row>
              )}
              
              <Divider style={{ margin: '8px 0' }} />
              
              {device.tags.slice(0, 3).map(tag => {
                const data = realTimeData.get(device.id)?.[tag.id];
                return (
                  <div key={tag.id} style={{ marginBottom: '4px' }}>
                    <span style={{ fontSize: '12px', color: '#666' }}>{tag.name}: </span>
                    <span style={{ fontWeight: 'bold' }}>
                      {data ? `${data.value.toFixed(2)} ${tag.unit || ''}` : '-'}
                    </span>
                  </div>
                );
              })}
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );

  return (
    <Layout style={{ height: '100vh' }}>
      <Header style={{ background: '#fff', padding: '0 16px', borderBottom: '1px solid #f0f0f0' }}>
        <Row justify="space-between" align="middle">
          <Col>
            <h2 style={{ margin: 0 }}>智慧工厂编辑器</h2>
          </Col>
          <Col>
            <Space>
              <Button icon={<SettingOutlined />}>设置</Button>
              <Button type="primary">保存配置</Button>
            </Space>
          </Col>
        </Row>
      </Header>
      
      <Content>
        <Tabs activeKey={activeTab} onChange={setActiveTab} style={{ height: '100%' }}>
          <TabPane tab="场景编辑" key="scene" style={{ height: '100%' }}>
            {renderSceneEditor()}
          </TabPane>
          <TabPane tab="设备管理" key="devices">
            {renderDeviceManagement()}
          </TabPane>
          <TabPane tab="实时监控" key="monitoring">
            {renderRealTimeMonitoring()}
          </TabPane>
        </Tabs>
      </Content>

      {/* 设备详情抽屉 */}
      <Drawer
        title={selectedDevice ? `设备详情 - ${selectedDevice.name}` : '设备详情'}
        placement="right"
        size="large"
        open={deviceDrawerVisible}
        onClose={() => setDeviceDrawerVisible(false)}
      >
        {selectedDevice && (
          <div>
            <Alert
              message={`设备状态: ${selectedDevice.status}`}
              type={selectedDevice.status === DeviceStatus.RUNNING ? 'success' : 'info'}
              showIcon
              style={{ marginBottom: '16px' }}
            />
            
            <Card title="基本信息" size="small" style={{ marginBottom: '16px' }}>
              <Row gutter={16}>
                <Col span={12}>
                  <p><strong>设备ID:</strong> {selectedDevice.id}</p>
                  <p><strong>设备类型:</strong> {selectedDevice.type}</p>
                  <p><strong>通信协议:</strong> {selectedDevice.protocol}</p>
                </Col>
                <Col span={12}>
                  <p><strong>设备地址:</strong> {selectedDevice.address}</p>
                  <p><strong>端口:</strong> {selectedDevice.port}</p>
                  <p><strong>最后更新:</strong> {selectedDevice.lastUpdate.toLocaleString()}</p>
                </Col>
              </Row>
            </Card>

            {selectedDevice.performance && (
              <Card title="性能指标" size="small" style={{ marginBottom: '16px' }}>
                <Row gutter={16}>
                  <Col span={12}>
                    <Progress
                      type="circle"
                      percent={selectedDevice.performance.availability}
                      size={80}
                      format={percent => `${percent}%`}
                    />
                    <p style={{ textAlign: 'center', marginTop: '8px' }}>可用性</p>
                  </Col>
                  <Col span={12}>
                    <Progress
                      type="circle"
                      percent={selectedDevice.performance.oee}
                      size={80}
                      format={percent => `${percent}%`}
                    />
                    <p style={{ textAlign: 'center', marginTop: '8px' }}>OEE</p>
                  </Col>
                </Row>
              </Card>
            )}

            <Card title="数据标签" size="small">
              <Table
                columns={[
                  { title: '标签名称', dataIndex: 'name', key: 'name' },
                  { title: '地址', dataIndex: 'address', key: 'address' },
                  { title: '数据类型', dataIndex: 'dataType', key: 'dataType' },
                  { title: '单位', dataIndex: 'unit', key: 'unit' },
                  {
                    title: '当前值',
                    key: 'value',
                    render: (tag: DeviceTag) => {
                      const data = realTimeData.get(selectedDevice.id)?.[tag.id];
                      return data ? `${data.value.toFixed(2)} ${tag.unit || ''}` : '-';
                    }
                  }
                ]}
                dataSource={selectedDevice.tags}
                rowKey="id"
                pagination={false}
                size="small"
              />
            </Card>
          </div>
        )}
      </Drawer>
    </Layout>
  );
};

export default SmartFactoryEditor;
