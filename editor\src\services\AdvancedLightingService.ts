/**
 * 高级光照系统服务
 * 提供全局光照、实时阴影、环境光遮蔽、光照探针、IBL等高级光照技术
 */
import { EventEmitter } from '../utils/EventEmitter';

// 光源类型枚举
export enum LightType {
  DIRECTIONAL = 'directional',
  POINT = 'point',
  SPOT = 'spot',
  AREA = 'area',
  HEMISPHERE = 'hemisphere',
  AMBIENT = 'ambient',
  IBL = 'ibl',
  VOLUMETRIC = 'volumetric'
}

// 阴影类型枚举
export enum ShadowType {
  NONE = 'none',
  BASIC = 'basic',
  PCF = 'pcf',
  PCSS = 'pcss',
  VSM = 'vsm',
  CSM = 'csm',
  RAYTRACED = 'raytraced'
}

// 全局光照类型枚举
export enum GlobalIlluminationType {
  NONE = 'none',
  LIGHTMAPS = 'lightmaps',
  LIGHT_PROBES = 'light_probes',
  VOXEL_GI = 'voxel_gi',
  SCREEN_SPACE_GI = 'screen_space_gi',
  RAYTRACED_GI = 'raytraced_gi'
}

// 光源属性接口
export interface LightProperties {
  // 基础属性
  name: string;
  type: LightType;
  enabled: boolean;
  
  // 位置和方向
  position: [number, number, number];
  direction: [number, number, number];
  target?: [number, number, number];
  
  // 颜色和强度
  color: [number, number, number];
  intensity: number;
  temperature?: number; // 色温 (K)
  
  // 衰减属性
  range: number;
  decay: number;
  
  // 聚光灯属性
  angle?: number;
  penumbra?: number;
  
  // 区域光属性
  width?: number;
  height?: number;
  
  // 阴影属性
  castShadow: boolean;
  shadowType: ShadowType;
  shadowMapSize: number;
  shadowBias: number;
  shadowNormalBias: number;
  shadowRadius: number;
  shadowBlurSamples: number;
  
  // 体积光属性
  volumetric?: boolean;
  volumetricScattering?: number;
  volumetricExtinction?: number;
  volumetricAsymmetry?: number;
  
  // 自定义属性
  customProperties: Record<string, any>;
}

// 光源定义接口
export interface LightDefinition {
  id: string;
  properties: LightProperties;
  metadata: LightMetadata;
  timestamp: number;
}

// 光源元数据接口
export interface LightMetadata {
  category: string;
  tags: string[];
  performance: number; // 0-100
  quality: number; // 0-100
  author: string;
  description: string;
}

// 光照探针接口
export interface LightProbe {
  id: string;
  name: string;
  position: [number, number, number];
  radius: number;
  intensity: number;
  irradianceData: Float32Array; // SH coefficients
  radianceData?: ArrayBuffer; // Cubemap data
  enabled: boolean;
  metadata: {
    resolution: number;
    format: string;
    captureTime: number;
  };
}

// IBL环境接口
export interface IBLEnvironment {
  id: string;
  name: string;
  description: string;
  
  // 环境贴图
  environmentMap: string; // HDR texture URL
  irradianceMap?: string; // Irradiance cubemap URL
  prefilterMap?: string; // Prefiltered environment map URL
  brdfLUT?: string; // BRDF lookup texture URL
  
  // 属性
  intensity: number;
  rotation: [number, number, number];
  exposure: number;
  
  // 元数据
  resolution: number;
  format: string;
  size: number; // File size in bytes
  tags: string[];
}

// 全局光照设置接口
export interface GlobalIlluminationSettings {
  type: GlobalIlluminationType;
  enabled: boolean;
  
  // 光照探针设置
  probeSpacing: number;
  probeIntensity: number;
  probeBounces: number;
  
  // 体素GI设置
  voxelResolution: number;
  voxelBounces: number;
  voxelIntensity: number;
  
  // 屏幕空间GI设置
  ssgiSamples: number;
  ssgiRadius: number;
  ssgiIntensity: number;
  
  // 光照贴图设置
  lightmapResolution: number;
  lightmapQuality: 'low' | 'medium' | 'high' | 'ultra';
  lightmapBounces: number;
  
  // 性能设置
  updateFrequency: number;
  maxProbes: number;
  cullingDistance: number;
}

// 环境光遮蔽设置接口
export interface AmbientOcclusionSettings {
  enabled: boolean;
  type: 'ssao' | 'hbao' | 'gtao' | 'raytraced';
  
  // SSAO设置
  radius: number;
  intensity: number;
  bias: number;
  samples: number;
  
  // 质量设置
  quality: 'low' | 'medium' | 'high' | 'ultra';
  denoise: boolean;
  temporalFilter: boolean;
  
  // 性能设置
  halfResolution: boolean;
  adaptiveQuality: boolean;
}

// 光照场景接口
export interface LightingScene {
  id: string;
  name: string;
  description: string;
  
  // 光源
  lights: LightDefinition[];
  
  // 环境设置
  iblEnvironment?: IBLEnvironment;
  globalIllumination: GlobalIlluminationSettings;
  ambientOcclusion: AmbientOcclusionSettings;
  
  // 光照探针
  lightProbes: LightProbe[];
  
  // 全局设置
  globalSettings: {
    ambientColor: [number, number, number];
    ambientIntensity: number;
    fogColor: [number, number, number];
    fogDensity: number;
    fogNear: number;
    fogFar: number;
    exposure: number;
    gamma: number;
  };
  
  // 元数据
  metadata: {
    author: string;
    tags: string[];
    category: string;
    thumbnail?: string;
    performance: number;
    quality: number;
  };
}

/**
 * 高级光照系统服务类
 */
export class AdvancedLightingService extends EventEmitter {
  private static instance: AdvancedLightingService;
  private lights: Map<string, LightDefinition> = new Map();
  private lightProbes: Map<string, LightProbe> = new Map();
  private iblEnvironments: Map<string, IBLEnvironment> = new Map();
  private lightingScenes: Map<string, LightingScene> = new Map();
  private activeLightingScene: LightingScene | null = null;
  private isUpdating: boolean = false;

  private constructor() {
    super();
    this.initializeDefaultLights();
    this.initializeDefaultEnvironments();
    this.initializeDefaultScene();
  }

  public static getInstance(): AdvancedLightingService {
    if (!AdvancedLightingService.instance) {
      AdvancedLightingService.instance = new AdvancedLightingService();
    }
    return AdvancedLightingService.instance;
  }

  /**
   * 初始化默认光源
   */
  private initializeDefaultLights(): void {
    // 主方向光（太阳光）
    const sunLight = this.createLight({
      name: 'Sun Light',
      type: LightType.DIRECTIONAL,
      enabled: true,
      position: [0, 10, 0],
      direction: [-0.5, -1, -0.5],
      color: [1, 0.95, 0.8],
      intensity: 3,
      temperature: 5500,
      range: 100,
      decay: 2,
      castShadow: true,
      shadowType: ShadowType.CSM,
      shadowMapSize: 2048,
      shadowBias: 0.0001,
      shadowNormalBias: 0.01,
      shadowRadius: 1,
      shadowBlurSamples: 16,
      customProperties: {}
    });

    // 环境光
    const ambientLight = this.createLight({
      name: 'Ambient Light',
      type: LightType.AMBIENT,
      enabled: true,
      position: [0, 0, 0],
      direction: [0, 1, 0],
      color: [0.4, 0.4, 0.6],
      intensity: 0.3,
      range: 0,
      decay: 0,
      castShadow: false,
      shadowType: ShadowType.NONE,
      shadowMapSize: 512,
      shadowBias: 0,
      shadowNormalBias: 0,
      shadowRadius: 0,
      shadowBlurSamples: 0,
      customProperties: {}
    });

    // 补光
    const fillLight = this.createLight({
      name: 'Fill Light',
      type: LightType.HEMISPHERE,
      enabled: true,
      position: [0, 5, 0],
      direction: [0, -1, 0],
      color: [0.8, 0.9, 1],
      intensity: 0.5,
      range: 50,
      decay: 2,
      castShadow: false,
      shadowType: ShadowType.NONE,
      shadowMapSize: 512,
      shadowBias: 0,
      shadowNormalBias: 0,
      shadowRadius: 0,
      shadowBlurSamples: 0,
      customProperties: {}
    });

    this.lights.set(sunLight.id, sunLight);
    this.lights.set(ambientLight.id, ambientLight);
    this.lights.set(fillLight.id, fillLight);
  }

  /**
   * 初始化默认环境
   */
  private initializeDefaultEnvironments(): void {
    const environments: IBLEnvironment[] = [
      {
        id: 'env_studio',
        name: 'Studio',
        description: 'Neutral studio lighting environment',
        environmentMap: '/environments/studio.hdr',
        irradianceMap: '/environments/studio_irradiance.hdr',
        prefilterMap: '/environments/studio_prefilter.hdr',
        brdfLUT: '/environments/brdf_lut.png',
        intensity: 1.0,
        rotation: [0, 0, 0],
        exposure: 0,
        resolution: 1024,
        format: 'HDR',
        size: 2048000,
        tags: ['studio', 'neutral', 'indoor']
      },
      {
        id: 'env_outdoor',
        name: 'Outdoor',
        description: 'Natural outdoor lighting environment',
        environmentMap: '/environments/outdoor.hdr',
        irradianceMap: '/environments/outdoor_irradiance.hdr',
        prefilterMap: '/environments/outdoor_prefilter.hdr',
        brdfLUT: '/environments/brdf_lut.png',
        intensity: 1.2,
        rotation: [0, 0, 0],
        exposure: 0.5,
        resolution: 2048,
        format: 'HDR',
        size: 8192000,
        tags: ['outdoor', 'natural', 'sky']
      },
      {
        id: 'env_sunset',
        name: 'Sunset',
        description: 'Warm sunset lighting environment',
        environmentMap: '/environments/sunset.hdr',
        irradianceMap: '/environments/sunset_irradiance.hdr',
        prefilterMap: '/environments/sunset_prefilter.hdr',
        brdfLUT: '/environments/brdf_lut.png',
        intensity: 0.8,
        rotation: [0, 0, 0],
        exposure: -0.5,
        resolution: 1024,
        format: 'HDR',
        size: 3072000,
        tags: ['sunset', 'warm', 'dramatic']
      }
    ];

    environments.forEach(env => {
      this.iblEnvironments.set(env.id, env);
    });
  }

  /**
   * 初始化默认场景
   */
  private initializeDefaultScene(): void {
    const defaultScene: LightingScene = {
      id: 'scene_default',
      name: 'Default Scene',
      description: 'Default lighting setup',
      lights: Array.from(this.lights.values()),
      iblEnvironment: this.iblEnvironments.get('env_studio'),
      globalIllumination: {
        type: GlobalIlluminationType.LIGHT_PROBES,
        enabled: true,
        probeSpacing: 5,
        probeIntensity: 1,
        probeBounces: 2,
        voxelResolution: 64,
        voxelBounces: 1,
        voxelIntensity: 1,
        ssgiSamples: 16,
        ssgiRadius: 1,
        ssgiIntensity: 1,
        lightmapResolution: 512,
        lightmapQuality: 'medium',
        lightmapBounces: 2,
        updateFrequency: 60,
        maxProbes: 100,
        cullingDistance: 50
      },
      ambientOcclusion: {
        enabled: true,
        type: 'ssao',
        radius: 0.5,
        intensity: 1,
        bias: 0.01,
        samples: 16,
        quality: 'medium',
        denoise: true,
        temporalFilter: true,
        halfResolution: false,
        adaptiveQuality: true
      },
      lightProbes: [],
      globalSettings: {
        ambientColor: [0.2, 0.2, 0.3],
        ambientIntensity: 0.1,
        fogColor: [0.7, 0.8, 0.9],
        fogDensity: 0.01,
        fogNear: 10,
        fogFar: 100,
        exposure: 1,
        gamma: 2.2
      },
      metadata: {
        author: 'System',
        tags: ['default', 'basic'],
        category: 'basic',
        performance: 80,
        quality: 70
      }
    };

    this.lightingScenes.set(defaultScene.id, defaultScene);
    this.activeLightingScene = defaultScene;
  }

  /**
   * 创建光源
   */
  public createLight(properties: LightProperties): LightDefinition {
    const lightId = `light_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const light: LightDefinition = {
      id: lightId,
      properties,
      metadata: {
        category: this.getCategoryForLightType(properties.type),
        tags: this.getTagsForLightType(properties.type),
        performance: this.calculateLightPerformance(properties),
        quality: this.calculateLightQuality(properties),
        author: 'User',
        description: properties.name
      },
      timestamp: Date.now()
    };

    this.lights.set(lightId, light);
    this.emit('lightCreated', light);
    
    return light;
  }

  /**
   * 更新光源
   */
  public updateLight(lightId: string, updates: Partial<LightProperties>): LightDefinition {
    const light = this.lights.get(lightId);
    if (!light) {
      throw new Error('Light not found');
    }

    light.properties = { ...light.properties, ...updates };
    light.metadata.performance = this.calculateLightPerformance(light.properties);
    light.metadata.quality = this.calculateLightQuality(light.properties);
    light.timestamp = Date.now();

    this.emit('lightUpdated', light);
    return light;
  }

  /**
   * 创建光照探针
   */
  public createLightProbe(
    name: string,
    position: [number, number, number],
    radius: number = 5
  ): LightProbe {
    const probeId = `probe_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 生成模拟的球谐系数数据
    const irradianceData = new Float32Array(27); // 9 SH coefficients * 3 channels
    for (let i = 0; i < 27; i++) {
      irradianceData[i] = Math.random() * 0.5;
    }

    const probe: LightProbe = {
      id: probeId,
      name,
      position,
      radius,
      intensity: 1.0,
      irradianceData,
      enabled: true,
      metadata: {
        resolution: 64,
        format: 'SH9',
        captureTime: Date.now()
      }
    };

    this.lightProbes.set(probeId, probe);
    this.emit('lightProbeCreated', probe);
    
    return probe;
  }

  /**
   * 捕获光照探针
   */
  public async captureLightProbe(probeId: string): Promise<void> {
    const probe = this.lightProbes.get(probeId);
    if (!probe) {
      throw new Error('Light probe not found');
    }

    this.emit('lightProbeCaptureStarted', probe);

    // 模拟捕获过程
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 更新捕获时间
    probe.metadata.captureTime = Date.now();

    this.emit('lightProbeCaptureCompleted', probe);
  }

  /**
   * 创建IBL环境
   */
  public createIBLEnvironment(config: {
    name: string;
    description: string;
    environmentMap: string;
    intensity?: number;
    rotation?: [number, number, number];
    exposure?: number;
  }): IBLEnvironment {
    const envId = `env_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const environment: IBLEnvironment = {
      id: envId,
      name: config.name,
      description: config.description,
      environmentMap: config.environmentMap,
      intensity: config.intensity || 1.0,
      rotation: config.rotation || [0, 0, 0],
      exposure: config.exposure || 0,
      resolution: 1024,
      format: 'HDR',
      size: 4096000,
      tags: ['custom']
    };

    this.iblEnvironments.set(envId, environment);
    this.emit('iblEnvironmentCreated', environment);
    
    return environment;
  }

  /**
   * 预处理IBL环境
   */
  public async preprocessIBLEnvironment(envId: string): Promise<void> {
    const environment = this.iblEnvironments.get(envId);
    if (!environment) {
      throw new Error('IBL environment not found');
    }

    this.emit('iblPreprocessStarted', environment);

    // 模拟预处理过程
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 生成预处理贴图URL
    environment.irradianceMap = environment.environmentMap.replace('.hdr', '_irradiance.hdr');
    environment.prefilterMap = environment.environmentMap.replace('.hdr', '_prefilter.hdr');
    environment.brdfLUT = '/environments/brdf_lut.png';

    this.emit('iblPreprocessCompleted', environment);
  }

  /**
   * 创建光照场景
   */
  public createLightingScene(name: string, description: string): LightingScene {
    const sceneId = `scene_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const scene: LightingScene = {
      id: sceneId,
      name,
      description,
      lights: [],
      globalIllumination: {
        type: GlobalIlluminationType.LIGHT_PROBES,
        enabled: true,
        probeSpacing: 5,
        probeIntensity: 1,
        probeBounces: 2,
        voxelResolution: 64,
        voxelBounces: 1,
        voxelIntensity: 1,
        ssgiSamples: 16,
        ssgiRadius: 1,
        ssgiIntensity: 1,
        lightmapResolution: 512,
        lightmapQuality: 'medium',
        lightmapBounces: 2,
        updateFrequency: 60,
        maxProbes: 100,
        cullingDistance: 50
      },
      ambientOcclusion: {
        enabled: true,
        type: 'ssao',
        radius: 0.5,
        intensity: 1,
        bias: 0.01,
        samples: 16,
        quality: 'medium',
        denoise: true,
        temporalFilter: true,
        halfResolution: false,
        adaptiveQuality: true
      },
      lightProbes: [],
      globalSettings: {
        ambientColor: [0.2, 0.2, 0.3],
        ambientIntensity: 0.1,
        fogColor: [0.7, 0.8, 0.9],
        fogDensity: 0.01,
        fogNear: 10,
        fogFar: 100,
        exposure: 1,
        gamma: 2.2
      },
      metadata: {
        author: 'User',
        tags: ['custom'],
        category: 'custom',
        performance: 80,
        quality: 70
      }
    };

    this.lightingScenes.set(sceneId, scene);
    this.emit('lightingSceneCreated', scene);
    
    return scene;
  }

  /**
   * 设置活动光照场景
   */
  public setActiveLightingScene(sceneId: string): void {
    const scene = this.lightingScenes.get(sceneId);
    if (!scene) {
      throw new Error('Lighting scene not found');
    }

    this.activeLightingScene = scene;
    this.emit('activeLightingSceneChanged', scene);
  }

  /**
   * 更新全局光照设置
   */
  public updateGlobalIllumination(settings: Partial<GlobalIlluminationSettings>): void {
    if (!this.activeLightingScene) {
      throw new Error('No active lighting scene');
    }

    this.activeLightingScene.globalIllumination = {
      ...this.activeLightingScene.globalIllumination,
      ...settings
    };

    this.emit('globalIlluminationUpdated', this.activeLightingScene.globalIllumination);
  }

  /**
   * 更新环境光遮蔽设置
   */
  public updateAmbientOcclusion(settings: Partial<AmbientOcclusionSettings>): void {
    if (!this.activeLightingScene) {
      throw new Error('No active lighting scene');
    }

    this.activeLightingScene.ambientOcclusion = {
      ...this.activeLightingScene.ambientOcclusion,
      ...settings
    };

    this.emit('ambientOcclusionUpdated', this.activeLightingScene.ambientOcclusion);
  }

  /**
   * 自动放置光照探针
   */
  public async autoPlaceLightProbes(bounds: {
    min: [number, number, number];
    max: [number, number, number];
  }): Promise<LightProbe[]> {
    if (!this.activeLightingScene) {
      throw new Error('No active lighting scene');
    }

    const spacing = this.activeLightingScene.globalIllumination.probeSpacing;
    const probes: LightProbe[] = [];

    this.emit('autoProbeplacementStarted');

    // 计算探针网格
    const [minX, minY, minZ] = bounds.min;
    const [maxX, maxY, maxZ] = bounds.max;

    for (let x = minX; x <= maxX; x += spacing) {
      for (let y = minY; y <= maxY; y += spacing) {
        for (let z = minZ; z <= maxZ; z += spacing) {
          const probe = this.createLightProbe(
            `Auto Probe ${probes.length + 1}`,
            [x, y, z],
            spacing * 0.5
          );
          probes.push(probe);

          // 添加到场景
          this.activeLightingScene.lightProbes.push(probe);
        }
      }
    }

    // 模拟捕获所有探针
    for (const probe of probes) {
      await this.captureLightProbe(probe.id);
    }

    this.emit('autoProbeplacementCompleted', probes);
    return probes;
  }

  /**
   * 烘焙光照贴图
   */
  public async bakeLightmaps(objects: string[]): Promise<void> {
    if (!this.activeLightingScene) {
      throw new Error('No active lighting scene');
    }

    this.isUpdating = true;
    this.emit('lightmapBakingStarted', objects);

    try {
      const settings = this.activeLightingScene.globalIllumination;

      // 模拟烘焙过程
      const totalSteps = objects.length * settings.lightmapBounces;
      let currentStep = 0;

      for (const objectId of objects) {
        for (let bounce = 0; bounce < settings.lightmapBounces; bounce++) {
          // 模拟烘焙步骤
          await new Promise(resolve => setTimeout(resolve, 1000));

          currentStep++;
          this.emit('lightmapBakingProgress', {
            objectId,
            bounce,
            progress: (currentStep / totalSteps) * 100
          });
        }
      }

      this.emit('lightmapBakingCompleted', objects);
    } catch (error) {
      this.emit('lightmapBakingError', error);
      throw error;
    } finally {
      this.isUpdating = false;
    }
  }

  /**
   * 计算光源性能影响
   */
  private calculateLightPerformance(properties: LightProperties): number {
    let performance = 100;

    // 基于光源类型的性能影响
    switch (properties.type) {
      case LightType.AREA:
        performance -= 30;
        break;
      case LightType.VOLUMETRIC:
        performance -= 40;
        break;
      case LightType.SPOT:
        performance -= 15;
        break;
      case LightType.POINT:
        performance -= 10;
        break;
      case LightType.DIRECTIONAL:
        performance -= 5;
        break;
    }

    // 阴影影响
    if (properties.castShadow) {
      switch (properties.shadowType) {
        case ShadowType.RAYTRACED:
          performance -= 50;
          break;
        case ShadowType.PCSS:
          performance -= 30;
          break;
        case ShadowType.CSM:
          performance -= 25;
          break;
        case ShadowType.PCF:
          performance -= 15;
          break;
        case ShadowType.VSM:
          performance -= 20;
          break;
        default:
          performance -= 10;
      }

      // 阴影贴图大小影响
      if (properties.shadowMapSize > 1024) {
        performance -= 10;
      }
      if (properties.shadowMapSize > 2048) {
        performance -= 15;
      }
    }

    // 体积光影响
    if (properties.volumetric) {
      performance -= 25;
    }

    return Math.max(0, performance);
  }

  /**
   * 计算光源质量
   */
  private calculateLightQuality(properties: LightProperties): number {
    let quality = 50;

    // 基于光源类型的质量提升
    switch (properties.type) {
      case LightType.AREA:
        quality += 25;
        break;
      case LightType.IBL:
        quality += 30;
        break;
      case LightType.VOLUMETRIC:
        quality += 20;
        break;
      case LightType.DIRECTIONAL:
        quality += 15;
        break;
    }

    // 阴影质量提升
    if (properties.castShadow) {
      switch (properties.shadowType) {
        case ShadowType.RAYTRACED:
          quality += 40;
          break;
        case ShadowType.PCSS:
          quality += 30;
          break;
        case ShadowType.CSM:
          quality += 25;
          break;
        case ShadowType.PCF:
          quality += 15;
          break;
        case ShadowType.VSM:
          quality += 20;
          break;
      }

      if (properties.shadowMapSize >= 2048) {
        quality += 10;
      }
    }

    // 体积光质量提升
    if (properties.volumetric) {
      quality += 15;
    }

    return Math.min(100, quality);
  }

  /**
   * 获取光源类型的分类
   */
  private getCategoryForLightType(type: LightType): string {
    switch (type) {
      case LightType.DIRECTIONAL:
        return 'environmental';
      case LightType.POINT:
      case LightType.SPOT:
        return 'local';
      case LightType.AREA:
        return 'area';
      case LightType.AMBIENT:
      case LightType.HEMISPHERE:
        return 'ambient';
      case LightType.IBL:
        return 'image-based';
      case LightType.VOLUMETRIC:
        return 'volumetric';
      default:
        return 'general';
    }
  }

  /**
   * 获取光源类型的标签
   */
  private getTagsForLightType(type: LightType): string[] {
    switch (type) {
      case LightType.DIRECTIONAL:
        return ['sun', 'directional', 'infinite'];
      case LightType.POINT:
        return ['point', 'omnidirectional', 'local'];
      case LightType.SPOT:
        return ['spot', 'cone', 'focused'];
      case LightType.AREA:
        return ['area', 'soft', 'realistic'];
      case LightType.AMBIENT:
        return ['ambient', 'global', 'uniform'];
      case LightType.HEMISPHERE:
        return ['hemisphere', 'sky', 'ambient'];
      case LightType.IBL:
        return ['ibl', 'environment', 'realistic'];
      case LightType.VOLUMETRIC:
        return ['volumetric', 'atmospheric', 'fog'];
      default:
        return [type];
    }
  }

  /**
   * 获取所有光源
   */
  public getAllLights(): LightDefinition[] {
    return Array.from(this.lights.values());
  }

  /**
   * 获取所有光照探针
   */
  public getAllLightProbes(): LightProbe[] {
    return Array.from(this.lightProbes.values());
  }

  /**
   * 获取所有IBL环境
   */
  public getAllIBLEnvironments(): IBLEnvironment[] {
    return Array.from(this.iblEnvironments.values());
  }

  /**
   * 获取所有光照场景
   */
  public getAllLightingScenes(): LightingScene[] {
    return Array.from(this.lightingScenes.values());
  }

  /**
   * 获取活动光照场景
   */
  public getActiveLightingScene(): LightingScene | null {
    return this.activeLightingScene;
  }

  /**
   * 删除光源
   */
  public deleteLight(lightId: string): boolean {
    const light = this.lights.get(lightId);
    if (!light) {
      return false;
    }

    this.lights.delete(lightId);

    // 从活动场景中移除
    if (this.activeLightingScene) {
      this.activeLightingScene.lights = this.activeLightingScene.lights.filter(l => l.id !== lightId);
    }

    this.emit('lightDeleted', light);
    return true;
  }

  /**
   * 删除光照探针
   */
  public deleteLightProbe(probeId: string): boolean {
    const probe = this.lightProbes.get(probeId);
    if (!probe) {
      return false;
    }

    this.lightProbes.delete(probeId);

    // 从活动场景中移除
    if (this.activeLightingScene) {
      this.activeLightingScene.lightProbes = this.activeLightingScene.lightProbes.filter(p => p.id !== probeId);
    }

    this.emit('lightProbeDeleted', probe);
    return true;
  }

  /**
   * 获取光照统计信息
   */
  public getLightingStats(): {
    totalLights: number;
    activeLights: number;
    totalProbes: number;
    activeProbes: number;
    averagePerformance: number;
    estimatedFrameTime: number;
    memoryUsage: number;
  } {
    const activeLights = this.activeLightingScene?.lights.filter(l => l.properties.enabled) || [];
    const activeProbes = this.activeLightingScene?.lightProbes.filter(p => p.enabled) || [];

    const totalPerformance = activeLights.reduce((sum, light) => sum + light.metadata.performance, 0);
    const averagePerformance = activeLights.length > 0 ? totalPerformance / activeLights.length : 100;

    // 估算帧时间影响（毫秒）
    const estimatedFrameTime = activeLights.length * (100 - averagePerformance) * 0.02;

    // 估算内存使用（MB）
    const memoryUsage = (activeLights.length * 0.5) + (activeProbes.length * 2);

    return {
      totalLights: this.lights.size,
      activeLights: activeLights.length,
      totalProbes: this.lightProbes.size,
      activeProbes: activeProbes.length,
      averagePerformance,
      estimatedFrameTime,
      memoryUsage
    };
  }

  /**
   * 优化光照设置
   */
  public optimizeLightingSettings(targetPerformance: number = 60): void {
    if (!this.activeLightingScene) {
      return;
    }

    const stats = this.getLightingStats();

    if (stats.averagePerformance < targetPerformance) {
      // 降低阴影质量
      this.activeLightingScene.lights.forEach(light => {
        if (light.properties.castShadow && light.properties.shadowMapSize > 1024) {
          light.properties.shadowMapSize = Math.max(512, light.properties.shadowMapSize / 2);
        }
      });

      // 降低全局光照质量
      if (this.activeLightingScene.globalIllumination.enabled) {
        this.activeLightingScene.globalIllumination.probeBounces = Math.max(1,
          this.activeLightingScene.globalIllumination.probeBounces - 1);
        this.activeLightingScene.globalIllumination.lightmapQuality = 'low';
      }

      // 降低环境光遮蔽质量
      if (this.activeLightingScene.ambientOcclusion.enabled) {
        this.activeLightingScene.ambientOcclusion.quality = 'low';
        this.activeLightingScene.ambientOcclusion.halfResolution = true;
      }

      this.emit('lightingOptimized', this.activeLightingScene);
    }
  }

  /**
   * 导出光照场景
   */
  public exportLightingScene(sceneId: string): string {
    const scene = this.lightingScenes.get(sceneId);
    if (!scene) {
      throw new Error('Lighting scene not found');
    }

    return JSON.stringify(scene, null, 2);
  }

  /**
   * 导入光照场景
   */
  public importLightingScene(sceneData: string): LightingScene {
    try {
      const scene = JSON.parse(sceneData) as LightingScene;
      scene.id = `scene_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      this.lightingScenes.set(scene.id, scene);
      this.emit('lightingSceneImported', scene);

      return scene;
    } catch (error) {
      throw new Error('Invalid lighting scene data');
    }
  }

  /**
   * 检查是否正在更新
   */
  public isUpdatingLighting(): boolean {
    return this.isUpdating;
  }
}

export default AdvancedLightingService;
