/**
 * TransformEditor 使用示例
 */
import React, { useState } from 'react';
import { Card, Space, Button, Switch, Divider, Typography, message, Row, Col } from 'antd';
import { PlayCircleOutlined, PauseCircleOutlined, UndoOutlined, RedoOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { undo, redo } from '../../store/editor/editorSlice';
import TransformEditor from './TransformEditor';

const { Title, Text } = Typography;

/**
 * 变换编辑器示例组件
 */
const TransformEditorExample: React.FC = () => {
  const dispatch = useDispatch();
  const { undoStack, redoStack } = useSelector((state: any) => state.editor);

  const [readonly, setReadonly] = useState(false);
  const [externalData, setExternalData] = useState({
    position: { x: 0, y: 0, z: 0 },
    rotation: { x: 0, y: 0, z: 0 },
    scale: { x: 1, y: 1, z: 1 }
  });
  const [useExternalData, setUseExternalData] = useState(false);

  // 处理外部数据变化
  const handleExternalDataChange = (data: any) => {
    setExternalData(data);
    console.log('变换数据变化:', data);
    message.success('变换数据已更新');
  };

  // 处理撤销
  const handleUndo = () => {
    dispatch(undo());
    message.info('已撤销');
  };

  // 处理重做
  const handleRedo = () => {
    dispatch(redo());
    message.info('已重做');
  };

  // 随机设置变换数据
  const handleRandomTransform = () => {
    const randomData = {
      position: {
        x: Math.random() * 20 - 10,
        y: Math.random() * 20 - 10,
        z: Math.random() * 20 - 10
      },
      rotation: {
        x: Math.random() * 360 - 180,
        y: Math.random() * 360 - 180,
        z: Math.random() * 360 - 180
      },
      scale: {
        x: Math.random() * 2 + 0.5,
        y: Math.random() * 2 + 0.5,
        z: Math.random() * 2 + 0.5
      }
    };
    setExternalData(randomData);
  };

  // 重置变换数据
  const handleResetTransform = () => {
    setExternalData({
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    });
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>TransformEditor 组件示例</Title>
      
      <Card title="控制面板" style={{ marginBottom: '20px' }}>
        <Row gutter={16}>
          <Col span={12}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text>只读模式: </Text>
                <Switch
                  checked={readonly}
                  onChange={setReadonly}
                  checkedChildren="只读"
                  unCheckedChildren="编辑"
                />
              </div>

              <div>
                <Text>使用外部数据: </Text>
                <Switch
                  checked={useExternalData}
                  onChange={setUseExternalData}
                  checkedChildren="外部"
                  unCheckedChildren="引擎"
                />
              </div>

              {useExternalData && (
                <Space>
                  <Button
                    type="primary"
                    icon={<PlayCircleOutlined />}
                    onClick={handleRandomTransform}
                  >
                    随机变换
                  </Button>
                  <Button
                    icon={<PauseCircleOutlined />}
                    onClick={handleResetTransform}
                  >
                    重置变换
                  </Button>
                </Space>
              )}
            </Space>
          </Col>

          <Col span={12}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>撤销/重做操作</Text>
              </div>
              <Space>
                <Button
                  icon={<UndoOutlined />}
                  onClick={handleUndo}
                  disabled={undoStack.length === 0}
                >
                  撤销 ({undoStack.length})
                </Button>
                <Button
                  icon={<RedoOutlined />}
                  onClick={handleRedo}
                  disabled={redoStack.length === 0}
                >
                  重做 ({redoStack.length})
                </Button>
              </Space>
              <div style={{ fontSize: '12px', color: '#666' }}>
                <Text type="secondary">
                  撤销栈: {undoStack.length} 项 | 重做栈: {redoStack.length} 项
                </Text>
              </div>
            </Space>
          </Col>
        </Row>
      </Card>

      <div style={{ display: 'flex', gap: '20px', flexWrap: 'wrap' }}>
        {/* 基本用法 */}
        <Card title="基本用法" style={{ flex: '1', minWidth: '300px' }}>
          <Text type="secondary" style={{ display: 'block', marginBottom: '10px' }}>
            编辑当前选中的实体变换
          </Text>
          <TransformEditor readonly={readonly} />
        </Card>

        {/* 外部数据控制 */}
        {useExternalData && (
          <Card title="外部数据控制" style={{ flex: '1', minWidth: '300px' }}>
            <Text type="secondary" style={{ display: 'block', marginBottom: '10px' }}>
              通过外部状态控制变换数据
            </Text>
            <TransformEditor 
              data={externalData}
              onChange={handleExternalDataChange}
              readonly={readonly}
            />
          </Card>
        )}
      </div>

      {/* 数据显示 */}
      {useExternalData && (
        <Card title="当前变换数据" style={{ marginTop: '20px' }}>
          <pre style={{ 
            backgroundColor: '#f5f5f5', 
            padding: '10px', 
            borderRadius: '4px',
            fontSize: '12px'
          }}>
            {JSON.stringify(externalData, null, 2)}
          </pre>
        </Card>
      )}

      <Divider />

      {/* 使用说明 */}
      <Card title="使用说明">
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Title level={4}>功能特性</Title>
            <ul>
              <li><strong>实时同步</strong>: 与底层引擎的Transform组件实时同步</li>
              <li><strong>链接缩放</strong>: 点击链接按钮可以统一调整XYZ轴缩放</li>
              <li><strong>复制粘贴</strong>: 支持变换数据的复制和粘贴</li>
              <li><strong>重置功能</strong>: 一键重置变换到默认值</li>
              <li><strong>防抖处理</strong>: 100ms防抖，避免频繁更新引擎</li>
              <li><strong>只读模式</strong>: 支持只读模式，用于查看变换数据</li>
              <li><strong>撤销/重做</strong>: 完整的撤销重做系统，支持变换操作历史</li>
              <li><strong>坐标空间</strong>: 支持本地坐标和世界坐标切换（位置）</li>
              <li><strong>属性面板集成</strong>: 无缝集成到编辑器的属性面板中</li>
            </ul>
          </div>

          <div>
            <Title level={4}>操作说明</Title>
            <ul>
              <li><strong>位置</strong>: 世界坐标系下的位置，支持小数</li>
              <li><strong>旋转</strong>: 欧拉角旋转，单位为度数（-360° 到 360°）</li>
              <li><strong>缩放</strong>: 非均匀缩放，最小值为0.01</li>
              <li><strong>工具栏</strong>: 复制、粘贴、重置按钮（只读模式下隐藏）</li>
            </ul>
          </div>

          <div>
            <Title level={4}>集成方式</Title>
            <ul>
              <li><strong>自动模式</strong>: 自动编辑当前选中的实体</li>
              <li><strong>指定实体</strong>: 通过entityId属性指定要编辑的实体</li>
              <li><strong>外部控制</strong>: 通过data和onChange属性进行外部数据控制</li>
            </ul>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default TransformEditorExample;
