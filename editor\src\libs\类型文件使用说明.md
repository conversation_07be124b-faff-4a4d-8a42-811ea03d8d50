# DL引擎类型文件使用说明

本目录包含了DL引擎的完整类型定义文件，已成功将所有类型文件导出到单个文件中。

## 📁 文件说明

### 1. `index.d.ts` - 主类型声明文件
- **用途**: 标准的TypeScript类型声明文件
- **特点**: 包含完整的import/export语句
- **适用**: 作为npm包的类型声明文件使用

### 2. `dl-engine-types.d.ts` - 完整类型定义文件
- **用途**: 包含所有模块的完整类型定义
- **大小**: 2.8 MB，137,488 行
- **特点**: 保留了原始的import/export结构
- **适用**: 完整的类型参考和文档

### 3. `dl-engine-clean-types.d.ts` - 纯类型定义文件 ⭐ **推荐**
- **用途**: 纯类型定义，无import/export语句
- **大小**: 2.8 MB，116,366 行
- **特点**: 使用namespace组织，便于直接使用
- **适用**: 直接集成到TypeScript项目中

## 🚀 使用方法

### 方法一：直接引用（推荐）
```typescript
// 在你的TypeScript文件顶部添加
/// <reference path="./dl-engine-clean-types.d.ts" />

// 现在可以直接使用DL引擎的类型
const engine: DL.Engine = new Engine();
const world: DL.World = new World();
const entity: DL.Entity = new Entity();
```

### 方法二：tsconfig.json配置
```json
{
  "compilerOptions": {
    "typeRoots": ["./node_modules/@types", "./types"],
    "types": ["dl-engine-clean-types"]
  },
  "include": [
    "src/**/*",
    "dl-engine-clean-types.d.ts"
  ]
}
```

### 方法三：作为模块声明
```typescript
// 在你的项目中创建 types/dl-engine.d.ts
declare module 'dl-engine' {
  // 复制 dl-engine-clean-types.d.ts 的内容到这里
}
```

## 📊 模块结构

DL引擎包含以下主要模块：

### 核心模块 (core)
- `Engine` - 引擎主类
- `World` - 世界管理
- `Entity` - 实体系统
- `Component` - 组件系统
- `System` - 系统架构

### 渲染模块 (rendering)
- `Camera` - 相机系统
- `Renderer` - 渲染器
- `Light` - 光照系统
- `Material` - 材质系统

### 物理模块 (physics)
- `PhysicsBody` - 物理体
- `PhysicsCollider` - 碰撞器
- `PhysicsSystem` - 物理系统

### 动画模块 (animation)
- `Animator` - 动画控制器
- `AnimationClip` - 动画片段
- `AnimationSystem` - 动画系统

### 视觉脚本模块 (visualscript)
- `VisualScriptSystem` - 视觉脚本系统
- `Node` - 节点基类
- `NodeRegistry` - 节点注册表

### AI模块 (ai)
- `AIContentGenerator` - AI内容生成器
- `AIRecommendationEngine` - AI推荐引擎
- `AIEmotionAnalysisSystem` - AI情感分析系统

### 网络模块 (network)
- `NetworkSystem` - 网络系统
- `NetworkManager` - 网络管理器
- `WebRTCConnection` - WebRTC连接

### 其他模块
- **音频模块** (audio) - 音频系统
- **交互模块** (interaction) - 交互系统
- **输入模块** (input) - 输入处理
- **场景模块** (scene) - 场景管理
- **资源模块** (assets) - 资源管理
- **粒子模块** (particles) - 粒子系统
- **头像模块** (avatar) - 头像系统
- **区块链模块** (blockchain) - 区块链集成
- **导航模块** (navigation) - 导航系统
- **UI模块** (ui) - 用户界面
- **工具模块** (utils) - 工具函数
- **安全模块** (safety) - 安全系统

## 💡 使用示例

```typescript
// 引用类型文件
/// <reference path="./dl-engine-clean-types.d.ts" />

// 创建引擎实例
const engine: DL.Engine = new Engine({
  canvas: 'canvas',
  autoStart: true
});

// 创建世界
const world: DL.World = new World(engine);

// 创建实体
const entity: DL.Entity = new Entity('MyEntity');

// 添加组件
const transform: DL.Component = new Transform({
  position: { x: 0, y: 0, z: 0 }
});
entity.addComponent(transform);

// 创建相机
const camera: DL.Camera = new Camera({
  type: 'perspective',
  fov: 60
});

// 使用视觉脚本系统
const visualScript: DL.VisualScriptSystem = new VisualScriptSystem();
```

## 📈 统计信息

- **总文件数**: 771个类型文件
- **总行数**: 116,366行
- **文件大小**: 2.8 MB
- **模块数量**: 20个主要模块
- **生成时间**: 2025年6月27日

## 🔧 重新生成

如果需要重新生成类型文件，请运行：

```bash
# 生成完整类型文件
node build-types.js

# 生成纯类型定义文件
node export-clean-types.js
```

## 📝 注意事项

1. **文件较大**: 类型文件较大，建议按需引用
2. **版本兼容**: 确保类型文件版本与引擎版本匹配
3. **性能考虑**: 在大型项目中，可以考虑按模块分别引用
4. **更新频率**: 当引擎更新时，需要重新生成类型文件

## 🆘 问题排查

### 类型错误
- 确保引用了正确的类型文件
- 检查TypeScript版本兼容性
- 验证tsconfig.json配置

### 性能问题
- 考虑按模块分别引用
- 使用TypeScript的增量编译
- 优化IDE的TypeScript服务配置

### 找不到类型
- 检查文件路径是否正确
- 确认类型文件已正确引用
- 查看IDE的类型解析设置
