/**
 * EnhancedNodeEditor.tsx
 * 
 * 增强的节点编辑器组件
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Button,
  Input,
  Select,
  InputNumber,
  Switch,
  ColorPicker,
  Slider,
  Space,
  Divider,
  Typography,
  Tooltip,
  Badge
} from 'antd';
import {
  DeleteOutlined,
  CopyOutlined,
  LinkOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  LockOutlined,
  UnlockOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;
const { Option } = Select;

/**
 * 节点属性接口
 */
interface NodeProperty {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'color' | 'select' | 'slider';
  value: any;
  label: string;
  description?: string;
  options?: { label: string; value: any }[];
  min?: number;
  max?: number;
  step?: number;
  required?: boolean;
}

/**
 * 节点数据接口
 */
interface NodeData {
  id: string;
  type: string;
  label: string;
  description: string;
  category: string;
  position: { x: number; y: number };
  size: { width: number; height: number };
  properties: NodeProperty[];
  inputs: any[];
  outputs: any[];
  visible: boolean;
  locked: boolean;
  color: string;
  tags: string[];
}

/**
 * 增强节点编辑器属性
 */
interface EnhancedNodeEditorProps {
  node: NodeData | null;
  onNodeChange: (node: NodeData) => void;
  onNodeDelete: (nodeId: string) => void;
  onNodeDuplicate: (node: NodeData) => void;
  onClose: () => void;
}

/**
 * 增强节点编辑器组件
 */
const EnhancedNodeEditor: React.FC<EnhancedNodeEditorProps> = ({
  node,
  onNodeChange,
  onNodeDelete,
  onNodeDuplicate,
  onClose
}) => {
  const { t } = useTranslation();
  const [localNode, setLocalNode] = useState<NodeData | null>(node);
  const [hasChanges, setHasChanges] = useState(false);

  // 同步外部节点数据
  useEffect(() => {
    setLocalNode(node);
    setHasChanges(false);
  }, [node]);

  // 处理属性变化
  const handlePropertyChange = useCallback((propertyName: string, value: any) => {
    if (!localNode) return;

    const updatedNode = {
      ...localNode,
      properties: localNode.properties.map(prop =>
        prop.name === propertyName ? { ...prop, value } : prop
      )
    };

    setLocalNode(updatedNode);
    setHasChanges(true);
  }, [localNode]);

  // 处理节点基本信息变化
  const handleNodeInfoChange = useCallback((field: string, value: any) => {
    if (!localNode) return;

    const updatedNode = {
      ...localNode,
      [field]: value
    };

    setLocalNode(updatedNode);
    setHasChanges(true);
  }, [localNode]);

  // 应用更改
  const applyChanges = useCallback(() => {
    if (localNode && hasChanges) {
      onNodeChange(localNode);
      setHasChanges(false);
    }
  }, [localNode, hasChanges, onNodeChange]);

  // 重置更改
  const resetChanges = useCallback(() => {
    setLocalNode(node);
    setHasChanges(false);
  }, [node]);

  // 渲染属性编辑器
  const renderPropertyEditor = (property: NodeProperty) => {
    const { name, type, value, label, description, options, min, max, step } = property;

    const commonProps = {
      value,
      onChange: (val: any) => handlePropertyChange(name, val),
      style: { width: '100%' }
    };

    let editor;

    switch (type) {
      case 'string':
        editor = <Input {...commonProps} placeholder={description} />;
        break;
      case 'number':
        editor = (
          <InputNumber
            {...commonProps}
            min={min}
            max={max}
            step={step || 1}
            placeholder={description}
          />
        );
        break;
      case 'boolean':
        editor = (
          <Switch
            checked={value}
            onChange={(checked) => handlePropertyChange(name, checked)}
          />
        );
        break;
      case 'color':
        editor = (
          <ColorPicker
            value={value}
            onChange={(color) => handlePropertyChange(name, color.toHexString())}
          />
        );
        break;
      case 'select':
        editor = (
          <Select {...commonProps} placeholder={description}>
            {options?.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        );
        break;
      case 'slider':
        editor = (
          <Slider
            {...commonProps}
            min={min || 0}
            max={max || 100}
            step={step || 1}
          />
        );
        break;
      default:
        editor = <Input {...commonProps} />;
    }

    return (
      <div key={name} style={{ marginBottom: '12px' }}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
          <Text strong>{label}</Text>
          {description && (
            <Tooltip title={description}>
              <InfoCircleOutlined style={{ marginLeft: '4px', color: '#999' }} />
            </Tooltip>
          )}
          {property.required && (
            <Text type="danger" style={{ marginLeft: '4px' }}>*</Text>
          )}
        </div>
        {editor}
      </div>
    );
  };

  if (!localNode) {
    return (
      <Card style={{ width: 300, height: '100%' }}>
        <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
          <Text>{t('节点编辑器.未选择节点')}</Text>
        </div>
      </Card>
    );
  }

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <span>{t('节点编辑器.节点属性')}</span>
          <Space>
            {hasChanges && (
              <Badge dot>
                <Button size="small" onClick={applyChanges}>
                  {t('节点编辑器.应用')}
                </Button>
              </Badge>
            )}
            <Button size="small" onClick={onClose}>
              {t('节点编辑器.关闭')}
            </Button>
          </Space>
        </div>
      }
      style={{ width: 300, height: '100%' }}
      styles={{ body: { padding: '16px', maxHeight: 'calc(100% - 60px)', overflow: 'auto' } }}
    >
      {/* 节点基本信息 */}
      <div style={{ marginBottom: '16px' }}>
        <Title level={5}>{t('节点编辑器.基本信息')}</Title>
        
        <div style={{ marginBottom: '8px' }}>
          <Text strong>{t('节点编辑器.节点名称')}</Text>
          <Input
            value={localNode.label}
            onChange={(e) => handleNodeInfoChange('label', e.target.value)}
            style={{ marginTop: '4px' }}
          />
        </div>

        <div style={{ marginBottom: '8px' }}>
          <Text strong>{t('节点编辑器.节点描述')}</Text>
          <Input.TextArea
            value={localNode.description}
            onChange={(e) => handleNodeInfoChange('description', e.target.value)}
            rows={2}
            style={{ marginTop: '4px' }}
          />
        </div>

        <div style={{ marginBottom: '8px' }}>
          <Text strong>{t('节点编辑器.节点颜色')}</Text>
          <div style={{ marginTop: '4px' }}>
            <ColorPicker
              value={localNode.color}
              onChange={(color) => handleNodeInfoChange('color', color.toHexString())}
            />
          </div>
        </div>

        <div style={{ display: 'flex', gap: '8px', marginTop: '8px' }}>
          <Tooltip title={localNode.visible ? t('节点编辑器.隐藏节点') : t('节点编辑器.显示节点')}>
            <Button
              icon={localNode.visible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              onClick={() => handleNodeInfoChange('visible', !localNode.visible)}
              type={localNode.visible ? 'default' : 'dashed'}
            />
          </Tooltip>
          
          <Tooltip title={localNode.locked ? t('节点编辑器.解锁节点') : t('节点编辑器.锁定节点')}>
            <Button
              icon={localNode.locked ? <LockOutlined /> : <UnlockOutlined />}
              onClick={() => handleNodeInfoChange('locked', !localNode.locked)}
              type={localNode.locked ? 'primary' : 'default'}
            />
          </Tooltip>
        </div>
      </div>

      <Divider />

      {/* 节点属性 */}
      <div style={{ marginBottom: '16px' }}>
        <Title level={5}>{t('节点编辑器.节点属性')}</Title>
        {localNode.properties.map(renderPropertyEditor)}
      </div>

      <Divider />

      {/* 节点操作 */}
      <div>
        <Title level={5}>{t('节点编辑器.节点操作')}</Title>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Button
            icon={<CopyOutlined />}
            onClick={() => onNodeDuplicate(localNode)}
            block
          >
            {t('节点编辑器.复制节点')}
          </Button>
          
          <Button
            icon={<LinkOutlined />}
            block
          >
            {t('节点编辑器.查看连接')}
          </Button>
          
          <Button
            icon={<DeleteOutlined />}
            onClick={() => onNodeDelete(localNode.id)}
            danger
            block
          >
            {t('节点编辑器.删除节点')}
          </Button>
        </Space>
      </div>

      {/* 更改提示 */}
      {hasChanges && (
        <div style={{ 
          position: 'sticky', 
          bottom: 0, 
          backgroundColor: '#fff', 
          padding: '8px 0',
          borderTop: '1px solid #f0f0f0',
          marginTop: '16px'
        }}>
          <Space style={{ width: '100%', justifyContent: 'space-between' }}>
            <Text type="warning">{t('节点编辑器.有未保存的更改')}</Text>
            <Space>
              <Button size="small" onClick={resetChanges}>
                {t('节点编辑器.重置')}
              </Button>
              <Button size="small" type="primary" onClick={applyChanges}>
                {t('节点编辑器.保存')}
              </Button>
            </Space>
          </Space>
        </div>
      )}
    </Card>
  );
};

export default EnhancedNodeEditor;
