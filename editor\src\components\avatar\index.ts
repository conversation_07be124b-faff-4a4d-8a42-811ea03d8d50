/**
 * 虚拟化身组件
 * 导出所有虚拟化身相关的组件
 */

// 主编辑器
export { default as AvatarCustomizationEditor } from './AvatarCustomizationEditor';

// 子组件
export { default as PhotoUploadPanel } from './PhotoUploadPanel';
export { default as AvatarPreviewCanvas } from './AvatarPreviewCanvas';

// 类型定义
export type {
  AvatarData,
  BodyParameters,
  FaceParameters,
  EditorState
} from './AvatarCustomizationEditor';

export type {
  PhotoQualityAssessment
} from './PhotoUploadPanel';

export type {
  PreviewSettings,
  RenderStats
} from './AvatarPreviewCanvas';
