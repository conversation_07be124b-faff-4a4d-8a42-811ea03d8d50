/**
 * 路径预览组件
 */
import React, { useRef, useEffect, useImperativeHandle, forwardRef, useState, useCallback } from 'react';
import { Card, Slider, Space, Button, Select, Switch, InputNumber } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import * as THREE from 'three';

const { Option } = Select;

/**
 * 路径数据接口
 */
interface PathData {
  id: string;
  name: string;
  points: PathPointData[];
  loopMode: 'none' | 'loop' | 'pingpong';
  interpolation: 'linear' | 'smooth' | 'bezier' | 'spline';
  totalDuration: number;
}

/**
 * 路径点数据接口
 */
interface PathPointData {
  id: string;
  position: { x: number; y: number; z: number };
  waitTime: number;
  speed: number;
  animation: string;
}

/**
 * 预览组件属性
 */
interface PathPreviewProps {
  /** 路径数据 */
  path: PathData;
  /** 进度变化回调 */
  onProgressChange?: (progress: number) => void;
  /** 播放状态变化回调 */
  onPlayStateChange?: (playing: boolean, paused: boolean) => void;
}

/**
 * 预览组件引用接口
 */
export interface PathPreviewRef {
  startPreview: () => void;
  pausePreview: () => void;
  stopPreview: () => void;
  setProgress: (progress: number) => void;
}

/**
 * 路径预览组件
 */
export const PathPreview = forwardRef<PathPreviewRef, PathPreviewProps>(({
  path,
  onProgressChange,
  onPlayStateChange
}, ref) => {
  const { t } = useTranslation();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const animationFrameRef = useRef<number>();
  
  // Three.js 对象
  const sceneRef = useRef<THREE.Scene>();
  const cameraRef = useRef<THREE.PerspectiveCamera>();
  const rendererRef = useRef<THREE.WebGLRenderer>();
  const avatarRef = useRef<THREE.Mesh>();
  const pathLineRef = useRef<THREE.Line>();
  const pathPointsRef = useRef<THREE.Group>();
  
  // 状态
  const [isPlaying, setIsPlaying] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [playbackSpeed, setPlaybackSpeed] = useState(1);
  const [showTrail, setShowTrail] = useState(true);
  const [cameraMode, setCameraMode] = useState<'follow' | 'fixed' | 'overview'>('follow');
  
  // 预览数据
  const startTimeRef = useRef<number>(0);
  const trailPointsRef = useRef<THREE.Vector3[]>([]);

  /**
   * 初始化Three.js场景
   */
  const initializeScene = useCallback(() => {
    if (!canvasRef.current || !containerRef.current) return;

    const container = containerRef.current;
    const canvas = canvasRef.current;

    // 创建场景
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x87CEEB); // 天蓝色背景
    sceneRef.current = scene;

    // 创建相机
    const camera = new THREE.PerspectiveCamera(
      75,
      container.clientWidth / container.clientHeight,
      0.1,
      1000
    );
    camera.position.set(0, 10, 10);
    camera.lookAt(0, 0, 0);
    cameraRef.current = camera;

    // 创建渲染器
    const renderer = new THREE.WebGLRenderer({ 
      canvas,
      antialias: true
    });
    renderer.setSize(container.clientWidth, container.clientHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    rendererRef.current = renderer;

    // 创建地面
    const groundGeometry = new THREE.PlaneGeometry(100, 100);
    const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x90EE90 });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    scene.add(ground);

    // 创建网格
    const grid = new THREE.GridHelper(100, 100, 0x888888, 0xcccccc);
    scene.add(grid);

    // 创建数字人（简单的胶囊体）
    const avatarGeometry = new THREE.CapsuleGeometry(0.5, 1.5, 4, 8);
    const avatarMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
    const avatar = new THREE.Mesh(avatarGeometry, avatarMaterial);
    avatar.position.y = 1;
    avatar.castShadow = true;
    scene.add(avatar);
    avatarRef.current = avatar;

    // 创建路径点组
    const pointsGroup = new THREE.Group();
    scene.add(pointsGroup);
    pathPointsRef.current = pointsGroup;

    // 添加光照
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    scene.add(directionalLight);

    // 初始渲染
    renderer.render(scene, camera);

    // 添加窗口大小变化监听
    const handleResize = () => {
      if (!container || !camera || !renderer) return;
      
      const width = container.clientWidth;
      const height = container.clientHeight;
      
      camera.aspect = width / height;
      camera.updateProjectionMatrix();
      renderer.setSize(width, height);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  /**
   * 更新路径显示
   */
  const updatePathDisplay = useCallback(() => {
    if (!sceneRef.current || !pathPointsRef.current) return;

    const scene = sceneRef.current;
    const pointsGroup = pathPointsRef.current;

    // 清除现有的路径点
    pointsGroup.clear();

    // 移除现有的路径线
    if (pathLineRef.current) {
      scene.remove(pathLineRef.current);
    }

    if (path.points.length === 0) return;

    // 创建路径点
    path.points.forEach((point, index) => {
      const geometry = new THREE.SphereGeometry(0.3, 16, 16);
      const material = new THREE.MeshLambertMaterial({ color: 0xFFD700 });
      const sphere = new THREE.Mesh(geometry, material);
      sphere.position.set(point.position.x, point.position.y + 0.3, point.position.z);
      sphere.castShadow = true;
      pointsGroup.add(sphere);

      // 添加路径点编号
      const textGeometry = new THREE.RingGeometry(0.1, 0.2, 8);
      const textMaterial = new THREE.MeshBasicMaterial({ color: 0x000000 });
      const textMesh = new THREE.Mesh(textGeometry, textMaterial);
      textMesh.position.set(point.position.x, point.position.y + 0.8, point.position.z);
      textMesh.lookAt(cameraRef.current!.position);
      pointsGroup.add(textMesh);
    });

    // 创建路径线
    if (path.points.length > 1) {
      const points = path.points.map(p => 
        new THREE.Vector3(p.position.x, p.position.y + 0.1, p.position.z)
      );

      // 根据循环模式添加额外的点
      if (path.loopMode === 'loop') {
        points.push(points[0]);
      } else if (path.loopMode === 'pingpong') {
        for (let i = points.length - 2; i >= 0; i--) {
          points.push(points[i]);
        }
      }

      const geometry = new THREE.BufferGeometry().setFromPoints(points);
      const material = new THREE.LineBasicMaterial({ 
        color: 0x00ff00,
        linewidth: 3
      });
      const line = new THREE.Line(geometry, material);
      scene.add(line);
      pathLineRef.current = line;
    }

    // 重新渲染
    if (rendererRef.current && cameraRef.current) {
      rendererRef.current.render(scene, cameraRef.current);
    }
  }, [path]);

  /**
   * 计算指定时间的位置
   */
  const getPositionAtTime = useCallback((time: number) => {
    if (path.points.length === 0) return null;

    // 简化的位置计算，实际应该使用路径插值
    const totalDuration = path.totalDuration || 10;
    const normalizedTime = (time % totalDuration) / totalDuration;
    
    if (path.points.length === 1) {
      const point = path.points[0];
      return new THREE.Vector3(point.position.x, point.position.y, point.position.z);
    }

    const segmentCount = path.points.length - 1;
    const segmentTime = normalizedTime * segmentCount;
    const segmentIndex = Math.floor(segmentTime);
    const localTime = segmentTime - segmentIndex;

    const point1 = path.points[Math.min(segmentIndex, path.points.length - 1)];
    const point2 = path.points[Math.min(segmentIndex + 1, path.points.length - 1)];

    const pos1 = new THREE.Vector3(point1.position.x, point1.position.y, point1.position.z);
    const pos2 = new THREE.Vector3(point2.position.x, point2.position.y, point2.position.z);

    return pos1.lerp(pos2, localTime);
  }, [path]);

  /**
   * 更新数字人位置
   */
  const updateAvatarPosition = useCallback((time: number) => {
    if (!avatarRef.current) return;

    const position = getPositionAtTime(time);
    if (position) {
      avatarRef.current.position.copy(position);
      avatarRef.current.position.y += 1; // 数字人高度偏移

      // 更新轨迹
      if (showTrail) {
        trailPointsRef.current.push(position.clone());
        if (trailPointsRef.current.length > 100) {
          trailPointsRef.current.shift();
        }
      }

      // 更新相机位置
      if (cameraMode === 'follow' && cameraRef.current) {
        const camera = cameraRef.current;
        const offset = new THREE.Vector3(0, 10, 10);
        camera.position.copy(position).add(offset);
        camera.lookAt(position);
      }
    }
  }, [getPositionAtTime, showTrail, cameraMode]);

  /**
   * 动画循环
   */
  const animate = useCallback(() => {
    if (!isPlaying || isPaused) return;

    const currentTimeMs = Date.now();
    const deltaTime = (currentTimeMs - startTimeRef.current) / 1000 * playbackSpeed;
    
    setCurrentTime(deltaTime);
    
    const totalDuration = path.totalDuration || 10;
    const newProgress = (deltaTime % totalDuration) / totalDuration;
    setProgress(newProgress);
    onProgressChange?.(newProgress);

    updateAvatarPosition(deltaTime);

    // 渲染
    if (rendererRef.current && cameraRef.current && sceneRef.current) {
      rendererRef.current.render(sceneRef.current, cameraRef.current);
    }

    animationFrameRef.current = requestAnimationFrame(animate);
  }, [isPlaying, isPaused, playbackSpeed, path.totalDuration, onProgressChange, updateAvatarPosition]);

  /**
   * 开始预览
   */
  const startPreview = useCallback(() => {
    if (path.points.length < 2) return;

    setIsPlaying(true);
    setIsPaused(false);
    startTimeRef.current = Date.now();
    trailPointsRef.current = [];
    
    onPlayStateChange?.(true, false);
    
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    animationFrameRef.current = requestAnimationFrame(animate);
  }, [path.points.length, onPlayStateChange, animate]);

  /**
   * 暂停预览
   */
  const pausePreview = useCallback(() => {
    setIsPaused(!isPaused);
    onPlayStateChange?.(isPlaying, !isPaused);
    
    if (!isPaused) {
      // 暂停
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    } else {
      // 恢复
      startTimeRef.current = Date.now() - currentTime * 1000 / playbackSpeed;
      animationFrameRef.current = requestAnimationFrame(animate);
    }
  }, [isPaused, isPlaying, currentTime, playbackSpeed, onPlayStateChange, animate]);

  /**
   * 停止预览
   */
  const stopPreview = useCallback(() => {
    setIsPlaying(false);
    setIsPaused(false);
    setProgress(0);
    setCurrentTime(0);
    trailPointsRef.current = [];
    
    onPlayStateChange?.(false, false);
    
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }

    // 重置数字人位置
    if (avatarRef.current && path.points.length > 0) {
      const firstPoint = path.points[0];
      avatarRef.current.position.set(
        firstPoint.position.x,
        firstPoint.position.y + 1,
        firstPoint.position.z
      );
    }

    // 重新渲染
    if (rendererRef.current && cameraRef.current && sceneRef.current) {
      rendererRef.current.render(sceneRef.current, cameraRef.current);
    }
  }, [path.points, onPlayStateChange]);

  /**
   * 设置进度
   */
  const setProgressValue = useCallback((newProgress: number) => {
    const totalDuration = path.totalDuration || 10;
    const newTime = newProgress * totalDuration;
    
    setProgress(newProgress);
    setCurrentTime(newTime);
    
    updateAvatarPosition(newTime);
    
    if (rendererRef.current && cameraRef.current && sceneRef.current) {
      rendererRef.current.render(sceneRef.current, cameraRef.current);
    }
  }, [path.totalDuration, updateAvatarPosition]);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    startPreview,
    pausePreview,
    stopPreview,
    setProgress: setProgressValue
  }));

  // 初始化
  useEffect(() => {
    const cleanup = initializeScene();
    return cleanup;
  }, [initializeScene]);

  // 更新路径显示
  useEffect(() => {
    updatePathDisplay();
  }, [updatePathDisplay]);

  // 清理动画
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  return (
    <div className="path-preview">
      {/* 控制面板 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          {/* 播放控制 */}
          <Space>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={startPreview}
              disabled={isPlaying || path.points.length < 2}
            >
              {t('pathPreview.play')}
            </Button>
            <Button
              icon={<PauseCircleOutlined />}
              onClick={pausePreview}
              disabled={!isPlaying}
            >
              {isPaused ? t('pathPreview.resume') : t('pathPreview.pause')}
            </Button>
            <Button
              icon={<StopOutlined />}
              onClick={stopPreview}
              disabled={!isPlaying}
            >
              {t('pathPreview.stop')}
            </Button>
          </Space>

          {/* 进度条 */}
          <div>
            <div style={{ marginBottom: 8 }}>
              {t('pathPreview.progress')}: {(progress * 100).toFixed(1)}%
            </div>
            <Slider
              value={progress * 100}
              onChange={(value) => setProgressValue(value / 100)}
              disabled={isPlaying && !isPaused}
            />
          </div>

          {/* 设置 */}
          <Space>
            <span>{t('pathPreview.speed')}:</span>
            <Select
              value={playbackSpeed}
              onChange={setPlaybackSpeed}
              style={{ width: 80 }}
            >
              <Option value={0.25}>0.25x</Option>
              <Option value={0.5}>0.5x</Option>
              <Option value={1}>1x</Option>
              <Option value={2}>2x</Option>
              <Option value={4}>4x</Option>
            </Select>

            <span>{t('pathPreview.camera')}:</span>
            <Select
              value={cameraMode}
              onChange={setCameraMode}
              style={{ width: 100 }}
            >
              <Option value="follow">{t('pathPreview.cameraFollow')}</Option>
              <Option value="fixed">{t('pathPreview.cameraFixed')}</Option>
              <Option value="overview">{t('pathPreview.cameraOverview')}</Option>
            </Select>

            <Switch
              checkedChildren={t('pathPreview.trail')}
              unCheckedChildren={t('pathPreview.trail')}
              checked={showTrail}
              onChange={setShowTrail}
            />
          </Space>
        </Space>
      </Card>

      {/* 预览画布 */}
      <div
        ref={containerRef}
        style={{
          width: '100%',
          height: '400px',
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          overflow: 'hidden'
        }}
      >
        <canvas ref={canvasRef} style={{ display: 'block' }} />
      </div>
    </div>
  );
});
