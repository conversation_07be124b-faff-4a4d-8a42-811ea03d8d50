# UI元素编辑器

UI元素编辑器是一个功能完整的用户界面元素编辑工具，支持创建、编辑和预览各种UI组件。

## 功能特性

### 🎨 完整的UI元素支持
- **按钮 (Button)**: 可交互的按钮组件
- **文本 (Text)**: 静态文本显示组件
- **图像 (Image)**: 图片显示组件
- **输入框 (Input)**: 文本输入组件
- **面板 (Panel)**: 容器面板组件
- **窗口 (Window)**: 窗口容器组件
- **滑块 (Slider)**: 数值选择滑块
- **复选框 (Checkbox)**: 布尔值选择组件
- **下拉框 (Dropdown)**: 选项选择组件

### 🛠️ 丰富的编辑功能
- **基本属性**: 类型、名称、可见性、交互性等
- **变换属性**: 位置、尺寸、透明度、层级
- **样式属性**: 颜色、边框、字体、对齐方式
- **布局属性**: 内边距、外边距
- **事件属性**: 点击、悬停、值变化事件
- **3D支持**: 支持3D模式和广告牌模式

### 🎯 预设系统
- **内置预设**: 提供多种常用UI元素预设
- **分类管理**: 按基础、现代、经典、游戏等风格分类
- **搜索功能**: 快速查找所需预设
- **自定义预设**: 支持保存和导入自定义预设

### 👀 实时预览
- **即时预览**: 编辑时实时显示效果
- **引擎集成**: 与底层dl-engine无缝集成
- **交互测试**: 支持预览中的交互测试

## 组件结构

```
ui/
├── UIElementEditor.tsx          # 主编辑器组件
├── UIElementEditor.module.css   # 编辑器样式
├── UIPresetManager.ts           # 预设管理器
├── UIPresetSelector.tsx         # 预设选择器
├── UIElementEditorTest.tsx      # 测试页面
└── README.md                    # 说明文档
```

## 使用方法

### 基本使用

```tsx
import UIElementEditor, { UIElementData, UIElementType } from './UIElementEditor';

const MyComponent = () => {
  const [uiData, setUIData] = useState<UIElementData>({
    type: UIElementType.BUTTON,
    name: '我的按钮',
    // ... 其他属性
  });

  const handleChange = (data: UIElementData) => {
    setUIData(data);
    // 处理数据变化
  };

  return (
    <UIElementEditor
      data={uiData}
      onChange={handleChange}
      showPreview={true}
      previewContainerId="my-preview"
    />
  );
};
```

### 预设管理

```tsx
import uiPresetManager from './UIPresetManager';

// 获取所有预设
const presets = uiPresetManager.getAllPresets();

// 按类型获取预设
const buttonPresets = uiPresetManager.getPresetsByType(UIElementType.BUTTON);

// 搜索预设
const searchResults = uiPresetManager.searchPresets('modern');

// 添加自定义预设
uiPresetManager.addPreset({
  id: 'my-custom-button',
  name: '我的自定义按钮',
  description: '自定义按钮样式',
  category: UIPresetCategory.CUSTOM,
  type: UIElementType.BUTTON,
  data: {
    backgroundColor: '#ff6b6b',
    borderRadius: 20,
    // ... 其他属性
  }
});
```

### 测试页面

运行测试页面来体验完整功能：

```tsx
import UIElementEditorTest from './UIElementEditorTest';

const App = () => {
  return <UIElementEditorTest />;
};
```

## 属性接口

### UIElementData

```typescript
interface UIElementData {
  // 基本属性
  id?: string;
  type: UIElementType;
  name?: string;
  visible?: boolean;
  interactive?: boolean;
  enabled?: boolean;
  
  // 位置和尺寸
  position?: { x: number; y: number; z?: number };
  size?: { width: number; height: number };
  
  // 样式属性
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
  borderRadius?: number;
  opacity?: number;
  zIndex?: number;
  
  // 文本属性
  textContent?: string;
  fontSize?: number;
  fontFamily?: string;
  fontColor?: string;
  textAlign?: 'left' | 'center' | 'right';
  
  // 布局属性
  padding?: { top: number; right: number; bottom: number; left: number };
  margin?: { top: number; right: number; bottom: number; left: number };
  
  // 3D属性
  is3D?: boolean;
  billboardMode?: string;
  
  // 事件属性
  onClick?: string;
  onHover?: string;
  onChange?: string;
  
  // 其他属性
  userData?: any;
}
```

### UIElementEditorProps

```typescript
interface UIElementEditorProps {
  /** 组件数据 */
  data?: UIElementData;
  /** 数据更新回调 */
  onChange?: (data: UIElementData) => void;
  /** 是否显示预览 */
  showPreview?: boolean;
  /** 预览容器ID */
  previewContainerId?: string;
}
```

## 样式定制

编辑器支持通过CSS模块进行样式定制：

```css
/* 自定义编辑器面板样式 */
.uiEditorPanel {
  background: #f8f9fa;
  border-radius: 8px;
}

/* 自定义预览容器样式 */
.previewContainer {
  border: 2px dashed #1890ff;
  background: linear-gradient(45deg, #f0f2f5, #ffffff);
}

/* 深色主题支持 */
[data-theme='dark'] .uiEditorPanel {
  background: #1f1f1f;
  color: #ffffff;
}
```

## 与引擎集成

UI元素编辑器与底层dl-engine深度集成：

```typescript
// 通过EngineService创建UI元素
const uiElement = engineService.createUI(elementData.type, elementData);

// 更新UI元素
engineService.updateUI(elementData.id, elementData);

// 移除UI元素
engineService.removeUI(elementData.id);
```

## 扩展开发

### 添加新的UI元素类型

1. 在`UIElementType`枚举中添加新类型
2. 在编辑器中添加对应的属性面板
3. 在预设管理器中添加默认预设
4. 在底层引擎中实现对应的UI组件

### 添加新的预设类别

1. 在`UIPresetCategory`枚举中添加新类别
2. 在预设管理器中添加对应的预设
3. 在预设选择器中添加类别标签和颜色

## 最佳实践

1. **性能优化**: 使用防抖处理频繁的属性更新
2. **数据验证**: 在应用预设前验证数据完整性
3. **错误处理**: 提供友好的错误提示和恢复机制
4. **用户体验**: 保持编辑器响应速度和操作流畅性
5. **可访问性**: 支持键盘导航和屏幕阅读器

## 故障排除

### 常见问题

1. **预览不显示**: 检查预览容器ID是否正确
2. **预设加载失败**: 确认预设管理器已正确初始化
3. **样式不生效**: 检查CSS模块是否正确导入
4. **引擎集成问题**: 确认EngineService连接状态

### 调试技巧

1. 开启调试模式查看详细日志
2. 使用浏览器开发者工具检查DOM结构
3. 检查控制台错误信息
4. 验证数据格式和类型

## 更新日志

### v1.0.0
- 初始版本发布
- 支持9种基本UI元素类型
- 完整的属性编辑功能
- 预设系统和选择器
- 实时预览功能
- 与dl-engine集成

## 贡献指南

欢迎提交问题报告和功能请求。在提交代码前，请确保：

1. 代码符合项目规范
2. 添加必要的测试用例
3. 更新相关文档
4. 通过所有现有测试

## 许可证

本项目采用MIT许可证。详见LICENSE文件。
