/**
 * AI设计助手面板样式
 */

.ai-design-assistant-panel {
  height: 100%;
  display: flex;
  flex-direction: column;

  .ant-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    .ant-card-body {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 12px;
    }
  }

  .ant-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;

    .ant-tabs-content-holder {
      flex: 1;
      overflow: hidden;

      .ant-tabs-content {
        height: 100%;

        .ant-tabs-tabpane {
          height: 100%;
          overflow-y: auto;
        }
      }
    }
  }

  // 建议列表样式
  .suggestions-list {
    .ant-collapse {
      .ant-collapse-item {
        margin-bottom: 8px;

        .ant-collapse-header {
          padding: 8px 12px;
          font-size: 13px;
          font-weight: 500;
        }

        .ant-collapse-content {
          .ant-collapse-content-box {
            padding: 8px 0;
          }
        }
      }
    }

    .suggestion-item {
      padding: 12px;
      border-radius: 6px;
      background: #fafafa;
      margin-bottom: 8px;
      transition: all 0.2s ease;

      &:hover {
        background: #f0f0f0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .ant-list-item-meta {
        .ant-list-item-meta-avatar {
          .ant-badge {
            .ant-avatar {
              background: #fff;
              color: #666;
              border: 1px solid #d9d9d9;
            }
          }
        }

        .ant-list-item-meta-content {
          .ant-list-item-meta-title {
            margin-bottom: 4px;
            font-size: 14px;
            font-weight: 500;

            .ant-rate {
              margin-left: 8px;
            }
          }

          .ant-list-item-meta-description {
            p {
              margin-bottom: 8px;
              color: #666;
              font-size: 13px;
              line-height: 1.4;
            }

            .ant-space {
              .ant-tag {
                font-size: 11px;
                padding: 2px 6px;
                border-radius: 3px;
              }

              .ant-typography {
                font-size: 12px;
              }
            }
          }
        }
      }

      .ant-list-item-action {
        margin-left: 12px;

        .ant-btn {
          margin-left: 4px;
          border-radius: 4px;

          &.ant-btn-primary {
            font-size: 12px;
            height: 28px;
            padding: 0 12px;
          }

          &.ant-btn-text {
            color: #666;

            &:hover {
              color: #1890ff;
              background: rgba(24, 144, 255, 0.1);
            }
          }
        }
      }
    }
  }

  // 加载状态样式
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #666;

    p {
      margin-top: 16px;
      font-size: 14px;
    }
  }

  // 设计评分卡片样式
  .design-score-card {
    .score-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      .ant-typography {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }

      .score-value {
        .ant-progress {
          .ant-progress-text {
            font-size: 14px;
            font-weight: 600;
          }
        }
      }
    }

    .score-details {
      .score-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .ant-typography {
          font-size: 13px;
          color: #666;
        }

        .ant-badge {
          .ant-badge-count {
            font-size: 11px;
            min-width: 18px;
            height: 18px;
            line-height: 18px;
          }
        }
      }
    }
  }

  // 空状态样式
  .ant-empty {
    .ant-empty-image {
      margin-bottom: 12px;
    }

    .ant-empty-description {
      font-size: 13px;
      color: #999;
    }
  }
}

// 深色主题支持
[data-theme='dark'] {
  .ai-design-assistant-panel {
    .suggestions-list {
      .suggestion-item {
        background: #1f1f1f;
        border: 1px solid #303030;

        &:hover {
          background: #262626;
          border-color: #434343;
        }
      }
    }

    .design-score-card {
      background: #1f1f1f;
      border-color: #303030;

      .score-details {
        .score-item {
          border-bottom-color: #303030;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .ai-design-assistant-panel {
    .suggestions-list {
      .suggestion-item {
        .ant-list-item-action {
          margin-left: 8px;

          .ant-btn {
            margin-left: 2px;
            padding: 0 8px;
            font-size: 11px;
          }
        }
      }
    }

    .design-score-card {
      .score-header {
        flex-direction: column;
        align-items: flex-start;

        .score-value {
          margin-top: 12px;
          align-self: center;
        }
      }
    }
  }
}
