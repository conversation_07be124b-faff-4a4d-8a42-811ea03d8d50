/**
 * 动画混合编辑器组件
 * 用于编辑动画混合空间
 */
import React, { useState, useEffect } from 'react';
import { Tabs, Card, Button, Space, message, Spin } from 'antd';
import { useTranslation } from 'react-i18next';
import BlendSpace1DEditor from './BlendSpace1DEditor';
import BlendSpace2DEditor from './BlendSpace2DEditor';
import { useSelector } from 'react-redux';
import { RootState, useAppDispatch } from '../../store';
import { loadBlendSpaces, clearBlendSpaces } from '../../store/animations/blendSpaceSlice';
import { blendSpaceService } from '../../services/blendSpaceService';
import './AnimationEditor.less';

const { TabPane } = Tabs;

/**
 * 动画混合编辑器属性
 */
interface BlendEditorProps {
  /** 实体ID */
  entityId: string;
  /** 保存回调 */
  onSave?: () => void;
  /** 取消回调 */
  onCancel?: () => void;
}

/**
 * 动画混合编辑器组件
 */
const BlendEditor: React.FC<BlendEditorProps> = ({ entityId, onSave, onCancel }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  
  // 从Redux获取状态
  const { blendSpaces, loading, error } = useSelector((state: RootState) => state.blendSpace);
  
  // 本地状态
  const [activeTab, setActiveTab] = useState('1d');
  const [selectedBlendSpace, setSelectedBlendSpace] = useState<string | null>(null);
  
  // 加载混合空间
  useEffect(() => {
    if (entityId) {
      dispatch(loadBlendSpaces(entityId));
    }
    
    return () => {
      dispatch(clearBlendSpaces());
    };
  }, [dispatch, entityId]);
  
  // 处理保存
  const handleSave = () => {
    if (onSave) {
      onSave();
    }
  };
  
  // 处理取消
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };
  
  // 处理创建1D混合空间
  const handleCreate1DBlendSpace = async () => {
    try {
      const name = `BlendSpace1D_${Date.now()}`;
      await blendSpaceService.createBlendSpace1D(entityId, name);
      message.success(t('editor.animation.createBlendSpaceSuccess'));
      dispatch(loadBlendSpaces(entityId));
    } catch (error) {
      message.error(t('editor.animation.createBlendSpaceFailed'));
    }
  };
  
  // 处理创建2D混合空间
  const handleCreate2DBlendSpace = async () => {
    try {
      const name = `BlendSpace2D_${Date.now()}`;
      await blendSpaceService.createBlendSpace2D(entityId, name);
      message.success(t('editor.animation.createBlendSpaceSuccess'));
      dispatch(loadBlendSpaces(entityId));
    } catch (error) {
      message.error(t('editor.animation.createBlendSpaceFailed'));
    }
  };
  
  // 渲染1D混合空间列表
  const render1DBlendSpaces = () => {
    const blendSpace1DList = blendSpaces.filter(bs => bs.type === '1D');
    
    if (blendSpace1DList.length === 0) {
      return (
        <div className="empty-message">
          <p>{t('editor.animation.noBlendSpaces')}</p>
          <Button type="primary" onClick={handleCreate1DBlendSpace}>
            {t('editor.animation.createBlendSpace')}
          </Button>
        </div>
      );
    }
    
    return (
      <div className="blend-space-list">
        <div className="list-header">
          <Button type="primary" onClick={handleCreate1DBlendSpace}>
            {t('editor.animation.createBlendSpace')}
          </Button>
        </div>
        
        <div className="list-content">
          {blendSpace1DList.map(bs => (
            <Card
              key={bs.id}
              title={bs.name}
              className={`blend-space-card ${selectedBlendSpace === bs.id ? 'selected' : ''}`}
              onClick={() => setSelectedBlendSpace(bs.id)}
            >
              <p>{bs.description || t('editor.animation.noDescription')}</p>
              <p>{t('editor.animation.nodes')}: {bs.nodeCount}</p>
            </Card>
          ))}
        </div>
        
        {selectedBlendSpace && (
          <BlendSpace1DEditor
            entityId={entityId}
            blendSpaceId={selectedBlendSpace}
            onClose={() => setSelectedBlendSpace(null)}
          />
        )}
      </div>
    );
  };
  
  // 渲染2D混合空间列表
  const render2DBlendSpaces = () => {
    const blendSpace2DList = blendSpaces.filter(bs => bs.type === '2D');
    
    if (blendSpace2DList.length === 0) {
      return (
        <div className="empty-message">
          <p>{t('editor.animation.noBlendSpaces')}</p>
          <Button type="primary" onClick={handleCreate2DBlendSpace}>
            {t('editor.animation.createBlendSpace')}
          </Button>
        </div>
      );
    }
    
    return (
      <div className="blend-space-list">
        <div className="list-header">
          <Button type="primary" onClick={handleCreate2DBlendSpace}>
            {t('editor.animation.createBlendSpace')}
          </Button>
        </div>
        
        <div className="list-content">
          {blendSpace2DList.map(bs => (
            <Card
              key={bs.id}
              title={bs.name}
              className={`blend-space-card ${selectedBlendSpace === bs.id ? 'selected' : ''}`}
              onClick={() => setSelectedBlendSpace(bs.id)}
            >
              <p>{bs.description || t('editor.animation.noDescription')}</p>
              <p>{t('editor.animation.nodes')}: {bs.nodeCount}</p>
            </Card>
          ))}
        </div>
        
        {selectedBlendSpace && (
          <BlendSpace2DEditor
            entityId={entityId}
            blendSpaceId={selectedBlendSpace}
            onClose={() => setSelectedBlendSpace(null)}
          />
        )}
      </div>
    );
  };
  
  return (
    <div className="blend-editor">
      <div className="blend-editor-header">
        <h2>{t('editor.animation.blendEditor')}</h2>
        
        <Space>
          <Button onClick={handleCancel}>{t('editor.cancel')}</Button>
          <Button type="primary" onClick={handleSave}>{t('editor.save')}</Button>
        </Space>
      </div>
      
      <Spin spinning={loading}>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={t('editor.animation.blendSpace1D')} key="1d">
            {render1DBlendSpaces()}
          </TabPane>
          
          <TabPane tab={t('editor.animation.blendSpace2D')} key="2d">
            {render2DBlendSpaces()}
          </TabPane>
        </Tabs>
      </Spin>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
    </div>
  );
};

export default BlendEditor;
