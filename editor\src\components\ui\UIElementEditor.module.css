/**
 * UI元素编辑器样式
 */

.uiElementEditor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.uiEditorPanel {
  padding: 16px;
}

.uiEditorPanel h4 {
  margin: 16px 0 8px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.uiEditorPanel h4:first-child {
  margin-top: 0;
}

.previewSection {
  margin-top: 16px;
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.previewContainer {
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  padding: 16px;
  min-height: 100px;
  background-color: #fafafa;
  position: relative;
  overflow: hidden;
}

.previewContainer:hover {
  border-color: #40a9ff;
  background-color: #f6ffed;
}

.previewLoading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #999;
  display: flex;
  align-items: center;
  gap: 8px;
}

.previewPlaceholder {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #999;
  text-align: center;
}

.colorPickerWrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.colorPickerWrapper .ant-color-picker {
  flex: 1;
}

.formSection {
  margin-bottom: 24px;
}

.formSection:last-child {
  margin-bottom: 0;
}

.compactForm .ant-form-item {
  margin-bottom: 12px;
}

.compactForm .ant-form-item-label {
  padding-bottom: 4px;
}

.tabContent {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 8px;
}

.tabContent::-webkit-scrollbar {
  width: 6px;
}

.tabContent::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.tabContent::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.tabContent::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.propertyGroup {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 16px;
}

.propertyGroup .propertyGroupTitle {
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
  font-size: 13px;
}

.inlineControls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.inlineControls .ant-form-item {
  margin-bottom: 0;
  flex: 1;
}

.paddingMarginGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  margin-top: 8px;
}

.paddingMarginGrid .ant-form-item {
  margin-bottom: 0;
}

.eventEditor {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 4px;
  padding: 8px;
}

.eventEditor .ant-input {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.previewToolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 8px;
  background: #f0f0f0;
  border-radius: 4px;
}

.previewToolbar .previewTitle {
  font-weight: 600;
  color: #333;
}

.previewToolbar .previewControls {
  display: flex;
  gap: 4px;
}

.typeSelector {
  margin-bottom: 16px;
}

.typeSelector .ant-select {
  width: 100%;
}

.quickActions {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.quickActions .ant-btn {
  flex: 1;
}

.advancedOptions {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
  margin-top: 16px;
}

.advancedToggle {
  color: #1890ff;
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 12px;
}

.advancedToggle:hover {
  color: #40a9ff;
}

.errorMessage {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

.successMessage {
  color: #52c41a;
  font-size: 12px;
  margin-top: 4px;
}

.warningMessage {
  color: #faad14;
  font-size: 12px;
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .uiEditorPanel {
    padding: 12px;
  }
  
  .paddingMarginGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
  }
  
  .quickActions {
    flex-direction: column;
  }
  
  .quickActions .ant-btn {
    flex: none;
  }
}

/* 深色主题支持 */
[data-theme='dark'] .uiEditorPanel {
  background: #1f1f1f;
  color: #fff;
}

[data-theme='dark'] .uiEditorPanel h4 {
  color: #fff;
}

[data-theme='dark'] .previewContainer {
  background-color: #2a2a2a;
  border-color: #404040;
}

[data-theme='dark'] .previewContainer:hover {
  border-color: #1890ff;
  background-color: #1a3a1a;
}

[data-theme='dark'] .propertyGroup {
  background: #2a2a2a;
  border-color: #404040;
}

[data-theme='dark'] .propertyGroup .propertyGroupTitle {
  color: #fff;
}

[data-theme='dark'] .eventEditor {
  background: #2a2a2a;
  border-color: #404040;
}

[data-theme='dark'] .previewToolbar {
  background: #2a2a2a;
  border-color: #404040;
}

[data-theme='dark'] .previewToolbar .previewTitle {
  color: #fff;
}
