/**
 * 骨骼动画面板组件
 * 提供骨骼系统和动作合成的用户界面
 */
import React, { useState, useCallback, useRef } from 'react';
import {
  Button,
  Card,
  Typography,
  Tabs,
  Progress,
  Alert,
  Tag,
  Slider,
  Select,
  List,
  Modal,
  Space
} from 'antd';
import {
  UploadOutlined,
  PlayCircleOutlined,
  DeleteOutlined,
  SettingOutlined
} from '@ant-design/icons';

interface SkeletonData {
  boneCount: number;
  boneNames: string[];
  qualityScore: number;
}

interface ActionFile {
  id: string;
  name: string;
  file: File;
  format: string;
  duration: number;
  status: 'pending' | 'processing' | 'completed' | 'error';
}

interface CompositionConfig {
  defaultDuration: number;
  smoothingFactor: number;
  minQualityThreshold: number;
  blendMode: 'linear' | 'spherical';
}

interface SkeletonAnimationPanelProps {
  onSkeletonGenerated?: (skeletonData: SkeletonData) => void;
  onActionsComposed?: (composedActions: any) => void;
  onAnimationSynchronized?: (synchronizedAnimations: any) => void;
}

export const SkeletonAnimationPanel: React.FC<SkeletonAnimationPanelProps> = ({
  onSkeletonGenerated,
  onActionsComposed,
  onAnimationSynchronized
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [skeletonData, setSkeletonData] = useState<SkeletonData | null>(null);
  const [actionFiles, setActionFiles] = useState<ActionFile[]>([]);
  const [compositionConfig, setCompositionConfig] = useState<CompositionConfig>({
    defaultDuration: 0.3,
    smoothingFactor: 0.5,
    minQualityThreshold: 0.7,
    blendMode: 'linear'
  });
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [showConfigDialog, setShowConfigDialog] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const actionFilesInputRef = useRef<HTMLInputElement>(null);

  const handleTabChange = (key: string) => {
    setActiveTab(parseInt(key));
  };

  const handleAvatarUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setAvatarFile(file);
      setError(null);
    }
  }, []);

  const handleActionFilesUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const newActionFiles: ActionFile[] = files.map((file, index) => ({
      id: `action_${Date.now()}_${index}`,
      name: file.name,
      file,
      format: file.name.split('.').pop()?.toLowerCase() || 'unknown',
      duration: 0, // 将在解析时确定
      status: 'pending'
    }));
    
    setActionFiles(prev => [...prev, ...newActionFiles]);
    setError(null);
  }, []);

  const handleGenerateSkeleton = useCallback(async () => {
    if (!avatarFile) {
      setError('请先上传虚拟化身文件');
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setError(null);

    try {
      // 模拟骨骼生成过程
      for (let i = 0; i <= 100; i += 10) {
        setProgress(i);
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // 模拟生成的骨骼数据
      const mockSkeletonData: SkeletonData = {
        boneCount: 21,
        boneNames: [
          'Hips', 'Spine', 'Spine1', 'Spine2', 'Neck', 'Head',
          'LeftShoulder', 'LeftArm', 'LeftForeArm', 'LeftHand',
          'RightShoulder', 'RightArm', 'RightForeArm', 'RightHand',
          'LeftUpLeg', 'LeftLeg', 'LeftFoot',
          'RightUpLeg', 'RightLeg', 'RightFoot'
        ],
        qualityScore: 0.92
      };

      setSkeletonData(mockSkeletonData);
      onSkeletonGenerated?.(mockSkeletonData);
    } catch (err) {
      setError('骨骼生成失败: ' + (err as Error).message);
    } finally {
      setIsProcessing(false);
      setProgress(0);
    }
  }, [avatarFile, onSkeletonGenerated]);

  const handleComposeActions = useCallback(async () => {
    if (actionFiles.length === 0) {
      setError('请先上传动作文件');
      return;
    }

    if (!skeletonData) {
      setError('请先生成骨骼结构');
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setError(null);

    try {
      // 模拟动作合成过程
      for (let i = 0; i <= 100; i += 5) {
        setProgress(i);
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // 更新动作文件状态
      setActionFiles(prev => prev.map(action => ({
        ...action,
        status: 'completed',
        duration: Math.random() * 5 + 1 // 随机持续时间
      })));

      const mockComposedActions = {
        totalActions: actionFiles.length,
        totalDuration: actionFiles.length * 3,
        qualityScore: 0.88
      };

      onActionsComposed?.(mockComposedActions);
    } catch (err) {
      setError('动作合成失败: ' + (err as Error).message);
    } finally {
      setIsProcessing(false);
      setProgress(0);
    }
  }, [actionFiles, skeletonData, onActionsComposed]);

  const handleSynchronizeAnimations = useCallback(async () => {
    if (!skeletonData || actionFiles.length === 0) {
      setError('请先生成骨骼结构和上传动作文件');
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setError(null);

    try {
      // 模拟面部动画同步过程
      for (let i = 0; i <= 100; i += 8) {
        setProgress(i);
        await new Promise(resolve => setTimeout(resolve, 150));
      }

      const mockSynchronizedAnimations = {
        synchronizationAccuracy: 0.95,
        emotionalCoherence: 0.87,
        transitionSmoothness: 0.91
      };

      onAnimationSynchronized?.(mockSynchronizedAnimations);
    } catch (err) {
      setError('动画同步失败: ' + (err as Error).message);
    } finally {
      setIsProcessing(false);
      setProgress(0);
    }
  }, [skeletonData, actionFiles, onAnimationSynchronized]);

  const handleRemoveActionFile = useCallback((id: string) => {
    setActionFiles(prev => prev.filter(action => action.id !== id));
  }, []);

  const handleConfigChange = useCallback((key: keyof CompositionConfig, value: any) => {
    setCompositionConfig(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const renderSkeletonTab = () => (
    <div style={{ padding: 16 }}>
      <Card>
        <div style={{ padding: 16 }}>
          <Typography.Title level={4}>
            自动骨骼绑定
          </Typography.Title>

          <div style={{ marginBottom: 16 }}>
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleAvatarUpload}
              accept=".fbx,.gltf,.glb,.obj"
              style={{ display: 'none' }}
            />
            <Space>
              <Button
                icon={<UploadOutlined />}
                onClick={() => fileInputRef.current?.click()}
              >
                上传虚拟化身
              </Button>
              {avatarFile && (
                <Tag color="blue">
                  {avatarFile.name}
                </Tag>
              )}
            </Space>
          </div>

          <Button
            type="primary"
            onClick={handleGenerateSkeleton}
            disabled={!avatarFile || isProcessing}
            icon={<SettingOutlined />}
          >
            生成骨骼结构
          </Button>

          {isProcessing && (
            <div style={{ marginTop: 16 }}>
              <Progress percent={progress} />
              <Typography.Text style={{ marginTop: 8, display: 'block' }}>
                正在生成骨骼结构... {progress}%
              </Typography.Text>
            </div>
          )}

          {skeletonData && (
            <div style={{ marginTop: 16 }}>
              <Alert
                message="骨骼结构生成成功！"
                type="success"
                showIcon
              />
              <div style={{ marginTop: 8 }}>
                <Typography.Text>
                  骨骼数量: {skeletonData.boneCount}
                </Typography.Text>
                <br />
                <Typography.Text>
                  质量评分: {(skeletonData.qualityScore * 100).toFixed(1)}%
                </Typography.Text>
              </div>
            </div>
          )}
        </div>
      </Card>
    </div>
  );

  const renderActionsTab = () => (
    <div style={{ padding: 16 }}>
      <Card>
        <div style={{ padding: 16 }}>
          <Typography.Title level={4}>
            多动作合成
          </Typography.Title>

          <div style={{ marginBottom: 16 }}>
            <input
              type="file"
              ref={actionFilesInputRef}
              onChange={handleActionFilesUpload}
              accept=".fbx,.gltf,.glb,.bvh"
              multiple
              style={{ display: 'none' }}
            />
            <Space>
              <Button
                icon={<UploadOutlined />}
                onClick={() => actionFilesInputRef.current?.click()}
              >
                上传动作文件
              </Button>
              <Button
                icon={<SettingOutlined />}
                onClick={() => setShowConfigDialog(true)}
              >
                配置参数
              </Button>
            </Space>
          </div>

          {actionFiles.length > 0 && (
            <List
              dataSource={actionFiles}
              renderItem={(action) => (
                <List.Item
                  key={action.id}
                  actions={[
                    <Button
                      key="delete"
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => handleRemoveActionFile(action.id)}
                    />
                  ]}
                >
                  <List.Item.Meta
                    title={action.name}
                    description={`格式: ${action.format.toUpperCase()} | 状态: ${action.status}`}
                  />
                </List.Item>
              )}
            />
          )}

          <Button
            type="primary"
            onClick={handleComposeActions}
            disabled={actionFiles.length === 0 || !skeletonData || isProcessing}
            icon={<PlayCircleOutlined />}
            style={{ marginTop: 16 }}
          >
            开始合成
          </Button>

          {isProcessing && (
            <div style={{ marginTop: 16 }}>
              <Progress percent={progress} />
              <Typography.Text style={{ marginTop: 8, display: 'block' }}>
                正在合成动作... {progress}%
              </Typography.Text>
            </div>
          )}
        </div>
      </Card>
    </div>
  );

  const renderSyncTab = () => (
    <div style={{ padding: 16 }}>
      <Card>
        <div style={{ padding: 16 }}>
          <Typography.Title level={4}>
            面部动画同步
          </Typography.Title>

          <Typography.Text type="secondary" style={{ marginBottom: 16, display: 'block' }}>
            将面部表情与身体动作进行智能同步，创建更自然的动画效果。
          </Typography.Text>

          <Button
            type="primary"
            onClick={handleSynchronizeAnimations}
            disabled={!skeletonData || actionFiles.length === 0 || isProcessing}
            icon={<PlayCircleOutlined />}
          >
            开始同步
          </Button>

          {isProcessing && (
            <div style={{ marginTop: 16 }}>
              <Progress percent={progress} />
              <Typography.Text style={{ marginTop: 8, display: 'block' }}>
                正在同步面部动画... {progress}%
              </Typography.Text>
            </div>
          )}
        </div>
      </Card>
    </div>
  );

  const renderConfigDialog = () => (
    <Modal
      title="动作合成配置"
      open={showConfigDialog}
      onCancel={() => setShowConfigDialog(false)}
      onOk={() => setShowConfigDialog(false)}
      width={600}
    >
      <div style={{ padding: '16px 0' }}>
        <div style={{ marginBottom: 24 }}>
          <Typography.Text>默认过渡时间 (秒)</Typography.Text>
          <Slider
            value={compositionConfig.defaultDuration}
            onChange={(value) => handleConfigChange('defaultDuration', value)}
            min={0.1}
            max={2.0}
            step={0.1}
            tooltip={{ formatter: (value) => `${value}s` }}
          />
        </div>

        <div style={{ marginBottom: 24 }}>
          <Typography.Text>平滑因子</Typography.Text>
          <Slider
            value={compositionConfig.smoothingFactor}
            onChange={(value) => handleConfigChange('smoothingFactor', value)}
            min={0.0}
            max={1.0}
            step={0.1}
            tooltip={{ formatter: (value) => `${value}` }}
          />
        </div>

        <div style={{ marginBottom: 24 }}>
          <Typography.Text>最小质量阈值</Typography.Text>
          <Slider
            value={compositionConfig.minQualityThreshold}
            onChange={(value) => handleConfigChange('minQualityThreshold', value)}
            min={0.0}
            max={1.0}
            step={0.1}
            tooltip={{ formatter: (value) => `${value}` }}
          />
        </div>

        <div>
          <Typography.Text>混合模式</Typography.Text>
          <Select
            value={compositionConfig.blendMode}
            onChange={(value) => handleConfigChange('blendMode', value)}
            style={{ width: '100%', marginTop: 8 }}
            options={[
              { value: 'linear', label: '线性' },
              { value: 'spherical', label: '球面' }
            ]}
          />
        </div>
      </div>
    </Modal>
  );

  return (
    <div style={{ width: '100%' }}>
      <Typography.Title level={3}>
        骨骼动画系统
      </Typography.Title>

      {error && (
        <Alert
          message={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <Tabs
        activeKey={activeTab.toString()}
        onChange={handleTabChange}
        items={[
          {
            key: '0',
            label: '骨骼绑定',
            children: renderSkeletonTab()
          },
          {
            key: '1',
            label: '动作合成',
            children: renderActionsTab()
          },
          {
            key: '2',
            label: '面部同步',
            children: renderSyncTab()
          }
        ]}
      />

      {renderConfigDialog()}
    </div>
  );
};
