/**
 * 交互预览组件样式
 */
.interaction-preview {
  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
  }

  .ant-card-body {
    padding: 16px;
  }

  // 预览容器
  .preview-container {
    display: flex;
    gap: 16px;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }

  // 预览视口
  .preview-viewport {
    flex: 1;
    position: relative;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    overflow: hidden;
    background: #fafafa;

    .preview-canvas {
      display: block;
      width: 100%;
      height: auto;
      background: #000;
    }

    .preview-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      z-index: 10;

      .ant-spin {
        margin-bottom: 16px;
      }

      .ant-typography {
        color: #666;
      }
    }

    .preview-placeholder {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translate(-50%, -50%) scale(1.1);
      }

      .anticon {
        display: block;
        margin-bottom: 8px;
      }
    }

    .ant-alert {
      margin: 16px;
    }
  }

  // 控制栏
  .preview-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;

    .preview-status {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;

      .ant-space {
        justify-content: center;
      }

      .preview-status {
        justify-content: center;
      }
    }
  }

  // 统计信息
  .preview-stats {
    min-width: 250px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;

    .ant-typography {
      margin-bottom: 12px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 12px;
      margin-bottom: 16px;

      .stat-item {
        text-align: center;
        padding: 8px;
        background: white;
        border-radius: 4px;
        border: 1px solid #f0f0f0;

        .ant-typography:first-child {
          display: block;
          font-size: 18px;
          font-weight: 600;
          color: #1890ff;
          margin-bottom: 4px;
        }

        .ant-typography:last-child {
          font-size: 12px;
          margin: 0;
        }
      }
    }

    .recent-events {
      .ant-typography {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
      }

      .events-list {
        max-height: 150px;
        overflow-y: auto;

        .event-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 4px 8px;
          margin-bottom: 4px;
          background: white;
          border-radius: 4px;
          border: 1px solid #f0f0f0;
          font-size: 12px;

          .ant-tag {
            margin: 0;
            font-size: 10px;
            padding: 1px 6px;
          }
        }
      }
    }

    @media (max-width: 768px) {
      min-width: auto;
      width: 100%;
    }
  }

  // 尺寸变体
  &.interaction-preview-small {
    .preview-viewport {
      min-height: 200px;
    }

    .preview-stats {
      min-width: 200px;

      .stats-grid {
        grid-template-columns: 1fr;
      }
    }
  }

  &.interaction-preview-medium {
    .preview-viewport {
      min-height: 350px;
    }

    .preview-stats {
      min-width: 250px;

      .stats-grid {
        grid-template-columns: 1fr;
      }
    }
  }

  &.interaction-preview-large {
    .preview-viewport {
      min-height: 600px;
    }

    .preview-stats {
      min-width: 300px;

      .stats-grid {
        grid-template-columns: 1fr 1fr;
      }
    }
  }

  // 全屏模式
  &:fullscreen {
    .preview-container {
      height: 100vh;
      flex-direction: row;
    }

    .preview-viewport {
      flex: 1;
    }

    .preview-stats {
      width: 300px;
      min-width: 300px;
    }
  }

  // 动画效果
  .preview-canvas {
    transition: opacity 0.3s ease;
  }

  .preview-loading {
    animation: fadeIn 0.3s ease;
  }

  .preview-placeholder {
    animation: fadeIn 0.3s ease;
  }

  .stat-item {
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .event-item {
    transition: all 0.2s ease;

    &:hover {
      background: #f5f5f5;
      border-color: #d9d9d9;
    }
  }

  // 按钮样式
  .ant-btn {
    &.ant-btn-primary {
      background: #1890ff;
      border-color: #1890ff;

      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  // 标签样式
  .ant-tag {
    border-radius: 4px;
    font-size: 12px;
    padding: 2px 8px;

    &.ant-tag-green {
      background: #f6ffed;
      border-color: #b7eb8f;
      color: #52c41a;
    }

    &.ant-tag-blue {
      background: #e6f7ff;
      border-color: #91d5ff;
      color: #1890ff;
    }
  }

  // 滚动条样式
  .events-list {
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 2px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

// 关键帧动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

// 加载动画
.preview-loading .ant-spin {
  animation: pulse 2s infinite;
}

// 响应式设计
@media (max-width: 576px) {
  .interaction-preview {
    .preview-container {
      gap: 8px;
    }

    .preview-stats {
      padding: 12px;

      .stats-grid {
        gap: 8px;

        .stat-item {
          padding: 6px;
        }
      }

      .events-list {
        max-height: 100px;

        .event-item {
          padding: 2px 6px;
          font-size: 11px;
        }
      }
    }

    .preview-controls {
      gap: 4px;

      .ant-btn {
        font-size: 12px;
        padding: 4px 8px;
        height: auto;
      }
    }
  }
}

// 深色主题支持
@media (prefers-color-scheme: dark) {
  .interaction-preview {
    .preview-viewport {
      background: #1f1f1f;
      border-color: #434343;
    }

    .preview-stats {
      background: #1f1f1f;

      .stat-item,
      .event-item {
        background: #2f2f2f;
        border-color: #434343;
        color: #fff;
      }
    }

    .preview-placeholder {
      .anticon {
        color: #666;
      }

      .ant-typography {
        color: #999;
      }
    }
  }
}
