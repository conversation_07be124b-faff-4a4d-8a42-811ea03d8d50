/**
 * 网络状态可视化组件
 * 用于实时显示网络状态数据和指标
 */
import React, { useState } from 'react';
import { Card, Row, Col, Statistic, Tooltip, Button, Space, Divider, Select, Progress } from 'antd';
import {
  InfoCircleOutlined,
  ReloadOutlined,
  FullscreenOutlined,
  DownloadOutlined,
  DashboardOutlined,
  ApiOutlined,
  WifiOutlined,
  GlobalOutlined
} from '@ant-design/icons';
import { Gauge } from '@ant-design/charts';
import { useTranslation } from 'react-i18next';
// 移除引擎直接导入
import './NetworkStatusVisualizer.css';

const { Option } = Select;

// 网络质量等级枚举
export enum NetworkQualityLevel {
  UNKNOWN = 'unknown',
  VERY_BAD = 'very_bad',
  BAD = 'bad',
  MEDIUM = 'medium',
  GOOD = 'good',
  EXCELLENT = 'excellent',
}

// 网络质量数据接口
export interface NetworkQualityData {
  rtt: number;
  packetLoss: number;
  jitter: number;
  bandwidth: number;
  uploadBandwidth?: number;
  downloadBandwidth?: number;
  stability?: number;
  congestion?: number;
  level: NetworkQualityLevel;
  timestamp: number;
  bandwidthUtilization?: number;
  qualityScore?: number;
  reliability?: number;
  latencyTrend?: number;
  networkType?: string;
  connectionType?: string;
  signalStrength?: number;
  hopCount?: number;
  serverResponseTime?: number;
  connectionEstablishTime?: number;
  dataTransferRate?: number;
  interfaceStatus?: string;
  dnsResolutionTime?: number;
  errorCount?: number;
  warningCount?: number;
}

interface NetworkStatusVisualizerProps {
  /** 当前网络质量数据 */
  currentQuality?: NetworkQualityData;
  /** 历史网络质量数据 */
  qualityHistory?: NetworkQualityData[];
  /** 是否连接 */
  connected?: boolean;
  /** 是否正在加载数据 */
  loading?: boolean;
  /** 刷新数据回调 */
  onRefresh?: () => void;
  /** 导出数据回调 */
  onExportData?: () => void;
}

/**
 * 网络状态可视化组件
 */
const NetworkStatusVisualizer: React.FC<NetworkStatusVisualizerProps> = ({
  currentQuality,
  loading = false,
  onRefresh,
  onExportData}) => {
  const { t } = useTranslation();
  const [visualizationType, setVisualizationType] = useState<'realtime' | 'heatmap' | 'correlation'>('realtime');
  const [timeRange, setTimeRange] = useState<'5min' | '15min' | '30min' | '1hour' | 'all'>('5min');

  // 格式化带宽显示
  const formatBandwidth = (bytes: number): string => {
    if (bytes < 1024) {
      return `${bytes.toFixed(0)} B/s`;
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(1)} KB/s`;
    } else {
      return `${(bytes / (1024 * 1024)).toFixed(2)} MB/s`;
    }
  };



  // 渲染实时网络状态
  const renderRealtimeStatus = () => {
    if (!currentQuality) {
      return (
        <div className="no-data-message">
          <ApiOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
          <p>{t('network.visualizer.noData')}</p>
        </div>
      );
    }

    return (
      <div className="realtime-status">
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Card>
              <div style={{ textAlign: 'center' }}>
                <h3>{t('network.visualizer.qualityScore')}</h3>
                <Gauge
                  percent={(currentQuality.qualityScore || 0) / 100}
                  range={{
                    color: ['#F4664A', '#FAAD14', '#30BF78']}}
                  indicator={{
                    pointer: {
                      style: {
                        stroke: '#D0D0D0'}},
                    pin: {
                      style: {
                        stroke: '#D0D0D0'}}}}
                  statistic={{
                    content: {
                      formatter: () => `${currentQuality.qualityScore || 0}`,
                      style: {
                        fontSize: '24px',
                        lineHeight: '24px'}}}}
                />
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <div style={{ textAlign: 'center' }}>
                <h3>{t('network.visualizer.reliability')}</h3>
                <div style={{ padding: '20px' }}>
                  <Progress
                    type="circle"
                    percent={((currentQuality.reliability || 1) * 100)}
                    strokeColor="#1890ff"
                    format={(percent) => `${percent?.toFixed(0)}%`}
                    size={120}
                  />
                </div>
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <div style={{ textAlign: 'center' }}>
                <h3>{t('network.visualizer.networkType')}</h3>
                <div className="network-type-icon">
                  {currentQuality.networkType === 'wifi' ? (
                    <WifiOutlined style={{ fontSize: 48, color: '#1890ff' }} />
                  ) : currentQuality.networkType === 'ethernet' ? (
                    <ApiOutlined style={{ fontSize: 48, color: '#52c41a' }} />
                  ) : (
                    <GlobalOutlined style={{ fontSize: 48, color: '#faad14' }} />
                  )}
                </div>
                <div className="network-type-text">
                  <p>{currentQuality.networkType || t('network.visualizer.unknown')}</p>
                  {currentQuality.connectionType && (
                    <p className="connection-type">{currentQuality.connectionType}</p>
                  )}
                </div>
              </div>
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('network.visualizer.latency')}
                value={currentQuality.rtt.toFixed(0)}
                suffix="ms"
                valueStyle={{
                  color: currentQuality.rtt > 200 ? '#f5222d' :
                         currentQuality.rtt > 100 ? '#faad14' : '#52c41a'
                }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('network.visualizer.packetLoss')}
                value={(currentQuality.packetLoss * 100).toFixed(1)}
                suffix="%"
                valueStyle={{
                  color: currentQuality.packetLoss > 0.1 ? '#f5222d' :
                         currentQuality.packetLoss > 0.05 ? '#faad14' : '#52c41a'
                }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('network.visualizer.jitter')}
                value={currentQuality.jitter.toFixed(1)}
                suffix="ms"
                valueStyle={{
                  color: currentQuality.jitter > 50 ? '#f5222d' :
                         currentQuality.jitter > 20 ? '#faad14' : '#52c41a'
                }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('network.visualizer.bandwidth')}
                value={formatBandwidth(currentQuality.bandwidth)}
                valueStyle={{
                  color: currentQuality.bandwidth < 100000 ? '#f5222d' :
                         currentQuality.bandwidth < 500000 ? '#faad14' : '#52c41a'
                }}
              />
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  return (
    <div className="network-status-visualizer">
      <div className="visualizer-header">
        <Space>
          <h2>
            <DashboardOutlined /> {t('network.visualizer.title')}
          </h2>
          <Tooltip title={t('network.visualizer.info')}>
            <InfoCircleOutlined />
          </Tooltip>
        </Space>
        
        <Space>
          <Select 
            value={visualizationType} 
            onChange={setVisualizationType}
            style={{ width: 150 }}
          >
            <Option value="realtime">{t('network.visualizer.realtime')}</Option>
            <Option value="heatmap">{t('network.visualizer.heatmap')}</Option>
            <Option value="correlation">{t('network.visualizer.correlation')}</Option>
          </Select>
          
          <Select 
            value={timeRange} 
            onChange={setTimeRange}
            style={{ width: 120 }}
          >
            <Option value="5min">{t('network.visualizer.5min')}</Option>
            <Option value="15min">{t('network.visualizer.15min')}</Option>
            <Option value="30min">{t('network.visualizer.30min')}</Option>
            <Option value="1hour">{t('network.visualizer.1hour')}</Option>
            <Option value="all">{t('network.visualizer.all')}</Option>
          </Select>
          
          <Button 
            icon={<ReloadOutlined />} 
            onClick={onRefresh}
            loading={loading}
          >
            {t('network.visualizer.refresh')}
          </Button>
          
          <Button 
            icon={<DownloadOutlined />} 
            onClick={onExportData}
          >
            {t('network.visualizer.export')}
          </Button>
          
          <Button 
            icon={<FullscreenOutlined />} 
          >
            {t('network.visualizer.fullscreen')}
          </Button>
        </Space>
      </div>
      
      <Divider />
      
      <div className="visualizer-content">
        {visualizationType === 'realtime' && renderRealtimeStatus()}
        {/* 其他可视化类型将在后续实现 */}
      </div>
    </div>
  );
};

export default NetworkStatusVisualizer;
