/**
 * 设置页面
 */
import React from 'react';
import { Card, Tabs, Form, Input, Button, Select, Switch, Radio, Slider, Divider, message } from 'antd';
import {
  UserOutlined,
  SettingOutlined,
  LockOutlined,
  KeyOutlined} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../store';
import { setTheme, setLanguage, ThemeType } from '../store/ui/uiSlice';

const { TabPane } = Tabs;
const { Option } = Select;

export const SettingsPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const dispatch = useAppDispatch();
  
  const { user } = useAppSelector((state) => state.auth);
  const { theme, language } = useAppSelector((state) => state.ui);
  
  const [profileForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [interfaceForm] = Form.useForm();
  
  // 处理个人资料更新
  const handleUpdateProfile = (values: any) => {
    console.log('更新个人资料:', values);
    message.success(t('settings.profileUpdateSuccess'));
  };
  
  // 处理密码更新
  const handleUpdatePassword = (values: any) => {
    console.log('更新密码:', values);
    passwordForm.resetFields();
    message.success(t('settings.passwordUpdateSuccess'));
  };
  
  // 处理界面设置更新
  const handleUpdateInterface = (values: any) => {
    console.log('更新界面设置:', values);
    
    // 更新主题
    if (values.theme !== theme) {
      dispatch(setTheme(values.theme));
    }
    
    // 更新语言
    if (values.language !== language) {
      dispatch(setLanguage(values.language));
      i18n.changeLanguage(values.language);
    }
    
    message.success(t('settings.interfaceUpdateSuccess'));
  };
  
  return (
    <div style={{ padding: 24, maxWidth: 800, margin: '0 auto' }}>
      <Card>
        <Tabs defaultActiveKey="profile">
          <TabPane
            tab={
              <span>
                <UserOutlined />
                {t('settings.profile')}
              </span>
            }
            key="profile"
          >
            <Form
              form={profileForm}
              layout="vertical"
              initialValues={{
                username: user?.username || '',
                email: user?.email || '',
                avatar: user?.avatar || '',
                bio: ''}}
              onFinish={handleUpdateProfile}
            >
              <Form.Item
                name="username"
                label={t('settings.username')}
                rules={[{ required: true, message: t('settings.usernameRequired') as string }]}
              >
                <Input prefix={<UserOutlined />} />
              </Form.Item>

              <Form.Item
                name="email"
                label={t('settings.email')}
                rules={[
                  { required: true, message: t('settings.emailRequired') as string },
                  { type: 'email', message: t('settings.emailInvalid') as string },
                ]}
              >
                <Input disabled />
              </Form.Item>

              <Form.Item name="avatar" label={t('settings.avatar')}>
                <Input placeholder={t('settings.avatarPlaceholder') as string} />
              </Form.Item>

              <Form.Item name="bio" label={t('settings.bio')}>
                <Input.TextArea rows={4} placeholder={t('settings.bioPlaceholder') as string} />
              </Form.Item>
              
              <Form.Item>
                <Button type="primary" htmlType="submit">
                  {t('settings.saveProfile')}
                </Button>
              </Form.Item>
            </Form>
          </TabPane>
          
          <TabPane
            tab={
              <span>
                <LockOutlined />
                {t('settings.security')}
              </span>
            }
            key="security"
          >
            <Form
              form={passwordForm}
              layout="vertical"
              onFinish={handleUpdatePassword}
            >
              <Form.Item
                name="currentPassword"
                label={t('settings.currentPassword')}
                rules={[{ required: true, message: t('settings.currentPasswordRequired') as string }]}
              >
                <Input.Password prefix={<KeyOutlined />} />
              </Form.Item>

              <Form.Item
                name="newPassword"
                label={t('settings.newPassword')}
                rules={[
                  { required: true, message: t('settings.newPasswordRequired') as string },
                  { min: 6, message: t('settings.passwordTooShort') as string },
                ]}
              >
                <Input.Password prefix={<LockOutlined />} />
              </Form.Item>

              <Form.Item
                name="confirmPassword"
                label={t('settings.confirmPassword')}
                dependencies={['newPassword']}
                rules={[
                  { required: true, message: t('settings.confirmPasswordRequired') as string },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('newPassword') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error(t('settings.passwordMismatch') as string));
                    }}),
                ]}
              >
                <Input.Password prefix={<LockOutlined />} />
              </Form.Item>
              
              <Form.Item>
                <Button type="primary" htmlType="submit">
                  {t('settings.updatePassword')}
                </Button>
              </Form.Item>
            </Form>
          </TabPane>
          
          <TabPane
            tab={
              <span>
                <SettingOutlined />
                {t('settings.interface')}
              </span>
            }
            key="interface"
          >
            <Form
              form={interfaceForm}
              layout="vertical"
              initialValues={{
                theme: theme,
                language: language,
                autoSave: true,
                autoSaveInterval: 5,
                showWelcomeScreen: true}}
              onFinish={handleUpdateInterface}
            >
              <Form.Item name="theme" label={t('settings.theme')}>
                <Radio.Group>
                  <Radio.Button value={ThemeType.LIGHT}>{t('settings.lightTheme')}</Radio.Button>
                  <Radio.Button value={ThemeType.DARK}>{t('settings.darkTheme')}</Radio.Button>
                  <Radio.Button value={ThemeType.SYSTEM}>{t('settings.systemTheme')}</Radio.Button>
                </Radio.Group>
              </Form.Item>
              
              <Form.Item name="language" label={t('settings.language')}>
                <Select>
                  <Option value="zh-CN">中文</Option>
                  <Option value="en-US">English</Option>
                </Select>
              </Form.Item>
              
              <Divider>{t('settings.editorSettings')}</Divider>
              
              <Form.Item name="autoSave" label={t('settings.autoSave')} valuePropName="checked">
                <Switch />
              </Form.Item>
              
              <Form.Item name="autoSaveInterval" label={t('settings.autoSaveInterval')}>
                <Slider
                  min={1}
                  max={30}
                  marks={{
                    1: '1',
                    5: '5',
                    10: '10',
                    30: '30'}}
                />
              </Form.Item>
              
              <Form.Item name="showWelcomeScreen" label={t('settings.showWelcomeScreen')} valuePropName="checked">
                <Switch />
              </Form.Item>
              
              <Form.Item>
                <Button type="primary" htmlType="submit">
                  {t('settings.saveSettings')}
                </Button>
              </Form.Item>
            </Form>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};
