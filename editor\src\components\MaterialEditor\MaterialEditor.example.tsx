/**
 * 材质编辑器使用示例
 */
import React, { useState } from 'react';
import { Button, Space, Modal, message, Card, List, Tag } from 'antd';
import { PlusOutlined, EditOutlined, CopyOutlined, DeleteOutlined } from '@ant-design/icons';
import MaterialEditor from './index';
import { MaterialType } from '../../store/materials/materialsSlice';

// 示例材质数据
const exampleMaterials = [
  {
    id: 'metal-steel',
    name: '钢铁材质',
    type: MaterialType.PHYSICAL,
    color: '#8C8C8C',
    metalness: 1.0,
    roughness: 0.2,
    emissive: '#000000',
    emissiveIntensity: 0,
    transparent: false,
    opacity: 1,
    side: 'front',
    textures: [
      { type: 'normalMap', url: '/textures/metal_normal.jpg' },
      { type: 'roughnessMap', url: '/textures/metal_roughness.jpg' }
    ],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'wood-oak',
    name: '橡木材质',
    type: MaterialType.STANDARD,
    color: '#8B4513',
    metalness: 0.0,
    roughness: 0.8,
    emissive: '#000000',
    emissiveIntensity: 0,
    transparent: false,
    opacity: 1,
    side: 'front',
    textures: [
      { type: 'map', url: '/textures/wood_diffuse.jpg' },
      { type: 'normalMap', url: '/textures/wood_normal.jpg' },
      { type: 'roughnessMap', url: '/textures/wood_roughness.jpg' }
    ],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'glass-clear',
    name: '透明玻璃',
    type: MaterialType.PHYSICAL,
    color: '#FFFFFF',
    metalness: 0.0,
    roughness: 0.0,
    emissive: '#000000',
    emissiveIntensity: 0,
    transparent: true,
    opacity: 0.1,
    side: 'double',
    textures: [],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'fabric-cotton',
    name: '棉布材质',
    type: MaterialType.LAMBERT,
    color: '#F5F5DC',
    metalness: 0.0,
    roughness: 0.9,
    emissive: '#000000',
    emissiveIntensity: 0,
    transparent: false,
    opacity: 1,
    side: 'front',
    textures: [
      { type: 'map', url: '/textures/fabric_diffuse.jpg' },
      { type: 'normalMap', url: '/textures/fabric_normal.jpg' }
    ],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
];

const MaterialEditorExample: React.FC = () => {
  const [materials, setMaterials] = useState(exampleMaterials);
  const [selectedMaterial, setSelectedMaterial] = useState<string | null>(null);
  const [editorVisible, setEditorVisible] = useState(false);
  const [editorMode, setEditorMode] = useState<'create' | 'edit' | 'view'>('create');

  // 打开编辑器
  const openEditor = (mode: 'create' | 'edit' | 'view', materialId?: string) => {
    setEditorMode(mode);
    setSelectedMaterial(materialId || null);
    setEditorVisible(true);
  };

  // 关闭编辑器
  const closeEditor = () => {
    setEditorVisible(false);
    setSelectedMaterial(null);
  };

  // 保存材质
  const handleSave = (materialData: any) => {
    if (editorMode === 'create') {
      // 创建新材质
      const newMaterial = {
        ...materialData,
        id: `material-${Date.now()}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      setMaterials(prev => [...prev, newMaterial]);
      message.success('材质创建成功');
    } else if (editorMode === 'edit') {
      // 更新现有材质
      setMaterials(prev => prev.map(m => 
        m.id === materialData.id 
          ? { ...materialData, updatedAt: new Date().toISOString() }
          : m
      ));
      message.success('材质更新成功');
    }
    closeEditor();
  };

  // 复制材质
  const duplicateMaterial = (materialId: string) => {
    const original = materials.find(m => m.id === materialId);
    if (original) {
      const duplicated = {
        ...original,
        id: `${materialId}-copy-${Date.now()}`,
        name: `${original.name} (副本)`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      setMaterials(prev => [...prev, duplicated]);
      message.success('材质复制成功');
    }
  };

  // 删除材质
  const deleteMaterial = (materialId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个材质吗？此操作不可撤销。',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
        setMaterials(prev => prev.filter(m => m.id !== materialId));
        message.success('材质删除成功');
      }
    });
  };

  // 获取材质类型标签颜色
  const getTypeTagColor = (type: MaterialType) => {
    switch (type) {
      case MaterialType.PHYSICAL: return 'blue';
      case MaterialType.STANDARD: return 'green';
      case MaterialType.BASIC: return 'orange';
      case MaterialType.LAMBERT: return 'purple';
      case MaterialType.PHONG: return 'red';
      case MaterialType.TOON: return 'pink';
      default: return 'default';
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card 
        title="材质编辑器示例" 
        extra={
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => openEditor('create')}
          >
            创建材质
          </Button>
        }
      >
        <List
          grid={{ gutter: 16, xs: 1, sm: 2, md: 3, lg: 4 }}
          dataSource={materials}
          renderItem={(material) => (
            <List.Item>
              <Card
                size="small"
                title={material.name}
                extra={
                  <Space>
                    <Tag color={getTypeTagColor(material.type)}>
                      {material.type.toUpperCase()}
                    </Tag>
                  </Space>
                }
                actions={[
                  <Button 
                    key="view"
                    type="text" 
                    icon={<EditOutlined />}
                    onClick={() => openEditor('view', material.id)}
                  >
                    查看
                  </Button>,
                  <Button 
                    key="edit"
                    type="text" 
                    icon={<EditOutlined />}
                    onClick={() => openEditor('edit', material.id)}
                  >
                    编辑
                  </Button>,
                  <Button 
                    key="copy"
                    type="text" 
                    icon={<CopyOutlined />}
                    onClick={() => duplicateMaterial(material.id)}
                  >
                    复制
                  </Button>,
                  <Button 
                    key="delete"
                    type="text" 
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => deleteMaterial(material.id)}
                  >
                    删除
                  </Button>
                ]}
              >
                <div style={{ marginBottom: '8px' }}>
                  <div 
                    style={{
                      width: '100%',
                      height: '60px',
                      backgroundColor: material.color,
                      borderRadius: '4px',
                      border: '1px solid #d9d9d9',
                      opacity: material.transparent ? material.opacity : 1
                    }}
                  />
                </div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  <div>金属度: {material.metalness}</div>
                  <div>粗糙度: {material.roughness}</div>
                  <div>纹理: {material.textures?.length || 0} 个</div>
                </div>
              </Card>
            </List.Item>
          )}
        />
      </Card>

      <Modal
        title={
          editorMode === 'create' ? '创建材质' :
          editorMode === 'edit' ? '编辑材质' : '查看材质'
        }
        open={editorVisible}
        onCancel={closeEditor}
        footer={null}
        width="90%"
        style={{ top: 20 }}
        styles={{ body: { padding: 0 } }}
      >
        <MaterialEditor
          materialId={selectedMaterial || undefined}
          onSave={handleSave}
          onCancel={closeEditor}
          readonly={editorMode === 'view'}
        />
      </Modal>
    </div>
  );
};

export default MaterialEditorExample;
