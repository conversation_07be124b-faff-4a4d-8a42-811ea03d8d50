/**
 * 交互示例样式
 */
.interaction-example {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .example-header {
    .ant-card-body {
      p {
        margin-bottom: 16px;
        color: #666;
        line-height: 1.6;
      }
    }
  }

  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;

      .ant-card-head-title {
        font-weight: 600;
      }
    }

    &.ant-card-small {
      .ant-card-head {
        min-height: 38px;
        padding: 0 12px;

        .ant-card-head-title {
          font-size: 14px;
        }
      }

      .ant-card-body {
        padding: 12px;
      }
    }
  }

  .code-example {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 16px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
    color: #333;
    overflow-x: auto;
    margin: 0;

    // 语法高亮（简单版本）
    .keyword {
      color: #d73a49;
      font-weight: 600;
    }

    .string {
      color: #032f62;
    }

    .comment {
      color: #6a737d;
      font-style: italic;
    }

    .function {
      color: #6f42c1;
    }
  }

  // 预设按钮样式
  .ant-btn {
    &.ant-btn-primary {
      background: linear-gradient(135deg, #1890ff, #36cfc9);
      border: none;

      &:hover {
        background: linear-gradient(135deg, #40a9ff, #5cdbd3);
      }
    }

    &:not(.ant-btn-primary) {
      &:hover {
        color: #1890ff;
        border-color: #1890ff;
      }
    }
  }

  // 功能特性列表
  h4 {
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
    color: #333;

    &::before {
      content: attr(data-icon);
      margin-right: 8px;
    }
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      padding: 4px 0;
      color: #666;
      position: relative;
      padding-left: 16px;

      &::before {
        content: '•';
        color: #1890ff;
        position: absolute;
        left: 0;
        font-weight: bold;
      }

      &:hover {
        color: #333;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .ant-col {
      margin-bottom: 16px;
    }
  }

  @media (max-width: 768px) {
    padding: 16px;

    .ant-row {
      .ant-col {
        width: 100%;
        margin-bottom: 16px;
      }
    }

    .code-example {
      font-size: 12px;
      padding: 12px;
    }

    h4 {
      font-size: 14px;
    }
  }

  // 动画效果
  .ant-card {
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
  }

  .ant-btn {
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
    }
  }

  // 加载状态
  &.loading {
    .ant-card {
      opacity: 0.6;
      pointer-events: none;
    }

    &::after {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      z-index: 1000;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  // 成功状态
  &.success {
    .example-header {
      border-left: 4px solid #52c41a;
    }
  }

  // 错误状态
  &.error {
    .example-header {
      border-left: 4px solid #ff4d4f;
    }
  }

  // 深色主题支持
  @media (prefers-color-scheme: dark) {
    background: #1f1f1f;

    .ant-card {
      background: #2f2f2f;
      border-color: #434343;

      .ant-card-head {
        background: #3f3f3f;
        border-bottom-color: #434343;

        .ant-card-head-title {
          color: #fff;
        }
      }

      .ant-card-body {
        background: #2f2f2f;
        color: #fff;

        p {
          color: #ccc;
        }
      }
    }

    .code-example {
      background: #1e1e1e;
      border-color: #434343;
      color: #d4d4d4;
    }

    h4 {
      color: #fff;
    }

    ul li {
      color: #ccc;

      &:hover {
        color: #fff;
      }

      &::before {
        color: #1890ff;
      }
    }
  }

  // 打印样式
  @media print {
    background: white;
    padding: 0;

    .ant-card {
      box-shadow: none;
      border: 1px solid #ddd;
      page-break-inside: avoid;
    }

    .ant-btn {
      display: none;
    }

    .code-example {
      background: #f8f9fa;
      border: 1px solid #ddd;
    }
  }
}
