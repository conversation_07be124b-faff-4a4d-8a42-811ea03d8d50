/**
 * 动画编辑器面板
 */
import React, { useState, useEffect, useRef } from 'react';
import { Layout, Tabs, Button, Select, Collapse, message, Space } from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  UndoOutlined,
  RedoOutlined,
  SaveOutlined,
  CommentOutlined
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { AnimationTimeline } from './AnimationTimeline';
import { AnimationPreview } from './AnimationPreview';
import { BlendModeEditor } from './BlendModeEditor';
import { MaskEditor } from './MaskEditor';
import { SubClipEditor } from './SubClipEditor';
import { AnimationPerformance } from './AnimationPerformance';
import { FeedbackButton } from '../feedback';
// 从 dl-engine 导入动画相关类型和枚举
import {
  BlendMode
} from '../../libs/dl-engine-types';

// 定义本地类型以替代引擎类型
interface AnimationMask {
  name: string;
  type: 'include' | 'exclude';
  weightType: 'binary' | 'weighted';
  bones: string[];
}

interface SubClip {
  name: string;
  originalClip: any;
  startTime: number;
  endTime: number;
  loop: boolean;
}

interface SubClipSequence {
  name: string;
  loop: boolean;
  autoPlay: boolean;
}

interface SubClipTransition {
  name: string;
  fromClip: any;
  toClip: any;
  duration: number;
  type: 'ease_in_out' | 'linear' | 'ease_in' | 'ease_out';
}

interface SubClipModifier {
  name: string;
  type: 'time_scale' | 'speed' | 'reverse';
  params: any;
  enabled: boolean;
}

interface SubClipEvent {
  name: string;
  triggerType: 'time' | 'frame' | 'marker';
  triggerValue: number;
  callback: (event: any) => void;
  once: boolean;
  enabled: boolean;
}

enum OptimizationLevel {
  NONE = 'none',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  EXTREME = 'extreme'
}
import './AnimationEditorPanel.css';

const { Content, Sider } = Layout;
const { TabPane } = Tabs;
const { Panel } = Collapse;
const { Option } = Select;

/**
 * 动画编辑器面板属性
 */
interface AnimationEditorPanelProps {
  /** 是否可见 */
  visible: boolean;
  /** 关闭回调 */
  onClose: () => void;
}

/**
 * 动画编辑器面板
 */
export const AnimationEditorPanel: React.FC<AnimationEditorPanelProps> = ({ visible, onClose }) => {
  // 状态
  const [activeTab, setActiveTab] = useState('timeline');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration] = useState(5);
  const [selectedLayer, setSelectedLayer] = useState<number | null>(null);
  const [selectedMask, setSelectedMask] = useState<string | null>(null);
  const [selectedSubClip, setSelectedSubClip] = useState<string | null>(null);
  const [selectedSequence, setSelectedSequence] = useState<string | null>(null);
  const [selectedTransition, setSelectedTransition] = useState<string | null>(null);
  const [selectedModifier, setSelectedModifier] = useState<string | null>(null);
  const [selectedEvent, setSelectedEvent] = useState<string | null>(null);
  const [optimizationLevel, setOptimizationLevel] = useState<OptimizationLevel>(OptimizationLevel.MEDIUM);

  // 引用
  const timelineRef = useRef<any>(null);
  const previewRef = useRef<any>(null);

  // Redux
  const dispatch = useDispatch();
  const animations = useSelector((state: any) => state.animation.animations);
  const layers = useSelector((state: any) => state.animation.layers);
  const masks = useSelector((state: any) => state.animation.masks);
  const subClips = useSelector((state: any) => state.animation.subClips);
  const sequences = useSelector((state: any) => state.animation.sequences);
  const transitions = useSelector((state: any) => state.animation.transitions);
  const modifiers = useSelector((state: any) => state.animation.modifiers);
  const events = useSelector((state: any) => state.animation.events);

  // 效果
  useEffect(() => {
    if (visible) {
      // 初始化
    }

    return () => {
      // 清理
    };
  }, [visible]);

  // 播放/暂停
  const togglePlay = () => {
    setIsPlaying(!isPlaying);

    if (previewRef.current) {
      if (!isPlaying) {
        previewRef.current.play();
      } else {
        previewRef.current.pause();
      }
    }
  };

  // 时间更新
  const handleTimeUpdate = (time: number) => {
    setCurrentTime(time);

    if (timelineRef.current) {
      timelineRef.current.setCurrentTime(time);
    }

    if (previewRef.current) {
      previewRef.current.setCurrentTime(time);
    }
  };

  // 添加混合层
  const handleAddLayer = () => {
    if (animations.length === 0) {
      message.warning('没有可用的动画');
      return;
    }

    const newLayer = {
      clipName: animations[0].name,
      weight: 1.0,
      timeScale: 1.0,
      blendMode: BlendMode.OVERRIDE
    };

    dispatch({ type: 'animation/addLayer', payload: newLayer });
    message.success('添加混合层成功');
  };

  // 删除混合层
  const handleDeleteLayer = (index: number) => {
    dispatch({ type: 'animation/deleteLayer', payload: index });

    if (selectedLayer === index) {
      setSelectedLayer(null);
    }

    message.success('删除混合层成功');
  };

  // 添加遮罩
  const handleAddMask = () => {
    const name = `mask_${masks.length + 1}`;

    const newMask: AnimationMask = {
      name,
      type: 'include',
      weightType: 'binary',
      bones: []
    };

    dispatch({ type: 'animation/addMask', payload: { name, mask: newMask } });
    message.success('添加遮罩成功');
  };

  // 删除遮罩
  const handleDeleteMask = (name: string) => {
    dispatch({ type: 'animation/deleteMask', payload: name });

    if (selectedMask === name) {
      setSelectedMask(null);
    }

    message.success('删除遮罩成功');
  };

  // 添加子片段
  const handleAddSubClip = () => {
    if (animations.length === 0) {
      message.warning('没有可用的动画');
      return;
    }

    const name = `subClip_${subClips.length + 1}`;
    const clip = animations[0];

    const newSubClip: SubClip = {
      name,
      originalClip: clip,
      startTime: 0,
      endTime: clip.duration || 5,
      loop: false
    };

    dispatch({ type: 'animation/addSubClip', payload: { name, subClip: newSubClip } });
    message.success('添加子片段成功');
  };

  // 删除子片段
  const handleDeleteSubClip = (name: string) => {
    dispatch({ type: 'animation/deleteSubClip', payload: name });

    if (selectedSubClip === name) {
      setSelectedSubClip(null);
    }

    message.success('删除子片段成功');
  };

  // 添加序列
  const handleAddSequence = () => {
    if (subClips.length === 0) {
      message.warning('没有可用的子片段');
      return;
    }

    const name = `sequence_${sequences.length + 1}`;

    const newSequence: SubClipSequence = {
      name,
      loop: true,
      autoPlay: false
    };

    dispatch({ type: 'animation/addSequence', payload: { name, sequence: newSequence } });
    message.success('添加序列成功');
  };

  // 删除序列
  const handleDeleteSequence = (name: string) => {
    dispatch({ type: 'animation/deleteSequence', payload: name });

    if (selectedSequence === name) {
      setSelectedSequence(null);
    }

    message.success('删除序列成功');
  };

  // 添加过渡
  const handleAddTransition = () => {
    if (subClips.length < 2) {
      message.warning('需要至少两个子片段');
      return;
    }

    const name = `transition_${transitions.length + 1}`;
    const subClipNames = Object.keys(subClips);

    const newTransition: SubClipTransition = {
      name,
      fromClip: subClips[subClipNames[0]],
      toClip: subClips[subClipNames[1]],
      duration: 1.0,
      type: 'ease_in_out'
    };

    dispatch({ type: 'animation/addTransition', payload: { name, transition: newTransition } });
    message.success('添加过渡成功');
  };

  // 删除过渡
  const handleDeleteTransition = (name: string) => {
    dispatch({ type: 'animation/deleteTransition', payload: name });

    if (selectedTransition === name) {
      setSelectedTransition(null);
    }

    message.success('删除过渡成功');
  };

  // 添加变形器
  const handleAddModifier = () => {
    const name = `modifier_${modifiers.length + 1}`;

    const newModifier: SubClipModifier = {
      name,
      type: 'time_scale',
      params: { scale: 2.0 },
      enabled: true
    };

    dispatch({ type: 'animation/addModifier', payload: { name, modifier: newModifier } });
    message.success('添加变形器成功');
  };

  // 删除变形器
  const handleDeleteModifier = (name: string) => {
    dispatch({ type: 'animation/deleteModifier', payload: name });

    if (selectedModifier === name) {
      setSelectedModifier(null);
    }

    message.success('删除变形器成功');
  };

  // 添加事件
  const handleAddEvent = () => {
    const name = `event_${events.length + 1}`;

    const newEvent: SubClipEvent = {
      name,
      triggerType: 'time',
      triggerValue: 0,
      callback: (event) => {
        console.log('事件触发:', event);
      },
      once: false,
      enabled: true
    };

    dispatch({ type: 'animation/addEvent', payload: { name, event: newEvent } });
    message.success('添加事件成功');
  };

  // 删除事件
  const handleDeleteEvent = (name: string) => {
    dispatch({ type: 'animation/deleteEvent', payload: name });

    if (selectedEvent === name) {
      setSelectedEvent(null);
    }

    message.success('删除事件成功');
  };

  // 优化
  const handleOptimize = () => {
    // 创建优化器配置
    const optimizerConfig = {
      level: optimizationLevel,
      debug: true
    };

    // 优化混合器
    // 这里应该根据实际情况实现
    console.log('执行动画优化:', optimizerConfig);

    message.success('优化成功');
  };

  // 撤销
  const handleUndo = () => {
    dispatch({ type: 'animation/undo' });
  };

  // 重做
  const handleRedo = () => {
    dispatch({ type: 'animation/redo' });
  };

  // 保存
  const handleSave = () => {
    // 保存动画数据
    // 这里应该根据实际情况实现

    message.success('保存成功');
  };

  // 获取性能数据
  const getPerformanceData = () => {
    // 从AnimationBlender获取性能数据
    // 这里应该根据实际情况实现
    return {
      // 基本信息
      timestamp: new Date().toISOString(),
      version: '1.0.0',

      // 混合层信息
      layers: {
        count: layers.length,
        types: layers.reduce((acc: Record<string, number>, layer: any) => {
          acc[layer.blendMode] = (acc[layer.blendMode] || 0) + 1;
          return acc;
        }, {})
      },

      // 遮罩信息
      masks: {
        count: Object.keys(masks).length,
        boneCount: Object.values(masks).reduce((sum: number, mask: any) => sum + (mask.bones?.length || 0), 0)
      },

      // 子片段信息
      subClips: {
        count: Object.keys(subClips).length
      },

      // 序列信息
      sequences: {
        count: Object.keys(sequences).length
      },

      // 过渡信息
      transitions: {
        count: Object.keys(transitions).length
      },

      // 事件信息
      events: {
        count: Object.keys(events).length
      },

      // 性能统计
      performance: {
        // 这里应该从AnimationPerformance组件获取实际的性能数据
        fps: 60,
        memoryUsage: 50,
        cpuUsage: 30,
        renderTime: 5,
        updateTime: 3,
        blendTime: 2
      },

      // 优化信息
      optimization: {
        level: optimizationLevel
      }
    };
  };

  // 渲染
  return (
    <Layout className="animation-editor-panel" style={{ display: visible ? 'flex' : 'none' }}>
      <div className="animation-editor-header">
        <div className="animation-editor-title">动画编辑器</div>
        <div className="animation-editor-controls">
          <Space>
            <Button icon={<UndoOutlined />} onClick={handleUndo} />
            <Button icon={<RedoOutlined />} onClick={handleRedo} />
            <Button icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />} onClick={togglePlay} />
            <Button icon={<SaveOutlined />} type="primary" onClick={handleSave} />
            <FeedbackButton
              type="animation"
              subType="blend"
              buttonType="default"
              buttonSize="middle"
              showText={false}
              useDrawer={true}
              performanceData={getPerformanceData()}
            />
            <Button onClick={onClose}>关闭</Button>
          </Space>
        </div>
      </div>

      <Layout className="animation-editor-content">
        <Sider width={300} className="animation-editor-sider">
          <Tabs defaultActiveKey="layers" className="animation-editor-tabs">
            <TabPane tab="混合层" key="layers">
              <div className="animation-editor-tab-header">
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAddLayer}>添加混合层</Button>
              </div>
              <div className="animation-editor-tab-content">
                {layers.map((layer: any, index: number) => (
                  <div
                    key={index}
                    className={`animation-editor-item ${selectedLayer === index ? 'selected' : ''}`}
                    onClick={() => setSelectedLayer(index)}
                  >
                    <div className="animation-editor-item-title">{`层 ${index + 1}: ${layer.clipName}`}</div>
                    <div className="animation-editor-item-controls">
                      <Button
                        icon={<DeleteOutlined />}
                        size="small"
                        danger
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteLayer(index);
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </TabPane>

            <TabPane tab="遮罩" key="masks">
              <div className="animation-editor-tab-header">
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAddMask}>添加遮罩</Button>
              </div>
              <div className="animation-editor-tab-content">
                {Object.entries(masks).map(([name, _mask]: [string, any]) => (
                  <div
                    key={name}
                    className={`animation-editor-item ${selectedMask === name ? 'selected' : ''}`}
                    onClick={() => setSelectedMask(name)}
                  >
                    <div className="animation-editor-item-title">{name}</div>
                    <div className="animation-editor-item-controls">
                      <Button
                        icon={<DeleteOutlined />}
                        size="small"
                        danger
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteMask(name);
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </TabPane>

            <TabPane tab="子片段" key="subClips">
              <div className="animation-editor-tab-header">
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAddSubClip}>添加子片段</Button>
              </div>
              <div className="animation-editor-tab-content">
                {Object.entries(subClips).map(([name, _subClip]: [string, any]) => (
                  <div
                    key={name}
                    className={`animation-editor-item ${selectedSubClip === name ? 'selected' : ''}`}
                    onClick={() => setSelectedSubClip(name)}
                  >
                    <div className="animation-editor-item-title">{name}</div>
                    <div className="animation-editor-item-controls">
                      <Button
                        icon={<DeleteOutlined />}
                        size="small"
                        danger
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteSubClip(name);
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </TabPane>

            <TabPane tab="序列" key="sequences">
              <div className="animation-editor-tab-header">
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAddSequence}>添加序列</Button>
              </div>
              <div className="animation-editor-tab-content">
                {Object.entries(sequences).map(([name, _sequence]: [string, any]) => (
                  <div
                    key={name}
                    className={`animation-editor-item ${selectedSequence === name ? 'selected' : ''}`}
                    onClick={() => setSelectedSequence(name)}
                  >
                    <div className="animation-editor-item-title">{name}</div>
                    <div className="animation-editor-item-controls">
                      <Button
                        icon={<DeleteOutlined />}
                        size="small"
                        danger
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteSequence(name);
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </TabPane>

            <TabPane tab="过渡" key="transitions">
              <div className="animation-editor-tab-header">
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAddTransition}>添加过渡</Button>
              </div>
              <div className="animation-editor-tab-content">
                {Object.entries(transitions).map(([name, _transition]: [string, any]) => (
                  <div
                    key={name}
                    className={`animation-editor-item ${selectedTransition === name ? 'selected' : ''}`}
                    onClick={() => setSelectedTransition(name)}
                  >
                    <div className="animation-editor-item-title">{name}</div>
                    <div className="animation-editor-item-controls">
                      <Button
                        icon={<DeleteOutlined />}
                        size="small"
                        danger
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteTransition(name);
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </TabPane>

            <TabPane tab="变形器" key="modifiers">
              <div className="animation-editor-tab-header">
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAddModifier}>添加变形器</Button>
              </div>
              <div className="animation-editor-tab-content">
                {Object.entries(modifiers).map(([name, _modifier]: [string, any]) => (
                  <div
                    key={name}
                    className={`animation-editor-item ${selectedModifier === name ? 'selected' : ''}`}
                    onClick={() => setSelectedModifier(name)}
                  >
                    <div className="animation-editor-item-title">{name}</div>
                    <div className="animation-editor-item-controls">
                      <Button
                        icon={<DeleteOutlined />}
                        size="small"
                        danger
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteModifier(name);
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </TabPane>

            <TabPane tab="事件" key="events">
              <div className="animation-editor-tab-header">
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAddEvent}>添加事件</Button>
              </div>
              <div className="animation-editor-tab-content">
                {Object.entries(events).map(([name, _event]: [string, any]) => (
                  <div
                    key={name}
                    className={`animation-editor-item ${selectedEvent === name ? 'selected' : ''}`}
                    onClick={() => setSelectedEvent(name)}
                  >
                    <div className="animation-editor-item-title">{name}</div>
                    <div className="animation-editor-item-controls">
                      <Button
                        icon={<DeleteOutlined />}
                        size="small"
                        danger
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteEvent(name);
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </TabPane>

            <TabPane tab="优化" key="optimization">
              <div className="animation-editor-tab-content">
                <div className="animation-editor-form-item">
                  <div className="animation-editor-form-label">优化级别</div>
                  <div className="animation-editor-form-control">
                    <Select
                      value={optimizationLevel}
                      onChange={setOptimizationLevel}
                      style={{ width: '100%' }}
                    >
                      <Option value={OptimizationLevel.NONE}>无优化</Option>
                      <Option value={OptimizationLevel.LOW}>低级优化</Option>
                      <Option value={OptimizationLevel.MEDIUM}>中级优化</Option>
                      <Option value={OptimizationLevel.HIGH}>高级优化</Option>
                      <Option value={OptimizationLevel.EXTREME}>极限优化</Option>
                    </Select>
                  </div>
                </div>

                <Button type="primary" onClick={handleOptimize} style={{ marginTop: 16 }}>优化</Button>

                <AnimationPerformance />
              </div>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <CommentOutlined />
                  反馈
                </span>
              }
              key="feedback"
            >
              <div className="animation-editor-tab-content">
                <div className="animation-editor-feedback-header">
                  <h3>动画混合系统反馈</h3>
                  <p>您的反馈对我们改进动画混合系统非常重要。请分享您的使用体验、问题或建议。</p>

                  <Space style={{ marginTop: 16, marginBottom: 16 }}>
                    <FeedbackButton
                      type="animation"
                      subType="blend"
                      buttonType="primary"
                      showText={true}
                      useDrawer={true}
                      performanceData={getPerformanceData()}
                    />
                  </Space>
                </div>

                <Collapse ghost>
                  <Panel header="查看反馈统计" key="1">
                    <div className="animation-editor-feedback-stats">
                      {/* 这里可以添加反馈统计组件 */}
                      <p>反馈统计功能即将推出...</p>
                    </div>
                  </Panel>
                </Collapse>
              </div>
            </TabPane>
          </Tabs>
        </Sider>

        <Content className="animation-editor-main">
          <Tabs activeKey={activeTab} onChange={setActiveTab} className="animation-editor-main-tabs">
            <TabPane tab="时间轴" key="timeline">
              <AnimationTimeline
                ref={timelineRef}
                currentTime={currentTime}
                duration={duration}
                onTimeUpdate={handleTimeUpdate}
                layers={layers}
                subClips={subClips}
                sequences={sequences}
                transitions={transitions}
                events={events}
              />
            </TabPane>

            <TabPane tab="预览" key="preview">
              <AnimationPreview
                ref={previewRef}
                currentTime={currentTime}
                duration={duration}
                isPlaying={isPlaying}
                layers={layers}
                onTimeUpdate={handleTimeUpdate}
                onPlayStateChange={setIsPlaying}
              />
            </TabPane>

            <TabPane tab="属性" key="properties">
              <div className="animation-editor-properties">
                {selectedLayer !== null && (
                  <BlendModeEditor
                    layers={layers}
                    selectedLayer={selectedLayer}
                    onLayerUpdate={(index: number, layer: any) => {
                      dispatch({
                        type: 'animation/updateLayer',
                        payload: { index, layer }
                      });
                    }}
                  />
                )}

                {selectedMask && (
                  <MaskEditor
                    mask={masks[selectedMask]}
                    onUpdate={(updatedMask) => {
                      dispatch({
                        type: 'animation/updateMask',
                        payload: { name: selectedMask, mask: updatedMask }
                      });
                    }}
                  />
                )}

                {selectedSubClip && (
                  <SubClipEditor
                    subClips={subClips}
                    selectedSubClip={selectedSubClip}
                    onSubClipUpdate={(name: string, subClip: any) => {
                      dispatch({
                        type: 'animation/updateSubClip',
                        payload: { name, subClip }
                      });
                    }}
                  />
                )}

                {/* 其他编辑器组件 */}
              </div>
            </TabPane>
          </Tabs>
        </Content>
      </Layout>
    </Layout>
  );
};
