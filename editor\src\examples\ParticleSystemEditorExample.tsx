/**
 * 粒子系统编辑器使用示例
 */
import React, { useState } from 'react';
import { Card, Row, Col, Button, Space, message } from 'antd';
import ParticleSystemEditor from '../components/effects/ParticleSystemEditor';

/**
 * 粒子系统编辑器示例组件
 */
const ParticleSystemEditorExample: React.FC = () => {
  const [particleData, setParticleData] = useState<any>(null);
  const [showPreview, setShowPreview] = useState(true);

  // 处理粒子系统数据变化
  const handleParticleChange = (data: any) => {
    setParticleData(data);
    console.log('粒子系统数据更新:', data);
  };

  // 保存粒子系统
  const handleSave = () => {
    if (particleData) {
      // 这里可以将数据保存到服务器或本地存储
      console.log('保存粒子系统:', particleData);
      message.success('粒子系统已保存');
    } else {
      message.warning('没有可保存的数据');
    }
  };

  // 导出粒子系统配置
  const handleExport = () => {
    if (particleData) {
      const dataStr = JSON.stringify(particleData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${particleData.name || '粒子系统'}.json`;
      link.click();
      URL.revokeObjectURL(url);
      message.success('配置已导出');
    } else {
      message.warning('没有可导出的数据');
    }
  };

  // 导入粒子系统配置
  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (event) => {
          try {
            const data = JSON.parse(event.target?.result as string);
            setParticleData(data);
            message.success('配置已导入');
          } catch (error) {
            message.error('配置文件格式错误');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  // 重置粒子系统
  const handleReset = () => {
    setParticleData(null);
    message.info('粒子系统已重置');
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card title="粒子系统编辑器示例" style={{ marginBottom: '16px' }}>
        <Space wrap>
          <Button type="primary" onClick={handleSave}>
            保存配置
          </Button>
          <Button onClick={handleExport}>
            导出配置
          </Button>
          <Button onClick={handleImport}>
            导入配置
          </Button>
          <Button onClick={handleReset}>
            重置
          </Button>
          <Button 
            type={showPreview ? 'default' : 'primary'} 
            onClick={() => setShowPreview(!showPreview)}
          >
            {showPreview ? '隐藏预览' : '显示预览'}
          </Button>
        </Space>
      </Card>

      <Row gutter={16}>
        <Col span={24}>
          <ParticleSystemEditor
            data={particleData}
            onChange={handleParticleChange}
            showPreview={showPreview}
            previewWidth={400}
            previewHeight={300}
          />
        </Col>
      </Row>

      {/* 数据预览 */}
      {particleData && (
        <Card title="当前配置数据" style={{ marginTop: '16px' }}>
          <pre style={{ 
            background: '#f5f5f5', 
            padding: '12px', 
            borderRadius: '4px',
            fontSize: '12px',
            maxHeight: '300px',
            overflow: 'auto'
          }}>
            {JSON.stringify(particleData, null, 2)}
          </pre>
        </Card>
      )}

      {/* 使用说明 */}
      <Card title="使用说明" style={{ marginTop: '16px' }}>
        <div style={{ lineHeight: '1.6' }}>
          <h4>功能介绍：</h4>
          <ul>
            <li><strong>预设效果</strong>：点击预设按钮快速应用常用效果（火焰、烟雾、雪花、爆炸）</li>
            <li><strong>实时预览</strong>：左侧预览区域实时显示粒子效果</li>
            <li><strong>分类编辑</strong>：通过标签页分类编辑不同类型的属性</li>
            <li><strong>配置管理</strong>：支持保存、导出、导入粒子系统配置</li>
          </ul>

          <h4>编辑标签页：</h4>
          <ul>
            <li><strong>发射器</strong>：配置粒子发射的基本参数</li>
            <li><strong>外观</strong>：设置粒子的视觉效果</li>
            <li><strong>运动</strong>：控制粒子的物理运动</li>
            <li><strong>形状</strong>：定义发射器的几何形状</li>
            <li><strong>高级</strong>：启用高级功能选项</li>
          </ul>

          <h4>操作提示：</h4>
          <ul>
            <li>修改任何参数都会实时更新预览效果</li>
            <li>可以通过预设快速开始，然后进行细节调整</li>
            <li>支持导出配置文件，便于在项目中复用</li>
            <li>范围输入支持最小值和最大值设置，系统会在范围内随机选择</li>
          </ul>
        </div>
      </Card>
    </div>
  );
};

export default ParticleSystemEditorExample;
