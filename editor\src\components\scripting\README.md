# 脚本编辑器

脚本编辑器是一个功能完整的脚本开发环境，支持JavaScript、TypeScript和可视化脚本编辑。它与底层DL引擎深度集成，提供强大的脚本执行和调试功能。

## 功能特性

### 🚀 核心功能
- **多语言支持**: JavaScript、TypeScript、可视化脚本
- **实时执行**: 支持脚本的实时编译和执行
- **代码高亮**: 语法高亮和智能提示
- **模板系统**: 预定义的脚本模板，快速开始开发
- **调试支持**: 内置调试工具和错误检查
- **引擎集成**: 与DL引擎的深度集成

### 📝 代码编辑器
- 语法高亮
- 代码折叠
- 自动完成
- 行号显示
- 全屏编辑
- 代码导入/导出
- 复制/粘贴功能

### 🎨 可视化脚本编辑器
- 节点式编程界面
- 拖拽式节点连接
- 节点搜索和分类
- 收藏和最近使用
- 实时预览
- 调试支持

### 📋 脚本模板
- 基础JavaScript模板
- TypeScript模板
- 玩家控制器模板
- UI管理器模板
- 自定义模板支持

## 组件结构

```
scripting/
├── ScriptEditor.tsx          # 主脚本编辑器组件
├── CodeEditor.tsx           # 代码编辑器组件
├── VisualScriptEditor.tsx   # 可视化脚本编辑器
├── ScriptTemplates.tsx      # 脚本模板管理器
├── NodeSearch.tsx           # 节点搜索组件
├── __tests__/              # 测试文件
└── README.md               # 文档
```

## 使用方法

### 基本使用

```tsx
import ScriptEditor from './components/scripting/ScriptEditor';

function MyComponent() {
  const [scriptData, setScriptData] = useState({
    type: 'javascript',
    content: '',
    enabled: true,
    autoRun: false,
    domain: 'default',
    variables: {}
  });

  return (
    <ScriptEditor
      data={scriptData}
      onChange={setScriptData}
      entityId="entity-123"
    />
  );
}
```

### 脚本数据格式

```typescript
interface ScriptData {
  type: 'javascript' | 'typescript' | 'visual_script';
  content: string;
  enabled: boolean;
  autoRun: boolean;
  domain: string;
  visualScript?: any;
  variables?: Record<string, any>;
}
```

### 与引擎集成

脚本编辑器通过`ScriptService`与底层DL引擎集成：

```typescript
import ScriptService from '../../services/ScriptService';

const scriptService = ScriptService.getInstance();

// 注册脚本
scriptService.registerScript(scriptData);

// 编译脚本
await scriptService.compileScript(scriptId);

// 执行脚本
await scriptService.executeScript(scriptId, context);
```

## API 参考

### ScriptEditor Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| data | ScriptData | - | 脚本数据 |
| onChange | (data: ScriptData) => void | - | 数据变化回调 |
| entityId | string | - | 关联的实体ID |

### CodeEditor Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| value | string | '' | 代码内容 |
| language | ScriptLanguage | 'javascript' | 编程语言 |
| readOnly | boolean | false | 是否只读 |
| height | string \| number | '400px' | 编辑器高度 |
| onChange | (value: string) => void | - | 内容变化回调 |

### VisualScriptEditor Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| value | VisualScriptData | - | 可视化脚本数据 |
| readOnly | boolean | false | 是否只读 |
| height | string \| number | '500px' | 编辑器高度 |
| onChange | (value: VisualScriptData) => void | - | 数据变化回调 |

## 脚本生命周期

脚本支持标准的生命周期函数：

```javascript
// 脚本开始时调用
function onStart() {
    console.log('脚本开始执行');
}

// 每帧更新时调用
function onUpdate(deltaTime) {
    // 更新逻辑
}

// 脚本销毁时调用
function onDestroy() {
    console.log('脚本销毁');
}

// 导出生命周期函数
export { onStart, onUpdate, onDestroy };
```

## 可视化脚本节点

### 事件节点
- `onStart`: 开始事件
- `onUpdate`: 更新事件
- `onDestroy`: 销毁事件

### 流程控制节点
- `delay`: 延迟执行
- `branch`: 条件分支
- `loop`: 循环执行

### 数学节点
- `add`: 加法运算
- `subtract`: 减法运算
- `multiply`: 乘法运算
- `divide`: 除法运算

### 调试节点
- `print`: 控制台输出
- `log`: 日志记录
- `assert`: 断言检查

## 最佳实践

### 1. 脚本组织
- 使用模块化的脚本结构
- 合理使用生命周期函数
- 避免在更新函数中进行重复计算

### 2. 性能优化
- 缓存频繁访问的对象引用
- 使用对象池减少垃圾回收
- 避免在热路径中创建新对象

### 3. 调试技巧
- 使用console.log进行调试输出
- 利用调试模式查看执行状态
- 使用断点和单步执行

### 4. 错误处理
- 使用try-catch包装可能出错的代码
- 提供有意义的错误消息
- 实现优雅的降级处理

## 扩展开发

### 添加新的脚本模板

```typescript
const newTemplate: ScriptTemplate = {
  id: 'custom-template',
  name: '自定义模板',
  description: '自定义脚本模板',
  category: 'custom',
  type: 'javascript',
  tags: ['自定义'],
  content: `// 自定义脚本模板
function customFunction() {
    // 自定义逻辑
}

export { customFunction };`,
  author: 'Your Name',
  version: '1.0.0'
};
```

### 添加新的可视化脚本节点

```typescript
const newNodeType: NodeInfo = {
  type: 'custom/myNode',
  label: '我的节点',
  description: '自定义节点描述',
  category: 'custom',
  icon: 'custom',
  color: '#ff6b6b',
  tags: ['自定义', '功能']
};
```

## 故障排除

### 常见问题

1. **脚本执行失败**
   - 检查语法错误
   - 验证依赖项是否正确导入
   - 查看控制台错误信息

2. **可视化脚本节点连接问题**
   - 确保节点类型兼容
   - 检查数据流向是否正确
   - 验证节点配置

3. **性能问题**
   - 检查是否有无限循环
   - 优化更新函数的执行频率
   - 使用性能分析工具

### 调试工具

- 浏览器开发者工具
- 脚本编辑器内置调试器
- 引擎性能分析器
- 日志系统

## 更新日志

### v1.0.0
- 初始版本发布
- 支持JavaScript和TypeScript编辑
- 可视化脚本编辑器
- 脚本模板系统
- 与DL引擎集成

---

更多信息请参考[DL引擎文档](../../../docs/engine.md)。
