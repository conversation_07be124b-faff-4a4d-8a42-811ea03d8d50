/**
 * 性能分析面板样式
 */

.performance-analysis-panel {
  .ant-modal-body {
    padding: 16px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .performance-overview {
    .ant-card {
      .ant-card-body {
        padding: 16px;
      }

      .ant-statistic {
        .ant-statistic-title {
          font-size: 12px;
          color: #666666;
          margin-bottom: 4px;
        }

        .ant-statistic-content {
          .ant-statistic-content-value {
            font-size: 24px;
            font-weight: 600;
          }

          .ant-statistic-content-suffix {
            font-size: 14px;
            margin-left: 4px;
          }
        }
      }
    }

    .ant-descriptions {
      .ant-descriptions-item-label {
        font-weight: 500;
        color: #333333;
        width: 120px;
      }

      .ant-descriptions-item-content {
        color: #666666;
      }
    }
  }

  .performance-charts {
    .ant-card {
      .ant-card-head {
        .ant-card-head-title {
          font-size: 14px;
          font-weight: 500;
        }
      }

      .ant-card-body {
        padding: 16px;
      }
    }
  }

  .performance-warnings {
    .warnings-header {
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;
    }

    .ant-list {
      .ant-list-item {
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;

        &:hover {
          background-color: #f9f9f9;
        }

        .ant-list-item-meta {
          .ant-list-item-meta-avatar {
            margin-right: 12px;
          }

          .ant-list-item-meta-title {
            margin-bottom: 4px;
            font-size: 14px;
          }

          .ant-list-item-meta-description {
            font-size: 12px;
            line-height: 1.4;
          }
        }

        .ant-list-item-action {
          margin-left: 16px;
        }
      }
    }
  }

  .warning-details {
    .ant-descriptions {
      .ant-descriptions-item-label {
        font-weight: 500;
        color: #333333;
      }

      .ant-descriptions-item-content {
        color: #666666;

        .ant-list {
          .ant-list-item {
            padding: 4px 0;
            border: none;
          }
        }
      }
    }
  }

  .ant-tabs {
    .ant-tabs-tab {
      .ant-badge {
        .ant-badge-count {
          font-size: 10px;
          min-width: 16px;
          height: 16px;
          line-height: 16px;
        }
      }
    }
  }

  .ant-alert {
    .ant-alert-message {
      font-weight: 500;
    }
  }
}

// 深色主题
.dark-theme {
  .performance-analysis-panel {
    .ant-card {
      background: #2d2d2d;
      border-color: #404040;

      .ant-card-head {
        background: #2d2d2d;
        border-bottom-color: #404040;

        .ant-card-head-title {
          color: #ffffff;
        }
      }

      .ant-card-body {
        background: #2d2d2d;
        color: #cccccc;
      }
    }

    .performance-warnings {
      .warnings-header {
        border-bottom-color: #404040;
      }

      .ant-list {
        .ant-list-item {
          border-bottom-color: #404040;

          &:hover {
            background-color: #404040;
          }

          .ant-list-item-meta {
            .ant-list-item-meta-title {
              color: #ffffff;
            }

            .ant-list-item-meta-description {
              color: #cccccc;
            }
          }
        }
      }
    }

    .ant-descriptions {
      background: #2d2d2d;
      border-color: #404040;

      .ant-descriptions-item-label {
        background: #2d2d2d;
        color: #ffffff;
      }

      .ant-descriptions-item-content {
        background: #2d2d2d;
        color: #cccccc;
      }
    }

    .ant-statistic {
      .ant-statistic-title {
        color: #cccccc;
      }

      .ant-statistic-content {
        color: #ffffff;
      }
    }
  }
}

// 紧凑模式
.compact-theme {
  .performance-analysis-panel {
    .ant-modal-body {
      padding: 12px;
    }

    .ant-card {
      .ant-card-body {
        padding: 12px;
      }
    }

    .performance-overview {
      .ant-statistic {
        .ant-statistic-content {
          .ant-statistic-content-value {
            font-size: 20px;
          }
        }
      }

      .ant-descriptions {
        .ant-descriptions-item-label {
          width: 100px;
          font-size: 12px;
        }

        .ant-descriptions-item-content {
          font-size: 12px;
        }
      }
    }

    .performance-warnings {
      .warnings-header {
        margin-bottom: 12px;
        padding-bottom: 8px;
      }

      .ant-list {
        .ant-list-item {
          padding: 8px 12px;

          .ant-list-item-meta {
            .ant-list-item-meta-title {
              font-size: 13px;
            }

            .ant-list-item-meta-description {
              font-size: 11px;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .performance-analysis-panel {
    .ant-modal {
      width: 95% !important;
      max-width: none;
    }

    .performance-overview {
      .ant-row {
        .ant-col {
          margin-bottom: 12px;
        }
      }
    }

    .performance-charts {
      .ant-row {
        .ant-col {
          margin-bottom: 12px;
        }
      }
    }

    .ant-statistic {
      .ant-statistic-content {
        .ant-statistic-content-value {
          font-size: 18px;
        }
      }
    }

    .ant-descriptions {
      .ant-descriptions-item {
        padding: 8px 12px;
      }
    }
  }
}

@media (max-width: 480px) {
  .performance-analysis-panel {
    .performance-overview {
      .ant-row {
        .ant-col {
          flex: 0 0 100%;
          max-width: 100%;
        }
      }
    }

    .performance-charts {
      .ant-row {
        .ant-col {
          flex: 0 0 100%;
          max-width: 100%;
        }
      }
    }

    .ant-tabs {
      .ant-tabs-tab {
        padding: 8px 12px;
        font-size: 12px;
      }
    }

    .performance-warnings {
      .ant-list {
        .ant-list-item {
          .ant-list-item-action {
            margin-left: 8px;
          }
        }
      }
    }
  }
}

// 动画效果
.performance-analysis-panel {
  .ant-card {
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .ant-statistic {
    .ant-statistic-content-value {
      transition: color 0.3s ease;
    }
  }

  .performance-warnings {
    .ant-list-item {
      transition: background-color 0.2s ease;
    }
  }

  .ant-btn {
    transition: all 0.2s ease;
  }

  .ant-tag {
    transition: all 0.2s ease;
  }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
  .performance-analysis-panel {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .performance-analysis-panel {
    .ant-card {
      border: 2px solid #000;
    }

    .ant-list {
      .ant-list-item {
        border-bottom: 2px solid #000;
      }
    }

    .ant-descriptions {
      border: 2px solid #000;

      .ant-descriptions-item {
        border: 1px solid #000;
      }
    }

    .warnings-header {
      border-bottom: 2px solid #000;
    }
  }
}

// 性能指标颜色
.performance-analysis-panel {
  .performance-good {
    color: #52c41a;
  }

  .performance-warning {
    color: #faad14;
  }

  .performance-critical {
    color: #ff4d4f;
  }

  .memory-usage-bar {
    .ant-progress-bg {
      transition: background-color 0.3s ease;
    }
  }

  .fps-indicator {
    &.fps-good {
      color: #52c41a;
    }

    &.fps-warning {
      color: #faad14;
    }

    &.fps-critical {
      color: #ff4d4f;
    }
  }
}
