# 材质编辑器组件 (Material Editor)

一个功能完整的3D材质编辑器组件，支持实时预览和与底层dl-engine引擎的深度集成。

## 功能特性

### 🎨 材质类型支持
- **标准材质 (Standard)**: 基于物理的渲染材质
- **物理材质 (Physical/PBR)**: 完整的PBR材质支持
- **基础材质 (Basic)**: 简单的无光照材质
- **Lambert材质**: 漫反射材质
- **Phong材质**: 高光材质
- **卡通材质 (Toon)**: 卡通风格渲染

### 🖼️ 实时预览
- **多种几何体预览**: 球体、立方体、平面、圆柱体、圆环
- **环境光照**: 工作室、户外、室内三种预设环境
- **实时渲染**: 材质属性变化即时反映在预览中
- **交互式控制**: 支持旋转、缩放等预览操作

### 🎛️ 材质属性编辑
- **基础属性**: 颜色、金属度、粗糙度、自发光等
- **透明度控制**: 透明度开关和不透明度调节
- **面渲染**: 正面、背面、双面渲染选择
- **高级属性**: 线框模式、平面着色、顶点颜色等
- **PBR扩展**: 清漆层、透射、厚度等物理属性

### 🖼️ 纹理管理
- **多种纹理类型**: 颜色贴图、法线贴图、粗糙度贴图等
- **文件上传**: 支持拖拽上传和文件选择
- **实时应用**: 纹理加载后立即应用到预览
- **纹理预览**: 显示纹理缩略图和属性

### 🎨 颜色选择
- **颜色选择器**: 集成的颜色选择器组件
- **十六进制输入**: 支持直接输入颜色值
- **实时预览**: 颜色变化实时反映

### 💾 数据管理
- **保存/加载**: 材质数据的保存和加载
- **导入/导出**: 支持多种格式的材质导入导出
- **复制/粘贴**: 材质数据的复制和粘贴
- **缓存机制**: 智能缓存提高性能

## 使用方法

### 基础使用

```tsx
import MaterialEditor from './components/MaterialEditor';

function App() {
  const handleSave = (materialData) => {
    console.log('保存材质:', materialData);
  };

  const handleCancel = () => {
    console.log('取消编辑');
  };

  return (
    <MaterialEditor
      onSave={handleSave}
      onCancel={handleCancel}
    />
  );
}
```

### 编辑现有材质

```tsx
<MaterialEditor
  materialId="existing-material-id"
  onSave={handleSave}
  onCancel={handleCancel}
/>
```

### 只读模式

```tsx
<MaterialEditor
  materialId="material-id"
  readonly={true}
/>
```

## API 接口

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `materialId` | `string` | - | 要编辑的材质ID |
| `onSave` | `(material: Material) => void` | - | 保存回调函数 |
| `onCancel` | `() => void` | - | 取消回调函数 |
| `readonly` | `boolean` | `false` | 是否为只读模式 |

### Material 数据结构

```typescript
interface Material {
  id: string;
  name: string;
  type: MaterialType;
  color: string;
  metalness?: number;
  roughness?: number;
  emissive?: string;
  emissiveIntensity?: number;
  transparent?: boolean;
  opacity?: number;
  side?: 'front' | 'back' | 'double';
  textures?: Texture[];
  // ... 其他属性
}
```

### Texture 数据结构

```typescript
interface Texture {
  id: string;
  type: string;
  url: string;
}
```

## 集成说明

### 与dl-engine集成

材质编辑器深度集成了dl-engine引擎，提供：

1. **实时渲染**: 使用引擎的渲染管线进行实时预览
2. **材质系统**: 直接使用引擎的材质系统
3. **纹理加载**: 利用引擎的纹理加载器
4. **几何体创建**: 使用引擎的几何体工厂

### Redux状态管理

组件集成了Redux状态管理：

```typescript
// 从状态中获取材质数据
const material = useSelector((state: RootState) => 
  state.materials?.materials?.find(m => m.id === materialId)
);
```

### 国际化支持

支持多语言界面：

```typescript
const { t } = useTranslation();
// 使用翻译键
{t('editor.material.name')}
```

## 样式定制

### CSS类名

- `.material-editor`: 主容器
- `.material-editor-preview`: 预览区域
- `.material-editor-form`: 表单区域
- `.preview-canvas`: 预览画布
- `.texture-item`: 纹理项目

### 主题支持

支持亮色和暗色主题：

```less
[data-theme='dark'] .material-editor {
  // 暗色主题样式
}
```

## 性能优化

1. **懒加载**: 引擎动态导入，减少初始包大小
2. **缓存机制**: 材质数据智能缓存
3. **防抖更新**: 属性变化防抖处理
4. **虚拟滚动**: 大量纹理时使用虚拟滚动

## 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 依赖项

- React 18+
- Antd 5+
- Three.js (通过dl-engine)
- Redux Toolkit
- React-i18next

## 开发和测试

### 运行测试

```bash
npm test MaterialEditor
```

### 查看示例

```bash
npm run storybook
```

## 更新日志

### v2.0.0
- 完全重构，支持dl-engine集成
- 新增PBR材质支持
- 改进预览系统
- 添加纹理管理功能

### v1.0.0
- 初始版本
- 基础材质编辑功能
