/* 语言切换器样式 */

.language-switcher-select {
  min-width: 120px;
}

.language-switcher-button {
  display: flex;
  align-items: center;
  gap: 6px;
  border: none;
  background: transparent;
  transition: all 0.2s ease;
}

.language-switcher-button:hover {
  background: rgba(0, 0, 0, 0.04);
}

.language-switcher-button:focus {
  background: rgba(0, 0, 0, 0.06);
}

/* 语言标签 */
.language-label {
  display: flex;
  align-items: center;
  gap: 6px;
}

.language-flag {
  font-size: 16px;
  line-height: 1;
}

.language-native-name {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.language-english-name {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

/* 下拉菜单项 */
.language-menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-width: 160px;
}

.language-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.language-check-icon {
  color: #1890ff;
  font-size: 12px;
}

/* 语言设置面板 */
.language-settings-panel {
  padding: 24px;
  max-width: 600px;
}

.language-settings-panel h3 {
  margin-bottom: 8px;
  font-size: 18px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
}

.language-settings-panel h4 {
  margin-bottom: 12px;
  margin-top: 24px;
  font-size: 14px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
}

.language-settings-description {
  margin-bottom: 24px;
  color: rgba(0, 0, 0, 0.65);
  line-height: 1.6;
}

/* 当前语言信息 */
.language-settings-current {
  margin-bottom: 24px;
}

.current-language-info {
  padding: 16px;
  background: #f5f5f5;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
}

.current-language-flag {
  font-size: 24px;
}

.current-language-native {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.current-language-english {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

/* 语言切换器区域 */
.language-settings-switcher {
  margin-bottom: 24px;
}

/* 支持的语言列表 */
.language-settings-info {
  margin-bottom: 24px;
}

.supported-languages-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.supported-language-item {
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  transition: all 0.2s ease;
}

.supported-language-item:hover {
  background: #f0f0f0;
  border-color: #d9d9d9;
}

.supported-language-english {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}

.current-language-indicator {
  color: #52c41a;
  font-size: 12px;
}

/* 加载状态 */
.language-settings-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  text-align: center;
}

.language-settings-loading p {
  margin-top: 16px;
  color: rgba(0, 0, 0, 0.45);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .language-settings-panel {
    padding: 16px;
  }
  
  .supported-languages-list {
    grid-template-columns: 1fr;
  }
  
  .language-menu-item {
    min-width: 140px;
  }
  
  .current-language-info {
    padding: 12px;
  }
  
  .current-language-flag {
    font-size: 20px;
  }
}

/* 暗色主题支持 */
[data-theme='dark'] .language-switcher-button:hover {
  background: rgba(255, 255, 255, 0.08);
}

[data-theme='dark'] .language-switcher-button:focus {
  background: rgba(255, 255, 255, 0.12);
}

[data-theme='dark'] .language-native-name {
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .language-english-name {
  color: rgba(255, 255, 255, 0.45);
}

[data-theme='dark'] .language-settings-panel h3,
[data-theme='dark'] .language-settings-panel h4 {
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .language-settings-description {
  color: rgba(255, 255, 255, 0.65);
}

[data-theme='dark'] .current-language-info {
  background: #1f1f1f;
  border-color: #434343;
}

[data-theme='dark'] .current-language-native {
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .current-language-english {
  color: rgba(255, 255, 255, 0.45);
}

[data-theme='dark'] .supported-language-item {
  background: #1f1f1f;
  border-color: #434343;
}

[data-theme='dark'] .supported-language-item:hover {
  background: #262626;
  border-color: #595959;
}

[data-theme='dark'] .supported-language-english {
  color: rgba(255, 255, 255, 0.45);
}

[data-theme='dark'] .language-settings-loading p {
  color: rgba(255, 255, 255, 0.45);
}

/* RTL支持 */
[dir='rtl'] .language-label {
  flex-direction: row-reverse;
}

[dir='rtl'] .language-menu-item {
  flex-direction: row-reverse;
}

[dir='rtl'] .language-option {
  flex-direction: row-reverse;
}

[dir='rtl'] .current-language-info {
  text-align: right;
}

[dir='rtl'] .supported-languages-list {
  text-align: right;
}

/* 动画效果 */
.language-switcher-select .ant-select-selector {
  transition: all 0.2s ease;
}

.language-switcher-select:hover .ant-select-selector {
  border-color: #40a9ff;
}

.language-menu-item {
  transition: all 0.2s ease;
}

.language-check-icon {
  animation: checkIn 0.2s ease;
}

@keyframes checkIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .language-native-name {
    color: #000;
  }
  
  .language-english-name {
    color: #666;
  }
  
  .current-language-info {
    border-width: 2px;
  }
  
  .supported-language-item {
    border-width: 2px;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .language-switcher-button,
  .language-switcher-select .ant-select-selector,
  .language-menu-item,
  .supported-language-item {
    transition: none;
  }
  
  .language-check-icon {
    animation: none;
  }
}
