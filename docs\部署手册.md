# DL引擎部署手册

## 目录
1. [环境要求](#环境要求)
2. [开发环境部署](#开发环境部署)
3. [生产环境部署](#生产环境部署)
4. [Docker部署](#docker部署)
5. [Kubernetes部署](#kuberne<PERSON>部署)
6. [微服务配置](#微服务配置)
7. [数据库配置](#数据库配置)
8. [监控配置](#监控配置)
9. [安全配置](#安全配置)
10. [性能优化](#性能优化)
11. [故障排除](#故障排除)

## 环境要求

### 硬件要求

#### 开发环境
- **CPU**: Intel i5-8400 / AMD Ryzen 5 2600 或更高
- **内存**: 16GB RAM（最低8GB）
- **存储**: 100GB可用空间（SSD推荐）
- **网络**: 稳定的互联网连接

#### 生产环境
- **CPU**: Intel Xeon E5-2680 v4 / AMD EPYC 7402P 或更高
- **内存**: 64GB RAM（最低32GB）
- **存储**: 500GB SSD + 2TB HDD
- **网络**: 千兆网络连接
- **负载均衡**: 支持SSL终止的负载均衡器

### 软件要求

#### 基础软件
```bash
# 操作系统
Ubuntu 20.04 LTS / CentOS 8 / Windows Server 2019

# 容器运行时
Docker 20.10+
Docker Compose 2.0+

# 编排工具
Kubernetes 1.21+
Helm 3.7+

# 数据库
MySQL 8.0+ / PostgreSQL 13+
Redis 6.2+
MongoDB 5.0+

# 监控工具
Prometheus 2.30+
Grafana 8.0+
ELK Stack 7.15+
```

#### 开发工具
```bash
# 运行时环境
Node.js 18.x LTS
npm 8.x / yarn 1.22.x
Python 3.9+

# 构建工具
Git 2.30+
Docker Desktop
Visual Studio Code
```

## 开发环境部署

### 快速启动

#### 1. 克隆项目
```bash
# 克隆主仓库
git clone https://github.com/your-org/dl-engine.git
cd dl-engine

# 初始化子模块
git submodule update --init --recursive
```

#### 2. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

#### 3. 安装依赖
```bash
# 安装根目录依赖
npm install

# 安装引擎依赖
cd engine && npm install && cd ..

# 安装编辑器依赖
cd editor && npm install && cd ..

# 安装服务端依赖
cd server && npm install && cd ..
```

#### 4. 启动服务
```bash
# 启动数据库服务
docker-compose up -d mysql redis

# 启动后端服务
npm run start:server

# 启动前端服务
npm run start:editor

# 启动引擎开发服务器
npm run start:engine
```

### 详细配置

#### 环境变量配置
```bash
# .env 文件配置
NODE_ENV=development
PORT=3000

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_password
DB_DATABASE=dl_engine

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT配置
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=100MB

# 邮件配置
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your_app_password
```

#### 数据库初始化
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE dl_engine CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 运行迁移
npm run migration:run

# 填充初始数据
npm run seed:run
```

## 生产环境部署

### 服务器准备

#### 1. 系统更新
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
```

#### 2. 安装Docker
```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 添加用户到docker组
sudo usermod -aG docker $USER
```

#### 3. 安装Docker Compose
```bash
# 下载Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.12.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# 添加执行权限
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker-compose --version
```

### 应用部署

#### 1. 准备部署文件
```bash
# 创建部署目录
sudo mkdir -p /opt/dl-engine
cd /opt/dl-engine

# 复制部署文件
scp -r ./docker-compose.prod.yml user@server:/opt/dl-engine/
scp -r ./config/ user@server:/opt/dl-engine/
scp -r ./.env.prod user@server:/opt/dl-engine/.env
```

#### 2. 构建镜像
```bash
# 构建所有服务镜像
docker-compose -f docker-compose.prod.yml build

# 或者从仓库拉取
docker-compose -f docker-compose.prod.yml pull
```

#### 3. 启动服务
```bash
# 启动所有服务
docker-compose -f docker-compose.prod.yml up -d

# 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f
```

## Docker部署

### Docker Compose配置

#### 生产环境配置文件
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  # 前端编辑器
  editor:
    build:
      context: ./editor
      dockerfile: Dockerfile.prod
    container_name: dl-engine-editor
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api-gateway
    networks:
      - dl-engine-network

  # API网关
  api-gateway:
    build:
      context: ./server/api-gateway
      dockerfile: Dockerfile
    container_name: dl-engine-api-gateway
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
    volumes:
      - ./logs:/app/logs
    depends_on:
      - mysql
      - redis
    networks:
      - dl-engine-network

  # 用户服务
  user-service:
    build:
      context: ./server/user-service
      dockerfile: Dockerfile
    container_name: dl-engine-user-service
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
    networks:
      - dl-engine-network

  # 项目服务
  project-service:
    build:
      context: ./server/project-service
      dockerfile: Dockerfile
    container_name: dl-engine-project-service
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - REDIS_HOST=redis
    volumes:
      - ./uploads:/app/uploads
    depends_on:
      - mysql
      - redis
    networks:
      - dl-engine-network

  # 数据库
  mysql:
    image: mysql:8.0
    container_name: dl-engine-mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=dl_engine
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - dl-engine-network

  # Redis缓存
  redis:
    image: redis:7.0-alpine
    container_name: dl-engine-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - dl-engine-network

volumes:
  mysql_data:
  redis_data:

networks:
  dl-engine-network:
    driver: bridge
```

#### Dockerfile示例
```dockerfile
# editor/Dockerfile.prod
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80 443
CMD ["nginx", "-g", "daemon off;"]
```

### 容器管理

#### 服务管理命令
```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启特定服务
docker-compose restart user-service

# 查看服务日志
docker-compose logs -f api-gateway

# 进入容器
docker-compose exec mysql bash

# 更新服务
docker-compose pull
docker-compose up -d --force-recreate
```

#### 数据备份
```bash
# 数据库备份
docker-compose exec mysql mysqldump -u root -p dl_engine > backup.sql

# Redis备份
docker-compose exec redis redis-cli BGSAVE

# 文件备份
tar -czf uploads_backup.tar.gz ./uploads/
```

## Kubernetes部署

### 集群准备

#### 1. 安装Kubernetes
```bash
# 使用kubeadm安装（主节点）
sudo kubeadm init --pod-network-cidr=10.244.0.0/16

# 配置kubectl
mkdir -p $HOME/.kube
sudo cp -i /etc/kubernetes/admin.conf $HOME/.kube/config
sudo chown $(id -u):$(id -g) $HOME/.kube/config

# 安装网络插件（Flannel）
kubectl apply -f https://raw.githubusercontent.com/coreos/flannel/master/Documentation/kube-flannel.yml
```

#### 2. 安装Helm
```bash
# 下载Helm
curl https://get.helm.sh/helm-v3.10.0-linux-amd64.tar.gz -o helm.tar.gz
tar -zxvf helm.tar.gz
sudo mv linux-amd64/helm /usr/local/bin/helm

# 验证安装
helm version
```

### 应用部署

#### 1. 创建命名空间
```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: dl-engine
  labels:
    name: dl-engine
```

#### 2. 配置ConfigMap
```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: dl-engine-config
  namespace: dl-engine
data:
  NODE_ENV: "production"
  DB_HOST: "mysql-service"
  DB_PORT: "3306"
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
```

#### 3. 配置Secret
```yaml
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: dl-engine-secret
  namespace: dl-engine
type: Opaque
data:
  DB_PASSWORD: <base64-encoded-password>
  JWT_SECRET: <base64-encoded-jwt-secret>
  REDIS_PASSWORD: <base64-encoded-redis-password>
```

#### 4. 数据库部署
```yaml
# mysql-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql
  namespace: dl-engine
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mysql
  template:
    metadata:
      labels:
        app: mysql
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        env:
        - name: MYSQL_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dl-engine-secret
              key: DB_PASSWORD
        - name: MYSQL_DATABASE
          value: "dl_engine"
        ports:
        - containerPort: 3306
        volumeMounts:
        - name: mysql-storage
          mountPath: /var/lib/mysql
      volumes:
      - name: mysql-storage
        persistentVolumeClaim:
          claimName: mysql-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: mysql-service
  namespace: dl-engine
spec:
  selector:
    app: mysql
  ports:
  - port: 3306
    targetPort: 3306
```

#### 5. 应用服务部署
```yaml
# api-gateway-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  namespace: dl-engine
spec:
  replicas: 3
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
    spec:
      containers:
      - name: api-gateway
        image: dl-engine/api-gateway:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: NODE_ENV
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: DB_HOST
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: api-gateway-service
  namespace: dl-engine
spec:
  selector:
    app: api-gateway
  ports:
  - port: 80
    targetPort: 3000
  type: LoadBalancer
```

#### 6. Ingress配置
```yaml
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dl-engine-ingress
  namespace: dl-engine
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - dl-engine.com
    - api.dl-engine.com
    secretName: dl-engine-tls
  rules:
  - host: dl-engine.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: editor-service
            port:
              number: 80
  - host: api.dl-engine.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: api-gateway-service
            port:
              number: 80
```

### Helm Chart部署

#### 1. Chart结构
```
dl-engine-chart/
├── Chart.yaml
├── values.yaml
├── templates/
│   ├── deployment.yaml
│   ├── service.yaml
│   ├── ingress.yaml
│   ├── configmap.yaml
│   └── secret.yaml
└── charts/
```

#### 2. Chart.yaml
```yaml
apiVersion: v2
name: dl-engine
description: DL Engine Helm Chart
version: 1.0.0
appVersion: "1.0.0"
dependencies:
- name: mysql
  version: 9.4.1
  repository: https://charts.bitnami.com/bitnami
- name: redis
  version: 17.3.7
  repository: https://charts.bitnami.com/bitnami
```

#### 3. values.yaml
```yaml
# 全局配置
global:
  imageRegistry: ""
  imagePullSecrets: []

# 应用配置
app:
  name: dl-engine
  version: "1.0.0"

# 副本数配置
replicaCount:
  apiGateway: 3
  userService: 2
  projectService: 2

# 镜像配置
image:
  repository: dl-engine
  tag: "latest"
  pullPolicy: IfNotPresent

# 服务配置
service:
  type: LoadBalancer
  port: 80

# Ingress配置
ingress:
  enabled: true
  className: "nginx"
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
  hosts:
    - host: dl-engine.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: dl-engine-tls
      hosts:
        - dl-engine.com

# 资源限制
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

# 数据库配置
mysql:
  enabled: true
  auth:
    rootPassword: "your-root-password"
    database: "dl_engine"

# Redis配置
redis:
  enabled: true
  auth:
    enabled: false
```

#### 4. 部署命令
```bash
# 添加依赖仓库
helm repo add bitnami https://charts.bitnami.com/bitnami
helm repo update

# 安装Chart
helm install dl-engine ./dl-engine-chart -n dl-engine --create-namespace

# 升级Chart
helm upgrade dl-engine ./dl-engine-chart -n dl-engine

# 卸载Chart
helm uninstall dl-engine -n dl-engine
```

## 微服务配置

### 服务注册与发现

#### 1. Consul配置
```yaml
# consul-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: consul
  namespace: dl-engine
spec:
  replicas: 3
  selector:
    matchLabels:
      app: consul
  template:
    metadata:
      labels:
        app: consul
    spec:
      containers:
      - name: consul
        image: consul:1.13
        ports:
        - containerPort: 8500
        - containerPort: 8600
        env:
        - name: CONSUL_BIND_INTERFACE
          value: "eth0"
        command:
        - consul
        - agent
        - -server
        - -bootstrap-expect=3
        - -ui
        - -client=0.0.0.0
```

#### 2. 服务配置
```javascript
// 服务注册配置
const consul = require('consul')({
  host: process.env.CONSUL_HOST || 'consul-service',
  port: process.env.CONSUL_PORT || 8500
});

// 注册服务
const registerService = async () => {
  await consul.agent.service.register({
    name: 'user-service',
    id: `user-service-${process.env.HOSTNAME}`,
    address: process.env.SERVICE_HOST,
    port: parseInt(process.env.SERVICE_PORT),
    check: {
      http: `http://${process.env.SERVICE_HOST}:${process.env.SERVICE_PORT}/health`,
      interval: '10s'
    }
  });
};
```

### API网关配置

#### 1. 路由配置
```yaml
# api-gateway配置
routes:
  - path: /api/users/*
    service: user-service
    load_balancer: round_robin
    timeout: 30s
    retry: 3

  - path: /api/projects/*
    service: project-service
    load_balancer: least_connections
    timeout: 60s
    retry: 2

  - path: /api/ai/*
    service: ai-service
    load_balancer: weighted_round_robin
    timeout: 120s
    retry: 1

# 中间件配置
middleware:
  - name: cors
    config:
      origins: ["*"]
      methods: ["GET", "POST", "PUT", "DELETE"]

  - name: rate_limit
    config:
      requests_per_minute: 100
      burst: 20

  - name: auth
    config:
      jwt_secret: ${JWT_SECRET}
      excluded_paths: ["/api/auth/login", "/api/health"]
```

#### 2. 负载均衡配置
```javascript
// 负载均衡策略
const loadBalancers = {
  round_robin: new RoundRobinBalancer(),
  least_connections: new LeastConnectionsBalancer(),
  weighted_round_robin: new WeightedRoundRobinBalancer(),
  ip_hash: new IPHashBalancer()
};

// 健康检查
const healthCheck = async (service) => {
  try {
    const response = await axios.get(`${service.url}/health`, {
      timeout: 5000
    });
    return response.status === 200;
  } catch (error) {
    return false;
  }
};
```

### 配置管理

#### 1. 配置中心
```yaml
# config-server配置
spring:
  cloud:
    config:
      server:
        git:
          uri: https://github.com/your-org/dl-engine-config
          search-paths: configs/{application}
          default-label: main
        encrypt:
          enabled: true
```

#### 2. 动态配置
```javascript
// 配置监听
const configWatcher = new ConfigWatcher({
  consul: consulClient,
  key: 'dl-engine/config',
  onChange: (newConfig) => {
    // 更新应用配置
    updateAppConfig(newConfig);

    // 重新加载服务
    reloadServices();
  }
});
```

## 数据库配置

### MySQL主从配置

#### 1. 主库配置
```ini
# my.cnf (主库)
[mysqld]
server-id = 1
log-bin = mysql-bin
binlog-format = ROW
binlog-do-db = dl_engine
expire_logs_days = 7

# 性能优化
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
sync_binlog = 0

# 连接配置
max_connections = 1000
max_connect_errors = 10000
```

#### 2. 从库配置
```ini
# my.cnf (从库)
[mysqld]
server-id = 2
relay-log = mysql-relay-bin
log-slave-updates = 1
read-only = 1

# 复制配置
slave-skip-errors = 1062,1053,1146
slave_parallel_workers = 4
slave_parallel_type = LOGICAL_CLOCK
```

#### 3. 主从同步设置
```sql
-- 主库创建复制用户
CREATE USER 'replication'@'%' IDENTIFIED BY 'replication_password';
GRANT REPLICATION SLAVE ON *.* TO 'replication'@'%';
FLUSH PRIVILEGES;

-- 从库配置主库信息
CHANGE MASTER TO
  MASTER_HOST='mysql-master',
  MASTER_USER='replication',
  MASTER_PASSWORD='replication_password',
  MASTER_LOG_FILE='mysql-bin.000001',
  MASTER_LOG_POS=154;

-- 启动从库
START SLAVE;

-- 检查同步状态
SHOW SLAVE STATUS\G
```

### Redis集群配置

#### 1. Redis Cluster配置
```conf
# redis.conf
port 7000
cluster-enabled yes
cluster-config-file nodes.conf
cluster-node-timeout 5000
appendonly yes
appendfsync everysec

# 内存配置
maxmemory 2gb
maxmemory-policy allkeys-lru

# 网络配置
bind 0.0.0.0
protected-mode no
```

#### 2. 集群初始化
```bash
# 创建Redis集群
redis-cli --cluster create \
  redis-1:7000 redis-2:7000 redis-3:7000 \
  redis-4:7000 redis-5:7000 redis-6:7000 \
  --cluster-replicas 1

# 检查集群状态
redis-cli --cluster check redis-1:7000
```

### 数据库监控

#### 1. MySQL监控
```yaml
# mysql-exporter配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql-exporter
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mysql-exporter
  template:
    metadata:
      labels:
        app: mysql-exporter
    spec:
      containers:
      - name: mysql-exporter
        image: prom/mysqld-exporter:latest
        ports:
        - containerPort: 9104
        env:
        - name: DATA_SOURCE_NAME
          value: "exporter:password@(mysql-service:3306)/"
```

#### 2. Redis监控
```yaml
# redis-exporter配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-exporter
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis-exporter
  template:
    metadata:
      labels:
        app: redis-exporter
    spec:
      containers:
      - name: redis-exporter
        image: oliver006/redis_exporter:latest
        ports:
        - containerPort: 9121
        env:
        - name: REDIS_ADDR
          value: "redis://redis-service:6379"
```

## 监控配置

### Prometheus配置

#### 1. Prometheus部署
```yaml
# prometheus-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      containers:
      - name: prometheus
        image: prom/prometheus:latest
        ports:
        - containerPort: 9090
        volumeMounts:
        - name: prometheus-config
          mountPath: /etc/prometheus
        - name: prometheus-data
          mountPath: /prometheus
        command:
        - prometheus
        - --config.file=/etc/prometheus/prometheus.yml
        - --storage.tsdb.path=/prometheus
        - --web.console.libraries=/etc/prometheus/console_libraries
        - --web.console.templates=/etc/prometheus/consoles
        - --storage.tsdb.retention.time=15d
        - --web.enable-lifecycle
      volumes:
      - name: prometheus-config
        configMap:
          name: prometheus-config
      - name: prometheus-data
        persistentVolumeClaim:
          claimName: prometheus-pvc
```

#### 2. Prometheus配置文件
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # 应用服务监控
  - job_name: 'dl-engine-services'
    kubernetes_sd_configs:
    - role: endpoints
      namespaces:
        names:
        - dl-engine
    relabel_configs:
    - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scrape]
      action: keep
      regex: true

  # MySQL监控
  - job_name: 'mysql'
    static_configs:
    - targets: ['mysql-exporter:9104']

  # Redis监控
  - job_name: 'redis'
    static_configs:
    - targets: ['redis-exporter:9121']

  # Kubernetes监控
  - job_name: 'kubernetes-nodes'
    kubernetes_sd_configs:
    - role: node
    relabel_configs:
    - action: labelmap
      regex: __meta_kubernetes_node_label_(.+)
```

### Grafana配置

#### 1. Grafana部署
```yaml
# grafana-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
      - name: grafana
        image: grafana/grafana:latest
        ports:
        - containerPort: 3000
        env:
        - name: GF_SECURITY_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grafana-secret
              key: admin-password
        volumeMounts:
        - name: grafana-data
          mountPath: /var/lib/grafana
        - name: grafana-config
          mountPath: /etc/grafana/provisioning
      volumes:
      - name: grafana-data
        persistentVolumeClaim:
          claimName: grafana-pvc
      - name: grafana-config
        configMap:
          name: grafana-config
```

#### 2. 数据源配置
```yaml
# grafana-datasources.yml
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true

  - name: Loki
    type: loki
    access: proxy
    url: http://loki:3100

  - name: MySQL
    type: mysql
    url: mysql:3306
    database: dl_engine
    user: grafana
    secureJsonData:
      password: grafana_password
```

### 日志收集

#### 1. ELK Stack部署
```yaml
# elasticsearch-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: elasticsearch
  namespace: logging
spec:
  replicas: 3
  selector:
    matchLabels:
      app: elasticsearch
  template:
    metadata:
      labels:
        app: elasticsearch
    spec:
      containers:
      - name: elasticsearch
        image: docker.elastic.co/elasticsearch/elasticsearch:7.15.0
        ports:
        - containerPort: 9200
        - containerPort: 9300
        env:
        - name: discovery.type
          value: single-node
        - name: ES_JAVA_OPTS
          value: "-Xms2g -Xmx2g"
        volumeMounts:
        - name: elasticsearch-data
          mountPath: /usr/share/elasticsearch/data
      volumes:
      - name: elasticsearch-data
        persistentVolumeClaim:
          claimName: elasticsearch-pvc
```

#### 2. Logstash配置
```yaml
# logstash.conf
input {
  beats {
    port => 5044
  }
}

filter {
  if [fields][service] == "dl-engine" {
    grok {
      match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:level} %{GREEDYDATA:message}" }
    }

    date {
      match => [ "timestamp", "ISO8601" ]
    }

    mutate {
      remove_field => [ "timestamp" ]
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "dl-engine-logs-%{+YYYY.MM.dd}"
  }
}
```

#### 3. Filebeat配置
```yaml
# filebeat.yml
filebeat.inputs:
- type: container
  paths:
    - /var/log/containers/*dl-engine*.log
  fields:
    service: dl-engine
  fields_under_root: true

output.logstash:
  hosts: ["logstash:5044"]

processors:
- add_kubernetes_metadata:
    host: ${NODE_NAME}
    matchers:
    - logs_path:
        logs_path: "/var/log/containers/"
```

## 安全配置

### SSL/TLS配置

#### 1. 证书管理
```yaml
# cert-manager安装
kubectl apply -f https://github.com/jetstack/cert-manager/releases/download/v1.6.1/cert-manager.yaml

# ClusterIssuer配置
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
```

#### 2. 网络策略
```yaml
# network-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: dl-engine-network-policy
  namespace: dl-engine
spec:
  podSelector:
    matchLabels:
      app: api-gateway
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 3000
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: mysql
    ports:
    - protocol: TCP
      port: 3306
```

### 身份认证

#### 1. RBAC配置
```yaml
# rbac.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: dl-engine
  name: dl-engine-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: dl-engine-rolebinding
  namespace: dl-engine
subjects:
- kind: ServiceAccount
  name: dl-engine-serviceaccount
  namespace: dl-engine
roleRef:
  kind: Role
  name: dl-engine-role
  apiGroup: rbac.authorization.k8s.io
```

#### 2. Pod安全策略
```yaml
# pod-security-policy.yaml
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: dl-engine-psp
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
```

## 性能优化

### 应用层优化

#### 1. 缓存策略
```javascript
// Redis缓存配置
const cacheConfig = {
  // 用户会话缓存
  session: {
    ttl: 3600, // 1小时
    prefix: 'session:'
  },

  // API响应缓存
  api: {
    ttl: 300, // 5分钟
    prefix: 'api:'
  },

  // 静态资源缓存
  static: {
    ttl: 86400, // 24小时
    prefix: 'static:'
  }
};

// 缓存中间件
const cacheMiddleware = (ttl, prefix) => {
  return async (req, res, next) => {
    const key = `${prefix}${req.originalUrl}`;
    const cached = await redis.get(key);

    if (cached) {
      return res.json(JSON.parse(cached));
    }

    res.sendResponse = res.json;
    res.json = (body) => {
      redis.setex(key, ttl, JSON.stringify(body));
      res.sendResponse(body);
    };

    next();
  };
};
```

#### 2. 数据库优化
```sql
-- 索引优化
CREATE INDEX idx_user_email ON users(email);
CREATE INDEX idx_project_user_id ON projects(user_id);
CREATE INDEX idx_scene_project_id ON scenes(project_id);

-- 分区表
CREATE TABLE user_activities (
    id BIGINT AUTO_INCREMENT,
    user_id INT,
    activity_type VARCHAR(50),
    created_at TIMESTAMP,
    PRIMARY KEY (id, created_at)
) PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026)
);

-- 查询优化
EXPLAIN SELECT * FROM projects
WHERE user_id = 123
AND status = 'active'
ORDER BY updated_at DESC
LIMIT 10;
```

### 基础设施优化

#### 1. 资源限制
```yaml
# 资源配置
resources:
  requests:
    memory: "256Mi"
    cpu: "250m"
  limits:
    memory: "512Mi"
    cpu: "500m"

# HPA配置
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-gateway-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-gateway
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

#### 2. 存储优化
```yaml
# 高性能存储类
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: fast-ssd
provisioner: kubernetes.io/aws-ebs
parameters:
  type: gp3
  iops: "3000"
  throughput: "125"
allowVolumeExpansion: true
volumeBindingMode: WaitForFirstConsumer
```

## 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查Pod状态
kubectl get pods -n dl-engine

# 查看Pod日志
kubectl logs -f pod-name -n dl-engine

# 查看Pod事件
kubectl describe pod pod-name -n dl-engine

# 检查配置
kubectl get configmap -n dl-engine
kubectl get secret -n dl-engine
```

#### 2. 数据库连接问题
```bash
# 测试数据库连接
kubectl exec -it mysql-pod -n dl-engine -- mysql -u root -p

# 检查网络连通性
kubectl exec -it api-gateway-pod -n dl-engine -- nslookup mysql-service

# 查看数据库日志
kubectl logs -f mysql-pod -n dl-engine
```

#### 3. 性能问题排查
```bash
# 查看资源使用情况
kubectl top pods -n dl-engine
kubectl top nodes

# 查看HPA状态
kubectl get hpa -n dl-engine

# 查看网络策略
kubectl get networkpolicy -n dl-engine
```

### 监控告警

#### 1. 告警规则
```yaml
# alert-rules.yml
groups:
- name: dl-engine-alerts
  rules:
  - alert: HighCPUUsage
    expr: cpu_usage_percent > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage detected"
      description: "CPU usage is above 80% for more than 5 minutes"

  - alert: HighMemoryUsage
    expr: memory_usage_percent > 85
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High memory usage detected"
      description: "Memory usage is above 85% for more than 5 minutes"

  - alert: DatabaseConnectionFailed
    expr: mysql_up == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Database connection failed"
      description: "Cannot connect to MySQL database"
```

#### 2. 告警通知
```yaml
# alertmanager.yml
global:
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'

receivers:
- name: 'web.hook'
  email_configs:
  - to: '<EMAIL>'
    subject: 'DL Engine Alert: {{ .GroupLabels.alertname }}'
    body: |
      {{ range .Alerts }}
      Alert: {{ .Annotations.summary }}
      Description: {{ .Annotations.description }}
      {{ end }}
```

---

*本部署手册涵盖了DL引擎的完整部署流程，如有问题请联系运维团队*