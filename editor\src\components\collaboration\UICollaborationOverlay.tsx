/**
 * UICollaborationOverlay.tsx
 * 
 * UI协作编辑覆盖层，显示其他用户的光标、选择和编辑状态
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Avatar, Tooltip, Badge } from 'antd';
import { UserOutlined, LockOutlined, EditOutlined } from '@ant-design/icons';
import { uiCollaborationService, UserCursor, UserSelection } from '../../services/UICollaborationService';
import './UICollaborationOverlay.module.css';

/**
 * 用户颜色映射
 */
const USER_COLORS = [
  '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1',
  '#13c2c2', '#eb2f96', '#fa541c', '#a0d911', '#2f54eb'
];

/**
 * 获取用户颜色
 */
const getUserColor = (userId: string): string => {
  const hash = userId.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);
  return USER_COLORS[Math.abs(hash) % USER_COLORS.length];
};

/**
 * 用户光标组件
 */
const UserCursorComponent: React.FC<{ cursor: UserCursor }> = ({ cursor }) => {
  const color = getUserColor(cursor.userId);
  
  return (
    <div
      className="user-cursor"
      style={{
        position: 'absolute',
        left: cursor.position.x,
        top: cursor.position.y,
        pointerEvents: 'none',
        zIndex: 10000
      }}
    >
      {/* 光标指针 */}
      <div
        className="cursor-pointer"
        style={{
          width: 0,
          height: 0,
          borderLeft: '8px solid transparent',
          borderRight: '8px solid transparent',
          borderBottom: `12px solid ${color}`,
          transform: 'rotate(-45deg)',
          transformOrigin: 'center bottom'
        }}
      />
      
      {/* 用户标签 */}
      <div
        className="cursor-label"
        style={{
          position: 'absolute',
          left: 12,
          top: -8,
          background: color,
          color: 'white',
          padding: '2px 6px',
          borderRadius: '4px',
          fontSize: '11px',
          fontWeight: 500,
          whiteSpace: 'nowrap',
          boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
        }}
      >
        {cursor.userName}
        {cursor.action && (
          <span style={{ marginLeft: 4, opacity: 0.8 }}>
            {cursor.action}
          </span>
        )}
      </div>
    </div>
  );
};

/**
 * 用户选择框组件
 */
const UserSelectionComponent: React.FC<{ 
  selection: UserSelection;
  getComponentBounds: (componentId: string) => DOMRect | null;
}> = ({ selection, getComponentBounds }) => {
  const color = getUserColor(selection.userId);
  
  return (
    <>
      {selection.componentIds.map(componentId => {
        const bounds = getComponentBounds(componentId);
        if (!bounds) return null;
        
        return (
          <div
            key={`${selection.userId}-${componentId}`}
            className="user-selection"
            style={{
              position: 'absolute',
              left: bounds.left,
              top: bounds.top,
              width: bounds.width,
              height: bounds.height,
              border: `2px solid ${color}`,
              borderRadius: '4px',
              pointerEvents: 'none',
              zIndex: 9999,
              background: `${color}10`
            }}
          >
            {/* 用户标签 */}
            <div
              className="selection-label"
              style={{
                position: 'absolute',
                top: -24,
                left: 0,
                background: color,
                color: 'white',
                padding: '2px 6px',
                borderRadius: '4px',
                fontSize: '11px',
                fontWeight: 500,
                whiteSpace: 'nowrap',
                boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
              }}
            >
              {selection.userName}
            </div>
          </div>
        );
      })}
    </>
  );
};

/**
 * 组件锁定指示器
 */
const ComponentLockIndicator: React.FC<{
  componentId: string;
  lockingUserId: string;
  lockingUserName: string;
  getComponentBounds: (componentId: string) => DOMRect | null;
}> = ({ componentId, lockingUserId, lockingUserName, getComponentBounds }) => {
  const bounds = getComponentBounds(componentId);
  if (!bounds) return null;
  
  const color = getUserColor(lockingUserId);
  
  return (
    <div
      className="component-lock-indicator"
      style={{
        position: 'absolute',
        left: bounds.left,
        top: bounds.top,
        width: bounds.width,
        height: bounds.height,
        border: `2px dashed ${color}`,
        borderRadius: '4px',
        pointerEvents: 'none',
        zIndex: 9998,
        background: `${color}05`
      }}
    >
      {/* 锁定图标 */}
      <div
        style={{
          position: 'absolute',
          top: -12,
          right: -12,
          width: 24,
          height: 24,
          background: color,
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
        }}
      >
        <LockOutlined style={{ color: 'white', fontSize: 12 }} />
      </div>
      
      {/* 锁定用户提示 */}
      <Tooltip title={`正在被 ${lockingUserName} 编辑`}>
        <div
          style={{
            position: 'absolute',
            top: -24,
            right: 0,
            background: color,
            color: 'white',
            padding: '2px 6px',
            borderRadius: '4px',
            fontSize: '11px',
            fontWeight: 500,
            whiteSpace: 'nowrap',
            boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
          }}
        >
          <EditOutlined style={{ marginRight: 4 }} />
          {lockingUserName}
        </div>
      </Tooltip>
    </div>
  );
};

/**
 * 在线用户列表
 */
const OnlineUsersList: React.FC<{
  users: Array<{ id: string; name: string; avatar?: string }>;
  currentUserId: string;
}> = ({ users, currentUserId }) => {
  return (
    <div className="online-users-list">
      <div className="users-header">
        <span>在线用户 ({users.length})</span>
      </div>
      <div className="users-avatars">
        {users.map(user => {
          const color = getUserColor(user.id);
          const isCurrentUser = user.id === currentUserId;
          
          return (
            <Tooltip key={user.id} title={user.name}>
              <Badge
                dot={!isCurrentUser}
                color={color}
                offset={[-2, 2]}
              >
                <Avatar
                  size="small"
                  src={user.avatar}
                  icon={<UserOutlined />}
                  style={{
                    backgroundColor: color,
                    border: isCurrentUser ? '2px solid #fff' : 'none',
                    boxShadow: isCurrentUser ? '0 0 0 2px #1890ff' : 'none'
                  }}
                />
              </Badge>
            </Tooltip>
          );
        })}
      </div>
    </div>
  );
};

/**
 * UI协作编辑覆盖层属性
 */
export interface UICollaborationOverlayProps {
  /** 编辑器容器引用 */
  editorRef: React.RefObject<HTMLElement>;
  /** 当前用户ID */
  currentUserId: string;
  /** 在线用户列表 */
  onlineUsers: Array<{ id: string; name: string; avatar?: string }>;
  /** 获取组件边界的函数 */
  getComponentBounds: (componentId: string) => DOMRect | null;
  /** 获取锁定用户名的函数 */
  getLockingUserName: (userId: string) => string;
  /** 是否显示在线用户列表 */
  showOnlineUsers?: boolean;
}

/**
 * UI协作编辑覆盖层
 */
export const UICollaborationOverlay: React.FC<UICollaborationOverlayProps> = ({
  editorRef,
  currentUserId,
  onlineUsers,
  getComponentBounds,
  getLockingUserName,
  showOnlineUsers = true
}) => {
  const [userCursors, setUserCursors] = useState<UserCursor[]>([]);
  const [userSelections, setUserSelections] = useState<UserSelection[]>([]);
  const [lockedComponents, setLockedComponents] = useState<Map<string, string>>(new Map());
  
  const overlayRef = useRef<HTMLDivElement>(null);

  // 更新用户光标
  const updateCursors = useCallback(() => {
    setUserCursors(uiCollaborationService.getUserCursors());
  }, []);

  // 更新用户选择
  const updateSelections = useCallback(() => {
    setUserSelections(uiCollaborationService.getUserSelections());
  }, []);

  // 更新锁定组件
  const updateLockedComponents = useCallback(() => {
    const locked = new Map<string, string>();
    // 这里应该从服务中获取锁定的组件信息
    setLockedComponents(locked);
  }, []);

  // 设置事件监听器
  useEffect(() => {
    const service = uiCollaborationService;

    // 光标事件
    service.on('remoteCursorUpdated', updateCursors);
    service.on('cursorUpdated', updateCursors);

    // 选择事件
    service.on('remoteSelectionUpdated', updateSelections);
    service.on('selectionUpdated', updateSelections);

    // 锁定事件
    service.on('remoteComponentLocked', updateLockedComponents);
    service.on('remoteComponentUnlocked', updateLockedComponents);
    service.on('componentLocked', updateLockedComponents);
    service.on('componentUnlocked', updateLockedComponents);

    // 初始化数据
    updateCursors();
    updateSelections();
    updateLockedComponents();

    return () => {
      service.off('remoteCursorUpdated', updateCursors);
      service.off('cursorUpdated', updateCursors);
      service.off('remoteSelectionUpdated', updateSelections);
      service.off('selectionUpdated', updateSelections);
      service.off('remoteComponentLocked', updateLockedComponents);
      service.off('remoteComponentUnlocked', updateLockedComponents);
      service.off('componentLocked', updateLockedComponents);
      service.off('componentUnlocked', updateLockedComponents);
    };
  }, [updateCursors, updateSelections, updateLockedComponents]);

  // 处理鼠标移动
  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!editorRef.current) return;

    const rect = editorRef.current.getBoundingClientRect();
    const position = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    };

    // 检测鼠标下的组件
    const element = document.elementFromPoint(event.clientX, event.clientY);
    const componentId = element?.getAttribute('data-component-id');

    uiCollaborationService.updateCursor(position, componentId);
  }, [editorRef]);

  // 绑定鼠标事件
  useEffect(() => {
    const editor = editorRef.current;
    if (!editor) return;

    editor.addEventListener('mousemove', handleMouseMove);
    
    return () => {
      editor.removeEventListener('mousemove', handleMouseMove);
    };
  }, [editorRef, handleMouseMove]);

  return (
    <div ref={overlayRef} className="ui-collaboration-overlay">
      {/* 在线用户列表 */}
      {showOnlineUsers && (
        <div className="online-users-container">
          <OnlineUsersList
            users={onlineUsers}
            currentUserId={currentUserId}
          />
        </div>
      )}

      {/* 用户光标 */}
      {userCursors.map(cursor => (
        <UserCursorComponent
          key={cursor.userId}
          cursor={cursor}
        />
      ))}

      {/* 用户选择 */}
      {userSelections.map(selection => (
        <UserSelectionComponent
          key={selection.userId}
          selection={selection}
          getComponentBounds={getComponentBounds}
        />
      ))}

      {/* 组件锁定指示器 */}
      {Array.from(lockedComponents.entries()).map(([componentId, userId]) => (
        <ComponentLockIndicator
          key={componentId}
          componentId={componentId}
          lockingUserId={userId}
          lockingUserName={getLockingUserName(userId)}
          getComponentBounds={getComponentBounds}
        />
      ))}
    </div>
  );
};

export default UICollaborationOverlay;
