.blockchain-panel {
  .wallet-disconnected {
    text-align: center;
    padding: 24px 16px;

    .wallet-status {
      margin-bottom: 24px;

      .anticon {
        margin-bottom: 16px;
      }

      h4 {
        margin-bottom: 8px;
      }

      p {
        margin-bottom: 0;
        font-size: 14px;
      }
    }
  }

  .wallet-connected {
    .account-info,
    .network-info,
    .balance-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 0;

      .ant-space {
        flex: 1;
      }
    }

    .balance-info {
      .ant-typography {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
      }
    }
  }

  .ant-card-head {
    .ant-card-head-title {
      font-size: 14px;
      font-weight: 600;
    }

    .ant-card-extra {
      .ant-btn {
        border: none;
        box-shadow: none;
      }
    }
  }

  .ant-card-body {
    padding: 16px;
  }

  .ant-divider {
    margin: 12px 0;
  }

  .ant-tag {
    margin: 0;
    font-size: 12px;
    border-radius: 4px;
  }

  .ant-alert {
    border-radius: 6px;
    font-size: 13px;
  }

  .ant-btn {
    border-radius: 6px;
    font-weight: 500;

    &.ant-btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

      &:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
      }
    }

    &.ant-btn-default {
      border-color: #d9d9d9;
      color: #666;

      &:hover {
        border-color: #40a9ff;
        color: #40a9ff;
      }
    }
  }

  .ant-space {
    &.ant-space-vertical {
      width: 100%;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .wallet-connected {
      .account-info,
      .network-info,
      .balance-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
      }
    }

    .ant-space {
      flex-direction: column;
      width: 100%;

      .ant-btn {
        width: 100%;
      }
    }
  }

  // 暗色主题支持
  .dark-theme & {
    .wallet-disconnected {
      .wallet-status {
        .anticon {
          color: #595959;
        }
      }
    }

    .ant-card {
      background: #1f1f1f;
      border-color: #303030;
    }

    .ant-tag {
      background: #262626;
      border-color: #434343;
      color: #fff;

      &.ant-tag-blue {
        background: #1890ff;
        border-color: #1890ff;
      }

      &.ant-tag-green {
        background: #52c41a;
        border-color: #52c41a;
      }

      &.ant-tag-orange {
        background: #fa8c16;
        border-color: #fa8c16;
      }
    }

    .ant-btn-default {
      background: #262626;
      border-color: #434343;
      color: #fff;

      &:hover {
        background: #303030;
        border-color: #40a9ff;
        color: #40a9ff;
      }
    }
  }

  // 动画效果
  .wallet-status {
    transition: all 0.3s ease;
  }

  .ant-tag {
    transition: all 0.2s ease;
  }

  .ant-btn {
    transition: all 0.2s ease;
  }

  // 加载状态
  .ant-btn-loading {
    pointer-events: none;
  }

  // 错误状态
  .ant-alert-error {
    animation: shake 0.5s ease-in-out;
  }

  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
  }

  // 成功状态
  .wallet-connected {
    animation: fadeIn 0.3s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
