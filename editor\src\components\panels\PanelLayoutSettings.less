/**
 * 面板布局设置样式
 */

.panel-layout-settings {
  .presets-tab {
    .presets-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;
    }

    .ant-list-item {
      .ant-card {
        height: 100%;
        
        .ant-card-head {
          min-height: 40px;
          
          .ant-card-head-title {
            font-size: 14px;
            font-weight: 500;
          }
        }

        .ant-card-body {
          padding: 12px;
          min-height: 80px;
        }

        .ant-card-actions {
          border-top: 1px solid #f0f0f0;
          
          li {
            margin: 0;
            
            .ant-btn-link {
              color: #1890ff;
              
              &:hover {
                color: #40a9ff;
              }
            }
          }
        }
      }
    }
  }

  .preferences-tab {
    .ant-card {
      margin-bottom: 16px;
      
      .ant-card-head {
        .ant-card-head-title {
          font-size: 14px;
          font-weight: 500;
        }
      }

      .ant-card-body {
        padding: 16px;
      }
    }

    .ant-row {
      .ant-col {
        .ant-space {
          .ant-typography {
            font-size: 13px;
            color: #666666;
            margin-bottom: 4px;
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .presets-tab {
      .presets-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
      }

      .ant-list-grid .ant-col {
        max-width: 100% !important;
        flex: 0 0 100% !important;
      }
    }

    .preferences-tab {
      .ant-row {
        .ant-col {
          max-width: 100% !important;
          flex: 0 0 100% !important;
          margin-bottom: 16px;
        }
      }
    }
  }
}

// 深色主题
.panel-layout-settings.dark-theme {
  .presets-tab {
    .presets-header {
      border-bottom-color: #404040;
    }

    .ant-list-item {
      .ant-card {
        background: #2d2d2d;
        border-color: #404040;

        .ant-card-head {
          background: #2d2d2d;
          border-bottom-color: #404040;
          
          .ant-card-head-title {
            color: #ffffff;
          }
        }

        .ant-card-body {
          color: #cccccc;
        }

        .ant-card-actions {
          background: #2d2d2d;
          border-top-color: #404040;
        }
      }
    }
  }

  .preferences-tab {
    .ant-card {
      background: #2d2d2d;
      border-color: #404040;

      .ant-card-head {
        background: #2d2d2d;
        border-bottom-color: #404040;
        
        .ant-card-head-title {
          color: #ffffff;
        }
      }

      .ant-card-body {
        color: #cccccc;
      }
    }
  }
}

// 紧凑模式
.panel-layout-settings.compact-mode {
  .presets-tab {
    .presets-header {
      margin-bottom: 12px;
      padding-bottom: 8px;
    }

    .ant-list-item {
      .ant-card {
        .ant-card-head {
          min-height: 36px;
          padding: 0 12px;
        }

        .ant-card-body {
          padding: 8px 12px;
          min-height: 60px;
        }
      }
    }
  }

  .preferences-tab {
    .ant-card {
      margin-bottom: 12px;
      
      .ant-card-head {
        min-height: 36px;
        padding: 0 12px;
      }

      .ant-card-body {
        padding: 12px;
      }
    }
  }
}

// 动画效果
.panel-layout-settings {
  .ant-card {
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }
  }

  .ant-btn {
    transition: all 0.2s ease;
  }

  .ant-switch {
    transition: all 0.2s ease;
  }

  .ant-select {
    transition: all 0.2s ease;
  }
}

// 加载状态
.panel-layout-settings {
  .loading-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #999999;
  }
}

// 空状态
.panel-layout-settings {
  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #999999;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .empty-text {
      font-size: 14px;
      margin-bottom: 16px;
    }
  }
}
