/**
 * 交互管理器组件
 * 统一管理所有交互功能，包括编辑器、预览、工具栏等
 */
import React, { useState, useEffect, useCallback } from 'react';
import { Layout, Card, Tabs, Space, Button, message, Modal } from 'antd';
import {
  ExperimentOutlined,
  EyeOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  BugOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../store';
import InteractionEditor from './InteractionEditor';
import InteractionPreview from './InteractionPreview';
import InteractionToolbar from './InteractionToolbar';
import { interactionService } from '../../services/InteractionService';
import type { InteractionEvent } from '../../services/InteractionService';
import './InteractionManager.less';

const { Content, Sider } = Layout;
const { TabPane } = Tabs;

interface InteractionManagerProps {
  /** 是否显示工具栏 */
  showToolbar?: boolean;
  /** 是否显示预览 */
  showPreview?: boolean;
  /** 默认激活的标签页 */
  defaultActiveTab?: string;
  /** 布局方向 */
  layout?: 'horizontal' | 'vertical';
}

/**
 * 交互管理器组件
 */
const InteractionManager: React.FC<InteractionManagerProps> = ({
  showToolbar = true,
  showPreview = true,
  defaultActiveTab = 'editor',
  layout = 'horizontal'
}) => {
  const { t } = useTranslation();
  const selectedEntityId = useAppSelector((state) => state.scene.selectedEntityId);
  const entities = useAppSelector((state) => state.scene.entities);
  const selectedEntity = selectedEntityId ? entities.find(entity => entity.id === selectedEntityId) : null;

  const [activeTab, setActiveTab] = useState(defaultActiveTab);
  const [previewMode, setPreviewMode] = useState(false);
  const [debugMode, setDebugMode] = useState(false);
  const [interactionConfig, setInteractionConfig] = useState<any>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [stats, setStats] = useState<any>(null);
  const [recentEvents, setRecentEvents] = useState<InteractionEvent[]>([]);
  const [showStatsModal, setShowStatsModal] = useState(false);

  // 初始化交互服务
  useEffect(() => {
    const initializeService = async () => {
      try {
        // 这里应该传入实际的世界实例
        // await interactionService.initialize(world);
        setIsInitialized(true);
        message.success(t('interaction.serviceInitialized'));
      } catch (error) {
        console.error('Failed to initialize interaction service:', error);
        message.error(t('interaction.serviceInitError'));
      }
    };

    if (!isInitialized) {
      initializeService();
    }
  }, [isInitialized, t]);

  // 监听交互事件
  useEffect(() => {
    const handleInteraction = (event: InteractionEvent) => {
      setRecentEvents(prev => [...prev.slice(-19), event]);
    };

    const handleStatsUpdate = () => {
      if (interactionService.isReady()) {
        setStats(interactionService.getStats());
      }
    };

    interactionService.on('interaction', handleInteraction);
    
    // 定期更新统计信息
    const statsInterval = setInterval(handleStatsUpdate, 2000);

    return () => {
      interactionService.off('interaction', handleInteraction);
      clearInterval(statsInterval);
    };
  }, []);

  // 监听选中实体变化
  useEffect(() => {
    if (selectedEntity) {
      // 获取实体的交互组件配置
      const interactableComponent = selectedEntity.components?.Interactable;
      setInteractionConfig(interactableComponent || null);
    } else {
      setInteractionConfig(null);
    }
  }, [selectedEntity]);

  /**
   * 处理配置更新
   */
  const handleConfigChange = useCallback((newConfig: any) => {
    setInteractionConfig(newConfig);
    
    if (selectedEntity && interactionService.isReady()) {
      // 更新实体的交互组件
      try {
        interactionService.removeInteractableComponent(selectedEntity);
        interactionService.createInteractableComponent(selectedEntity, newConfig);
        message.success(t('interaction.configUpdated'));
      } catch (error) {
        console.error('Failed to update interaction config:', error);
        message.error(t('interaction.configUpdateError'));
      }
    }
  }, [selectedEntity, t]);

  /**
   * 处理预览模式切换
   */
  const handlePreviewModeChange = useCallback((enabled: boolean) => {
    setPreviewMode(enabled);
    if (enabled) {
      setActiveTab('preview');
      message.info(t('interaction.previewModeEnabled'));
    } else {
      message.info(t('interaction.previewModeDisabled'));
    }
  }, [t]);

  /**
   * 处理调试模式切换
   */
  const handleDebugModeChange = useCallback((enabled: boolean) => {
    setDebugMode(enabled);
    message.info(enabled ? t('interaction.debugModeEnabled') : t('interaction.debugModeDisabled'));
  }, [t]);

  /**
   * 清除统计信息
   */
  const handleClearStats = useCallback(() => {
    Modal.confirm({
      title: t('interaction.clearStatsTitle'),
      content: t('interaction.clearStatsContent'),
      onOk: () => {
        interactionService.clearStats();
        setStats(null);
        setRecentEvents([]);
        message.success(t('interaction.statsClearedSuccess'));
      }
    });
  }, [t]);

  /**
   * 渲染统计信息模态框
   */
  const renderStatsModal = () => (
    <Modal
      title={t('interaction.detailedStatistics')}
      open={showStatsModal}
      onCancel={() => setShowStatsModal(false)}
      footer={[
        <Button key="clear" danger onClick={handleClearStats}>
          {t('interaction.clearStats')}
        </Button>,
        <Button key="close" onClick={() => setShowStatsModal(false)}>
          {t('common.close')}
        </Button>
      ]}
      width={600}
    >
      {stats && (
        <div className="stats-detail">
          <div className="stats-summary">
            <div className="stat-card">
              <div className="stat-value">{stats.totalInteractions}</div>
              <div className="stat-label">{t('interaction.totalInteractions')}</div>
            </div>
            <div className="stat-card">
              <div className="stat-value">{Object.keys(stats.interactionsByType).length}</div>
              <div className="stat-label">{t('interaction.interactionTypes')}</div>
            </div>
            <div className="stat-card">
              <div className="stat-value">{stats.mostInteractedEntity || 'N/A'}</div>
              <div className="stat-label">{t('interaction.mostInteracted')}</div>
            </div>
          </div>
          
          <div className="stats-breakdown">
            <h4>{t('interaction.interactionsByType')}</h4>
            {Object.entries(stats.interactionsByType).map(([type, count]) => (
              <div key={type} className="type-stat">
                <span className="type-name">{t(`interaction.${type}`)}</span>
                <span className="type-count">{count as number}</span>
              </div>
            ))}
          </div>

          <div className="recent-events-detail">
            <h4>{t('interaction.recentEvents')} ({recentEvents.length})</h4>
            <div className="events-timeline">
              {recentEvents.slice(-10).map((event, index) => (
                <div key={index} className="event-timeline-item">
                  <div className="event-time">
                    {new Date(event.timestamp).toLocaleTimeString()}
                  </div>
                  <div className="event-details">
                    <span className="event-type">{event.interactionType}</span>
                    <span className="event-entity">{event.entityId}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </Modal>
  );

  return (
    <div className={`interaction-manager ${layout}`}>
      {showToolbar && (
        <InteractionToolbar
          showDebug={debugMode}
          onDebugChange={handleDebugModeChange}
          previewMode={previewMode}
          onPreviewModeChange={handlePreviewModeChange}
        />
      )}

      <Layout className="interaction-layout">
        <Content className="interaction-content">
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            tabBarExtraContent={
              <Space>
                <Button
                  type="text"
                  icon={<InfoCircleOutlined />}
                  onClick={() => setShowStatsModal(true)}
                  disabled={!stats}
                >
                  {t('interaction.statistics')}
                </Button>
                <Button
                  type="text"
                  icon={<BugOutlined />}
                  onClick={() => setDebugMode(!debugMode)}
                >
                  {debugMode ? t('interaction.hideDebug') : t('interaction.showDebug')}
                </Button>
              </Space>
            }
          >
            <TabPane
              tab={
                <span>
                  <ExperimentOutlined />
                  {t('interaction.editor')}
                </span>
              }
              key="editor"
            >
              <InteractionEditor
                data={interactionConfig}
                onChange={handleConfigChange}
                readonly={previewMode}
              />
            </TabPane>

            {showPreview && (
              <TabPane
                tab={
                  <span>
                    <EyeOutlined />
                    {t('interaction.preview')}
                  </span>
                }
                key="preview"
              >
                <InteractionPreview
                  config={interactionConfig}
                  autoPlay={previewMode}
                  size="large"
                  showControls={true}
                  showStats={true}
                />
              </TabPane>
            )}

            <TabPane
              tab={
                <span>
                  <SettingOutlined />
                  {t('interaction.settings')}
                </span>
              }
              key="settings"
            >
              <Card title={t('interaction.globalSettings')}>
                <p>{t('interaction.globalSettingsDescription')}</p>
                {/* 这里可以添加全局设置内容 */}
              </Card>
            </TabPane>
          </Tabs>
        </Content>

        {debugMode && (
          <Sider width={300} className="debug-panel">
            <Card title={t('interaction.debugInfo')} size="small">
              <div className="debug-content">
                <div className="debug-section">
                  <h5>{t('interaction.serviceStatus')}</h5>
                  <p>
                    {isInitialized ? 
                      <span className="status-ok">{t('interaction.initialized')}</span> :
                      <span className="status-error">{t('interaction.notInitialized')}</span>
                    }
                  </p>
                </div>

                <div className="debug-section">
                  <h5>{t('interaction.selectedEntity')}</h5>
                  <p>{selectedEntity ? selectedEntity.name || selectedEntity.id : t('common.none')}</p>
                </div>

                <div className="debug-section">
                  <h5>{t('interaction.hasInteraction')}</h5>
                  <p>{interactionConfig ? t('common.yes') : t('common.no')}</p>
                </div>

                {stats && (
                  <div className="debug-section">
                    <h5>{t('interaction.quickStats')}</h5>
                    <p>{t('interaction.totalInteractions')}: {stats.totalInteractions}</p>
                    <p>{t('interaction.recentEvents')}: {recentEvents.length}</p>
                  </div>
                )}
              </div>
            </Card>
          </Sider>
        )}
      </Layout>

      {renderStatsModal()}
    </div>
  );
};

export default InteractionManager;
