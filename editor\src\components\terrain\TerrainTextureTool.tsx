/**
 * 地形纹理工具组件
 * 用于编辑地形纹理
 */
import React, { useState, useEffect } from 'react';
import {
  Form,
  Button,
  Select,
  Slider,
  Divider,
  Card,
  Row,
  Col,
  Tooltip,
  Switch,
  Typography,
  Upload,
  Popconfirm,
  message
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  SaveOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import './TerrainTextureTool.less';

const { Option } = Select;
const { Text } = Typography;

// 本地类型定义
interface TerrainTextureLayer {
  /** 纹理 */
  texture: string;
  /** 法线贴图 */
  normalMap?: string;
  /** 高光贴图 */
  roughnessMap?: string;
  /** 置换贴图 */
  displacementMap?: string;
  /** 环境光遮蔽贴图 */
  aoMap?: string;
  /** 纹理平铺系数 */
  tiling: number;
  /** 混合权重 */
  weight?: number;
  /** 最小高度 */
  minHeight?: number;
  /** 最大高度 */
  maxHeight?: number;
  /** 最小斜度 */
  minSlope?: number;
  /** 最大斜度 */
  maxSlope?: number;
}

enum BrushShape {
  CIRCLE = 'circle',
  SQUARE = 'square'
}

/**
 * 地形纹理工具属性
 */
interface TerrainTextureToolProps {
  /** 实体ID */
  entityId?: string;
  /** 是否可编辑 */
  editable?: boolean;
  /** 地形修改回调 */
  onTerrainModified?: () => void;
  /** 操作回调 */
  onOperation?: (operation: any) => void;
}

/**
 * 地形纹理工具组件
 */
const TerrainTextureTool: React.FC<TerrainTextureToolProps> = ({
  entityId,
  editable = true,
  onTerrainModified,
  onOperation
}) => {
  const { t } = useTranslation();

  // 从Redux获取地形数据
  const terrainData = useSelector((state: RootState) => {
    if (!entityId) return null;
    const entity = state.scene.entities.find(e => e.id === entityId);
    return entity?.components?.TerrainComponent || null;
  });
  
  // 状态
  const [textureLayers, setTextureLayers] = useState<TerrainTextureLayer[]>([]);
  const [selectedLayerIndex, setSelectedLayerIndex] = useState<number>(-1);
  const [editingLayer, setEditingLayer] = useState<TerrainTextureLayer | null>(null);
  const [brushSize, setBrushSize] = useState<number>(10);
  const [brushStrength, setBrushStrength] = useState<number>(0.5);
  const [brushFalloff, setBrushFalloff] = useState<number>(2);
  const [brushShape, setBrushShape] = useState<BrushShape>(BrushShape.CIRCLE);
  const [showBrushPreview, setShowBrushPreview] = useState<boolean>(true);
  const [autoBlendMode, setAutoBlendMode] = useState<boolean>(true);
  
  // 引用（保留以备将来使用）
  // const brushPreviewRef = useRef<any>(null);
  
  // 初始化纹理层
  useEffect(() => {
    if (terrainData && terrainData.layers) {
      setTextureLayers(terrainData.layers);
      if (terrainData.layers.length > 0 && selectedLayerIndex === -1) {
        setSelectedLayerIndex(0);
      }
    }
  }, [terrainData]);
  
  // 处理选择纹理层
  const handleSelectLayer = (index: number) => {
    setSelectedLayerIndex(index);
    setEditingLayer(null);
  };
  
  // 处理添加纹理层
  const handleAddLayer = () => {
    const newLayer: TerrainTextureLayer = {
      texture: '',
      tiling: 10,
      weight: 1.0
    };
    
    const newLayers = [...textureLayers, newLayer];
    setTextureLayers(newLayers);
    setSelectedLayerIndex(newLayers.length - 1);
    
    // 通知地形修改
    if (onTerrainModified) {
      onTerrainModified();
    }
    
    // 记录操作
    if (onOperation) {
      onOperation({
        type: 'ADD_TEXTURE_LAYER',
        layer: newLayer
      });
    }
  };
  
  // 处理删除纹理层
  const handleDeleteLayer = (index: number) => {
    if (textureLayers.length <= 1) {
      message.warning(t('terrain.texture.atLeastOneLayer'));
      return;
    }
    
    const deletedLayer = textureLayers[index];
    const newLayers = textureLayers.filter((_, i) => i !== index);
    setTextureLayers(newLayers);
    
    if (selectedLayerIndex === index) {
      setSelectedLayerIndex(Math.max(0, index - 1));
    } else if (selectedLayerIndex > index) {
      setSelectedLayerIndex(selectedLayerIndex - 1);
    }
    
    // 通知地形修改
    if (onTerrainModified) {
      onTerrainModified();
    }
    
    // 记录操作
    if (onOperation) {
      onOperation({
        type: 'DELETE_TEXTURE_LAYER',
        index,
        layer: deletedLayer
      });
    }
  };
  
  // 处理编辑纹理层
  const handleEditLayer = (index: number) => {
    setEditingLayer({ ...textureLayers[index] });
  };
  
  // 处理保存纹理层
  const handleSaveLayer = () => {
    if (!editingLayer) return;
    
    const newLayers = [...textureLayers];
    newLayers[selectedLayerIndex] = editingLayer;
    setTextureLayers(newLayers);
    setEditingLayer(null);
    
    // 通知地形修改
    if (onTerrainModified) {
      onTerrainModified();
    }
    
    // 记录操作
    if (onOperation) {
      onOperation({
        type: 'EDIT_TEXTURE_LAYER',
        index: selectedLayerIndex,
        layer: editingLayer
      });
    }
  };
  
  // 处理取消编辑纹理层
  const handleCancelEditLayer = () => {
    setEditingLayer(null);
  };
  
  // 处理移动纹理层
  const handleMoveLayer = (index: number, direction: 'up' | 'down') => {
    const newIndex = direction === 'up' ? index - 1 : index + 1;
    
    if (newIndex < 0 || newIndex >= textureLayers.length) {
      return;
    }
    
    const newLayers = [...textureLayers];
    const temp = newLayers[index];
    newLayers[index] = newLayers[newIndex];
    newLayers[newIndex] = temp;
    
    setTextureLayers(newLayers);
    setSelectedLayerIndex(newIndex);
    
    // 通知地形修改
    if (onTerrainModified) {
      onTerrainModified();
    }
    
    // 记录操作
    if (onOperation) {
      onOperation({
        type: 'MOVE_TEXTURE_LAYER',
        fromIndex: index,
        toIndex: newIndex
      });
    }
  };
  
  // 处理纹理上传
  const handleTextureUpload = (file: any, fieldName: string) => {
    if (!editingLayer) return false;
    
    // 这里需要实现纹理上传逻辑
    // 临时使用文件URL
    const fileUrl = URL.createObjectURL(file);
    
    setEditingLayer({
      ...editingLayer,
      [fieldName]: fileUrl
    });
    
    return false;
  };
  
  // 处理编辑层属性变更
  const handleEditingLayerChange = (field: string, value: any) => {
    if (!editingLayer) return;
    
    setEditingLayer({
      ...editingLayer,
      [field]: value
    });
  };
  
  // 处理笔刷大小变更
  const handleBrushSizeChange = (value: number) => {
    setBrushSize(value);
  };
  
  // 处理笔刷强度变更
  const handleBrushStrengthChange = (value: number) => {
    setBrushStrength(value);
  };
  
  // 处理笔刷衰减变更
  const handleBrushFalloffChange = (value: number) => {
    setBrushFalloff(value);
  };
  
  // 处理笔刷形状变更
  const handleBrushShapeChange = (value: BrushShape) => {
    setBrushShape(value);
  };
  
  // 处理显示笔刷预览变更
  const handleShowBrushPreviewChange = (checked: boolean) => {
    setShowBrushPreview(checked);
  };
  
  // 处理自动混合模式变更
  const handleAutoBlendModeChange = (checked: boolean) => {
    setAutoBlendMode(checked);
  };
  
  // 获取笔刷形状选项
  const brushShapeOptions = [
    { value: BrushShape.CIRCLE, label: t('terrain.brushShapes.circle') },
    { value: BrushShape.SQUARE, label: t('terrain.brushShapes.square') }
  ];
  
  // 渲染纹理层列表
  const renderLayerList = () => {
    return (
      <div className="texture-layer-list">
        {textureLayers.map((layer, index) => (
          <div 
            key={index} 
            className={`texture-layer-item ${selectedLayerIndex === index ? 'selected' : ''}`}
            onClick={() => handleSelectLayer(index)}
          >
            <div className="texture-layer-preview">
              {layer.texture ? (
                <img 
                  src={typeof layer.texture === 'string' ? layer.texture : '#'} 
                  alt={`Layer ${index + 1}`} 
                />
              ) : (
                <div className="texture-layer-placeholder">
                  <InfoCircleOutlined />
                </div>
              )}
            </div>
            <div className="texture-layer-info">
              <div className="texture-layer-name">
                {t('terrain.texture.layer')} {index + 1}
              </div>
              <div className="texture-layer-tiling">
                {t('terrain.texture.tiling')}: {layer.tiling}
              </div>
            </div>
            <div className="texture-layer-actions">
              <Button 
                icon={<EditOutlined />} 
                size="small" 
                onClick={(e) => {
                  e.stopPropagation();
                  handleEditLayer(index);
                }}
                disabled={!editable}
              />
              <Button 
                icon={<ArrowUpOutlined />} 
                size="small" 
                onClick={(e) => {
                  e.stopPropagation();
                  handleMoveLayer(index, 'up');
                }}
                disabled={!editable || index === 0}
              />
              <Button 
                icon={<ArrowDownOutlined />} 
                size="small" 
                onClick={(e) => {
                  e.stopPropagation();
                  handleMoveLayer(index, 'down');
                }}
                disabled={!editable || index === textureLayers.length - 1}
              />
              <Popconfirm
                title={t('terrain.texture.confirmDelete')}
                onConfirm={(e) => {
                  e?.stopPropagation();
                  handleDeleteLayer(index);
                }}
                okText={t('common.yes')}
                cancelText={t('common.no')}
              >
                <Button 
                  icon={<DeleteOutlined />} 
                  size="small" 
                  onClick={(e) => e.stopPropagation()}
                  disabled={!editable || textureLayers.length <= 1}
                  danger
                />
              </Popconfirm>
            </div>
          </div>
        ))}
        
        <Button 
          type="dashed" 
          icon={<PlusOutlined />} 
          className="add-layer-button" 
          onClick={handleAddLayer}
          disabled={!editable || textureLayers.length >= 8}
        >
          {t('terrain.texture.addLayer')}
        </Button>
      </div>
    );
  };
  
  // 渲染纹理层编辑表单
  const renderLayerEditForm = () => {
    if (!editingLayer) return null;
    
    return (
      <div className="texture-layer-edit-form">
        <Form layout="vertical">
          <Form.Item label={t('terrain.texture.diffuseTexture')}>
            <Upload
              name="texture"
              listType="picture-card"
              showUploadList={false}
              beforeUpload={(file) => handleTextureUpload(file, 'texture')}
              disabled={!editable}
            >
              {editingLayer.texture ? (
                <img 
                  src={typeof editingLayer.texture === 'string' ? editingLayer.texture : '#'} 
                  alt="Texture" 
                  style={{ width: '100%' }} 
                />
              ) : (
                <div>
                  <PlusOutlined />
                  <div style={{ marginTop: 8 }}>{t('terrain.texture.upload')}</div>
                </div>
              )}
            </Upload>
          </Form.Item>
          
          <Form.Item label={t('terrain.texture.normalMap')}>
            <Upload
              name="normalMap"
              listType="picture-card"
              showUploadList={false}
              beforeUpload={(file) => handleTextureUpload(file, 'normalMap')}
              disabled={!editable}
            >
              {editingLayer.normalMap ? (
                <img 
                  src={typeof editingLayer.normalMap === 'string' ? editingLayer.normalMap : '#'} 
                  alt="Normal Map" 
                  style={{ width: '100%' }} 
                />
              ) : (
                <div>
                  <PlusOutlined />
                  <div style={{ marginTop: 8 }}>{t('terrain.texture.upload')}</div>
                </div>
              )}
            </Upload>
          </Form.Item>
          
          <Form.Item label={t('terrain.texture.roughnessMap')}>
            <Upload
              name="roughnessMap"
              listType="picture-card"
              showUploadList={false}
              beforeUpload={(file) => handleTextureUpload(file, 'roughnessMap')}
              disabled={!editable}
            >
              {editingLayer.roughnessMap ? (
                <img 
                  src={typeof editingLayer.roughnessMap === 'string' ? editingLayer.roughnessMap : '#'} 
                  alt="Roughness Map" 
                  style={{ width: '100%' }} 
                />
              ) : (
                <div>
                  <PlusOutlined />
                  <div style={{ marginTop: 8 }}>{t('terrain.texture.upload')}</div>
                </div>
              )}
            </Upload>
          </Form.Item>
          
          <Form.Item label={t('terrain.texture.tiling')}>
            <Slider
              min={1}
              max={100}
              value={editingLayer.tiling}
              onChange={(value) => handleEditingLayerChange('tiling', value)}
              disabled={!editable}
            />
          </Form.Item>
          
          <Divider>{t('terrain.texture.blendSettings')}</Divider>
          
          <Form.Item label={t('terrain.texture.minHeight')}>
            <Slider
              min={0}
              max={1}
              step={0.01}
              value={editingLayer.minHeight || 0}
              onChange={(value) => handleEditingLayerChange('minHeight', value)}
              disabled={!editable || !autoBlendMode}
            />
          </Form.Item>
          
          <Form.Item label={t('terrain.texture.maxHeight')}>
            <Slider
              min={0}
              max={1}
              step={0.01}
              value={editingLayer.maxHeight || 1}
              onChange={(value) => handleEditingLayerChange('maxHeight', value)}
              disabled={!editable || !autoBlendMode}
            />
          </Form.Item>
          
          <Form.Item label={t('terrain.texture.minSlope')}>
            <Slider
              min={0}
              max={90}
              step={1}
              value={editingLayer.minSlope || 0}
              onChange={(value) => handleEditingLayerChange('minSlope', value)}
              disabled={!editable || !autoBlendMode}
            />
          </Form.Item>
          
          <Form.Item label={t('terrain.texture.maxSlope')}>
            <Slider
              min={0}
              max={90}
              step={1}
              value={editingLayer.maxSlope || 90}
              onChange={(value) => handleEditingLayerChange('maxSlope', value)}
              disabled={!editable || !autoBlendMode}
            />
          </Form.Item>
          
          <div className="form-actions">
            <Button 
              type="primary" 
              icon={<SaveOutlined />} 
              onClick={handleSaveLayer}
              disabled={!editable}
            >
              {t('common.save')}
            </Button>
            <Button 
              onClick={handleCancelEditLayer}
            >
              {t('common.cancel')}
            </Button>
          </div>
        </Form>
      </div>
    );
  };
  
  return (
    <div className="terrain-texture-tool">
      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Card title={t('terrain.texture.layers')} className="texture-layers-card">
            {renderLayerList()}
          </Card>
        </Col>
        
        <Col span={8}>
          <Card title={t('terrain.texture.layerProperties')} className="texture-properties-card">
            {selectedLayerIndex >= 0 && !editingLayer ? (
              <div className="layer-properties">
                <Button 
                  type="primary" 
                  icon={<EditOutlined />} 
                  onClick={() => handleEditLayer(selectedLayerIndex)}
                  disabled={!editable}
                >
                  {t('terrain.texture.editLayer')}
                </Button>
                <Divider />
                <div className="layer-preview">
                  {textureLayers[selectedLayerIndex].texture ? (
                    <img 
                      src={typeof textureLayers[selectedLayerIndex].texture === 'string' ? textureLayers[selectedLayerIndex].texture : '#'} 
                      alt={`Layer ${selectedLayerIndex + 1}`} 
                    />
                  ) : (
                    <div className="layer-preview-placeholder">
                      <InfoCircleOutlined />
                      <Text>{t('terrain.texture.noTextureSelected')}</Text>
                    </div>
                  )}
                </div>
                <Divider />
                <div className="layer-info">
                  <div className="info-item">
                    <Text strong>{t('terrain.texture.tiling')}:</Text>
                    <Text>{textureLayers[selectedLayerIndex].tiling}</Text>
                  </div>
                  <div className="info-item">
                    <Text strong>{t('terrain.texture.minHeight')}:</Text>
                    <Text>{textureLayers[selectedLayerIndex].minHeight || 0}</Text>
                  </div>
                  <div className="info-item">
                    <Text strong>{t('terrain.texture.maxHeight')}:</Text>
                    <Text>{textureLayers[selectedLayerIndex].maxHeight || 1}</Text>
                  </div>
                  <div className="info-item">
                    <Text strong>{t('terrain.texture.minSlope')}:</Text>
                    <Text>{textureLayers[selectedLayerIndex].minSlope || 0}°</Text>
                  </div>
                  <div className="info-item">
                    <Text strong>{t('terrain.texture.maxSlope')}:</Text>
                    <Text>{textureLayers[selectedLayerIndex].maxSlope || 90}°</Text>
                  </div>
                </div>
              </div>
            ) : (
              renderLayerEditForm()
            )}
          </Card>
        </Col>
        
        <Col span={8}>
          <Card title={t('terrain.texture.paintingTools')} className="painting-tools-card">
            <Form layout="vertical">
              <Form.Item>
                <Switch
                  checked={autoBlendMode}
                  onChange={handleAutoBlendModeChange}
                  disabled={!editable}
                />
                <Text style={{ marginLeft: 8 }}>{t('terrain.texture.autoBlendMode')}</Text>
                <Tooltip title={t('terrain.texture.autoBlendModeTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </Form.Item>
              
              {!autoBlendMode && (
                <>
                  <Divider>{t('terrain.texture.brushSettings')}</Divider>
                  
                  <Form.Item label={t('terrain.texture.brushShape')}>
                    <Select 
                      value={brushShape} 
                      onChange={handleBrushShapeChange}
                      disabled={!editable}
                    >
                      {brushShapeOptions.map(option => (
                        <Option key={option.value} value={option.value}>
                          {option.label}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                  
                  <Form.Item label={t('terrain.texture.brushSize')}>
                    <Slider
                      min={1}
                      max={100}
                      value={brushSize}
                      onChange={handleBrushSizeChange}
                      disabled={!editable}
                    />
                  </Form.Item>
                  
                  <Form.Item label={t('terrain.texture.brushStrength')}>
                    <Slider
                      min={0.01}
                      max={1}
                      step={0.01}
                      value={brushStrength}
                      onChange={handleBrushStrengthChange}
                      disabled={!editable}
                    />
                  </Form.Item>
                  
                  <Form.Item label={t('terrain.texture.brushFalloff')}>
                    <Slider
                      min={0.1}
                      max={5}
                      step={0.1}
                      value={brushFalloff}
                      onChange={handleBrushFalloffChange}
                      disabled={!editable}
                    />
                  </Form.Item>
                  
                  <Form.Item>
                    <Switch
                      checked={showBrushPreview}
                      onChange={handleShowBrushPreviewChange}
                      disabled={!editable}
                    />
                    <Text style={{ marginLeft: 8 }}>{t('terrain.texture.showBrushPreview')}</Text>
                  </Form.Item>
                </>
              )}
            </Form>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default TerrainTextureTool;
