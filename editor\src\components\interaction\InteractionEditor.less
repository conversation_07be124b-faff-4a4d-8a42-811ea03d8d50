/**
 * 交互编辑器样式
 */
.interaction-editor {
  .ant-card-body {
    padding: 16px;
  }

  .ant-tabs-content-holder {
    padding: 16px 0;
  }

  // 基础设置样式
  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-divider {
    margin: 16px 0;
  }

  // 条件设置样式
  .interaction-conditions {
    .conditions-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .ant-typography {
        margin: 0;
      }
    }

    .ant-list-item {
      padding: 12px 16px;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      margin-bottom: 8px;
      background: #fafafa;

      &:hover {
        background: #f5f5f5;
        border-color: #d9d9d9;
      }
    }

    .ant-list-item-meta-title {
      margin-bottom: 4px;
    }

    .ant-list-item-action {
      margin-left: 16px;
    }
  }

  // 事件设置样式
  .interaction-events {
    .events-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .ant-typography {
        margin: 0;
      }
    }

    .ant-list-item {
      padding: 12px 16px;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      margin-bottom: 8px;
      background: #fafafa;

      &:hover {
        background: #f5f5f5;
        border-color: #d9d9d9;
      }
    }

    .ant-list-item-meta-title {
      margin-bottom: 4px;
    }

    .ant-list-item-action {
      margin-left: 16px;
    }
  }

  // 高级设置样式
  .ant-alert {
    margin-bottom: 16px;
  }

  .ant-collapse {
    .ant-collapse-header {
      font-weight: 500;
    }

    .ant-collapse-content-box {
      padding: 16px;
    }
  }

  // 颜色选择器样式
  .ant-color-picker {
    width: 100%;

    .ant-color-picker-trigger {
      width: 100%;
    }
  }

  // 滑块样式
  .ant-slider {
    margin: 8px 0;
  }

  // 开关样式
  .ant-switch {
    &.ant-switch-checked {
      background-color: #52c41a;
    }
  }

  // 标签样式
  .ant-tag {
    margin-right: 8px;
    border-radius: 4px;
    font-size: 12px;
    padding: 2px 8px;
  }

  // 工具提示样式
  .ant-tooltip {
    .ant-tooltip-inner {
      max-width: 300px;
    }
  }

  // 模态框样式
  .ant-modal {
    .ant-modal-header {
      border-bottom: 1px solid #f0f0f0;
    }

    .ant-modal-body {
      padding: 24px;
    }

    .ant-modal-footer {
      border-top: 1px solid #f0f0f0;
      padding: 16px 24px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .ant-col {
      margin-bottom: 16px;
    }

    .conditions-header,
    .events-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }

    .ant-list-item {
      .ant-list-item-action {
        margin-left: 0;
        margin-top: 8px;
      }
    }
  }

  // 预览模式样式
  &.preview-mode {
    .ant-form-item {
      pointer-events: none;
    }

    .ant-btn {
      pointer-events: auto;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.05);
      pointer-events: none;
      z-index: 1;
    }
  }

  // 只读模式样式
  &.readonly-mode {
    .ant-btn:not(.ant-btn-link) {
      display: none;
    }

    .ant-form-item-control-input {
      cursor: not-allowed;
    }
  }

  // 动画效果
  .ant-list-item {
    transition: all 0.3s ease;
  }

  .ant-collapse-item {
    transition: all 0.3s ease;
  }

  .ant-tag {
    transition: all 0.2s ease;
  }

  // 错误状态样式
  .ant-form-item-has-error {
    .ant-input,
    .ant-select-selector,
    .ant-input-number {
      border-color: #ff4d4f;
      box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
    }
  }

  // 成功状态样式
  .ant-form-item-has-success {
    .ant-input,
    .ant-select-selector,
    .ant-input-number {
      border-color: #52c41a;
      box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
    }
  }

  // 禁用状态样式
  .ant-form-item-control-input-disabled {
    .ant-input,
    .ant-select-selector,
    .ant-input-number,
    .ant-switch {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

// 全局交互编辑器相关样式
.interaction-editor-modal {
  .ant-modal-content {
    border-radius: 8px;
    overflow: hidden;
  }

  .ant-modal-header {
    background: linear-gradient(90deg, #1890ff, #36cfc9);
    color: white;

    .ant-modal-title {
      color: white;
      font-weight: 600;
    }
  }

  .ant-modal-close {
    color: white;

    &:hover {
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

// 交互预览样式
.interaction-preview {
  position: relative;
  border: 2px dashed #1890ff;
  border-radius: 8px;
  padding: 16px;
  background: rgba(24, 144, 255, 0.05);
  text-align: center;

  &::before {
    content: '预览模式';
    position: absolute;
    top: -12px;
    left: 16px;
    background: #1890ff;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
  }

  .preview-content {
    margin-top: 8px;
    color: #666;
  }
}
