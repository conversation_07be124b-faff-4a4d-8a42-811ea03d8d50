/**
 * 网络调试面板组件
 * 整合网络状态监控、网络模拟和网络诊断功能
 */
import React, { useState, useEffect, useCallback } from 'react';
import { Tabs, message, Button, Space } from 'antd';
import {
  DashboardOutlined,
  ExperimentOutlined,
  BugOutlined,
  ReloadOutlined,
  DisconnectOutlined,
  ApiOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import NetworkStatusPanel from './NetworkStatusPanel';
import NetworkSimulatorPanel from './NetworkSimulatorPanel';
import NetworkDiagnosticPanel from './NetworkDiagnosticPanel';
import './NetworkDebugPanel.css';

// 网络相关类型定义
export enum NetworkQualityLevel {
  UNKNOWN = 'unknown',
  VERY_BAD = 'very_bad',
  BAD = 'bad',
  MEDIUM = 'medium',
  GOOD = 'good',
  EXCELLENT = 'excellent',
}

export enum NetworkIssueType {
  NONE = 'none',
  HIGH_LATENCY = 'high_latency',
  PACKET_LOSS = 'packet_loss',
  HIGH_JITTER = 'high_jitter',
  LOW_BANDWIDTH = 'low_bandwidth',
  UNSTABLE_CONNECTION = 'unstable_connection',
  CONNECTION_INTERRUPTED = 'connection_interrupted',
  NETWORK_CONGESTION = 'network_congestion',
  BANDWIDTH_FLUCTUATION = 'bandwidth_fluctuation',
  SLOW_SERVER_RESPONSE = 'slow_server_response',
  DNS_RESOLUTION_ISSUE = 'dns_resolution_issue',
}

export interface NetworkIssue {
  type: NetworkIssueType;
  severity: number;
  description: string;
  solution: string;
  startTime: number;
  duration: number;
  resolved: boolean;
}

export interface NetworkQualityData {
  rtt: number;
  packetLoss: number;
  jitter: number;
  bandwidth: number;
  uploadBandwidth?: number;
  downloadBandwidth?: number;
  stability?: number;
  congestion?: number;
  level: NetworkQualityLevel;
  timestamp: number;
  issues?: NetworkIssue[];
}

export interface NetworkSimulatorConfig {
  enabled: boolean;
  latency: number;
  latencyJitter: number;
  packetLoss: number;
  bandwidthLimit: number;
  enableRandomDisconnect: boolean;
  disconnectProbability?: number;
  reconnectTime?: number;
}

const { TabPane } = Tabs;

// 模拟网络质量数据
const generateMockNetworkData = (config: NetworkSimulatorConfig): NetworkQualityData => {
  // 基础延迟加上抖动
  const jitterFactor = config.latencyJitter ? (Math.random() * 2 - 1) * config.latencyJitter : 0;
  const rtt = Math.max(1, config.latency + jitterFactor);
  
  // 丢包率加上随机波动
  const packetLossVariation = Math.random() * 0.01 - 0.005; // -0.5% to +0.5%
  const packetLoss = Math.max(0, Math.min(1, config.packetLoss + packetLossVariation));
  
  // 抖动值
  const jitter = config.latencyJitter * (0.5 + Math.random() * 0.5);
  
  // 带宽值
  let bandwidth = config.bandwidthLimit;
  if (bandwidth === 0) {
    bandwidth = 1000000 + Math.random() * 1000000; // 1-2 MB/s
  } else {
    // 带宽波动 ±10%
    const bandwidthVariation = (Math.random() * 0.2 - 0.1) * bandwidth;
    bandwidth = Math.max(1000, bandwidth + bandwidthVariation);
  }
  
  // 计算上行和下行带宽
  const uploadBandwidth = bandwidth * (0.3 + Math.random() * 0.2); // 30-50% of total
  const downloadBandwidth = bandwidth - uploadBandwidth;
  
  // 计算稳定性 (0-1)
  const stabilityBase = 1 - (packetLoss * 5) - (jitter / 200);
  const stability = Math.max(0, Math.min(1, stabilityBase + (Math.random() * 0.1 - 0.05)));
  
  // 计算拥塞程度 (0-1)
  const congestionBase = (rtt / 500) * 0.5 + packetLoss * 0.5;
  const congestion = Math.max(0, Math.min(1, congestionBase + (Math.random() * 0.1 - 0.05)));
  
  // 确定质量等级
  let level = NetworkQualityLevel.EXCELLENT;
  if (rtt > 300 || packetLoss > 0.1 || jitter > 150 || bandwidth < 50000) {
    level = NetworkQualityLevel.VERY_BAD;
  } else if (rtt > 200 || packetLoss > 0.06 || jitter > 70 || bandwidth < 200000) {
    level = NetworkQualityLevel.BAD;
  } else if (rtt > 100 || packetLoss > 0.03 || jitter > 30 || bandwidth < 500000) {
    level = NetworkQualityLevel.MEDIUM;
  } else if (rtt > 50 || packetLoss > 0.01 || jitter > 10 || bandwidth < 1000000) {
    level = NetworkQualityLevel.GOOD;
  }
  
  // 检测网络问题
  const issues: NetworkIssue[] = [];
  
  if (rtt > 200) {
    issues.push({
      type: NetworkIssueType.HIGH_LATENCY,
      severity: Math.min(1, (rtt - 200) / 300),
      description: '网络延迟过高，可能导致操作响应缓慢',
      solution: '尝试连接到更近的服务器，检查本地网络连接，关闭其他占用带宽的应用',
      startTime: Date.now() - 60000,
      duration: 60000,
      resolved: false
    });
  }

  if (packetLoss > 0.05) {
    issues.push({
      type: NetworkIssueType.PACKET_LOSS,
      severity: Math.min(1, (packetLoss - 0.05) / 0.15),
      description: '网络丢包率过高，可能导致数据丢失或不完整',
      solution: '检查网络连接稳定性，避免使用无线网络，减少网络拥塞',
      startTime: Date.now() - 120000,
      duration: 120000,
      resolved: false
    });
  }

  if (jitter > 70) {
    issues.push({
      type: NetworkIssueType.HIGH_JITTER,
      severity: Math.min(1, (jitter - 70) / 130),
      description: '网络抖动过大，可能导致连接不稳定',
      solution: '使用有线网络连接，减少网络干扰，关闭其他占用带宽的应用',
      startTime: Date.now() - 90000,
      duration: 90000,
      resolved: false
    });
  }

  if (bandwidth < 200000) {
    issues.push({
      type: NetworkIssueType.LOW_BANDWIDTH,
      severity: Math.min(1, 1 - (bandwidth / 200000)),
      description: '网络带宽不足，可能导致数据传输速度慢',
      solution: '提高网络带宽，关闭其他占用带宽的应用，降低数据传输质量',
      startTime: Date.now() - 180000,
      duration: 180000,
      resolved: false
    });
  }

  if (stability < 0.7) {
    issues.push({
      type: NetworkIssueType.UNSTABLE_CONNECTION,
      severity: Math.min(1, 1 - (stability / 0.7)),
      description: '网络连接不稳定，可能导致频繁断线或重连',
      solution: '检查网络连接稳定性，使用有线网络连接，更换网络环境',
      startTime: Date.now() - 150000,
      duration: 150000,
      resolved: false
    });
  }
  
  return {
    rtt,
    packetLoss,
    jitter,
    bandwidth,
    uploadBandwidth,
    downloadBandwidth,
    stability,
    congestion,
    level,
    timestamp: Date.now(),
    issues};
};

interface NetworkDebugPanelProps {
  /** 服务器URL */
  serverUrl?: string;
}

/**
 * 网络调试面板组件
 */
const NetworkDebugPanel: React.FC<NetworkDebugPanelProps> = ({
  serverUrl = 'wss://sync.example.com'}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('status');
  const [connected, setConnected] = useState(true);
  const [loading, setLoading] = useState(false);
  const [currentQuality, setCurrentQuality] = useState<NetworkQualityData | null>(null);
  const [qualityHistory, setQualityHistory] = useState<NetworkQualityData[]>([]);
  const [issues, setIssues] = useState<NetworkIssue[]>([]);
  const [simulatorConfig, setSimulatorConfig] = useState<NetworkSimulatorConfig>({
    enabled: true,
    latency: 50,
    latencyJitter: 10,
    packetLoss: 0.01,
    bandwidthLimit: 0,
    enableRandomDisconnect: false,
    disconnectProbability: 0.001,
    reconnectTime: 3000});
  
  // 更新网络数据
  const updateNetworkData = useCallback(() => {
    if (!connected) {
      return;
    }
    
    const newData = generateMockNetworkData(simulatorConfig);
    setCurrentQuality(newData);
    setQualityHistory(prev => [...prev, newData].slice(-100));
    setIssues(newData.issues || []);
  }, [connected, simulatorConfig]);
  
  // 初始化和定时更新
  useEffect(() => {
    // 初始更新
    updateNetworkData();
    
    // 设置定时更新
    const intervalId = setInterval(() => {
      updateNetworkData();
    }, 1000);
    
    return () => {
      clearInterval(intervalId);
    };
  }, [updateNetworkData]);
  
  // 刷新数据
  const handleRefresh = () => {
    setLoading(true);
    
    setTimeout(() => {
      updateNetworkData();
      setLoading(false);
      message.success(t('network.debug.dataRefreshed'));
    }, 500);
  };
  
  // 模拟断线
  const handleSimulateDisconnect = () => {
    setConnected(false);
    message.warning(t('network.debug.disconnected'));
  };
  
  // 模拟重连
  const handleSimulateReconnect = () => {
    setConnected(true);
    message.success(t('network.debug.reconnected'));
  };
  
  // 更新模拟器配置
  const handleConfigChange = (config: NetworkSimulatorConfig) => {
    setSimulatorConfig(config);
    message.success(t('network.debug.configUpdated'));
  };
  
  // 解决网络问题
  const handleResolveIssue = (issue: NetworkIssue) => {
    setIssues(prev => prev.filter(i => i.type !== issue.type));
    message.success(t('network.debug.issueResolved', { type: issue.type }));
  };
  
  // 开始诊断
  const handleStartDiagnostic = () => {
    message.info(t('network.debug.diagnosticStarted'));
  };
  
  return (
    <div className="network-debug-panel">
      <div className="panel-header">
        <h1>{t('network.debug.title')}</h1>
        <Space>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={handleRefresh}
            loading={loading}
          >
            {t('network.debug.refresh')}
          </Button>
          {connected ? (
            <Button 
              icon={<DisconnectOutlined />} 
              onClick={handleSimulateDisconnect}
              danger
            >
              {t('network.debug.disconnect')}
            </Button>
          ) : (
            <Button 
              icon={<ApiOutlined />} 
              onClick={handleSimulateReconnect}
              type="primary"
            >
              {t('network.debug.reconnect')}
            </Button>
          )}
        </Space>
      </div>
      
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane 
          tab={
            <span>
              <DashboardOutlined />
              {t('network.debug.status')}
            </span>
          } 
          key="status"
        >
          <NetworkStatusPanel
            currentQuality={currentQuality || undefined}
            qualityHistory={qualityHistory}
            issues={issues}
            connected={connected}
            loading={loading}
            onRefresh={handleRefresh}
            onResolveIssue={handleResolveIssue}
          />
        </TabPane>
        <TabPane 
          tab={
            <span>
              <ExperimentOutlined />
              {t('network.debug.simulator')}
            </span>
          } 
          key="simulator"
        >
          <NetworkSimulatorPanel
            config={simulatorConfig}
            onConfigChange={handleConfigChange}
            onSimulateDisconnect={handleSimulateDisconnect}
            onSimulateReconnect={handleSimulateReconnect}
          />
        </TabPane>
        <TabPane 
          tab={
            <span>
              <BugOutlined />
              {t('network.debug.diagnostic')}
            </span>
          } 
          key="diagnostic"
        >
          <NetworkDiagnosticPanel
            issues={issues}
            connected={connected}
            serverUrl={serverUrl}
            onStartDiagnostic={handleStartDiagnostic}
            onResolveIssue={handleResolveIssue}
            onReconnect={handleSimulateReconnect}
          />
        </TabPane>
      </Tabs>
    </div>
  );
};

export default NetworkDebugPanel;
