/**
 * 增强节点库管理组件
 * 提供完整的节点库管理功能，包括分类、搜索、收藏、自定义节点等
 */
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Input,
  Button,
  Space,
  Tooltip,
  Badge,
  Drawer,
  Card,
  Tag,
  Rate,
  Modal,
  Form,
  Select,
  message,
  Switch,
  Divider,
  Typography,
  Empty,
  Spin
} from 'antd';
import {
  StarOutlined,
  StarFilled,
  PlusOutlined,
  AppstoreOutlined,
  UnorderedListOutlined,
  SortAscendingOutlined,
  DownloadOutlined,
  UploadOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Search } = Input;
const { Text } = Typography;
const { Option } = Select;

// 节点数据接口
interface NodeInfo {
  id: string;
  type: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  icon: string;
  color: string;
  version: string;
  author: string;
  rating: number;
  downloads: number;
  isFavorite: boolean;
  isCustom: boolean;
  isInstalled: boolean;
  dependencies: string[];
  examples: string[];
  documentation: string;
  thumbnail?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 节点分类接口
interface NodeCategory {
  id: string;
  name: string;
  icon: string;
  color: string;
  description: string;
  nodeCount: number;
  children?: NodeCategory[];
}

// 组件属性接口
interface EnhancedNodeLibraryProps {
  visible: boolean;
  onClose: () => void;
  onNodeSelect: (nodeType: string) => void;
  onNodeInstall?: (nodeId: string) => Promise<void>;
  onNodeUninstall?: (nodeId: string) => Promise<void>;
  onNodeCreate?: () => void;

  searchQuery?: string;
  selectedCategory?: string;
}

/**
 * 增强节点库管理组件
 */
const EnhancedNodeLibrary: React.FC<EnhancedNodeLibraryProps> = ({
  visible,
  onClose,
  onNodeSelect,
  onNodeInstall,
  onNodeUninstall,
  onNodeCreate,
  searchQuery = '',
  selectedCategory = ''
}) => {
  const { t } = useTranslation();

  // 状态管理
  const [nodes, setNodes] = useState<NodeInfo[]>([]);
  const [categories, setCategories] = useState<NodeCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState(searchQuery);
  const [selectedCategoryId, setSelectedCategoryId] = useState(selectedCategory);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'name' | 'rating' | 'downloads' | 'date'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);
  const [showInstalledOnly, setShowInstalledOnly] = useState(false);


  // 模态框状态
  const [nodeDetailVisible, setNodeDetailVisible] = useState(false);
  const [selectedNode, setSelectedNode] = useState<NodeInfo | null>(null);
  const [createNodeVisible, setCreateNodeVisible] = useState(false);
  const [importNodeVisible, setImportNodeVisible] = useState(false);

  // 表单实例
  const [createForm] = Form.useForm();

  // 处理节点详情显示
  const handleNodeDetail = useCallback((node: NodeInfo) => {
    setSelectedNode(node);
    setNodeDetailVisible(true);
  }, []);

  // 处理创建节点
  const handleCreateNode = useCallback(() => {
    if (onNodeCreate) {
      onNodeCreate();
    }
    setCreateNodeVisible(true);
  }, [onNodeCreate]);

  // 处理导入节点
  const handleImportNode = useCallback(() => {
    setImportNodeVisible(true);
  }, []);

  // 加载节点数据
  const loadNodes = useCallback(async () => {
    setLoading(true);
    try {
      // 模拟API调用
      const mockNodes: NodeInfo[] = [
        {
          id: 'ui-button',
          type: 'ui/button/create',
          name: '创建按钮',
          description: '创建一个可点击的按钮组件',
          category: 'ui',
          tags: ['ui', 'button', 'interactive'],
          icon: 'button',
          color: '#1890ff',
          version: '1.0.0',
          author: 'System',
          rating: 4.5,
          downloads: 1250,
          isFavorite: false,
          isCustom: false,
          isInstalled: true,
          dependencies: [],
          examples: ['basic-button', 'styled-button'],
          documentation: 'https://docs.example.com/ui/button',
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-15')
        },
        {
          id: 'math-add',
          type: 'math/add',
          name: '数学加法',
          description: '计算两个数的和',
          category: 'math',
          tags: ['math', 'arithmetic', 'basic'],
          icon: 'plus',
          color: '#52c41a',
          version: '1.2.0',
          author: 'System',
          rating: 5.0,
          downloads: 2100,
          isFavorite: true,
          isCustom: false,
          isInstalled: true,
          dependencies: [],
          examples: ['simple-addition', 'vector-addition'],
          documentation: 'https://docs.example.com/math/add',
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-02-01')
        }
        // 更多节点数据...
      ];

      setNodes(mockNodes);
    } catch (error) {
      console.error('加载节点失败:', error);
      message.error(t('节点库.加载失败'));
    } finally {
      setLoading(false);
    }
  }, [t]);

  // 加载分类数据
  const loadCategories = useCallback(async () => {
    try {
      // 模拟API调用
      const mockCategories: NodeCategory[] = [
        {
          id: 'core',
          name: '核心节点',
          icon: 'setting',
          color: '#1890ff',
          description: '基础核心功能节点',
          nodeCount: 15
        },
        {
          id: 'ui',
          name: 'UI组件',
          icon: 'appstore',
          color: '#722ed1',
          description: '用户界面组件节点',
          nodeCount: 25
        },
        {
          id: 'math',
          name: '数学运算',
          icon: 'calculator',
          color: '#52c41a',
          description: '数学计算和运算节点',
          nodeCount: 20
        },
        {
          id: 'logic',
          name: '逻辑控制',
          icon: 'branches',
          color: '#fa8c16',
          description: '逻辑判断和流程控制节点',
          nodeCount: 18
        }
        // 更多分类数据...
      ];

      setCategories(mockCategories);
    } catch (error) {
      console.error('加载分类失败:', error);
    }
  }, []);

  // 初始化数据
  useEffect(() => {
    if (visible) {
      loadNodes();
      loadCategories();
    }
  }, [visible, loadNodes, loadCategories]);

  // 过滤和排序节点
  const filteredAndSortedNodes = useMemo(() => {
    let filtered = nodes.filter(node => {
      // 搜索过滤
      if (searchText) {
        const searchLower = searchText.toLowerCase();
        if (!node.name.toLowerCase().includes(searchLower) &&
            !node.description.toLowerCase().includes(searchLower) &&
            !node.tags.some(tag => tag.toLowerCase().includes(searchLower))) {
          return false;
        }
      }

      // 分类过滤
      if (selectedCategoryId && node.category !== selectedCategoryId) {
        return false;
      }

      // 收藏过滤
      if (showFavoritesOnly && !node.isFavorite) {
        return false;
      }

      // 已安装过滤
      if (showInstalledOnly && !node.isInstalled) {
        return false;
      }

      // 标签过滤 - 暂时跳过，因为没有标签选择功能
      // if (selectedTags.length > 0) {
      //   if (!selectedTags.some(tag => node.tags.includes(tag))) {
      //     return false;
      //   }
      // }

      return true;
    });

    // 排序
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'rating':
          comparison = a.rating - b.rating;
          break;
        case 'downloads':
          comparison = a.downloads - b.downloads;
          break;
        case 'date':
          comparison = a.updatedAt.getTime() - b.updatedAt.getTime();
          break;
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return filtered;
  }, [nodes, searchText, selectedCategoryId, showFavoritesOnly, showInstalledOnly, sortBy, sortOrder]);

  // 切换收藏状态
  const toggleFavorite = useCallback(async (nodeId: string) => {
    try {
      setNodes(prev => prev.map(node => 
        node.id === nodeId 
          ? { ...node, isFavorite: !node.isFavorite }
          : node
      ));
      message.success(t('节点库.收藏状态已更新'));
    } catch (error) {
      console.error('切换收藏失败:', error);
      message.error(t('节点库.操作失败'));
    }
  }, [t]);

  // 安装节点
  const handleInstallNode = useCallback(async (nodeId: string) => {
    try {
      if (onNodeInstall) {
        await onNodeInstall(nodeId);
      }
      
      setNodes(prev => prev.map(node => 
        node.id === nodeId 
          ? { ...node, isInstalled: true, downloads: node.downloads + 1 }
          : node
      ));
      
      message.success(t('节点库.安装成功'));
    } catch (error) {
      console.error('安装节点失败:', error);
      message.error(t('节点库.安装失败'));
    }
  }, [onNodeInstall, t]);

  // 卸载节点
  const handleUninstallNode = useCallback(async (nodeId: string) => {
    try {
      if (onNodeUninstall) {
        await onNodeUninstall(nodeId);
      }
      
      setNodes(prev => prev.map(node => 
        node.id === nodeId 
          ? { ...node, isInstalled: false }
          : node
      ));
      
      message.success(t('节点库.卸载成功'));
    } catch (error) {
      console.error('卸载节点失败:', error);
      message.error(t('节点库.卸载失败'));
    }
  }, [onNodeUninstall, t]);

  // 渲染节点卡片
  const renderNodeCard = useCallback((node: NodeInfo) => (
    <Card
      key={node.id}
      size="small"
      hoverable
      style={{ marginBottom: 8 }}
      actions={[
        <Tooltip title={node.isFavorite ? t('节点库.取消收藏') : t('节点库.添加收藏')}>
          <Button
            type="text"
            icon={node.isFavorite ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />}
            onClick={() => toggleFavorite(node.id)}
          />
        </Tooltip>,
        <Tooltip title={t('节点库.查看详情')}>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleNodeDetail(node)}
          />
        </Tooltip>,
        node.isInstalled ? (
          <Tooltip title={t('节点库.使用节点')}>
            <Button
              type="text"
              icon={<PlusOutlined />}
              onClick={() => onNodeSelect(node.type)}
            />
          </Tooltip>
        ) : (
          <Tooltip title={t('节点库.安装节点')}>
            <Button
              type="text"
              icon={<DownloadOutlined />}
              onClick={() => handleInstallNode(node.id)}
            />
          </Tooltip>
        ),
        node.isInstalled && (
          <Tooltip title={t('节点库.卸载节点')}>
            <Button
              type="text"
              danger
              onClick={() => handleUninstallNode(node.id)}
            >
              {t('节点库.卸载')}
            </Button>
          </Tooltip>
        )
      ]}
    >
      <Card.Meta
        avatar={
          <div style={{ 
            width: 40, 
            height: 40, 
            backgroundColor: node.color, 
            borderRadius: 4,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontSize: 16
          }}>
            {node.icon}
          </div>
        }
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <span>{node.name}</span>
            {node.isInstalled && <Badge status="success" />}
          </div>
        }
        description={
          <div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {node.description}
            </Text>
            <div style={{ marginTop: 4 }}>
              <Rate disabled value={node.rating} style={{ fontSize: 12 }} />
              <Text type="secondary" style={{ marginLeft: 8, fontSize: 11 }}>
                {node.downloads} {t('节点库.下载量')}
              </Text>
            </div>
            <div style={{ marginTop: 4 }}>
              {node.tags.slice(0, 3).map(tag => (
                <Tag key={tag} style={{ fontSize: 10 }}>
                  {tag}
                </Tag>
              ))}
            </div>
          </div>
        }
      />
    </Card>
  ), [t, toggleFavorite, onNodeSelect, handleInstallNode]);

  return (
    <Drawer
      title={t('节点库.标题')}
      placement="right"
      width={800}
      open={visible}
      onClose={onClose}
      extra={
        <Space>
          <Button
            icon={<PlusOutlined />}
            onClick={handleCreateNode}
          >
            {t('节点库.创建节点')}
          </Button>
          <Button
            icon={<UploadOutlined />}
            onClick={handleImportNode}
          >
            {t('节点库.导入节点')}
          </Button>
        </Space>
      }
    >
      {/* 搜索和过滤区域 */}
      <div style={{ marginBottom: 16 }}>
        <Search
          placeholder={t('节点库.搜索节点') || '搜索节点'}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ marginBottom: 8 }}
        />
        
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <Select
              placeholder={t('节点库.选择分类')}
              value={selectedCategoryId || undefined}
              onChange={setSelectedCategoryId}
              allowClear
              style={{ width: 120 }}
            >
              {categories.map(category => (
                <Option key={category.id} value={category.id}>
                  {category.name}
                </Option>
              ))}
            </Select>
            
            <Select
              value={sortBy}
              onChange={setSortBy}
              style={{ width: 100 }}
            >
              <Option value="name">{t('节点库.按名称')}</Option>
              <Option value="rating">{t('节点库.按评分')}</Option>
              <Option value="downloads">{t('节点库.按下载')}</Option>
              <Option value="date">{t('节点库.按日期')}</Option>
            </Select>
            
            <Button
              icon={<SortAscendingOutlined />}
              onClick={() => setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc')}
            />
          </Space>
          
          <Space>
            <Button
              type={viewMode === 'grid' ? 'primary' : 'default'}
              icon={<AppstoreOutlined />}
              onClick={() => setViewMode('grid')}
            />
            <Button
              type={viewMode === 'list' ? 'primary' : 'default'}
              icon={<UnorderedListOutlined />}
              onClick={() => setViewMode('list')}
            />
          </Space>
        </div>
        
        <div style={{ marginTop: 8 }}>
          <Space>
            <Switch
              checked={showFavoritesOnly}
              onChange={setShowFavoritesOnly}
              checkedChildren={<StarFilled />}
              unCheckedChildren={<StarOutlined />}
            />
            <Text>{t('节点库.仅显示收藏')}</Text>
            
            <Switch
              checked={showInstalledOnly}
              onChange={setShowInstalledOnly}
            />
            <Text>{t('节点库.仅显示已安装')}</Text>
          </Space>
        </div>
      </div>

      <Divider />

      {/* 节点列表 */}
      <Spin spinning={loading}>
        {filteredAndSortedNodes.length === 0 ? (
          <Empty description={t('节点库.暂无节点')} />
        ) : (
          <div style={{ 
            display: viewMode === 'grid' ? 'grid' : 'block',
            gridTemplateColumns: viewMode === 'grid' ? 'repeat(auto-fill, minmax(300px, 1fr))' : undefined,
            gap: viewMode === 'grid' ? 16 : 0
          }}>
            {filteredAndSortedNodes.map(renderNodeCard)}
          </div>
        )}
      </Spin>

      {/* 节点详情模态框 */}
      <Modal
        title={selectedNode?.name}
        open={nodeDetailVisible}
        onCancel={() => setNodeDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setNodeDetailVisible(false)}>
            {t('通用.关闭')}
          </Button>,
          <Button
            key="install"
            type="primary"
            onClick={() => selectedNode && handleInstallNode(selectedNode.id)}
          >
            {t('节点库.安装')}
          </Button>
        ]}
      >
        {selectedNode && (
          <div>
            <p>{selectedNode.description}</p>
            <div style={{ marginTop: 16 }}>
              <Text strong>{t('节点库.版本')}: </Text>
              <Text>{selectedNode.version}</Text>
            </div>
            <div style={{ marginTop: 8 }}>
              <Text strong>{t('节点库.作者')}: </Text>
              <Text>{selectedNode.author}</Text>
            </div>
            <div style={{ marginTop: 8 }}>
              <Text strong>{t('节点库.标签')}: </Text>
              {selectedNode.tags.map(tag => (
                <Tag key={tag} style={{ marginLeft: 4 }}>{tag}</Tag>
              ))}
            </div>
          </div>
        )}
      </Modal>

      {/* 创建节点模态框 */}
      <Modal
        title={t('节点库.创建节点')}
        open={createNodeVisible}
        onCancel={() => setCreateNodeVisible(false)}
        onOk={() => {
          createForm.validateFields().then(() => {
            setCreateNodeVisible(false);
            createForm.resetFields();
          });
        }}
      >
        <Form form={createForm} layout="vertical">
          <Form.Item name="name" label={t('节点库.节点名称')} rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name="description" label={t('节点库.节点描述')}>
            <Input.TextArea />
          </Form.Item>
        </Form>
      </Modal>

      {/* 导入节点模态框 */}
      <Modal
        title={t('节点库.导入节点')}
        open={importNodeVisible}
        onCancel={() => setImportNodeVisible(false)}
        onOk={() => setImportNodeVisible(false)}
      >
        <div>
          <p>{t('节点库.选择要导入的节点文件')}</p>
          <Input type="file" accept=".json,.js" />
        </div>
      </Modal>
    </Drawer>
  );
};

export default EnhancedNodeLibrary;
