/**
 * 用户行为分析服务
 * 收集和分析用户在UI编辑器中的操作行为
 */

// 事件类型枚举
export enum AnalyticsEventType {
  // UI组件操作
  COMPONENT_CREATE = 'component_create',
  COMPONENT_DELETE = 'component_delete',
  COMPONENT_UPDATE = 'component_update',
  COMPONENT_MOVE = 'component_move',
  COMPONENT_RESIZE = 'component_resize',
  COMPONENT_COPY = 'component_copy',
  
  // 编辑器操作
  EDITOR_OPEN = 'editor_open',
  EDITOR_CLOSE = 'editor_close',
  EDITOR_SAVE = 'editor_save',
  EDITOR_UNDO = 'editor_undo',
  EDITOR_REDO = 'editor_redo',
  EDITOR_PREVIEW = 'editor_preview',
  
  // 主题和样式
  THEME_SWITCH = 'theme_switch',
  STYLE_CHANGE = 'style_change',
  
  // 拖拽操作
  DRAG_START = 'drag_start',
  DRAG_END = 'drag_end',
  DROP_SUCCESS = 'drop_success',
  DROP_FAILED = 'drop_failed',
  
  // 用户交互
  CLICK = 'click',
  HOVER = 'hover',
  FOCUS = 'focus',
  SCROLL = 'scroll',
  
  // 性能相关
  PERFORMANCE_SLOW = 'performance_slow',
  ERROR_OCCURRED = 'error_occurred',
  
  // 功能使用
  FEATURE_USED = 'feature_used',
  SHORTCUT_USED = 'shortcut_used',
  HELP_VIEWED = 'help_viewed'
}

// 分析事件数据接口
export interface AnalyticsEvent {
  id: string;
  type: AnalyticsEventType;
  timestamp: number;
  userId?: string;
  sessionId: string;
  data: Record<string, any>;
  context: {
    url: string;
    userAgent: string;
    viewport: { width: number; height: number };
    component?: string;
    feature?: string;
  };
}

// 用户会话信息
export interface UserSession {
  id: string;
  userId?: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  events: AnalyticsEvent[];
  metadata: {
    browser: string;
    os: string;
    device: string;
    language: string;
    timezone: string;
  };
}

// 分析统计数据
export interface AnalyticsStats {
  totalEvents: number;
  totalSessions: number;
  averageSessionDuration: number;
  mostUsedFeatures: Array<{ feature: string; count: number }>;
  commonUserPaths: Array<{ path: string[]; count: number }>;
  errorRate: number;
  performanceMetrics: {
    averageLoadTime: number;
    slowOperations: Array<{ operation: string; averageTime: number }>;
  };
}

// 用户行为模式
export interface UserBehaviorPattern {
  pattern: string;
  frequency: number;
  description: string;
  suggestions: string[];
}

/**
 * 用户行为分析服务类
 */
export class UserAnalyticsService {
  private events: AnalyticsEvent[] = [];
  private currentSession: UserSession | null = null;
  private sessionId: string;
  private isEnabled: boolean = true;
  private batchSize: number = 50;
  private flushInterval: number = 30000; // 30秒
  private flushTimer?: number;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.initializeSession();
    this.setupEventListeners();
    this.startPeriodicFlush();
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 初始化用户会话
   */
  private initializeSession(): void {
    this.currentSession = {
      id: this.sessionId,
      startTime: Date.now(),
      events: [],
      metadata: {
        browser: this.getBrowserInfo(),
        os: this.getOSInfo(),
        device: this.getDeviceInfo(),
        language: navigator.language,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
      }
    };

    // 记录会话开始事件
    this.trackEvent(AnalyticsEventType.EDITOR_OPEN, {
      sessionStart: true
    });
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 页面卸载时结束会话
    window.addEventListener('beforeunload', () => {
      this.endSession();
    });

    // 页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.trackEvent(AnalyticsEventType.EDITOR_CLOSE, {
          reason: 'tab_hidden'
        });
      } else {
        this.trackEvent(AnalyticsEventType.EDITOR_OPEN, {
          reason: 'tab_visible'
        });
      }
    });

    // 错误监听
    window.addEventListener('error', (event) => {
      this.trackEvent(AnalyticsEventType.ERROR_OCCURRED, {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack
      });
    });

    // 性能监听
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 1000) { // 超过1秒的操作
            this.trackEvent(AnalyticsEventType.PERFORMANCE_SLOW, {
              name: entry.name,
              duration: entry.duration,
              startTime: entry.startTime
            });
          }
        }
      });
      observer.observe({ entryTypes: ['measure', 'navigation'] });
    }
  }

  /**
   * 开始定期刷新
   */
  private startPeriodicFlush(): void {
    this.flushTimer = window.setInterval(() => {
      this.flush();
    }, this.flushInterval);
  }

  /**
   * 跟踪事件
   */
  trackEvent(type: AnalyticsEventType, data: Record<string, any> = {}, component?: string): void {
    if (!this.isEnabled) return;

    const event: AnalyticsEvent = {
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      data,
      context: {
        url: window.location.href,
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        component,
        feature: data.feature
      }
    };

    this.events.push(event);
    
    if (this.currentSession) {
      this.currentSession.events.push(event);
    }

    // 如果事件数量达到批次大小，立即刷新
    if (this.events.length >= this.batchSize) {
      this.flush();
    }
  }

  /**
   * 跟踪组件操作
   */
  trackComponentOperation(operation: string, componentType: string, data: Record<string, any> = {}): void {
    const eventType = this.getComponentEventType(operation);
    this.trackEvent(eventType, {
      operation,
      componentType,
      ...data
    }, componentType);
  }

  /**
   * 跟踪功能使用
   */
  trackFeatureUsage(feature: string, data: Record<string, any> = {}): void {
    this.trackEvent(AnalyticsEventType.FEATURE_USED, {
      feature,
      ...data
    });
  }

  /**
   * 跟踪性能指标
   */
  trackPerformance(operation: string, duration: number, data: Record<string, any> = {}): void {
    this.trackEvent(AnalyticsEventType.PERFORMANCE_SLOW, {
      operation,
      duration,
      ...data
    });
  }

  /**
   * 跟踪用户路径
   */
  trackUserPath(path: string[], data: Record<string, any> = {}): void {
    this.trackEvent(AnalyticsEventType.FEATURE_USED, {
      userPath: path,
      pathLength: path.length,
      ...data
    });
  }

  /**
   * 获取组件事件类型
   */
  private getComponentEventType(operation: string): AnalyticsEventType {
    switch (operation.toLowerCase()) {
      case 'create':
        return AnalyticsEventType.COMPONENT_CREATE;
      case 'delete':
        return AnalyticsEventType.COMPONENT_DELETE;
      case 'update':
        return AnalyticsEventType.COMPONENT_UPDATE;
      case 'move':
        return AnalyticsEventType.COMPONENT_MOVE;
      case 'resize':
        return AnalyticsEventType.COMPONENT_RESIZE;
      case 'copy':
        return AnalyticsEventType.COMPONENT_COPY;
      default:
        return AnalyticsEventType.FEATURE_USED;
    }
  }

  /**
   * 刷新事件到服务器
   */
  private async flush(): Promise<void> {
    if (this.events.length === 0) return;

    const eventsToSend = [...this.events];
    this.events = [];

    try {
      // 这里应该发送到分析服务器
      await this.sendEventsToServer(eventsToSend);
      console.log(`已发送 ${eventsToSend.length} 个分析事件`);
    } catch (error) {
      console.error('发送分析事件失败:', error);
      // 失败时重新加入队列
      this.events.unshift(...eventsToSend);
    }
  }

  /**
   * 发送事件到服务器
   */
  private async sendEventsToServer(_events: AnalyticsEvent[]): Promise<void> {
    // 模拟API调用
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (Math.random() > 0.1) { // 90%成功率
          resolve();
        } else {
          reject(new Error('网络错误'));
        }
      }, 100);
    });
  }

  /**
   * 结束会话
   */
  endSession(): void {
    if (this.currentSession) {
      this.currentSession.endTime = Date.now();
      this.currentSession.duration = this.currentSession.endTime - this.currentSession.startTime;
      
      this.trackEvent(AnalyticsEventType.EDITOR_CLOSE, {
        sessionEnd: true,
        sessionDuration: this.currentSession.duration
      });
    }

    // 立即刷新所有待发送事件
    this.flush();

    // 清理定时器
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
  }

  /**
   * 获取分析统计
   */
  getAnalyticsStats(): AnalyticsStats {
    const allEvents = this.currentSession?.events || [];
    
    // 计算最常用功能
    const featureUsage = new Map<string, number>();
    allEvents.forEach(event => {
      if (event.data.feature) {
        featureUsage.set(event.data.feature, (featureUsage.get(event.data.feature) || 0) + 1);
      }
    });

    const mostUsedFeatures = Array.from(featureUsage.entries())
      .map(([feature, count]) => ({ feature, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // 计算错误率
    const errorEvents = allEvents.filter(e => e.type === AnalyticsEventType.ERROR_OCCURRED);
    const errorRate = allEvents.length > 0 ? (errorEvents.length / allEvents.length) * 100 : 0;

    // 计算性能指标
    const performanceEvents = allEvents.filter(e => e.type === AnalyticsEventType.PERFORMANCE_SLOW);
    const averageLoadTime = performanceEvents.length > 0 
      ? performanceEvents.reduce((sum, e) => sum + (e.data.duration || 0), 0) / performanceEvents.length 
      : 0;

    return {
      totalEvents: allEvents.length,
      totalSessions: 1,
      averageSessionDuration: this.currentSession?.duration || 0,
      mostUsedFeatures,
      commonUserPaths: [], // 需要更复杂的分析
      errorRate,
      performanceMetrics: {
        averageLoadTime,
        slowOperations: []
      }
    };
  }

  /**
   * 分析用户行为模式
   */
  analyzeUserBehavior(): UserBehaviorPattern[] {
    const patterns: UserBehaviorPattern[] = [];
    const events = this.currentSession?.events || [];

    // 分析频繁的操作序列
    const operationSequences = this.findOperationSequences(events);
    
    operationSequences.forEach(sequence => {
      if (sequence.frequency > 3) {
        patterns.push({
          pattern: sequence.pattern,
          frequency: sequence.frequency,
          description: `用户经常执行操作序列: ${sequence.pattern}`,
          suggestions: this.generateSuggestions(sequence.pattern)
        });
      }
    });

    return patterns;
  }

  /**
   * 查找操作序列
   */
  private findOperationSequences(events: AnalyticsEvent[]): Array<{ pattern: string; frequency: number }> {
    const sequences = new Map<string, number>();
    const windowSize = 3;

    for (let i = 0; i <= events.length - windowSize; i++) {
      const sequence = events.slice(i, i + windowSize)
        .map(e => e.type)
        .join(' -> ');
      
      sequences.set(sequence, (sequences.get(sequence) || 0) + 1);
    }

    return Array.from(sequences.entries())
      .map(([pattern, frequency]) => ({ pattern, frequency }))
      .sort((a, b) => b.frequency - a.frequency);
  }

  /**
   * 生成改进建议
   */
  private generateSuggestions(pattern: string): string[] {
    const suggestions: string[] = [];

    if (pattern.includes('COMPONENT_CREATE -> COMPONENT_DELETE')) {
      suggestions.push('考虑添加组件预览功能，减少创建后删除的操作');
    }

    if (pattern.includes('EDITOR_UNDO -> EDITOR_UNDO')) {
      suggestions.push('考虑添加批量撤销功能或操作历史面板');
    }

    if (pattern.includes('DRAG_START -> DROP_FAILED')) {
      suggestions.push('改进拖拽反馈，提供更清晰的放置区域指示');
    }

    return suggestions;
  }

  /**
   * 获取浏览器信息
   */
  private getBrowserInfo(): string {
    const userAgent = navigator.userAgent;
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Unknown';
  }

  /**
   * 获取操作系统信息
   */
  private getOSInfo(): string {
    const userAgent = navigator.userAgent;
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS')) return 'iOS';
    return 'Unknown';
  }

  /**
   * 获取设备信息
   */
  private getDeviceInfo(): string {
    const width = window.screen.width;
    if (width < 768) return 'Mobile';
    if (width < 1024) return 'Tablet';
    return 'Desktop';
  }

  /**
   * 启用/禁用分析
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * 获取当前会话
   */
  getCurrentSession(): UserSession | null {
    return this.currentSession;
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this.endSession();
  }
}

// 创建全局实例
export const userAnalyticsService = new UserAnalyticsService();
