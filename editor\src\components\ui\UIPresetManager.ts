/**
 * UI预设管理器
 * 提供常用的UI元素预设和模板
 */
import { UIElementData, UIElementType } from './UIElementEditor';

/**
 * UI预设接口
 */
export interface UIPreset {
  id: string;
  name: string;
  description: string;
  category: string;
  type: UIElementType;
  data: Partial<UIElementData>;
  thumbnail?: string;
  tags?: string[];
}

/**
 * UI预设类别
 */
export enum UIPresetCategory {
  BASIC = 'basic',
  MODERN = 'modern',
  CLASSIC = 'classic',
  GAMING = 'gaming',
  MOBILE = 'mobile',
  CUSTOM = 'custom'
}

/**
 * UI预设管理器类
 */
export class UIPresetManager {
  private static instance: UIPresetManager;
  private presets: Map<string, UIPreset> = new Map();

  private constructor() {
    this.initializeBuiltinPresets();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): UIPresetManager {
    if (!UIPresetManager.instance) {
      UIPresetManager.instance = new UIPresetManager();
    }
    return UIPresetManager.instance;
  }

  /**
   * 初始化内置预设
   */
  private initializeBuiltinPresets(): void {
    // 基础按钮预设
    this.addPreset({
      id: 'button-basic',
      name: '基础按钮',
      description: '简单的基础按钮样式',
      category: UIPresetCategory.BASIC,
      type: UIElementType.BUTTON,
      data: {
        backgroundColor: '#f0f0f0',
        borderColor: '#d9d9d9',
        borderWidth: 1,
        borderRadius: 4,
        fontColor: '#333333',
        fontSize: 14,
        padding: { top: 8, right: 16, bottom: 8, left: 16 }
      },
      tags: ['button', 'basic', 'simple']
    });

    // 主要按钮预设
    this.addPreset({
      id: 'button-primary',
      name: '主要按钮',
      description: '突出的主要操作按钮',
      category: UIPresetCategory.BASIC,
      type: UIElementType.BUTTON,
      data: {
        backgroundColor: '#1890ff',
        borderColor: '#1890ff',
        borderWidth: 1,
        borderRadius: 6,
        fontColor: '#ffffff',
        fontSize: 14,
        padding: { top: 8, right: 16, bottom: 8, left: 16 }
      },
      tags: ['button', 'primary', 'action']
    });

    // 危险按钮预设
    this.addPreset({
      id: 'button-danger',
      name: '危险按钮',
      description: '用于危险操作的警告按钮',
      category: UIPresetCategory.BASIC,
      type: UIElementType.BUTTON,
      data: {
        backgroundColor: '#ff4d4f',
        borderColor: '#ff4d4f',
        borderWidth: 1,
        borderRadius: 6,
        fontColor: '#ffffff',
        fontSize: 14,
        padding: { top: 8, right: 16, bottom: 8, left: 16 }
      },
      tags: ['button', 'danger', 'warning']
    });

    // 现代风格按钮
    this.addPreset({
      id: 'button-modern',
      name: '现代按钮',
      description: '现代扁平化设计风格按钮',
      category: UIPresetCategory.MODERN,
      type: UIElementType.BUTTON,
      data: {
        backgroundColor: '#6366f1',
        borderColor: 'transparent',
        borderWidth: 0,
        borderRadius: 12,
        fontColor: '#ffffff',
        fontSize: 16,
        padding: { top: 12, right: 24, bottom: 12, left: 24 }
      },
      tags: ['button', 'modern', 'flat']
    });

    // 游戏风格按钮
    this.addPreset({
      id: 'button-gaming',
      name: '游戏按钮',
      description: '游戏界面风格的按钮',
      category: UIPresetCategory.GAMING,
      type: UIElementType.BUTTON,
      data: {
        backgroundColor: '#722ed1',
        borderColor: '#9254de',
        borderWidth: 2,
        borderRadius: 8,
        fontColor: '#ffffff',
        fontSize: 16,
        padding: { top: 12, right: 20, bottom: 12, left: 20 }
      },
      tags: ['button', 'gaming', 'colorful']
    });

    // 基础文本预设
    this.addPreset({
      id: 'text-basic',
      name: '基础文本',
      description: '标准的文本样式',
      category: UIPresetCategory.BASIC,
      type: UIElementType.TEXT,
      data: {
        backgroundColor: 'transparent',
        borderWidth: 0,
        fontColor: '#333333',
        fontSize: 14,
        fontFamily: 'Arial'
      },
      tags: ['text', 'basic', 'content']
    });

    // 标题文本预设
    this.addPreset({
      id: 'text-heading',
      name: '标题文本',
      description: '大号标题文本样式',
      category: UIPresetCategory.BASIC,
      type: UIElementType.TEXT,
      data: {
        backgroundColor: 'transparent',
        borderWidth: 0,
        fontColor: '#262626',
        fontSize: 24,
        fontFamily: 'Arial'
      },
      tags: ['text', 'heading', 'title']
    });

    // 基础面板预设
    this.addPreset({
      id: 'panel-basic',
      name: '基础面板',
      description: '简单的容器面板',
      category: UIPresetCategory.BASIC,
      type: UIElementType.PANEL,
      data: {
        backgroundColor: '#ffffff',
        borderColor: '#d9d9d9',
        borderWidth: 1,
        borderRadius: 8,
        padding: { top: 16, right: 16, bottom: 16, left: 16 },
        size: { width: 300, height: 200 }
      },
      tags: ['panel', 'container', 'basic']
    });

    // 卡片面板预设
    this.addPreset({
      id: 'panel-card',
      name: '卡片面板',
      description: '带阴影的卡片样式面板',
      category: UIPresetCategory.MODERN,
      type: UIElementType.PANEL,
      data: {
        backgroundColor: '#ffffff',
        borderColor: '#f0f0f0',
        borderWidth: 1,
        borderRadius: 12,
        padding: { top: 20, right: 20, bottom: 20, left: 20 },
        size: { width: 320, height: 240 }
      },
      tags: ['panel', 'card', 'modern']
    });

    // 基础输入框预设
    this.addPreset({
      id: 'input-basic',
      name: '基础输入框',
      description: '标准的文本输入框',
      category: UIPresetCategory.BASIC,
      type: UIElementType.INPUT,
      data: {
        backgroundColor: '#ffffff',
        borderColor: '#d9d9d9',
        borderWidth: 1,
        borderRadius: 4,
        fontColor: '#333333',
        fontSize: 14,
        padding: { top: 8, right: 12, bottom: 8, left: 12 },
        size: { width: 200, height: 32 }
      },
      tags: ['input', 'form', 'basic']
    });

    // 现代输入框预设
    this.addPreset({
      id: 'input-modern',
      name: '现代输入框',
      description: '现代风格的输入框',
      category: UIPresetCategory.MODERN,
      type: UIElementType.INPUT,
      data: {
        backgroundColor: '#f8f9fa',
        borderColor: '#e9ecef',
        borderWidth: 1,
        borderRadius: 8,
        fontColor: '#495057',
        fontSize: 14,
        padding: { top: 12, right: 16, bottom: 12, left: 16 },
        size: { width: 240, height: 40 }
      },
      tags: ['input', 'modern', 'form']
    });

    // 基础窗口预设
    this.addPreset({
      id: 'window-basic',
      name: '基础窗口',
      description: '标准的窗口容器',
      category: UIPresetCategory.BASIC,
      type: UIElementType.WINDOW,
      data: {
        backgroundColor: '#ffffff',
        borderColor: '#d9d9d9',
        borderWidth: 1,
        borderRadius: 8,
        padding: { top: 20, right: 20, bottom: 20, left: 20 },
        size: { width: 400, height: 300 }
      },
      tags: ['window', 'container', 'dialog']
    });

    // 模态窗口预设
    this.addPreset({
      id: 'window-modal',
      name: '模态窗口',
      description: '模态对话框样式窗口',
      category: UIPresetCategory.MODERN,
      type: UIElementType.WINDOW,
      data: {
        backgroundColor: '#ffffff',
        borderColor: '#f0f0f0',
        borderWidth: 1,
        borderRadius: 12,
        padding: { top: 24, right: 24, bottom: 24, left: 24 },
        size: { width: 480, height: 360 }
      },
      tags: ['window', 'modal', 'dialog']
    });
  }

  /**
   * 添加预设
   */
  public addPreset(preset: UIPreset): void {
    this.presets.set(preset.id, preset);
  }

  /**
   * 获取预设
   */
  public getPreset(id: string): UIPreset | undefined {
    return this.presets.get(id);
  }

  /**
   * 获取所有预设
   */
  public getAllPresets(): UIPreset[] {
    return Array.from(this.presets.values());
  }

  /**
   * 按类别获取预设
   */
  public getPresetsByCategory(category: UIPresetCategory): UIPreset[] {
    return Array.from(this.presets.values()).filter(preset => preset.category === category);
  }

  /**
   * 按类型获取预设
   */
  public getPresetsByType(type: UIElementType): UIPreset[] {
    return Array.from(this.presets.values()).filter(preset => preset.type === type);
  }

  /**
   * 搜索预设
   */
  public searchPresets(query: string): UIPreset[] {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.presets.values()).filter(preset => 
      preset.name.toLowerCase().includes(lowerQuery) ||
      preset.description.toLowerCase().includes(lowerQuery) ||
      preset.tags?.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  /**
   * 删除预设
   */
  public removePreset(id: string): boolean {
    return this.presets.delete(id);
  }

  /**
   * 清空所有预设
   */
  public clearPresets(): void {
    this.presets.clear();
  }

  /**
   * 导出预设数据
   */
  public exportPresets(): UIPreset[] {
    return this.getAllPresets();
  }

  /**
   * 导入预设数据
   */
  public importPresets(presets: UIPreset[]): void {
    presets.forEach(preset => this.addPreset(preset));
  }
}

// 导出单例实例
export default UIPresetManager.getInstance();
