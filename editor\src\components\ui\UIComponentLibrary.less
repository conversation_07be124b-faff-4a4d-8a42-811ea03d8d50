.ui-component-library {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;

  .library-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: #ffffff;
    border-bottom: 1px solid #e8e8e8;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .toolbar-right {
      display: flex;
      align-items: center;
    }
  }

  .library-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;

    .component-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 16px;

      .component-card {
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 8px;
        overflow: hidden;

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }

        .ant-card-cover {
          height: 160px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .component-placeholder {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff;
            font-size: 32px;
          }
        }

        .ant-card-body {
          padding: 16px;

          .component-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            span {
              font-weight: 600;
              color: #262626;
            }
          }

          .component-description {
            p {
              margin-bottom: 8px;
              color: #595959;
              font-size: 13px;
              line-height: 1.4;
            }

            .component-meta {
              display: flex;
              justify-content: space-between;
              margin-bottom: 8px;
              font-size: 12px;
              color: #8c8c8c;

              span {
                &:not(:last-child)::after {
                  content: '•';
                  margin: 0 6px;
                  color: #d9d9d9;
                }
              }
            }

            .component-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 4px;
            }
          }
        }

        .ant-card-actions {
          border-top: 1px solid #f0f0f0;

          li {
            margin: 8px 0;

            .anticon {
              font-size: 16px;
              color: #595959;
              transition: color 0.3s ease;

              &:hover {
                color: #1890ff;
              }
            }

            .anticon-star {
              &:hover {
                color: #faad14;
              }
            }

            .anticon-delete {
              &:hover {
                color: #ff4d4f;
              }
            }
          }
        }
      }
    }

    .component-list {
      background: #ffffff;
      border-radius: 8px;

      .ant-list-item {
        cursor: pointer;
        padding: 16px 24px;
        transition: background-color 0.3s ease;

        &:hover {
          background: #f5f5f5;
        }

        .list-item-title {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 4px;

          span {
            font-weight: 600;
            color: #262626;
          }
        }

        .list-item-meta {
          display: flex;
          gap: 16px;
          margin-bottom: 8px;
          font-size: 12px;
          color: #8c8c8c;
        }

        .list-item-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 4px;
        }

        .ant-list-item-action {
          li {
            .ant-btn {
              border: none;
              box-shadow: none;

              &:hover {
                background: #f0f0f0;
              }
            }
          }
        }
      }
    }
  }

  // 预览模态框样式
  .component-preview {
    .preview-info {
      margin-bottom: 24px;
      padding: 16px;
      background: #f9f9f9;
      border-radius: 6px;

      h4 {
        margin-bottom: 12px;
        color: #262626;
        font-weight: 600;
      }

      p {
        margin-bottom: 8px;
        color: #595959;

        strong {
          color: #262626;
        }
      }

      .preview-tags {
        margin-top: 12px;
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
      }
    }

    .preview-demo {
      h4 {
        margin-bottom: 12px;
        color: #262626;
        font-weight: 600;
      }

      .demo-container {
        min-height: 200px;
        padding: 24px;
        background: #ffffff;
        border: 1px solid #e8e8e8;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #8c8c8c;
        font-style: italic;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .ui-component-library {
    .library-content {
      .component-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 12px;
      }
    }
  }
}

@media (max-width: 768px) {
  .ui-component-library {
    .library-toolbar {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .toolbar-left {
        flex-direction: column;
        gap: 8px;

        .ant-input-affix-wrapper,
        .ant-select {
          width: 100% !important;
        }
      }

      .toolbar-right {
        justify-content: center;
      }
    }

    .library-content {
      padding: 12px;

      .component-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 8px;

        .component-card {
          .ant-card-cover {
            height: 120px;

            .component-placeholder {
              font-size: 24px;
            }
          }

          .ant-card-body {
            padding: 12px;
          }
        }
      }

      .component-list {
        .ant-list-item {
          padding: 12px 16px;

          .ant-list-item-meta {
            .ant-list-item-meta-avatar {
              margin-right: 12px;
            }
          }

          .ant-list-item-action {
            margin-left: 12px;

            li {
              margin: 0 4px;
            }
          }
        }
      }
    }
  }
}

// 动画效果
@keyframes cardAppear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.component-card {
  animation: cardAppear 0.3s ease;
}

// 加载状态
.ant-spin-container {
  min-height: 300px;
}

// 空状态
.ant-empty {
  padding: 60px 0;
}

// 标签样式
.ant-tag {
  border-radius: 12px;
  font-size: 11px;
  line-height: 1.2;
  padding: 2px 8px;
  margin: 0;

  &.ant-tag-blue {
    background: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
  }

  &.ant-tag-green {
    background: #f6ffed;
    border-color: #b7eb8f;
    color: #52c41a;
  }

  &.ant-tag-purple {
    background: #f9f0ff;
    border-color: #d3adf7;
    color: #722ed1;
  }

  &.ant-tag-orange {
    background: #fff7e6;
    border-color: #ffd591;
    color: #fa8c16;
  }

  &.ant-tag-cyan {
    background: #e6fffb;
    border-color: #87e8de;
    color: #13c2c2;
  }

  &.ant-tag-magenta {
    background: #fff0f6;
    border-color: #ffadd2;
    color: #eb2f96;
  }

  &.ant-tag-gold {
    background: #fffbe6;
    border-color: #ffe58f;
    color: #faad14;
  }
}

// 工具提示样式
.ant-tooltip {
  .ant-tooltip-inner {
    background: #262626;
    color: #ffffff;
    border-radius: 4px;
    font-size: 12px;
  }

  .ant-tooltip-arrow {
    &::before {
      background: #262626;
    }
  }
}
