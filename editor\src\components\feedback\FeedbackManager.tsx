/**
 * 反馈管理组件
 * 用于管理和分析用户反馈
 */
import React, { useState, useEffect } from 'react';
import {
  Table,
  Tabs,
  Button,
  Space,
  Tag,
  Select,
  Tooltip,
  Drawer,
  Divider,
  Typography,
  Empty,
  Popconfirm,
  message,
  DatePicker,
  Input,
  Rate
} from 'antd';
import {
  ReloadOutlined,
  FilterOutlined,
  ExportOutlined,
  EyeOutlined,
  DeleteOutlined,
  CheckOutlined,
  BugOutlined,
  RocketOutlined,
  ToolOutlined,
  CommentOutlined,
  UserOutlined,
  ClockCircleOutlined,
  PieChartOutlined,
  FileExcelOutlined,
  FilePdfOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { FeedbackService, FeedbackData, FeedbackStats } from '../../services/FeedbackService';
import FeedbackAnalytics from './FeedbackAnalytics';
import FeedbackDetail from './FeedbackDetail';
import './FeedbackManager.less';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { Search } = Input;
const { Title, Text } = Typography;

export interface FeedbackManagerProps {
  /** 是否自动刷新 */
  autoRefresh?: boolean;
  /** 刷新间隔（毫秒） */
  refreshInterval?: number;
}

/**
 * 反馈管理组件
 */
const FeedbackManager: React.FC<FeedbackManagerProps> = ({
  autoRefresh = false,
  refreshInterval = 60000
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [feedbackList, setFeedbackList] = useState<FeedbackData[]>([]);
  const [, setStats] = useState<FeedbackStats | null>(null);
  const [filters, setFilters] = useState<any>({});
  const [searchText, setSearchText] = useState('');
  const [selectedFeedback, setSelectedFeedback] = useState<FeedbackData | null>(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('list');

  // 加载反馈数据
  const loadFeedbackData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 准备过滤条件
      const queryFilters: any = { ...filters };
      
      // 处理日期范围
      if (queryFilters.dateRange && queryFilters.dateRange.length === 2) {
        queryFilters.startDate = queryFilters.dateRange[0].toISOString();
        queryFilters.endDate = queryFilters.dateRange[1].toISOString();
        delete queryFilters.dateRange;
      }

      // 处理搜索文本
      if (searchText) {
        queryFilters.search = searchText;
      }

      // 获取反馈列表
      const list = await FeedbackService.getFeedbackList(queryFilters);
      setFeedbackList(list);

      // 获取反馈统计
      const statistics = await FeedbackService.getFeedbackStats(queryFilters);
      setStats(statistics);
    } catch (error) {
      console.error('加载反馈数据失败:', error);
      setError(t('feedback.manager.loadError'));
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadFeedbackData();

    // 设置自动刷新
    if (autoRefresh && refreshInterval > 0) {
      const intervalId = setInterval(loadFeedbackData, refreshInterval);
      return () => clearInterval(intervalId);
    }
  }, []);

  // 过滤条件变化时重新加载
  useEffect(() => {
    loadFeedbackData();
  }, [filters, searchText]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  // 处理过滤器变化
  const handleFilterChange = (key: string, value: any) => {
    setFilters((prev: any) => ({
      ...prev,
      [key]: value
    }));
  };

  // 重置过滤器
  const resetFilters = () => {
    setFilters({});
    setSearchText('');
  };

  // 查看反馈详情
  const viewFeedbackDetail = (feedback: FeedbackData) => {
    setSelectedFeedback(feedback);
    setDetailVisible(true);
  };

  // 关闭详情抽屉
  const closeDetail = () => {
    setDetailVisible(false);
    setSelectedFeedback(null);
  };

  // 导出反馈数据
  const exportFeedback = async (format: 'excel' | 'pdf' | 'json') => {
    try {
      setExportLoading(true);
      await FeedbackService.exportFeedback(format, filters);
      message.success(t('feedback.manager.exportSuccess'));
    } catch (error) {
      console.error('导出反馈失败:', error);
      message.error(t('feedback.manager.exportError'));
    } finally {
      setExportLoading(false);
    }
  };

  // 删除反馈
  const deleteFeedback = async (id: string) => {
    try {
      await FeedbackService.deleteFeedback(id);
      message.success(t('feedback.manager.deleteSuccess'));
      loadFeedbackData();
    } catch (error) {
      console.error('删除反馈失败:', error);
      message.error(t('feedback.manager.deleteError'));
    }
  };

  // 标记反馈状态
  const markFeedbackStatus = async (id: string, status: 'new' | 'inProgress' | 'resolved' | 'closed') => {
    try {
      await FeedbackService.updateFeedback(id, { status });
      message.success(t('feedback.manager.updateSuccess'));
      loadFeedbackData();
    } catch (error) {
      console.error('更新反馈状态失败:', error);
      message.error(t('feedback.manager.updateError'));
    }
  };

  // 渲染过滤器
  const renderFilters = () => {
    return (
      <div className="feedback-filters">
        <Space wrap>
          <Search
            placeholder={t('feedback.manager.search') || ''}
            onSearch={handleSearch}
            style={{ width: 200 }}
            allowClear
          />
          
          <Select
            placeholder={t('feedback.manager.filterType') || ''}
            style={{ width: 150 }}
            onChange={(value) => handleFilterChange('type', value)}
            value={filters.type}
            allowClear
          >
            <Option value="animation">{t('feedback.title.animation') || ''}</Option>
            <Option value="physics">{t('feedback.title.physics') || ''}</Option>
            <Option value="rendering">{t('feedback.title.rendering') || ''}</Option>
            <Option value="editor">{t('feedback.title.editor') || ''}</Option>
            <Option value="general">{t('feedback.title.general') || ''}</Option>
          </Select>

          <Select
            placeholder={t('feedback.manager.filterFeedbackType') || ''}
            style={{ width: 150 }}
            onChange={(value) => handleFilterChange('feedbackType', value)}
            value={filters.feedbackType}
            allowClear
          >
            <Option value="bug">{t('feedback.type.bug') || ''}</Option>
            <Option value="feature">{t('feedback.type.feature') || ''}</Option>
            <Option value="improvement">{t('feedback.type.improvement') || ''}</Option>
            <Option value="performance">{t('feedback.type.performance') || ''}</Option>
            <Option value="usability">{t('feedback.type.usability') || ''}</Option>
            <Option value="other">{t('feedback.type.other') || ''}</Option>
          </Select>

          <Select
            placeholder={t('feedback.manager.filterStatus') || ''}
            style={{ width: 150 }}
            onChange={(value) => handleFilterChange('status', value)}
            value={filters.status}
            allowClear
          >
            <Option value="new">{t('feedback.manager.statusNew') || ''}</Option>
            <Option value="inProgress">{t('feedback.manager.statusInProgress') || ''}</Option>
            <Option value="resolved">{t('feedback.manager.statusResolved') || ''}</Option>
            <Option value="closed">{t('feedback.manager.statusClosed') || ''}</Option>
          </Select>
          
          <RangePicker
            placeholder={[
              t('feedback.manager.startDate') || '',
              t('feedback.manager.endDate') || ''
            ]}
            onChange={(dates) => handleFilterChange('dateRange', dates)}
            value={filters.dateRange}
          />
          
          <Button
            icon={<ReloadOutlined />}
            onClick={loadFeedbackData}
            loading={loading}
          >
            {t('feedback.manager.refresh') || ''}
          </Button>

          <Button
            icon={<FilterOutlined />}
            onClick={resetFilters}
          >
            {t('feedback.manager.resetFilters') || ''}
          </Button>
        </Space>
      </div>
    );
  };

  // 渲染表格
  const renderTable = () => {
    const columns = [
      {
        title: t('feedback.manager.id') || '',
        dataIndex: 'id',
        key: 'id',
        width: 80,
        ellipsis: true
      },
      {
        title: t('feedback.manager.title') || '',
        dataIndex: 'title',
        key: 'title',
        render: (text: string, record: FeedbackData) => (
          <a onClick={() => viewFeedbackDetail(record)}>{text}</a>
        )
      },
      {
        title: t('feedback.manager.type') || '',
        dataIndex: 'type',
        key: 'type',
        width: 120,
        render: (text: string) => (
          <Tag color="blue">{t(`feedback.title.${text}`) || ''}</Tag>
        )
      },
      {
        title: t('feedback.manager.feedbackType') || '',
        dataIndex: 'feedbackType',
        key: 'feedbackType',
        width: 120,
        render: (text: string) => {
          let color = 'default';
          let icon = null;

          switch (text) {
            case 'bug':
              color = 'error';
              icon = <BugOutlined />;
              break;
            case 'feature':
              color = 'success';
              icon = <RocketOutlined />;
              break;
            case 'improvement':
              color = 'processing';
              icon = <ToolOutlined />;
              break;
            case 'performance':
              color = 'warning';
              icon = <ClockCircleOutlined />;
              break;
            case 'usability':
              color = 'cyan';
              icon = <UserOutlined />;
              break;
            default:
              icon = <CommentOutlined />;
          }

          return (
            <Tag color={color} icon={icon}>
              {t(`feedback.type.${text}`) || ''}
            </Tag>
          );
        }
      },
      {
        title: t('feedback.manager.satisfaction') || '',
        dataIndex: 'satisfaction',
        key: 'satisfaction',
        width: 120,
        render: (value: number) => (
          value ? <Rate disabled defaultValue={value} /> : '-'
        )
      },
      {
        title: t('feedback.manager.status') || '',
        dataIndex: 'status',
        key: 'status',
        width: 120,
        render: (text: string) => {
          let color = 'default';
          let statusText = t('feedback.manager.statusNew') || '';

          switch (text) {
            case 'new':
              color = 'blue';
              statusText = t('feedback.manager.statusNew') || '';
              break;
            case 'inProgress':
              color = 'processing';
              statusText = t('feedback.manager.statusInProgress') || '';
              break;
            case 'resolved':
              color = 'success';
              statusText = t('feedback.manager.statusResolved') || '';
              break;
            case 'closed':
              color = 'default';
              statusText = t('feedback.manager.statusClosed') || '';
              break;
            default:
              color = 'blue';
              statusText = t('feedback.manager.statusNew') || '';
          }

          return <Tag color={color}>{statusText}</Tag>;
        }
      },
      {
        title: t('feedback.manager.date') || '',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 150,
        render: (text: string) => new Date(text).toLocaleString()
      },
      {
        title: t('feedback.manager.actions') || '',
        key: 'actions',
        width: 180,
        render: (_: any, record: FeedbackData) => (
          <Space size="small">
            <Tooltip title={t('feedback.manager.view') || ''}>
              <Button
                type="text"
                icon={<EyeOutlined />}
                onClick={() => viewFeedbackDetail(record)}
              />
            </Tooltip>

            <Tooltip title={t('feedback.manager.markInProgress') || ''}>
              <Button
                type="text"
                icon={<ClockCircleOutlined />}
                onClick={() => markFeedbackStatus(record.id!, 'inProgress')}
                disabled={record.status === 'inProgress'}
              />
            </Tooltip>

            <Tooltip title={t('feedback.manager.markResolved') || ''}>
              <Button
                type="text"
                icon={<CheckOutlined />}
                onClick={() => markFeedbackStatus(record.id!, 'resolved')}
                disabled={record.status === 'resolved'}
              />
            </Tooltip>

            <Tooltip title={t('feedback.manager.delete') || ''}>
              <Popconfirm
                title={t('feedback.manager.confirmDelete') || ''}
                onConfirm={() => deleteFeedback(record.id!)}
                okText={t('common.yes') || ''}
                cancelText={t('common.no') || ''}
              >
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Popconfirm>
            </Tooltip>
          </Space>
        )
      }
    ];

    return (
      <Table
        dataSource={feedbackList}
        columns={columns}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10 }}
        locale={{
          emptyText: error ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={error}
            />
          ) : (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={t('feedback.manager.noData') || ''}
            />
          )
        }}
      />
    );
  };

  // 渲染导出按钮
  const renderExportButtons = () => {
    return (
      <Space>
        <Tooltip title={t('feedback.manager.exportExcel') || ''}>
          <Button
            icon={<FileExcelOutlined />}
            onClick={() => exportFeedback('excel')}
            loading={exportLoading}
          >
            Excel
          </Button>
        </Tooltip>

        <Tooltip title={t('feedback.manager.exportPDF') || ''}>
          <Button
            icon={<FilePdfOutlined />}
            onClick={() => exportFeedback('pdf')}
            loading={exportLoading}
          >
            PDF
          </Button>
        </Tooltip>

        <Tooltip title={t('feedback.manager.exportJSON') || ''}>
          <Button
            icon={<ExportOutlined />}
            onClick={() => exportFeedback('json')}
            loading={exportLoading}
          >
            JSON
          </Button>
        </Tooltip>
      </Space>
    );
  };

  return (
    <div className="feedback-manager">
      <div className="feedback-manager-header">
        <div className="feedback-manager-title">
          <Title level={4}>{t('feedback.manager.title') || ''}</Title>
          <Text type="secondary">{t('feedback.manager.description') || ''}</Text>
        </div>

        <div className="feedback-manager-actions">
          {renderExportButtons()}
        </div>
      </div>

      <Divider />

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <CommentOutlined />
              {t('feedback.manager.tabList') || ''}
            </span>
          }
          key="list"
        >
          {renderFilters()}
          {renderTable()}
        </TabPane>

        <TabPane
          tab={
            <span>
              <PieChartOutlined />
              {t('feedback.manager.tabAnalytics') || ''}
            </span>
          }
          key="analytics"
        >
          <FeedbackAnalytics />
        </TabPane>
      </Tabs>

      <Drawer
        title={t('feedback.manager.feedbackDetail') || ''}
        placement="right"
        width={600}
        onClose={closeDetail}
        open={detailVisible}
      >
        {selectedFeedback && (
          <FeedbackDetail feedback={selectedFeedback} onStatusChange={loadFeedbackData} />
        )}
      </Drawer>
    </div>
  );
};

export default FeedbackManager;
