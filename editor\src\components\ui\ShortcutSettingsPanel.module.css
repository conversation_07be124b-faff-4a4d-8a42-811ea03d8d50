/**
 * ShortcutSettingsPanel.module.css
 * 
 * 快捷键设置面板样式
 */

.shortcut-settings-panel {
  /* 面板整体样式 */
  position: relative;
}

.shortcut-settings-panel :global(.ant-modal-content) {
  border-radius: 8px;
  overflow: hidden;
}

.shortcut-settings-panel :global(.ant-modal-header) {
  background: var(--header-background, #fafafa);
  border-bottom: 1px solid var(--border-color, #d9d9d9);
}

.panel-header {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color, #000000);
}

.panel-content {
  padding: 0;
}

/* 工具栏 */
.panel-toolbar {
  padding: 16px;
  background: var(--surface-color, #fafafa);
  border-bottom: 1px solid var(--border-color, #d9d9d9);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 分类标签页 */
.shortcut-settings-panel :global(.ant-tabs) {
  margin: 0;
}

.shortcut-settings-panel :global(.ant-tabs-nav) {
  margin: 0;
  padding: 0 16px;
  background: var(--background-color, #ffffff);
}

.shortcut-settings-panel :global(.ant-tabs-tab) {
  padding: 8px 16px;
  margin: 0 4px 0 0;
}

.shortcut-settings-panel :global(.ant-tabs-content-holder) {
  padding: 16px;
}

/* 表格样式 */
.shortcut-settings-panel :global(.ant-table) {
  background: var(--background-color, #ffffff);
}

.shortcut-settings-panel :global(.ant-table-thead > tr > th) {
  background: var(--surface-color, #fafafa);
  border-bottom: 1px solid var(--border-color, #d9d9d9);
  font-weight: 500;
}

.shortcut-settings-panel :global(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid var(--border-color-light, #f0f0f0);
}

.shortcut-settings-panel :global(.ant-table-tbody > tr:hover > td) {
  background: var(--item-hover-background, #f5f5f5);
}

/* 快捷键标签 */
.shortcut-settings-panel :global(.ant-tag) {
  margin: 2px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-weight: 500;
}

/* 开关样式 */
.shortcut-settings-panel :global(.ant-switch) {
  min-width: 44px;
}

.shortcut-settings-panel :global(.ant-switch-checked) {
  background-color: var(--primary-color, #1890ff);
}

/* 徽章样式 */
.shortcut-settings-panel :global(.ant-badge) {
  font-size: 10px;
}

.shortcut-settings-panel :global(.ant-badge-count) {
  background: var(--success-color, #52c41a);
  color: white;
  font-size: 10px;
  height: 16px;
  line-height: 16px;
  min-width: 16px;
  padding: 0 4px;
}

/* 快捷键编辑模态框 */
.shortcut-edit-content {
  padding: 16px 0;
}

.shortcut-info {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-color-light, #f0f0f0);
}

.shortcut-info h5 {
  margin-bottom: 8px;
  color: var(--text-color, #000000);
}

.shortcut-keys {
  margin-bottom: 24px;
}

.shortcut-keys > span {
  display: block;
  margin-bottom: 8px;
  color: var(--text-color, #000000);
}

.keys-display {
  min-height: 32px;
  padding: 8px;
  border: 1px solid var(--border-color, #d9d9d9);
  border-radius: 6px;
  background: var(--surface-color, #fafafa);
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
}

.shortcut-record {
  padding: 16px;
  background: var(--info-background, #e6f7ff);
  border: 1px solid var(--info-border, #91d5ff);
  border-radius: 6px;
}

/* 搜索框 */
.panel-toolbar :global(.ant-input-search) {
  border-radius: 6px;
}

.panel-toolbar :global(.ant-input-search .ant-input) {
  border-radius: 6px 0 0 6px;
}

.panel-toolbar :global(.ant-input-search .ant-input-search-button) {
  border-radius: 0 6px 6px 0;
}

/* 按钮样式 */
.panel-toolbar :global(.ant-btn) {
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.panel-toolbar :global(.ant-btn-danger) {
  border-color: var(--error-color, #ff4d4f);
  color: var(--error-color, #ff4d4f);
}

.panel-toolbar :global(.ant-btn-danger:hover) {
  background: var(--error-background, #fff2f0);
  border-color: var(--error-color-hover, #ff7875);
  color: var(--error-color-hover, #ff7875);
}

/* 分页器 */
.shortcut-settings-panel :global(.ant-pagination) {
  margin-top: 16px;
  text-align: right;
}

.shortcut-settings-panel :global(.ant-pagination-total-text) {
  color: var(--text-color-secondary, #8c8c8c);
}

/* 暗色主题 */
[data-theme="dark"] .shortcut-settings-panel {
  --background-color: #141414;
  --surface-color: #1f1f1f;
  --header-background: #262626;
  --border-color: #434343;
  --border-color-light: #303030;
  --text-color: #ffffff;
  --text-color-secondary: #a6a6a6;
  --item-hover-background: #262626;
  --primary-color: #177ddc;
  --success-color: #49aa19;
  --error-color: #d32029;
  --error-background: #2a1215;
  --error-color-hover: #e84749;
  --info-background: #111b26;
  --info-border: #153450;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .shortcut-settings-panel :global(.ant-modal) {
    width: 90% !important;
    max-width: 900px;
  }
}

@media (max-width: 768px) {
  .shortcut-settings-panel :global(.ant-modal) {
    width: 95% !important;
    margin: 10px;
  }
  
  .panel-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .panel-toolbar :global(.ant-space) {
    justify-content: center;
  }
  
  .shortcut-settings-panel :global(.ant-table) {
    font-size: 12px;
  }
  
  .shortcut-settings-panel :global(.ant-table-thead > tr > th),
  .shortcut-settings-panel :global(.ant-table-tbody > tr > td) {
    padding: 8px 4px;
  }
  
  .shortcut-settings-panel :global(.ant-tabs-nav) {
    padding: 0 8px;
  }
  
  .shortcut-settings-panel :global(.ant-tabs-content-holder) {
    padding: 8px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .shortcut-settings-panel :global(.ant-modal-content) {
    border: 2px solid var(--border-color, #d9d9d9);
  }
  
  .shortcut-settings-panel :global(.ant-table-tbody > tr:hover > td) {
    outline: 2px solid var(--primary-color, #1890ff);
    outline-offset: -2px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .shortcut-settings-panel :global(.ant-table-tbody > tr > td),
  .shortcut-settings-panel :global(.ant-btn),
  .shortcut-settings-panel :global(.ant-switch) {
    transition: none;
  }
}

/* 打印样式 */
@media print {
  .panel-toolbar {
    display: none;
  }
  
  .shortcut-settings-panel :global(.ant-tabs-nav) {
    display: none;
  }
  
  .shortcut-settings-panel :global(.ant-pagination) {
    display: none;
  }
  
  .shortcut-settings-panel :global(.ant-table) {
    font-size: 10px;
  }
}

/* 焦点状态 */
.shortcut-settings-panel :global(.ant-btn:focus),
.shortcut-settings-panel :global(.ant-input:focus),
.shortcut-settings-panel :global(.ant-switch:focus) {
  outline: 2px solid var(--primary-color, #1890ff);
  outline-offset: 2px;
}

/* 加载状态 */
.shortcut-settings-panel :global(.ant-table-loading) {
  position: relative;
}

.shortcut-settings-panel :global(.ant-spin-container) {
  position: relative;
}

/* 空状态 */
.shortcut-settings-panel :global(.ant-empty) {
  padding: 40px 20px;
}

.shortcut-settings-panel :global(.ant-empty-description) {
  color: var(--text-color-secondary, #8c8c8c);
}

/* 工具提示 */
.shortcut-settings-panel :global(.ant-tooltip) {
  font-size: 12px;
}

.shortcut-settings-panel :global(.ant-tooltip-inner) {
  background: var(--tooltip-background, rgba(0, 0, 0, 0.85));
  color: white;
  border-radius: 4px;
}

/* 确认弹窗 */
.shortcut-settings-panel :global(.ant-popconfirm) {
  font-size: 14px;
}

.shortcut-settings-panel :global(.ant-popconfirm-message) {
  color: var(--text-color, #000000);
}

.shortcut-settings-panel :global(.ant-popconfirm-buttons) {
  margin-top: 12px;
}

/* 性能优化 */
.shortcut-settings-panel {
  contain: layout style paint;
}

.panel-content {
  contain: layout style paint;
}

.shortcut-settings-panel :global(.ant-table-tbody) {
  contain: layout style paint;
}
