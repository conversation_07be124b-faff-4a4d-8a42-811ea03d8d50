/**
 * 材质编辑器面板样式
 */

.material-editor-panel {
  .ant-modal-body {
    padding: 16px;
    height: 70vh;
    overflow: hidden;
  }

  // 材质库
  .material-library {
    height: 100%;
    display: flex;
    flex-direction: column;

    .library-controls {
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;

      .ant-btn {
        margin-right: 8px;
        margin-bottom: 8px;
      }
    }

    .ant-list {
      flex: 1;
      overflow-y: auto;

      .ant-list-item {
        padding: 12px;
        border: 1px solid transparent;
        border-radius: 6px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          border-color: #d9d9d9;
          background-color: #f9f9f9;
        }

        &.selected {
          border-color: #1890ff;
          background-color: #e6f7ff;
        }

        .material-thumbnail {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f0f2f5;
          border-radius: 4px;
        }

        .ant-list-item-meta {
          .ant-list-item-meta-title {
            font-size: 14px;
            margin-bottom: 4px;
          }

          .ant-list-item-meta-description {
            font-size: 12px;

            .ant-tag {
              margin-bottom: 4px;
            }

            .ant-rate {
              margin-right: 8px;
            }
          }
        }

        .ant-list-item-action {
          margin-left: 8px;

          .ant-btn {
            margin-left: 4px;
          }
        }
      }
    }
  }

  // 属性编辑器
  .properties-editor {
    height: 100%;
    overflow-y: auto;

    .ant-form {
      .ant-form-item {
        margin-bottom: 16px;

        .ant-form-item-label {
          font-weight: 500;
        }
      }

      .ant-slider {
        margin: 8px 0;
      }

      .ant-color-picker {
        width: 100%;
      }

      .ant-collapse {
        .ant-collapse-item {
          .ant-collapse-header {
            font-size: 13px;
            padding: 8px 16px;
          }

          .ant-collapse-content {
            .ant-collapse-content-box {
              padding: 12px 16px;
            }
          }
        }
      }
    }
  }

  // 纹理编辑器
  .texture-editor {
    height: 100%;
    overflow-y: auto;

    .ant-card {
      .ant-card-head {
        .ant-card-head-title {
          font-size: 12px;
        }

        .ant-card-extra {
          .ant-btn {
            padding: 4px;
          }
        }
      }

      .ant-card-body {
        padding: 8px;
      }

      .texture-preview {
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f9f9f9;
        border: 1px dashed #d9d9d9;
        border-radius: 4px;

        .no-texture {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: #d9d9d9;
        }

        img {
          border-radius: 4px;
        }
      }
    }
  }

  // 材质预览
  .material-preview {
    height: 100%;
    display: flex;
    flex-direction: column;

    .preview-controls {
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;
    }

    .preview-viewport {
      flex: 1;
      margin-bottom: 12px;
    }

    .material-info {
      padding: 12px;
      background: #f9f9f9;
      border-radius: 4px;

      .ant-progress {
        margin-top: 4px;
      }
    }
  }

  // 标签页
  .ant-tabs {
    height: 100%;

    .ant-tabs-content-holder {
      .ant-tabs-content {
        height: calc(100% - 44px);

        .ant-tabs-tabpane {
          height: 100%;
          overflow: hidden;
        }
      }
    }
  }
}

// 预设选择对话框
.preset-modal {
  .ant-card {
    .ant-card-cover {
      height: 120px;
      overflow: hidden;

      img {
        transition: transform 0.3s ease;
      }
    }

    &:hover {
      .ant-card-cover img {
        transform: scale(1.05);
      }
    }

    .ant-card-meta {
      .ant-card-meta-title {
        font-size: 14px;
      }

      .ant-card-meta-description {
        font-size: 12px;

        .ant-tag {
          margin-bottom: 2px;
        }
      }
    }
  }
}

// 程序化生成对话框
.procedural-modal {
  .ant-form {
    .ant-form-item {
      margin-bottom: 16px;
    }

    .ant-slider {
      margin: 8px 0;
    }
  }
}

// 深色主题
.dark-theme {
  .material-editor-panel {
    .ant-modal-content {
      background: #2d2d2d;
      color: #cccccc;
    }

    .material-library {
      .library-controls {
        border-bottom-color: #404040;
      }

      .ant-list {
        .ant-list-item {
          &:hover {
            border-color: #555555;
            background-color: #404040;
          }

          &.selected {
            border-color: #1890ff;
            background-color: #1f3a5f;
          }

          .material-thumbnail {
            background: #404040;
          }

          .ant-list-item-meta {
            .ant-list-item-meta-title {
              color: #ffffff;
            }

            .ant-list-item-meta-description {
              color: #cccccc;
            }
          }
        }
      }
    }

    .texture-editor {
      .ant-card {
        background: #2d2d2d;
        border-color: #404040;

        .ant-card-head {
          background: #2d2d2d;
          border-bottom-color: #404040;

          .ant-card-head-title {
            color: #ffffff;
          }
        }

        .ant-card-body {
          background: #2d2d2d;
        }

        .texture-preview {
          background: #404040;
          border-color: #555555;

          .no-texture {
            color: #666666;
          }
        }
      }
    }

    .material-preview {
      .preview-controls {
        border-bottom-color: #404040;
      }

      .material-info {
        background: #404040;
        color: #cccccc;
      }
    }

    .ant-tabs {
      .ant-tabs-tab {
        color: #cccccc;

        &.ant-tabs-tab-active {
          color: #1890ff;
        }
      }

      .ant-tabs-ink-bar {
        background: #1890ff;
      }
    }
  }
}

// 紧凑模式
.compact-theme {
  .material-editor-panel {
    .ant-modal-body {
      padding: 12px;
    }

    .material-library {
      .library-controls {
        margin-bottom: 12px;
        padding-bottom: 8px;
      }

      .ant-list {
        .ant-list-item {
          padding: 8px;
          margin-bottom: 6px;

          .material-thumbnail {
            width: 32px;
            height: 32px;
          }

          .ant-list-item-meta {
            .ant-list-item-meta-title {
              font-size: 13px;
            }

            .ant-list-item-meta-description {
              font-size: 11px;
            }
          }
        }
      }
    }

    .properties-editor {
      .ant-form {
        .ant-form-item {
          margin-bottom: 12px;
        }
      }
    }

    .texture-editor {
      .ant-card {
        .ant-card-body {
          padding: 6px;
        }

        .texture-preview {
          height: 60px;
        }
      }
    }

    .material-preview {
      .material-info {
        padding: 8px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .material-editor-panel {
    .ant-modal {
      width: 95% !important;
      max-width: none;
    }
  }
}

@media (max-width: 768px) {
  .material-editor-panel {
    .ant-row {
      .ant-col {
        margin-bottom: 16px;
      }
    }

    .material-library {
      .library-controls {
        .ant-space {
          flex-direction: column;
          align-items: stretch;
          width: 100%;
        }
      }
    }

    .texture-editor {
      .ant-row {
        .ant-col {
          flex: 0 0 50%;
          max-width: 50%;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .material-editor-panel {
    .texture-editor {
      .ant-row {
        .ant-col {
          flex: 0 0 100%;
          max-width: 100%;
        }
      }
    }

    .properties-editor {
      .ant-row {
        .ant-col {
          flex: 0 0 100%;
          max-width: 100%;
        }
      }
    }
  }
}

// 动画效果
.material-editor-panel {
  .material-library {
    .ant-list-item {
      transition: all 0.2s ease;
    }
  }

  .texture-editor {
    .ant-card {
      transition: all 0.2s ease;

      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .preset-modal {
    .ant-card {
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .ant-btn {
    transition: all 0.2s ease;
  }

  .ant-slider {
    .ant-slider-handle {
      transition: all 0.2s ease;
    }
  }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
  .material-editor-panel {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }

    .preset-modal {
      .ant-card {
        &:hover {
          transform: none;
        }
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .material-editor-panel {
    .material-library {
      .ant-list-item {
        border: 2px solid #000;

        &.selected {
          border-color: #0066cc;
        }
      }
    }

    .texture-editor {
      .ant-card {
        border: 2px solid #000;
      }

      .texture-preview {
        border: 2px solid #000;
      }
    }

    .material-preview {
      .preview-viewport {
        border: 2px solid #000;
      }
    }

    .ant-tabs {
      .ant-tabs-tab {
        border: 1px solid #000;
      }
    }
  }
}

// 材质编辑器特定样式
.material-editor-panel {
  .material-type-pbr {
    border-left: 4px solid #1890ff;
  }

  .material-type-metal {
    border-left: 4px solid #722ed1;
  }

  .material-type-glass {
    border-left: 4px solid #13c2c2;
  }

  .material-type-fabric {
    border-left: 4px solid #eb2f96;
  }

  .complexity-high {
    color: #ff4d4f;
  }

  .complexity-medium {
    color: #faad14;
  }

  .complexity-low {
    color: #52c41a;
  }

  .performance-excellent {
    color: #52c41a;
  }

  .performance-good {
    color: #faad14;
  }

  .performance-poor {
    color: #ff4d4f;
  }

  .quality-high {
    color: #52c41a;
  }

  .quality-medium {
    color: #faad14;
  }

  .quality-low {
    color: #ff4d4f;
  }

  .texture-slot {
    position: relative;

    &.has-texture {
      &::after {
        content: '';
        position: absolute;
        top: 4px;
        right: 4px;
        width: 8px;
        height: 8px;
        background: #52c41a;
        border-radius: 50%;
      }
    }
  }

  .material-preview-sphere {
    background: radial-gradient(circle at 30% 30%, #ffffff, #cccccc);
  }

  .material-preview-cube {
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 50%, #cccccc 100%);
  }

  .material-preview-plane {
    background: linear-gradient(90deg, #ffffff 0%, #cccccc 100%);
  }
}
