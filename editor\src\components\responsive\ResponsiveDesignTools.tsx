/**
 * ResponsiveDesignTools.tsx
 * 
 * 响应式设计工具组件，提供断点管理和响应式样式编辑
 */

import React, { useState, useCallback, useMemo } from 'react';
import {
  Tabs,
  Form,
  Input,
  InputNumber,
  Select,
  Switch,
  Button,
  Space,
  Table,
  Modal,
  Tooltip,
  Tag,
  Collapse,
  Alert
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  CopyOutlined,
  MobileOutlined,
  TabletOutlined,
  DesktopOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import './ResponsiveDesignTools.module.css';

const { TabPane } = Tabs;
const { Panel } = Collapse;

/**
 * 响应式断点接口
 */
export interface ResponsiveBreakpoint {
  id: string;
  name: string;
  minWidth: number;
  maxWidth?: number;
  icon: React.ReactNode;
  color: string;
  enabled: boolean;
}

/**
 * 响应式样式规则接口
 */
export interface ResponsiveStyleRule {
  id: string;
  breakpointId: string;
  componentId: string;
  property: string;
  value: any;
  unit?: string;
  important?: boolean;
}

/**
 * CSS属性配置
 */
const CSS_PROPERTIES = [
  { label: '宽度', value: 'width', unit: 'px' },
  { label: '高度', value: 'height', unit: 'px' },
  { label: '边距', value: 'margin', unit: 'px' },
  { label: '内边距', value: 'padding', unit: 'px' },
  { label: '字体大小', value: 'fontSize', unit: 'px' },
  { label: '行高', value: 'lineHeight', unit: '' },
  { label: '显示方式', value: 'display', unit: '' },
  { label: '弹性方向', value: 'flexDirection', unit: '' },
  { label: '对齐方式', value: 'justifyContent', unit: '' },
  { label: '项目对齐', value: 'alignItems', unit: '' },
  { label: '网格列数', value: 'gridTemplateColumns', unit: '' },
  { label: '网格行数', value: 'gridTemplateRows', unit: '' },
  { label: '网格间距', value: 'gap', unit: 'px' }
];

/**
 * 默认断点配置
 */
const DEFAULT_BREAKPOINTS: ResponsiveBreakpoint[] = [
  {
    id: 'mobile',
    name: '手机',
    minWidth: 0,
    maxWidth: 767,
    icon: <MobileOutlined />,
    color: '#52c41a',
    enabled: true
  },
  {
    id: 'tablet',
    name: '平板',
    minWidth: 768,
    maxWidth: 1023,
    icon: <TabletOutlined />,
    color: '#1890ff',
    enabled: true
  },
  {
    id: 'desktop',
    name: '桌面',
    minWidth: 1024,
    icon: <DesktopOutlined />,
    color: '#722ed1',
    enabled: true
  }
];

/**
 * 响应式设计工具属性
 */
export interface ResponsiveDesignToolsProps {
  /** 断点配置 */
  breakpoints?: ResponsiveBreakpoint[];
  /** 样式规则 */
  styleRules?: ResponsiveStyleRule[];
  /** 当前选中的组件ID */
  selectedComponentId?: string;
  /** 当前断点ID */
  currentBreakpointId?: string;
  /** 断点变化回调 */
  onBreakpointsChange?: (breakpoints: ResponsiveBreakpoint[]) => void;
  /** 样式规则变化回调 */
  onStyleRulesChange?: (rules: ResponsiveStyleRule[]) => void;
  /** 样式类名 */
  className?: string;
}

/**
 * 响应式设计工具组件
 */
export const ResponsiveDesignTools: React.FC<ResponsiveDesignToolsProps> = ({
  breakpoints = DEFAULT_BREAKPOINTS,
  styleRules = [],
  selectedComponentId,
  currentBreakpointId: _currentBreakpointId,
  onBreakpointsChange,
  onStyleRulesChange,
  className
}) => {
  const [activeTab, setActiveTab] = useState('breakpoints');
  const [isBreakpointModalVisible, setIsBreakpointModalVisible] = useState(false);
  const [editingBreakpoint, setEditingBreakpoint] = useState<ResponsiveBreakpoint | null>(null);
  const [isStyleModalVisible, setIsStyleModalVisible] = useState(false);
  const [editingStyleRule, setEditingStyleRule] = useState<ResponsiveStyleRule | null>(null);

  const [breakpointForm] = Form.useForm();
  const [styleForm] = Form.useForm();

  // 获取当前组件的样式规则
  const componentStyleRules = useMemo(() => {
    if (!selectedComponentId) return [];
    return styleRules.filter(rule => rule.componentId === selectedComponentId);
  }, [styleRules, selectedComponentId]);

  // 按断点分组的样式规则
  const rulesByBreakpoint = useMemo(() => {
    return componentStyleRules.reduce((acc, rule) => {
      if (!acc[rule.breakpointId]) {
        acc[rule.breakpointId] = [];
      }
      acc[rule.breakpointId].push(rule);
      return acc;
    }, {} as Record<string, ResponsiveStyleRule[]>);
  }, [componentStyleRules]);

  // 断点表格列定义
  const breakpointColumns: ColumnsType<ResponsiveBreakpoint> = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          {record.icon}
          <span>{text}</span>
        </Space>
      )
    },
    {
      title: '范围',
      key: 'range',
      render: (_, record) => (
        <span>
          {record.minWidth}px - {record.maxWidth ? `${record.maxWidth}px` : '∞'}
        </span>
      )
    },
    {
      title: '颜色',
      dataIndex: 'color',
      key: 'color',
      render: (color) => (
        <div
          style={{
            width: 20,
            height: 20,
            backgroundColor: color,
            borderRadius: 4,
            border: '1px solid #d9d9d9'
          }}
        />
      )
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      key: 'enabled',
      render: (enabled, record) => (
        <Switch
          checked={enabled}
          onChange={(checked) => handleBreakpointToggle(record.id, checked)}
        />
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditBreakpoint(record)}
            />
          </Tooltip>
          <Tooltip title="复制">
            <Button
              type="text"
              size="small"
              icon={<CopyOutlined />}
              onClick={() => handleCopyBreakpoint(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              size="small"
              icon={<DeleteOutlined />}
              danger
              onClick={() => handleDeleteBreakpoint(record.id)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  // 处理断点切换
  const handleBreakpointToggle = useCallback((breakpointId: string, enabled: boolean) => {
    const updatedBreakpoints = breakpoints.map(bp =>
      bp.id === breakpointId ? { ...bp, enabled } : bp
    );
    onBreakpointsChange?.(updatedBreakpoints);
  }, [breakpoints, onBreakpointsChange]);

  // 处理编辑断点
  const handleEditBreakpoint = useCallback((breakpoint: ResponsiveBreakpoint) => {
    setEditingBreakpoint(breakpoint);
    breakpointForm.setFieldsValue(breakpoint);
    setIsBreakpointModalVisible(true);
  }, [breakpointForm]);

  // 处理复制断点
  const handleCopyBreakpoint = useCallback((breakpoint: ResponsiveBreakpoint) => {
    const newBreakpoint: ResponsiveBreakpoint = {
      ...breakpoint,
      id: `${breakpoint.id}_copy_${Date.now()}`,
      name: `${breakpoint.name} 副本`
    };
    onBreakpointsChange?.([...breakpoints, newBreakpoint]);
  }, [breakpoints, onBreakpointsChange]);

  // 处理删除断点
  const handleDeleteBreakpoint = useCallback((breakpointId: string) => {
    const updatedBreakpoints = breakpoints.filter(bp => bp.id !== breakpointId);
    onBreakpointsChange?.(updatedBreakpoints);
  }, [breakpoints, onBreakpointsChange]);

  // 处理保存断点
  const handleSaveBreakpoint = useCallback(async () => {
    try {
      const values = await breakpointForm.validateFields();
      
      if (editingBreakpoint) {
        // 更新现有断点
        const updatedBreakpoints = breakpoints.map(bp =>
          bp.id === editingBreakpoint.id ? { ...bp, ...values } : bp
        );
        onBreakpointsChange?.(updatedBreakpoints);
      } else {
        // 添加新断点
        const newBreakpoint: ResponsiveBreakpoint = {
          id: `breakpoint_${Date.now()}`,
          icon: <DesktopOutlined />,
          enabled: true,
          ...values
        };
        onBreakpointsChange?.([...breakpoints, newBreakpoint]);
      }
      
      setIsBreakpointModalVisible(false);
      setEditingBreakpoint(null);
      breakpointForm.resetFields();
    } catch (error) {
      console.error('保存断点失败:', error);
    }
  }, [breakpointForm, editingBreakpoint, breakpoints, onBreakpointsChange]);

  // 处理添加样式规则
  const handleAddStyleRule = useCallback((breakpointId: string) => {
    if (!selectedComponentId) return;
    
    setEditingStyleRule({
      id: '',
      breakpointId,
      componentId: selectedComponentId,
      property: 'width',
      value: '',
      unit: 'px'
    });
    styleForm.resetFields();
    setIsStyleModalVisible(true);
  }, [selectedComponentId, styleForm]);

  // 处理编辑样式规则
  const handleEditStyleRule = useCallback((rule: ResponsiveStyleRule) => {
    setEditingStyleRule(rule);
    styleForm.setFieldsValue(rule);
    setIsStyleModalVisible(true);
  }, [styleForm]);

  // 处理保存样式规则
  const handleSaveStyleRule = useCallback(async () => {
    try {
      const values = await styleForm.validateFields();
      
      if (editingStyleRule?.id) {
        // 更新现有规则
        const updatedRules = styleRules.map(rule =>
          rule.id === editingStyleRule.id ? { ...rule, ...values } : rule
        );
        onStyleRulesChange?.(updatedRules);
      } else {
        // 添加新规则
        const newRule: ResponsiveStyleRule = {
          id: `rule_${Date.now()}`,
          ...editingStyleRule,
          ...values
        };
        onStyleRulesChange?.([...styleRules, newRule]);
      }
      
      setIsStyleModalVisible(false);
      setEditingStyleRule(null);
      styleForm.resetFields();
    } catch (error) {
      console.error('保存样式规则失败:', error);
    }
  }, [styleForm, editingStyleRule, styleRules, onStyleRulesChange]);

  // 处理删除样式规则
  const handleDeleteStyleRule = useCallback((ruleId: string) => {
    const updatedRules = styleRules.filter(rule => rule.id !== ruleId);
    onStyleRulesChange?.(updatedRules);
  }, [styleRules, onStyleRulesChange]);

  return (
    <div className={`responsive-design-tools ${className || ''}`}>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        {/* 断点管理 */}
        <TabPane tab="断点管理" key="breakpoints">
          <div className="breakpoints-panel">
            <div className="panel-header">
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setEditingBreakpoint(null);
                  breakpointForm.resetFields();
                  setIsBreakpointModalVisible(true);
                }}
              >
                添加断点
              </Button>
            </div>
            
            <Table
              columns={breakpointColumns}
              dataSource={breakpoints}
              rowKey="id"
              size="small"
              pagination={false}
            />
          </div>
        </TabPane>

        {/* 响应式样式 */}
        <TabPane tab="响应式样式" key="styles">
          <div className="styles-panel">
            {!selectedComponentId ? (
              <Alert
                message="请先选择一个组件"
                description="选择组件后可以为不同断点设置响应式样式"
                type="info"
                showIcon
              />
            ) : (
              <Collapse size="small" ghost>
                {breakpoints.filter(bp => bp.enabled).map(breakpoint => (
                  <Panel
                    key={breakpoint.id}
                    header={
                      <div className="breakpoint-panel-header">
                        <Space>
                          {breakpoint.icon}
                          <span>{breakpoint.name}</span>
                          <Tag color={breakpoint.color}>
                            {breakpoint.minWidth}px - {breakpoint.maxWidth ? `${breakpoint.maxWidth}px` : '∞'}
                          </Tag>
                          <span className="rule-count">
                            ({rulesByBreakpoint[breakpoint.id]?.length || 0} 条规则)
                          </span>
                        </Space>
                      </div>
                    }
                    extra={
                      <Button
                        type="text"
                        size="small"
                        icon={<PlusOutlined />}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAddStyleRule(breakpoint.id);
                        }}
                      />
                    }
                  >
                    <div className="style-rules">
                      {rulesByBreakpoint[breakpoint.id]?.map(rule => (
                        <div key={rule.id} className="style-rule">
                          <div className="rule-content">
                            <span className="rule-property">{rule.property}:</span>
                            <span className="rule-value">
                              {rule.value}{rule.unit}
                              {rule.important && <span className="important">!important</span>}
                            </span>
                          </div>
                          <div className="rule-actions">
                            <Button
                              type="text"
                              size="small"
                              icon={<EditOutlined />}
                              onClick={() => handleEditStyleRule(rule)}
                            />
                            <Button
                              type="text"
                              size="small"
                              icon={<DeleteOutlined />}
                              danger
                              onClick={() => handleDeleteStyleRule(rule.id)}
                            />
                          </div>
                        </div>
                      )) || (
                        <div className="empty-rules">
                          暂无样式规则
                        </div>
                      )}
                    </div>
                  </Panel>
                ))}
              </Collapse>
            )}
          </div>
        </TabPane>
      </Tabs>

      {/* 断点编辑模态框 */}
      <Modal
        title={editingBreakpoint ? '编辑断点' : '添加断点'}
        open={isBreakpointModalVisible}
        onOk={handleSaveBreakpoint}
        onCancel={() => {
          setIsBreakpointModalVisible(false);
          setEditingBreakpoint(null);
          breakpointForm.resetFields();
        }}
        okText="保存"
        cancelText="取消"
      >
        <Form form={breakpointForm} layout="vertical">
          <Form.Item
            name="name"
            label="断点名称"
            rules={[{ required: true, message: '请输入断点名称' }]}
          >
            <Input placeholder="例如：手机、平板、桌面" />
          </Form.Item>
          
          <Form.Item
            name="minWidth"
            label="最小宽度"
            rules={[{ required: true, message: '请输入最小宽度' }]}
          >
            <InputNumber
              min={0}
              addonAfter="px"
              style={{ width: '100%' }}
            />
          </Form.Item>
          
          <Form.Item
            name="maxWidth"
            label="最大宽度"
          >
            <InputNumber
              min={0}
              addonAfter="px"
              style={{ width: '100%' }}
              placeholder="留空表示无限制"
            />
          </Form.Item>
          
          <Form.Item
            name="color"
            label="标识颜色"
            rules={[{ required: true, message: '请选择标识颜色' }]}
          >
            <Select>
              <Select.Option value="#52c41a">绿色</Select.Option>
              <Select.Option value="#1890ff">蓝色</Select.Option>
              <Select.Option value="#722ed1">紫色</Select.Option>
              <Select.Option value="#fa541c">橙色</Select.Option>
              <Select.Option value="#f5222d">红色</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 样式规则编辑模态框 */}
      <Modal
        title={editingStyleRule?.id ? '编辑样式规则' : '添加样式规则'}
        open={isStyleModalVisible}
        onOk={handleSaveStyleRule}
        onCancel={() => {
          setIsStyleModalVisible(false);
          setEditingStyleRule(null);
          styleForm.resetFields();
        }}
        okText="保存"
        cancelText="取消"
      >
        <Form form={styleForm} layout="vertical">
          <Form.Item
            name="property"
            label="CSS属性"
            rules={[{ required: true, message: '请选择CSS属性' }]}
          >
            <Select
              showSearch
              placeholder="选择或输入CSS属性"
              options={CSS_PROPERTIES}
            />
          </Form.Item>
          
          <Form.Item
            name="value"
            label="属性值"
            rules={[{ required: true, message: '请输入属性值' }]}
          >
            <Input placeholder="例如：100、auto、flex" />
          </Form.Item>
          
          <Form.Item
            name="unit"
            label="单位"
          >
            <Select allowClear placeholder="选择单位">
              <Select.Option value="px">px</Select.Option>
              <Select.Option value="%">%</Select.Option>
              <Select.Option value="em">em</Select.Option>
              <Select.Option value="rem">rem</Select.Option>
              <Select.Option value="vw">vw</Select.Option>
              <Select.Option value="vh">vh</Select.Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="important"
            valuePropName="checked"
          >
            <Switch checkedChildren="!important" unCheckedChildren="normal" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ResponsiveDesignTools;
