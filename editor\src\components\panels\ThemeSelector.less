/**
 * 主题选择器样式
 */

.theme-selector {
  .theme-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .theme-preview {
      display: flex;
      align-items: center;
      gap: 8px;

      .color-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 1px solid #e8e8e8;
      }
    }
  }

  .ant-select {
    .ant-select-selector {
      transition: all 0.2s ease;
    }

    &:hover .ant-select-selector {
      border-color: #40a9ff;
    }
  }
}

.theme-customizer {
  .ant-modal-body {
    max-height: 600px;
    overflow-y: auto;
  }

  .ant-card {
    margin-bottom: 16px;

    .ant-card-head {
      .ant-card-head-title {
        font-size: 14px;
        font-weight: 500;
      }
    }

    .ant-card-body {
      padding: 16px;
    }
  }

  .ant-form-item {
    margin-bottom: 16px;

    .ant-form-item-label {
      padding-bottom: 4px;

      label {
        font-size: 12px;
        color: #666666;
      }
    }
  }

  .ant-color-picker {
    width: 100%;

    .ant-color-picker-trigger {
      width: 100%;
      height: 32px;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      transition: all 0.2s ease;

      &:hover {
        border-color: #40a9ff;
      }
    }
  }
}

// 深色主题
.dark-theme {
  .theme-selector {
    .theme-option {
      .theme-preview {
        .color-dot {
          border-color: #404040;
        }
      }
    }
  }

  .theme-customizer {
    .ant-card {
      background: #2d2d2d;
      border-color: #404040;

      .ant-card-head {
        background: #2d2d2d;
        border-bottom-color: #404040;

        .ant-card-head-title {
          color: #ffffff;
        }
      }

      .ant-card-body {
        color: #cccccc;
      }
    }

    .ant-form-item {
      .ant-form-item-label {
        label {
          color: #cccccc;
        }
      }
    }

    .ant-color-picker {
      .ant-color-picker-trigger {
        background: #2d2d2d;
        border-color: #404040;

        &:hover {
          border-color: #40a9ff;
        }
      }
    }
  }
}

// 紧凑模式
.compact-theme {
  .theme-selector {
    .ant-space {
      gap: 4px !important;
    }

    .ant-select {
      min-width: 100px;
    }

    .ant-btn {
      padding: 0 6px;
    }
  }

  .theme-customizer {
    .ant-card {
      margin-bottom: 12px;

      .ant-card-body {
        padding: 12px;
      }
    }

    .ant-form-item {
      margin-bottom: 12px;
    }

    .ant-color-picker {
      .ant-color-picker-trigger {
        height: 28px;
      }
    }
  }
}

// 动画效果
.theme-selector {
  .ant-select {
    transition: all 0.3s ease;
  }

  .ant-btn {
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
    }
  }
}

.theme-customizer {
  .ant-card {
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .ant-color-picker {
    .ant-color-picker-trigger {
      transition: all 0.2s ease;

      &:hover {
        transform: scale(1.02);
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .theme-customizer {
    .ant-modal {
      width: 95% !important;
      max-width: none;
    }

    .ant-row {
      .ant-col {
        max-width: 100% !important;
        flex: 0 0 100% !important;
        margin-bottom: 12px;
      }
    }
  }
}

@media (max-width: 480px) {
  .theme-selector {
    .ant-space {
      flex-direction: column;
      align-items: stretch;
    }

    .ant-select {
      width: 100%;
    }
  }
}
