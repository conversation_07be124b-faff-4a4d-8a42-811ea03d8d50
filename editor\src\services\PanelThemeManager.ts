/**
 * 面板主题管理器
 * 负责面板主题的管理和切换
 */

export interface ThemeColors {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  border: string;
  text: string;
  textSecondary: string;
  accent: string;
  success: string;
  warning: string;
  error: string;
  info: string;
}

export interface ThemeConfig {
  id: string;
  name: string;
  description: string;
  colors: ThemeColors;
  isDark: boolean;
  isCompact: boolean;
  customCSS?: string;
}

// 预定义主题
const lightTheme: ThemeConfig = {
  id: 'light',
  name: 'Light Theme',
  description: 'Default light theme',
  isDark: false,
  isCompact: false,
  colors: {
    primary: '#1890ff',
    secondary: '#f0f0f0',
    background: '#ffffff',
    surface: '#fafafa',
    border: '#e8e8e8',
    text: '#333333',
    textSecondary: '#666666',
    accent: '#52c41a',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    info: '#1890ff'
  }
};

const darkTheme: ThemeConfig = {
  id: 'dark',
  name: 'Dark Theme',
  description: 'Dark theme for low-light environments',
  isDark: true,
  isCompact: false,
  colors: {
    primary: '#1890ff',
    secondary: '#404040',
    background: '#1f1f1f',
    surface: '#2d2d2d',
    border: '#404040',
    text: '#ffffff',
    textSecondary: '#cccccc',
    accent: '#52c41a',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    info: '#1890ff'
  }
};

const compactTheme: ThemeConfig = {
  id: 'compact',
  name: 'Compact Theme',
  description: 'Compact theme with reduced spacing',
  isDark: false,
  isCompact: true,
  colors: {
    primary: '#1890ff',
    secondary: '#f0f0f0',
    background: '#ffffff',
    surface: '#fafafa',
    border: '#e8e8e8',
    text: '#333333',
    textSecondary: '#666666',
    accent: '#52c41a',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    info: '#1890ff'
  }
};

class PanelThemeManager {
  private static instance: PanelThemeManager;
  private readonly STORAGE_KEY = 'dl-editor-panel-theme';
  private themes: Map<string, ThemeConfig> = new Map();
  private currentTheme: ThemeConfig;
  private styleElement: HTMLStyleElement | null = null;

  private constructor() {
    // 注册默认主题
    this.registerTheme(lightTheme);
    this.registerTheme(darkTheme);
    this.registerTheme(compactTheme);

    // 加载保存的主题
    const savedThemeId = localStorage.getItem(this.STORAGE_KEY);
    this.currentTheme = this.themes.get(savedThemeId || 'light') || lightTheme;
    
    this.applyTheme(this.currentTheme);
  }

  public static getInstance(): PanelThemeManager {
    if (!PanelThemeManager.instance) {
      PanelThemeManager.instance = new PanelThemeManager();
    }
    return PanelThemeManager.instance;
  }

  /**
   * 注册主题
   */
  public registerTheme(theme: ThemeConfig): void {
    this.themes.set(theme.id, theme);
  }

  /**
   * 获取所有主题
   */
  public getAllThemes(): ThemeConfig[] {
    return Array.from(this.themes.values());
  }

  /**
   * 获取主题
   */
  public getTheme(id: string): ThemeConfig | null {
    return this.themes.get(id) || null;
  }

  /**
   * 获取当前主题
   */
  public getCurrentTheme(): ThemeConfig {
    return this.currentTheme;
  }

  /**
   * 切换主题
   */
  public switchTheme(themeId: string): boolean {
    const theme = this.themes.get(themeId);
    if (theme) {
      this.currentTheme = theme;
      this.applyTheme(theme);
      localStorage.setItem(this.STORAGE_KEY, themeId);
      return true;
    }
    return false;
  }

  /**
   * 应用主题
   */
  private applyTheme(theme: ThemeConfig): void {
    // 移除旧的样式
    if (this.styleElement) {
      this.styleElement.remove();
    }

    // 创建新的样式元素
    this.styleElement = document.createElement('style');
    this.styleElement.id = 'panel-theme-styles';
    
    const css = this.generateThemeCSS(theme);
    this.styleElement.textContent = css;
    
    document.head.appendChild(this.styleElement);

    // 更新body类名
    document.body.classList.remove('light-theme', 'dark-theme', 'compact-theme');
    document.body.classList.add(`${theme.id}-theme`);

    // 触发主题变化事件
    window.dispatchEvent(new CustomEvent('themeChanged', { 
      detail: { theme } 
    }));
  }

  /**
   * 生成主题CSS
   */
  private generateThemeCSS(theme: ThemeConfig): string {
    const { colors } = theme;
    
    let css = `
      :root {
        --panel-primary: ${colors.primary};
        --panel-secondary: ${colors.secondary};
        --panel-background: ${colors.background};
        --panel-surface: ${colors.surface};
        --panel-border: ${colors.border};
        --panel-text: ${colors.text};
        --panel-text-secondary: ${colors.textSecondary};
        --panel-accent: ${colors.accent};
        --panel-success: ${colors.success};
        --panel-warning: ${colors.warning};
        --panel-error: ${colors.error};
        --panel-info: ${colors.info};
      }

      .dock-layout-container.${theme.id}-theme {
        background: var(--panel-background);
        color: var(--panel-text);
      }

      .dock-layout-container.${theme.id}-theme .dock-toolbar {
        background: var(--panel-surface);
        border-bottom-color: var(--panel-border);
        color: var(--panel-text);
      }

      .dock-layout-container.${theme.id}-theme .dock-layout .dock-bar {
        background: var(--panel-surface);
        border-bottom-color: var(--panel-border);
      }

      .dock-layout-container.${theme.id}-theme .dock-layout .dock-tab {
        color: var(--panel-text-secondary);
      }

      .dock-layout-container.${theme.id}-theme .dock-layout .dock-tab:hover {
        background: var(--panel-secondary);
        color: var(--panel-text);
      }

      .dock-layout-container.${theme.id}-theme .dock-layout .dock-tab.dock-tab-active {
        background: var(--panel-primary);
        color: #ffffff;
      }

      .dock-layout-container.${theme.id}-theme .dock-layout .dock-panel {
        background: var(--panel-background);
        border-color: var(--panel-border);
      }

      .dock-layout-container.${theme.id}-theme .dock-layout .dock-divider {
        background: var(--panel-border);
      }

      .dock-layout-container.${theme.id}-theme .dock-layout .dock-divider:hover {
        background: var(--panel-primary);
      }
    `;

    // 紧凑模式样式
    if (theme.isCompact) {
      css += `
        .dock-layout-container.${theme.id}-theme .dock-toolbar {
          height: 32px;
          padding: 0 8px;
        }

        .dock-layout-container.${theme.id}-theme .dock-layout .dock-bar {
          height: 28px;
        }

        .dock-layout-container.${theme.id}-theme .dock-layout .dock-tab {
          height: 28px;
          line-height: 28px;
          padding: 0 8px;
          font-size: 11px;
        }
      `;
    }

    // 添加自定义CSS
    if (theme.customCSS) {
      css += theme.customCSS;
    }

    return css;
  }

  /**
   * 创建自定义主题
   */
  public createCustomTheme(
    id: string,
    name: string,
    baseTheme: string,
    customizations: Partial<ThemeColors>
  ): ThemeConfig {
    const base = this.themes.get(baseTheme) || lightTheme;
    const customTheme: ThemeConfig = {
      id,
      name,
      description: `Custom theme based on ${base.name}`,
      isDark: base.isDark,
      isCompact: base.isCompact,
      colors: { ...base.colors, ...customizations }
    };

    this.registerTheme(customTheme);
    return customTheme;
  }

  /**
   * 删除自定义主题
   */
  public deleteCustomTheme(id: string): boolean {
    // 不能删除内置主题
    if (['light', 'dark', 'compact'].includes(id)) {
      return false;
    }

    if (this.themes.has(id)) {
      this.themes.delete(id);
      
      // 如果删除的是当前主题，切换到默认主题
      if (this.currentTheme.id === id) {
        this.switchTheme('light');
      }
      
      return true;
    }
    return false;
  }

  /**
   * 导出主题配置
   */
  public exportTheme(id: string): string | null {
    const theme = this.themes.get(id);
    if (theme) {
      return JSON.stringify(theme, null, 2);
    }
    return null;
  }

  /**
   * 导入主题配置
   */
  public importTheme(themeData: string): boolean {
    try {
      const theme: ThemeConfig = JSON.parse(themeData);
      
      // 验证主题数据
      if (this.validateTheme(theme)) {
        this.registerTheme(theme);
        return true;
      }
    } catch (error) {
      console.error('Failed to import theme:', error);
    }
    return false;
  }

  /**
   * 验证主题数据
   */
  private validateTheme(theme: any): theme is ThemeConfig {
    return (
      typeof theme === 'object' &&
      typeof theme.id === 'string' &&
      typeof theme.name === 'string' &&
      typeof theme.colors === 'object' &&
      typeof theme.isDark === 'boolean' &&
      typeof theme.isCompact === 'boolean'
    );
  }
}

export default PanelThemeManager;
