/**
 * 脚本编辑器测试
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import ScriptEditor from '../ScriptEditor';

// 模拟翻译
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// 模拟服务
jest.mock('../../../services/ScriptService', () => ({
  __esModule: true,
  default: {
    getInstance: () => ({
      registerScript: jest.fn(),
      compileScript: jest.fn().mockResolvedValue(true),
      executeScript: jest.fn().mockResolvedValue(true),
      removeScript: jest.fn(),
    }),
  },
}));

// 创建测试store
const createTestStore = () => {
  return configureStore({
    reducer: {
      scene: (state = { selectedEntity: null }) => state,
    },
  });
};

describe('ScriptEditor', () => {
  let store: any;

  beforeEach(() => {
    store = createTestStore();
  });

  const renderWithProvider = (component: React.ReactElement) => {
    return render(
      <Provider store={store}>
        {component}
      </Provider>
    );
  };

  test('应该渲染脚本编辑器', () => {
    renderWithProvider(<ScriptEditor />);
    
    expect(screen.getByText('脚本编辑器.标题')).toBeInTheDocument();
  });

  test('应该显示执行按钮', () => {
    renderWithProvider(<ScriptEditor />);
    
    expect(screen.getByText('脚本编辑器.执行')).toBeInTheDocument();
  });

  test('应该显示保存按钮', () => {
    renderWithProvider(<ScriptEditor />);
    
    expect(screen.getByText('脚本编辑器.保存')).toBeInTheDocument();
  });

  test('应该显示模板按钮', () => {
    renderWithProvider(<ScriptEditor />);
    
    expect(screen.getByText('脚本编辑器.模板')).toBeInTheDocument();
  });

  test('应该能够切换脚本类型', () => {
    renderWithProvider(<ScriptEditor />);
    
    // 查找脚本类型选择器
    const typeSelector = screen.getByDisplayValue('JavaScript');
    expect(typeSelector).toBeInTheDocument();
    
    // 切换到TypeScript
    fireEvent.mouseDown(typeSelector);
    fireEvent.click(screen.getByText('TypeScript'));
    
    expect(screen.getByDisplayValue('TypeScript')).toBeInTheDocument();
  });

  test('应该能够打开模板选择器', () => {
    renderWithProvider(<ScriptEditor />);
    
    const templateButton = screen.getByText('脚本编辑器.模板');
    fireEvent.click(templateButton);
    
    // 模板选择器应该打开（这里需要根据实际实现调整）
    // expect(screen.getByText('选择脚本模板')).toBeInTheDocument();
  });

  test('应该能够执行脚本', async () => {
    const mockOnChange = jest.fn();
    const scriptData = {
      type: 'javascript',
      content: 'console.log("Hello World");',
      enabled: true,
      autoRun: false,
      domain: 'default',
      variables: {}
    };

    renderWithProvider(
      <ScriptEditor data={scriptData} onChange={mockOnChange} />
    );
    
    const executeButton = screen.getByText('脚本编辑器.执行');
    fireEvent.click(executeButton);
    
    // 等待执行完成
    await waitFor(() => {
      // 这里应该验证脚本执行的结果
      // 由于我们模拟了服务，所以主要验证UI状态
      expect(executeButton).not.toBeDisabled();
    });
  });

  test('应该能够保存脚本', () => {
    const mockOnChange = jest.fn();
    const scriptData = {
      type: 'javascript',
      content: 'console.log("Hello World");',
      enabled: true,
      autoRun: false,
      domain: 'default',
      variables: {}
    };

    renderWithProvider(
      <ScriptEditor data={scriptData} onChange={mockOnChange} />
    );
    
    const saveButton = screen.getByText('脚本编辑器.保存');
    fireEvent.click(saveButton);
    
    // 验证onChange被调用
    expect(mockOnChange).toHaveBeenCalled();
  });

  test('应该能够切换调试模式', () => {
    renderWithProvider(<ScriptEditor />);
    
    // 查找调试模式开关
    const debugSwitch = screen.getByRole('switch');
    fireEvent.click(debugSwitch);
    
    // 验证调试标签页出现
    expect(screen.getByText('脚本编辑器.调试')).toBeInTheDocument();
  });

  test('应该显示脚本执行状态', () => {
    renderWithProvider(<ScriptEditor />);
    
    // 应该显示默认状态
    expect(screen.getByText('脚本编辑器.状态.已停止')).toBeInTheDocument();
  });

  test('应该能够在代码编辑器和可视化编辑器之间切换', () => {
    renderWithProvider(<ScriptEditor />);
    
    // 默认应该显示代码编辑器标签
    expect(screen.getByText('脚本编辑器.代码编辑器')).toBeInTheDocument();
    
    // 切换到可视化脚本类型
    const typeSelector = screen.getByDisplayValue('JavaScript');
    fireEvent.mouseDown(typeSelector);
    fireEvent.click(screen.getByText('脚本编辑器.可视化脚本'));
    
    // 应该显示可视化编辑器
    expect(screen.getByText('脚本编辑器.可视化脚本')).toBeInTheDocument();
  });

  test('应该处理空脚本内容的执行', async () => {
    const scriptData = {
      type: 'javascript',
      content: '',
      enabled: true,
      autoRun: false,
      domain: 'default',
      variables: {}
    };

    renderWithProvider(<ScriptEditor data={scriptData} />);
    
    const executeButton = screen.getByText('脚本编辑器.执行');
    fireEvent.click(executeButton);
    
    // 应该显示警告消息（这里需要根据实际实现调整）
    await waitFor(() => {
      // 验证警告消息或其他适当的反馈
      expect(executeButton).not.toBeDisabled();
    });
  });

  test('应该能够禁用脚本', () => {
    const mockOnChange = jest.fn();
    const scriptData = {
      type: 'javascript',
      content: 'console.log("Hello World");',
      enabled: false,
      autoRun: false,
      domain: 'default',
      variables: {}
    };

    renderWithProvider(
      <ScriptEditor data={scriptData} onChange={mockOnChange} />
    );
    
    // 执行按钮应该被禁用
    const executeButton = screen.getByText('脚本编辑器.执行');
    expect(executeButton).toBeDisabled();
  });
});
