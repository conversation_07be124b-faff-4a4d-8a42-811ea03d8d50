/**
 * 面部动画模板管理器样式
 */
.facial-animation-template-manager {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .template-manager-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .template-list {
    flex: 1;
    overflow-y: auto;
    
    .ant-card {
      height: 100%;
      
      .ant-card-meta-description {
        height: 60px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      
      .template-tags {
        margin-top: 8px;
        
        .ant-tag {
          margin-bottom: 4px;
        }
      }
    }
  }
  
  .template-preview {
    display: flex;
    flex-direction: column;
    
    .preview-container {
      position: relative;
      
      .facial-animation-preview {
        height: 300px;
        margin-bottom: 16px;
      }
      
      .preview-controls {
        position: absolute;
        bottom: 24px;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.5);
        padding: 8px;
        border-radius: 50%;
      }
    }
    
    .template-parameters {
      padding: 0 16px;
      
      h3 {
        margin-bottom: 16px;
      }
      
      .parameters-list {
        display: flex;
        flex-direction: column;
        gap: 16px;
        
        .parameter-item {
          margin-bottom: 8px;
          
          .parameter-control {
            display: flex;
            flex-direction: column;
            gap: 8px;
            
            .parameter-label {
              font-weight: 500;
            }
          }
        }
      }
    }
  }
  
  .ant-upload-drag {
    padding: 16px;
  }
}

/* 响应式布局 */
@media (min-width: 768px) {
  .facial-animation-template-manager {
    .template-preview {
      flex-direction: row;
      
      .preview-container {
        width: 60%;
        
        .facial-animation-preview {
          height: 400px;
          margin-bottom: 0;
        }
      }
      
      .template-parameters {
        width: 40%;
        padding: 0 0 0 16px;
      }
    }
  }
}
