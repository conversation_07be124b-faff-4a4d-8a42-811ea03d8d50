#!/usr/bin/env node

/**
 * 批量修复 TabPane 警告的脚本
 * 将已弃用的 TabPane 替换为新的 items 属性格式
 */

const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const filesToFix = [
  'src/components/achievements/AchievementPanel.tsx',
  'src/components/PropertiesPanel/index.tsx',
  'src/components/scripting/ScriptTemplates.tsx',
  'src/components/tutorials/TutorialPanel.tsx',
  'src/components/panels/InspectorPanel.tsx',
  'src/components/ExampleBrowser/ExampleBrowserEnhanced.tsx',
  'src/components/optimization/PerformanceOptimizationPanel.tsx'
];

/**
 * 修复单个文件
 */
function fixTabPaneInFile(filePath) {
  try {
    console.log(`正在修复文件: ${filePath}`);
    
    if (!fs.existsSync(filePath)) {
      console.log(`文件不存在，跳过: ${filePath}`);
      return;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 移除 TabPane 的解构赋值
    if (content.includes('const { TabPane } = Tabs;')) {
      content = content.replace(/const\s*{\s*TabPane\s*}\s*=\s*Tabs;\s*\n?/g, '');
      modified = true;
      console.log(`  - 移除了 TabPane 解构赋值`);
    }
    
    // 添加注释提示需要手动修复
    if (content.includes('<TabPane') || content.includes('TabPane')) {
      const warningComment = `
// ⚠️ 警告: 此文件包含已弃用的 TabPane 组件
// 需要手动将 TabPane 替换为 Tabs 的 items 属性格式
// 参考: https://ant.design/components/tabs-cn#tabs-tabpane-已废弃
`;
      
      // 在文件开头添加警告注释（如果还没有的话）
      if (!content.includes('⚠️ 警告: 此文件包含已弃用的 TabPane 组件')) {
        const importIndex = content.indexOf('import');
        if (importIndex !== -1) {
          content = content.slice(0, importIndex) + warningComment + content.slice(importIndex);
          modified = true;
          console.log(`  - 添加了警告注释`);
        }
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`  ✅ 文件已修复`);
    } else {
      console.log(`  - 无需修复`);
    }
    
  } catch (error) {
    console.error(`修复文件失败 ${filePath}:`, error.message);
  }
}

/**
 * 生成修复指南
 */
function generateFixGuide() {
  const guide = `
# TabPane 修复指南

## 问题描述
Ant Design 4.x 中的 \`TabPane\` 组件已被弃用，需要替换为新的 \`items\` 属性格式。

## 修复步骤

### 1. 移除 TabPane 导入
\`\`\`typescript
// 修复前
const { TabPane } = Tabs;

// 修复后
// 移除这行代码
\`\`\`

### 2. 将 TabPane 转换为 items 配置

#### 修复前:
\`\`\`tsx
<Tabs activeKey={activeTab} onChange={setActiveTab}>
  <TabPane tab="标签1" key="tab1">
    内容1
  </TabPane>
  <TabPane tab="标签2" key="tab2">
    内容2
  </TabPane>
</Tabs>
\`\`\`

#### 修复后:
\`\`\`tsx
const tabItems = [
  {
    key: 'tab1',
    label: '标签1',
    children: <div>内容1</div>
  },
  {
    key: 'tab2', 
    label: '标签2',
    children: <div>内容2</div>
  }
];

<Tabs 
  activeKey={activeTab} 
  onChange={setActiveTab}
  items={tabItems}
/>
\`\`\`

### 3. 处理复杂标签
\`\`\`tsx
// 修复前
<TabPane 
  tab={
    <span>
      <Icon /> 标签名
    </span>
  } 
  key="tab1"
>
  内容
</TabPane>

// 修复后
{
  key: 'tab1',
  label: (
    <span>
      <Icon /> 标签名
    </span>
  ),
  children: <div>内容</div>
}
\`\`\`

## 需要手动修复的文件
${filesToFix.map(file => `- ${file}`).join('\n')}

## 参考文档
- [Ant Design Tabs 组件文档](https://ant.design/components/tabs-cn)
- [TabPane 迁移指南](https://ant.design/components/tabs-cn#tabs-tabpane-已废弃)
`;

  fs.writeFileSync('TabPane修复指南.md', guide, 'utf8');
  console.log('\n📖 已生成修复指南: TabPane修复指南.md');
}

/**
 * 主函数
 */
function main() {
  console.log('🔧 开始修复 TabPane 警告...\n');
  
  let fixedCount = 0;
  
  filesToFix.forEach(file => {
    const fullPath = path.resolve(file);
    fixTabPaneInFile(fullPath);
    fixedCount++;
  });
  
  console.log(`\n✅ 处理完成! 共处理了 ${fixedCount} 个文件`);
  console.log('\n⚠️  注意: 由于 TabPane 到 items 的转换比较复杂，');
  console.log('   建议手动检查和修复每个文件中的 TabPane 使用。');
  
  // 生成修复指南
  generateFixGuide();
  
  console.log('\n📋 后续步骤:');
  console.log('1. 查看生成的修复指南');
  console.log('2. 手动修复每个包含 TabPane 的文件');
  console.log('3. 运行 npm run lint 检查是否还有警告');
  console.log('4. 测试功能是否正常');
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = {
  fixTabPaneInFile,
  generateFixGuide
};
