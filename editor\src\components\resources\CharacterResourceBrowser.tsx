/**
 * 角色资源浏览器组件
 * 用于浏览和选择角色模型和动画资源
 */
import React, { useState, useEffect } from 'react';
import { Card, List, Tabs, Input, Tag, Button, Empty, Spin, Modal, Divider, Typography } from 'antd';
import {
  SearchOutlined,
  InfoCircleOutlined,
  DownloadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { resourceService } from '../../services/resourceService';
import './CharacterResourceBrowser.less';

const { TabPane } = Tabs;
const { Meta } = Card;
const { Title, Text, Paragraph } = Typography;

// 模型资源接口
export interface ModelResource {
  id: string;
  name: string;
  path: string;
  thumbnail: string;
  description: string;
  tags: string[];
  features: string[];
  polyCount: number;
  textureSize: number;
  blendShapes?: {
    expressions: string[];
    visemes: string[];
  };
}

// 动画资源接口
export interface AnimationResource {
  id: string;
  name: string;
  path: string;
  thumbnail: string;
  description: string;
  tags: string[];
  clips: {
    name: string;
    duration: number;
    loop: boolean;
    description: string;
  }[];
}

interface CharacterResourceBrowserProps {
  onSelectModel?: (model: ModelResource) => void;
  onSelectAnimation?: (animation: AnimationResource, clipName?: string) => void;
}

const CharacterResourceBrowser: React.FC<CharacterResourceBrowserProps> = ({ 
  onSelectModel, 
  onSelectAnimation 
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('models');
  const [models, setModels] = useState<ModelResource[]>([]);
  const [animations, setAnimations] = useState<AnimationResource[]>([]);
  const [filteredModels, setFilteredModels] = useState<ModelResource[]>([]);
  const [filteredAnimations, setFilteredAnimations] = useState<AnimationResource[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedModel, setSelectedModel] = useState<ModelResource | null>(null);
  const [selectedAnimation, setSelectedAnimation] = useState<AnimationResource | null>(null);
  const [previewPlaying, setPreviewPlaying] = useState(false);

  // 加载资源
  useEffect(() => {
    const loadResources = async () => {
      setLoading(true);
      try {
        // 加载模型资源
        const modelData = await resourceService.getCharacterModels();
        setModels(modelData);
        setFilteredModels(modelData);

        // 加载动画资源
        const animationData = await resourceService.getAnimationResources();
        setAnimations(animationData);
        setFilteredAnimations(animationData);
      } catch (error) {
        console.error('加载资源失败:', error);
      } finally {
        setLoading(false);
      }
    };

    loadResources();
  }, []);

  // 处理搜索
  useEffect(() => {
    if (searchQuery.trim() === '' && selectedTags.length === 0) {
      setFilteredModels(models);
      setFilteredAnimations(animations);
      return;
    }

    // 过滤模型
    const filteredModels = models.filter(model => {
      const matchesSearch = searchQuery.trim() === '' || 
        model.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        model.description.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesTags = selectedTags.length === 0 || 
        selectedTags.every(tag => model.tags.includes(tag));
      
      return matchesSearch && matchesTags;
    });
    setFilteredModels(filteredModels);

    // 过滤动画
    const filteredAnimations = animations.filter(animation => {
      const matchesSearch = searchQuery.trim() === '' || 
        animation.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        animation.description.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesTags = selectedTags.length === 0 || 
        selectedTags.every(tag => animation.tags.includes(tag));
      
      return matchesSearch && matchesTags;
    });
    setFilteredAnimations(filteredAnimations);
  }, [searchQuery, selectedTags, models, animations]);

  // 处理标签选择
  const handleTagSelect = (tag: string) => {
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter(t => t !== tag));
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
  };

  // 显示模型详情
  const showModelDetail = (model: ModelResource) => {
    setSelectedModel(model);
    setSelectedAnimation(null);
    setDetailModalVisible(true);
  };

  // 显示动画详情
  const showAnimationDetail = (animation: AnimationResource) => {
    setSelectedAnimation(animation);
    setSelectedModel(null);
    setDetailModalVisible(true);
  };

  // 渲染模型卡片
  const renderModelCard = (model: ModelResource) => (
    <Card
      hoverable
      className="resource-card"
      cover={<img alt={model.name} src={model.thumbnail} />}
      actions={[
        <Button 
          type="text" 
          icon={<InfoCircleOutlined />} 
          onClick={() => showModelDetail(model)}
        >
          {t('resources.details') || '详情'}
        </Button>,
        <Button
          type="primary"
          onClick={() => onSelectModel && onSelectModel(model)}
        >
          {t('resources.select') || '选择'}
        </Button>
      ]}
    >
      <Meta
        title={model.name}
        description={
          <div>
            <Paragraph ellipsis={{ rows: 2 }}>{model.description}</Paragraph>
            <div className="resource-tags">
              {model.tags.map(tag => (
                <Tag key={tag} onClick={() => handleTagSelect(tag)}>{tag}</Tag>
              ))}
            </div>
          </div>
        }
      />
    </Card>
  );

  // 渲染动画卡片
  const renderAnimationCard = (animation: AnimationResource) => (
    <Card
      hoverable
      className="resource-card"
      cover={<img alt={animation.name} src={animation.thumbnail} />}
      actions={[
        <Button 
          type="text" 
          icon={<InfoCircleOutlined />} 
          onClick={() => showAnimationDetail(animation)}
        >
          {t('resources.details') || '详情'}
        </Button>,
        <Button
          type="primary"
          onClick={() => onSelectAnimation && onSelectAnimation(animation)}
        >
          {t('resources.select') || '选择'}
        </Button>
      ]}
    >
      <Meta
        title={animation.name}
        description={
          <div>
            <Paragraph ellipsis={{ rows: 2 }}>{animation.description}</Paragraph>
            <div className="resource-tags">
              {animation.tags.map(tag => (
                <Tag key={tag} onClick={() => handleTagSelect(tag)}>{tag}</Tag>
              ))}
            </div>
            <div className="clip-count">
              {t('resources.clipCount', { count: animation.clips.length }) || `${animation.clips.length} 个动画片段`}
            </div>
          </div>
        }
      />
    </Card>
  );

  // 渲染模型详情
  const renderModelDetail = () => {
    if (!selectedModel) return null;

    return (
      <div className="resource-detail">
        <div className="resource-detail-header">
          <img 
            src={selectedModel.thumbnail} 
            alt={selectedModel.name} 
            className="detail-thumbnail" 
          />
          <div className="detail-info">
            <Title level={4}>{selectedModel.name}</Title>
            <Paragraph>{selectedModel.description}</Paragraph>
            <div className="detail-meta">
              <div className="meta-item">
                <Text strong>{t('resources.polyCount') || '多边形数量'}:</Text> {selectedModel.polyCount.toLocaleString()}
              </div>
              <div className="meta-item">
                <Text strong>{t('resources.textureSize') || '纹理尺寸'}:</Text> {selectedModel.textureSize}x{selectedModel.textureSize}
              </div>
            </div>
            <div className="detail-tags">
              {selectedModel.tags.map(tag => (
                <Tag key={tag}>{tag}</Tag>
              ))}
            </div>
            <div className="detail-features">
              <Text strong>{t('resources.features') || '特性'}:</Text>
              <div className="feature-list">
                {selectedModel.features.map(feature => (
                  <Tag key={feature} color="blue">{feature}</Tag>
                ))}
              </div>
            </div>
          </div>
        </div>

        <Divider />

        {selectedModel.blendShapes && (
          <div className="blend-shapes-section">
            <Title level={5}>{t('resources.blendShapes') || '混合形状'}</Title>
            <div className="blend-shapes-container">
              <div className="blend-shapes-group">
                <Text strong>{t('resources.expressions') || '表情'}:</Text>
                <div className="blend-shapes-list">
                  {selectedModel.blendShapes.expressions.map(expression => (
                    <Tag key={expression}>{expression}</Tag>
                  ))}
                </div>
              </div>
              <div className="blend-shapes-group">
                <Text strong>{t('resources.visemes') || '口型'}:</Text>
                <div className="blend-shapes-list">
                  {selectedModel.blendShapes.visemes.map(viseme => (
                    <Tag key={viseme}>{viseme}</Tag>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="detail-actions">
          <Button
            type="primary"
            icon={<DownloadOutlined />}
            onClick={() => onSelectModel && onSelectModel(selectedModel)}
          >
            {t('resources.useModel') || '使用模型'}
          </Button>
        </div>
      </div>
    );
  };

  // 渲染动画详情
  const renderAnimationDetail = () => {
    if (!selectedAnimation) return null;

    return (
      <div className="resource-detail">
        <div className="resource-detail-header">
          <div className="preview-container">
            <img 
              src={selectedAnimation.thumbnail} 
              alt={selectedAnimation.name} 
              className="detail-thumbnail" 
            />
            <Button
              className="preview-button"
              type="primary"
              shape="circle"
              icon={previewPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={() => setPreviewPlaying(!previewPlaying)}
            />
          </div>
          <div className="detail-info">
            <Title level={4}>{selectedAnimation.name}</Title>
            <Paragraph>{selectedAnimation.description}</Paragraph>
            <div className="detail-tags">
              {selectedAnimation.tags.map(tag => (
                <Tag key={tag}>{tag}</Tag>
              ))}
            </div>
          </div>
        </div>

        <Divider />

        <div className="animation-clips-section">
          <Title level={5}>{t('resources.animationClips') || '动画片段'}</Title>
          <List
            className="clip-list"
            itemLayout="horizontal"
            dataSource={selectedAnimation.clips}
            renderItem={clip => (
              <List.Item
                actions={[
                  <Button
                    type="primary"
                    size="small"
                    onClick={() => onSelectAnimation && onSelectAnimation(selectedAnimation, clip.name)}
                  >
                    {t('resources.useClip') || '使用片段'}
                  </Button>
                ]}
              >
                <List.Item.Meta
                  title={clip.name}
                  description={
                    <div>
                      <div>{clip.description}</div>
                      <div className="clip-meta">
                        <span>{t('resources.duration') || '时长'}: {clip.duration.toFixed(1)}s</span>
                        <span>{t('resources.loop') || '循环'}: {clip.loop ? (t('common.yes') || '是') : (t('common.no') || '否')}</span>
                      </div>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        </div>

        <div className="detail-actions">
          <Button
            type="primary"
            icon={<DownloadOutlined />}
            onClick={() => onSelectAnimation && onSelectAnimation(selectedAnimation)}
          >
            {t('resources.useAnimation') || '使用动画'}
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className="character-resource-browser">
      <div className="resource-search">
        <Input
          placeholder={t('resources.searchPlaceholder') || '搜索资源...'}
          prefix={<SearchOutlined />}
          value={searchQuery}
          onChange={e => setSearchQuery(e.target.value)}
          allowClear
        />
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={t('resources.models') || '模型'} key="models">
          {loading ? (
            <div className="loading-container">
              <Spin size="large" />
            </div>
          ) : filteredModels.length > 0 ? (
            <div className="resource-grid">
              {filteredModels.map(model => (
                <div key={model.id} className="resource-grid-item">
                  {renderModelCard(model)}
                </div>
              ))}
            </div>
          ) : (
            <Empty description={t('resources.noModelsFound') || '未找到模型'} />
          )}
        </TabPane>

        <TabPane tab={t('resources.animations') || '动画'} key="animations">
          {loading ? (
            <div className="loading-container">
              <Spin size="large" />
            </div>
          ) : filteredAnimations.length > 0 ? (
            <div className="resource-grid">
              {filteredAnimations.map(animation => (
                <div key={animation.id} className="resource-grid-item">
                  {renderAnimationCard(animation)}
                </div>
              ))}
            </div>
          ) : (
            <Empty description={t('resources.noAnimationsFound') || '未找到动画'} />
          )}
        </TabPane>
      </Tabs>

      <Modal
        title={null}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
        styles={{ body: { padding: 24 } }}
      >
        {selectedModel && renderModelDetail()}
        {selectedAnimation && renderAnimationDetail()}
      </Modal>
    </div>
  );
};

export default CharacterResourceBrowser;
