/**
 * 帮助面板组件
 * 用于显示帮助内容
 */
import React, { useEffect, useState } from 'react';
import { Card, Tabs, Input, List, Tag, Typography, Space, Divider, Button, Empty, Spin } from 'antd';
import { SearchOutlined, HistoryOutlined, BookOutlined, TagOutlined, PlayCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import MarkdownViewer from '../common/MarkdownViewer';
import helpSystem, { HelpContent } from './HelpSystem';
import VideoPlayer from './VideoPlayer';
import './HelpPanel.less';

const { TabPane } = Tabs;
const { Title, Paragraph } = Typography;

// 帮助面板属性
interface HelpPanelProps {
  defaultHelpId?: string;  // 默认显示的帮助内容ID
  width?: number | string; // 面板宽度
  height?: number | string; // 面板高度
}

/**
 * 帮助面板组件
 */
const HelpPanel: React.FC<HelpPanelProps> = ({
  defaultHelpId,
  width = '100%',
  height = '100%'
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<string>('content');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [searchResults, setSearchResults] = useState<HelpContent[]>([]);
  const [currentHelp, setCurrentHelp] = useState<HelpContent | undefined>(undefined);
  const [helpHistory, setHelpHistory] = useState<HelpContent[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [showVideo, setShowVideo] = useState<boolean>(false);

  // 初始化
  useEffect(() => {
    // 监听帮助显示事件
    helpSystem.on('helpShown', (content: HelpContent) => {
      setCurrentHelp(content);
      setActiveTab('content');
    });

    // 监听搜索完成事件
    helpSystem.on('searchCompleted', ({ results }) => {
      setSearchResults(results);
      setLoading(false);
      if (results.length > 0) {
        setActiveTab('search');
      }
    });

    // 获取分类
    setCategories(helpSystem.getHelpCategories());

    // 如果有默认帮助ID，显示对应内容
    if (defaultHelpId) {
      helpSystem.showHelp(defaultHelpId);
    }

    // 获取历史记录
    setHelpHistory(helpSystem.getHelpHistory());

    // 清理事件监听
    return () => {
      helpSystem.removeAllListeners('helpShown');
      helpSystem.removeAllListeners('searchCompleted');
    };
  }, [defaultHelpId]);

  // 处理搜索
  const handleSearch = () => {
    if (!searchQuery.trim()) return;
    
    setLoading(true);
    const results = helpSystem.searchHelp(searchQuery);
    setSearchResults(results);
    setLoading(false);
    setActiveTab('search');
  };

  // 处理帮助项点击
  const handleHelpItemClick = (helpId: string) => {
    helpSystem.showHelp(helpId);
    // 更新历史记录
    setHelpHistory(helpSystem.getHelpHistory());
  };

  // 渲染帮助内容
  const renderHelpContent = () => {
    if (!currentHelp) {
      return (
        <Empty
          description={t('help.noContentSelected')}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }

    return (
      <div className="help-content">
        <Title level={3}>{currentHelp.title}</Title>
        <Space>
          <Tag color="blue">{currentHelp.category}</Tag>
          {currentHelp.tags.map(tag => (
            <Tag key={tag}>{tag}</Tag>
          ))}
        </Space>
        <Divider />
        <div className="markdown-content">
          <MarkdownViewer content={currentHelp.content} />
        </div>
        {currentHelp.videoUrl && (
          <div className="video-section">
            <Divider orientation="left">{t('help.relatedVideo')}</Divider>
            {showVideo ? (
              <VideoPlayer url={currentHelp.videoUrl} />
            ) : (
              <Button 
                type="primary" 
                icon={<PlayCircleOutlined />} 
                onClick={() => setShowVideo(true)}
              >
                {t('help.watchVideo')}
              </Button>
            )}
          </div>
        )}
        {currentHelp.relatedTopics && currentHelp.relatedTopics.length > 0 && (
          <div className="related-topics">
            <Divider orientation="left">{t('help.relatedTopics')}</Divider>
            <Space wrap>
              {currentHelp.relatedTopics.map(topicId => {
                const topic = helpSystem.getHelpContent(topicId);
                return topic ? (
                  <Button 
                    key={topicId} 
                    type="link" 
                    onClick={() => handleHelpItemClick(topicId)}
                  >
                    {topic.title}
                  </Button>
                ) : null;
              })}
            </Space>
          </div>
        )}
      </div>
    );
  };

  // 渲染搜索结果
  const renderSearchResults = () => {
    if (loading) {
      return <Spin size="large" />;
    }

    if (searchResults.length === 0) {
      return (
        <Empty
          description={t('help.noSearchResults')}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }

    return (
      <List
        itemLayout="vertical"
        dataSource={searchResults}
        renderItem={item => (
          <List.Item
            key={item.id}
            onClick={() => handleHelpItemClick(item.id)}
            className="help-list-item"
          >
            <List.Item.Meta
              title={item.title}
              description={
                <Space>
                  <Tag color="blue">{item.category}</Tag>
                  {item.tags.slice(0, 3).map(tag => (
                    <Tag key={tag}>{tag}</Tag>
                  ))}
                </Space>
              }
            />
            <Paragraph ellipsis={{ rows: 2 }}>
              {item.content.substring(0, 150)}...
            </Paragraph>
          </List.Item>
        )}
      />
    );
  };

  // 渲染历史记录
  const renderHistory = () => {
    if (helpHistory.length === 0) {
      return (
        <Empty
          description={t('help.noHistory')}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }

    return (
      <List
        itemLayout="horizontal"
        dataSource={helpHistory}
        renderItem={item => (
          <List.Item
            key={item.id}
            onClick={() => handleHelpItemClick(item.id)}
            className="help-list-item"
          >
            <List.Item.Meta
              title={item.title}
              description={
                <Space>
                  <Tag color="blue">{item.category}</Tag>
                  {item.tags.slice(0, 2).map(tag => (
                    <Tag key={tag}>{tag}</Tag>
                  ))}
                </Space>
              }
            />
          </List.Item>
        )}
      />
    );
  };

  // 渲染分类
  const renderCategories = () => {
    return (
      <List
        itemLayout="horizontal"
        dataSource={categories}
        renderItem={category => {
          // 获取该分类下的所有帮助内容
          const categoryContents = helpSystem.getAllHelpContents()
            .filter(content => content.category === category);
          
          return (
            <List.Item key={category}>
              <List.Item.Meta
                title={category}
                description={`${categoryContents.length} ${t('help.topics')}`}
              />
              <div className="category-contents">
                {categoryContents.slice(0, 5).map(content => (
                  <Button 
                    key={content.id} 
                    type="link" 
                    onClick={() => handleHelpItemClick(content.id)}
                  >
                    {content.title}
                  </Button>
                ))}
                {categoryContents.length > 5 && (
                  <Button type="link">
                    {t('help.moreTopics', { count: categoryContents.length - 5 })}
                  </Button>
                )}
              </div>
            </List.Item>
          );
        }}
      />
    );
  };

  return (
    <Card
      className="help-panel"
      style={{ width, height }}
      styles={{ body: { padding: 0, height: '100%' } }}
    >
      <div className="help-search">
        <Input
          placeholder={t('help.searchPlaceholder') as string}
          prefix={<SearchOutlined />}
          value={searchQuery}
          onChange={e => setSearchQuery(e.target.value)}
          onPressEnter={handleSearch}
          suffix={
            <Button
              type="primary"
              size="small"
              onClick={handleSearch}
              disabled={!searchQuery.trim()}
            >
              {t('help.search')}
            </Button>
          }
        />
      </div>
      <Tabs activeKey={activeTab} onChange={setActiveTab} className="help-tabs">
        <TabPane
          tab={
            <span>
              <BookOutlined />
              {t('help.content')}
            </span>
          }
          key="content"
        >
          {renderHelpContent()}
        </TabPane>
        <TabPane
          tab={
            <span>
              <SearchOutlined />
              {t('help.search')}
              {searchResults.length > 0 && <Tag>{searchResults.length}</Tag>}
            </span>
          }
          key="search"
        >
          {renderSearchResults()}
        </TabPane>
        <TabPane
          tab={
            <span>
              <HistoryOutlined />
              {t('help.history')}
            </span>
          }
          key="history"
        >
          {renderHistory()}
        </TabPane>
        <TabPane
          tab={
            <span>
              <TagOutlined />
              {t('help.categories')}
            </span>
          }
          key="categories"
        >
          {renderCategories()}
        </TabPane>
      </Tabs>
    </Card>
  );
};

export default HelpPanel;
