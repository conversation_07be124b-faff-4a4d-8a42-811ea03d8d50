/**
 * JSON数据查看组件
 */
import React, { useState } from 'react';
import { Tree, Typography, Switch, Space } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import { DataNode } from 'antd/lib/tree';

const { Text } = Typography;

// 组件属性
interface JsonViewProps {
  data: any;
  expandAll?: boolean;
  showDataTypes?: boolean;
}

/**
 * 将JSON数据转换为树节点
 * @param data JSON数据
 * @param key 节点键
 * @param showDataTypes 是否显示数据类型
 * @returns 树节点数组
 */
const convertJsonToTreeData = (
  data: any, 
  key: string = 'root', 
  showDataTypes: boolean = false
): DataNode[] => {
  if (data === null) {
    return [{
      key,
      title: (
        <Space>
          <Text>{key}:</Text>
          <Text type="secondary">null</Text>
          {showDataTypes && <Text type="secondary" italic>(null)</Text>}
        </Space>
      ),
      isLeaf: true
    }];
  }
  
  if (data === undefined) {
    return [{
      key,
      title: (
        <Space>
          <Text>{key}:</Text>
          <Text type="secondary">undefined</Text>
          {showDataTypes && <Text type="secondary" italic>(undefined)</Text>}
        </Space>
      ),
      isLeaf: true
    }];
  }
  
  if (typeof data !== 'object') {
    let valueDisplay;
    let typeDisplay = typeof data;
    
    if (typeof data === 'string') {
      valueDisplay = <Text type="success">"{data}"</Text>;
    } else if (typeof data === 'number') {
      valueDisplay = <Text type="warning">{data}</Text>;
    } else if (typeof data === 'boolean') {
      valueDisplay = <Text type="danger">{data ? 'true' : 'false'}</Text>;
    } else {
      valueDisplay = <Text>{String(data)}</Text>;
    }
    
    return [{
      key,
      title: (
        <Space>
          <Text>{key}:</Text>
          {valueDisplay}
          {showDataTypes && <Text type="secondary" italic>({typeDisplay})</Text>}
        </Space>
      ),
      isLeaf: true
    }];
  }
  
  if (Array.isArray(data)) {
    const children: DataNode[] = [];

    data.forEach((item, index) => {
      const childNodes = convertJsonToTreeData(item, `[${index}]`, showDataTypes);
      children.push(...childNodes);
    });
    
    return [{
      key,
      title: (
        <Space>
          <Text>{key}:</Text>
          <Text type="secondary">Array[{data.length}]</Text>
          {showDataTypes && <Text type="secondary" italic>(array)</Text>}
        </Space>
      ),
      children
    }];
  }
  
  // 对象类型
  const children: DataNode[] = [];

  Object.keys(data).forEach(prop => {
    const childNodes = convertJsonToTreeData(data[prop], prop, showDataTypes);
    children.push(...childNodes);
  });
  
  return [{
    key,
    title: (
      <Space>
        <Text>{key}:</Text>
        <Text type="secondary">Object</Text>
        {showDataTypes && <Text type="secondary" italic>(object)</Text>}
      </Space>
    ),
    children
  }];
};

/**
 * JSON数据查看组件
 */
const JsonView: React.FC<JsonViewProps> = ({ 
  data, 
  expandAll = false,
  showDataTypes = true
}) => {
  const [expanded, setExpanded] = useState<boolean>(expandAll);
  const [showTypes, setShowTypes] = useState<boolean>(showDataTypes);
  
  // 转换数据为树节点
  const treeData = convertJsonToTreeData(data, 'root', showTypes);
  
  // 获取默认展开的键
  const getDefaultExpandedKeys = (nodes: DataNode[], keys: string[] = []): string[] => {
    if (!expanded) {
      return [];
    }
    
    nodes.forEach(node => {
      keys.push(node.key as string);
      
      if (node.children) {
        getDefaultExpandedKeys(node.children, keys);
      }
    });
    
    return keys;
  };
  
  const defaultExpandedKeys = getDefaultExpandedKeys(treeData);
  
  return (
    <div className="json-view">
      <div className="json-view-controls" style={{ marginBottom: 8 }}>
        <Space>
          <Switch 
            checked={expanded} 
            onChange={setExpanded} 
            size="small" 
          />
          <Text type="secondary">展开全部</Text>
          
          <Switch 
            checked={showTypes} 
            onChange={setShowTypes} 
            size="small" 
            style={{ marginLeft: 16 }}
          />
          <Text type="secondary">显示类型</Text>
        </Space>
      </div>
      
      <Tree
        showLine
        switcherIcon={<DownOutlined />}
        defaultExpandedKeys={defaultExpandedKeys}
        treeData={treeData}
      />
    </div>
  );
};

export default JsonView;
