/**
 * 版本控制面板组件
 * 提供Git风格的版本控制功能界面
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Button,
  Space,
  Typography,
  List,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Timeline,
  Badge,
  Alert,
  Drawer
} from 'antd';
import {
  BranchesOutlined,
  PlusOutlined,
  DeleteOutlined,
  FileOutlined,
  DiffOutlined,
  GitlabOutlined,
  MergeOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import VersionControlService, {
  Repository,
  Branch,
  Commit,
  FileChange,
  FileChangeStatus,
  BranchType
} from '../../services/VersionControlService';
import './VersionControlPanel.less';

const { Title, Text } = Typography;
const { Option } = Select;

interface VersionControlPanelProps {
  visible: boolean;
  onClose: () => void;
}

const VersionControlPanel: React.FC<VersionControlPanelProps> = ({
  visible,
  onClose
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('branches');
  const [repository, setRepository] = useState<Repository | null>(null);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [commits, setCommits] = useState<Commit[]>([]);
  const [workingStatus, setWorkingStatus] = useState<any>({});
  const [newBranchModalVisible, setNewBranchModalVisible] = useState(false);
  const [commitModalVisible, setCommitModalVisible] = useState(false);
  const [diffDrawerVisible, setDiffDrawerVisible] = useState(false);
  const [selectedFile, setSelectedFile] = useState<FileChange | null>(null);

  const versionControlService = VersionControlService.getInstance();

  useEffect(() => {
    if (visible) {
      loadData();
      setupEventListeners();
    }

    return () => {
      cleanupEventListeners();
    };
  }, [visible]);

  const setupEventListeners = () => {
    versionControlService.on('branchCreated', handleBranchCreated);
    versionControlService.on('branchSwitched', handleBranchSwitched);
    versionControlService.on('commitCreated', handleCommitCreated);
    versionControlService.on('branchMerged', handleBranchMerged);
  };

  const cleanupEventListeners = () => {
    versionControlService.off('branchCreated', handleBranchCreated);
    versionControlService.off('branchSwitched', handleBranchSwitched);
    versionControlService.off('commitCreated', handleCommitCreated);
    versionControlService.off('branchMerged', handleBranchMerged);
  };

  const loadData = () => {
    const repo = versionControlService.getActiveRepository();
    setRepository(repo);

    if (repo) {
      setBranches(versionControlService.getBranches());
      setCommits(versionControlService.getCommitHistory());
      setWorkingStatus(versionControlService.getWorkingDirectoryStatus());
    }
  };

  const handleBranchCreated = (branch: Branch) => {
    setBranches(prev => [...prev, branch]);
  };

  const handleBranchSwitched = ({ to }: { from?: string; to: string }) => {
    setBranches(prev => prev.map(b => ({
      ...b,
      isActive: b.name === to
    })));
  };

  const handleCommitCreated = (commit: Commit) => {
    setCommits(prev => [commit, ...prev]);
    setWorkingStatus(versionControlService.getWorkingDirectoryStatus());
  };

  const handleBranchMerged = ({ commit }: any) => {
    if (commit) {
      setCommits(prev => [commit, ...prev]);
    }
    loadData(); // 重新加载数据
  };

  const handleCreateBranch = async (values: any) => {
    try {
      versionControlService.createBranch(
        values.name,
        values.type,
        values.description,
        values.baseBranch
      );
      setNewBranchModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('Failed to create branch:', error);
    }
  };

  const handleSwitchBranch = async (branchName: string) => {
    try {
      await versionControlService.switchBranch(branchName);
    } catch (error) {
      console.error('Failed to switch branch:', error);
    }
  };

  const handleCommit = async (values: any) => {
    try {
      await versionControlService.commit(values.message);
      setCommitModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('Failed to commit:', error);
    }
  };



  const handleStageFile = (filePath: string) => {
    versionControlService.stageFile(filePath);
    setWorkingStatus(versionControlService.getWorkingDirectoryStatus());
  };

  const handleUnstageFile = (filePath: string) => {
    versionControlService.unstageFile(filePath);
    setWorkingStatus(versionControlService.getWorkingDirectoryStatus());
  };

  const handleViewDiff = (file: FileChange) => {
    setSelectedFile(file);
    setDiffDrawerVisible(true);
  };

  // 渲染分支列表
  const renderBranches = () => (
    <div className="branches-section">
      <div className="section-header">
        <Space>
          <Title level={5}>{t('versionControl.branches')}</Title>
          <Badge count={branches.length} showZero />
        </Space>
        <Button type="primary" size="small" onClick={() => setNewBranchModalVisible(true)}>
          <PlusOutlined />
          {t('versionControl.newBranch')}
        </Button>
      </div>

      <List
        dataSource={branches}
        renderItem={(branch) => (
          <List.Item
            className={`branch-item ${branch.isActive ? 'active' : ''}`}
            actions={[
              !branch.isActive && (
                <Button
                  type="text"
                  size="small"
                  onClick={() => handleSwitchBranch(branch.name)}
                >
                  {t('versionControl.switch')}
                </Button>
              ),

              !branch.isProtected && (
                <Button
                  type="text"
                  size="small"
                  icon={<DeleteOutlined />}
                  danger
                />
              )
            ].filter(Boolean)}
          >
            <List.Item.Meta
              avatar={
                <div className={`branch-icon ${branch.type}`}>
                  <BranchesOutlined />
                </div>
              }
              title={
                <div className="branch-info">
                  <Text strong>{branch.name}</Text>
                  {branch.isActive && (
                    <Tag color="green" style={{ marginLeft: 8, fontSize: '12px' }}>
                      {t('versionControl.current')}
                    </Tag>
                  )}
                  {branch.isProtected && (
                    <Tag color="orange" style={{ marginLeft: 4, fontSize: '12px' }}>
                      {t('versionControl.protected')}
                    </Tag>
                  )}
                </div>
              }
              description={
                <div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {branch.description || branch.type}
                  </Text>
                  <br />
                  <Space style={{ marginTop: 4 }}>
                    <Text type="secondary" style={{ fontSize: '11px' }}>
                      {branch.stats.commits} commits
                    </Text>
                    <Text type="secondary" style={{ fontSize: '11px' }}>
                      {new Date(branch.lastCommitAt).toLocaleDateString()}
                    </Text>
                  </Space>
                </div>
              }
            />
          </List.Item>
        )}
      />
    </div>
  );

  // 渲染提交历史
  const renderCommits = () => (
    <div className="commits-section">
      <div className="section-header">
        <Space>
          <Title level={5}>{t('versionControl.commits')}</Title>
          <Badge count={commits.length} showZero />
        </Space>
        <Button
          type="primary"
          size="small"
          onClick={() => setCommitModalVisible(true)}
          disabled={workingStatus.staged?.length === 0}
        >
          <PlusOutlined />
          {t('versionControl.commit')}
        </Button>
      </div>

      <Timeline>
        {commits.slice(0, 20).map((commit) => (
          <Timeline.Item
            key={commit.id}
            dot={
              commit.metadata.isMerge ? (
                <MergeOutlined style={{ color: '#1890ff' }} />
              ) : (
                <PlusOutlined style={{ color: '#52c41a' }} />
              )
            }
          >
            <div className="commit-item">
              <div className="commit-header">
                <Text strong>{commit.message}</Text>
                <Text type="secondary" style={{ fontSize: '12px', marginLeft: 8 }}>
                  {commit.hash.substring(0, 8)}
                </Text>
              </div>
              <div className="commit-meta">
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  {commit.author.name} • {new Date(commit.timestamp).toLocaleString()}
                </Text>
                {commit.tags.length > 0 && (
                  <div style={{ marginTop: 4 }}>
                    {commit.tags.map(tag => (
                      <Tag key={tag} color="blue" style={{ fontSize: '12px' }}>{tag}</Tag>
                    ))}
                  </div>
                )}
              </div>
              <div className="commit-stats">
                <Space size="small">
                  <Text type="secondary" style={{ fontSize: '11px' }}>
                    +{commit.metadata.stats.additions}
                  </Text>
                  <Text type="secondary" style={{ fontSize: '11px' }}>
                    -{commit.metadata.stats.deletions}
                  </Text>
                  <Text type="secondary" style={{ fontSize: '11px' }}>
                    {commit.metadata.stats.filesChanged} files
                  </Text>
                </Space>
              </div>
            </div>
          </Timeline.Item>
        ))}
      </Timeline>
    </div>
  );

  // 渲染工作目录状态
  const renderWorkingDirectory = () => (
    <div className="working-directory">
      <Title level={5}>{t('versionControl.workingDirectory')}</Title>
      
      {workingStatus.staged?.length > 0 && (
        <Card size="small" title={t('versionControl.stagedChanges')} style={{ marginBottom: 16 }}>
          <List
            dataSource={workingStatus.staged}
            renderItem={(file: FileChange) => (
              <List.Item
                actions={[
                  <Button
                    type="text"
                    size="small"
                    onClick={() => handleUnstageFile(file.path)}
                  >
                    {t('versionControl.unstage')}
                  </Button>,
                  <Button
                    type="text"
                    size="small"
                    icon={<DiffOutlined />}
                    onClick={() => handleViewDiff(file)}
                  />
                ]}
              >
                <List.Item.Meta
                  avatar={<FileOutlined />}
                  title={file.path}
                  description={
                    <Tag color={getStatusColor(file.status)} style={{ fontSize: '12px' }}>
                      {file.status}
                    </Tag>
                  }
                />
              </List.Item>
            )}
          />
        </Card>
      )}

      {workingStatus.unstaged?.length > 0 && (
        <Card size="small" title={t('versionControl.unstagedChanges')}>
          <List
            dataSource={workingStatus.unstaged}
            renderItem={(file: FileChange) => (
              <List.Item
                actions={[
                  <Button
                    type="text"
                    size="small"
                    onClick={() => handleStageFile(file.path)}
                  >
                    {t('versionControl.stage')}
                  </Button>,
                  <Button
                    type="text"
                    size="small"
                    icon={<DiffOutlined />}
                    onClick={() => handleViewDiff(file)}
                  />
                ]}
              >
                <List.Item.Meta
                  avatar={<FileOutlined />}
                  title={file.path}
                  description={
                    <Tag color={getStatusColor(file.status)} style={{ fontSize: '12px' }}>
                      {file.status}
                    </Tag>
                  }
                />
              </List.Item>
            )}
          />
        </Card>
      )}

      {workingStatus.staged?.length === 0 && workingStatus.unstaged?.length === 0 && (
        <Alert
          message={t('versionControl.noChanges')}
          description={t('versionControl.workingDirectoryClean')}
          type="success"
          showIcon
        />
      )}
    </div>
  );

  const getStatusColor = (status: FileChangeStatus): string => {
    switch (status) {
      case FileChangeStatus.ADDED:
        return 'green';
      case FileChangeStatus.MODIFIED:
        return 'blue';
      case FileChangeStatus.DELETED:
        return 'red';
      case FileChangeStatus.RENAMED:
        return 'orange';
      case FileChangeStatus.UNTRACKED:
        return 'default';
      default:
        return 'default';
    }
  };

  return (
    <Modal
      title={
        <Space>
          <GitlabOutlined />
          {t('versionControl.versionControl')}
          {repository && (
            <Tag color="blue">{repository.name}</Tag>
          )}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1000}
      footer={[
        <Button key="close" onClick={onClose}>
          {t('common.close')}
        </Button>
      ]}
      className="version-control-panel"
    >
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'branches',
            label: t('versionControl.branches'),
            children: renderBranches()
          },
          {
            key: 'commits',
            label: t('versionControl.commits'),
            children: renderCommits()
          },
          {
            key: 'working',
            label: t('versionControl.workingDirectory'),
            children: renderWorkingDirectory()
          }
        ]}
      />

      {/* 新建分支对话框 */}
      <Modal
        title={t('versionControl.createBranch')}
        open={newBranchModalVisible}
        onCancel={() => setNewBranchModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} layout="vertical" onFinish={handleCreateBranch}>
          <Form.Item
            name="name"
            label={t('versionControl.branchName')}
            rules={[{ required: true }]}
          >
            <Input />
          </Form.Item>
          <Form.Item name="type" label={t('versionControl.branchType')}>
            <Select defaultValue={BranchType.FEATURE}>
              {Object.values(BranchType).map(type => (
                <Option key={type} value={type}>{type}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="description" label={t('versionControl.description')}>
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item name="baseBranch" label={t('versionControl.baseBranch')}>
            <Select>
              {branches.map(branch => (
                <Option key={branch.name} value={branch.name}>{branch.name}</Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 提交对话框 */}
      <Modal
        title={t('versionControl.createCommit')}
        open={commitModalVisible}
        onCancel={() => setCommitModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} layout="vertical" onFinish={handleCommit}>
          <Form.Item
            name="message"
            label={t('versionControl.commitMessage')}
            rules={[{ required: true }]}
          >
            <Input.TextArea rows={4} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 文件差异抽屉 */}
      <Drawer
        title={`${t('versionControl.fileDiff')}: ${selectedFile?.path}`}
        open={diffDrawerVisible}
        onClose={() => setDiffDrawerVisible(false)}
        width={800}
      >
        {selectedFile && (
          <div className="file-diff">
            <pre>{versionControlService.getFileDiff(selectedFile.path)}</pre>
          </div>
        )}
      </Drawer>
    </Modal>
  );
};

export default VersionControlPanel;
