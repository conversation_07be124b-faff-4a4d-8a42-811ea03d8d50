/**
 * 环境感知面板
 * 
 * 该面板显示和编辑环境感知组件的属性，包括环境类型、天气、光照等。
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Select,
  Button,
  Slider,
  Tooltip,
  Tag,
  Row,
  Col,
  Tabs,
  Space,
  message
} from 'antd';
import {
  EnvironmentOutlined,
  CloudOutlined,
  BulbOutlined,
  SoundOutlined,
  SettingOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

// 本地类型定义
export enum EnvironmentType {
  INDOOR = 'indoor',
  OUTDOOR = 'outdoor',
  UNDERWATER = 'underwater',
  SPACE = 'space',
  CAVE = 'cave',
  FOREST = 'forest',
  DESERT = 'desert',
  SNOW = 'snow',
  URBAN = 'urban',
  CUSTOM = 'custom'
}

export enum WeatherType {
  CLEAR = 'clear',
  CLOUDY = 'cloudy',
  RAINY = 'rainy',
  STORMY = 'stormy',
  SNOWY = 'snowy',
  FOGGY = 'foggy',
  CUSTOM = 'custom'
}

export enum TerrainType {
  FLAT = 'flat',
  HILLS = 'hills',
  MOUNTAINS = 'mountains',
  WATER = 'water',
  URBAN = 'urban',
  CUSTOM = 'custom'
}

export interface EnvironmentAwarenessData {
  environmentType: EnvironmentType;
  weatherType: WeatherType;
  terrainType: TerrainType;
  lightIntensity: number;
  temperature: number;
  humidity: number;
  windSpeed: number;
  noiseLevel: number;
  airQuality: number;
  waterLevel: number;
  visibility: number;
  timeOfDay: number;
}

const { Option } = Select;
const { TabPane } = Tabs;

/**
 * 环境感知面板属性接口
 */
interface EnvironmentAwarenessPanelProps {
  entityId?: string;
  onSave?: (data: Partial<EnvironmentAwarenessData>) => void;
  onRefresh?: () => void;
  environmentData?: EnvironmentAwarenessData;
}

/**
 * 环境感知面板组件
 */
const EnvironmentAwarenessPanel: React.FC<EnvironmentAwarenessPanelProps> = ({
  onSave,
  onRefresh,
  environmentData
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState<string>('basic');
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [localEnvironmentData, setLocalEnvironmentData] = useState<EnvironmentAwarenessData | null>(null);

  // 翻译辅助函数
  const translate = (key: string, defaultValue: string = ''): string => {
    const result = t(key);
    return typeof result === 'string' ? result : defaultValue;
  };

  // 初始化环境数据
  useEffect(() => {
    if (environmentData) {
      setLocalEnvironmentData(environmentData);
      form.setFieldsValue({
        environmentType: environmentData.environmentType,
        weatherType: environmentData.weatherType,
        terrainType: environmentData.terrainType,
        lightIntensity: environmentData.lightIntensity,
        temperature: environmentData.temperature,
        humidity: environmentData.humidity,
        windSpeed: environmentData.windSpeed,
        noiseLevel: environmentData.noiseLevel,
        airQuality: environmentData.airQuality,
        waterLevel: environmentData.waterLevel,
        visibility: environmentData.visibility,
        timeOfDay: environmentData.timeOfDay
      });
    }
  }, [environmentData, form]);

  /**
   * 处理刷新
   */
  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
      message.success(translate('environment.dataRefreshed', '数据已刷新'));
    }
  };

  /**
   * 处理编辑
   */
  const handleEdit = () => {
    setIsEditing(true);
  };

  /**
   * 处理取消
   */
  const handleCancel = () => {
    setIsEditing(false);
    if (environmentData) {
      form.setFieldsValue({
        environmentType: environmentData.environmentType,
        weatherType: environmentData.weatherType,
        terrainType: environmentData.terrainType,
        lightIntensity: environmentData.lightIntensity,
        temperature: environmentData.temperature,
        humidity: environmentData.humidity,
        windSpeed: environmentData.windSpeed,
        noiseLevel: environmentData.noiseLevel,
        airQuality: environmentData.airQuality,
        waterLevel: environmentData.waterLevel,
        visibility: environmentData.visibility,
        timeOfDay: environmentData.timeOfDay
      });
    }
  };

  /**
   * 处理保存
   */
  const handleSave = () => {
    form.validateFields().then(values => {
      if (onSave) {
        onSave(values);
        setIsEditing(false);
        message.success(translate('environment.dataSaved', '数据已保存'));
      }
    });
  };

  /**
   * 渲染基本信息标签页
   */
  const renderBasicTab = () => {
    return (
      <div className="basic-tab">
        <Form
          form={form}
          layout="vertical"
          disabled={!isEditing}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="environmentType"
                label={translate('environment.environmentType', '环境类型')}
                rules={[{ required: true, message: translate('environment.environmentTypeRequired', '请选择环境类型') }]}
              >
                <Select>
                  <Option value={EnvironmentType.INDOOR}>{translate('environment.environmentTypes.indoor', '室内')}</Option>
                  <Option value={EnvironmentType.OUTDOOR}>{translate('environment.environmentTypes.outdoor', '室外')}</Option>
                  <Option value={EnvironmentType.UNDERWATER}>{translate('environment.environmentTypes.underwater', '水下')}</Option>
                  <Option value={EnvironmentType.SPACE}>{translate('environment.environmentTypes.space', '太空')}</Option>
                  <Option value={EnvironmentType.CAVE}>{translate('environment.environmentTypes.cave', '洞穴')}</Option>
                  <Option value={EnvironmentType.FOREST}>{translate('environment.environmentTypes.forest', '森林')}</Option>
                  <Option value={EnvironmentType.DESERT}>{translate('environment.environmentTypes.desert', '沙漠')}</Option>
                  <Option value={EnvironmentType.SNOW}>{translate('environment.environmentTypes.snow', '雪地')}</Option>
                  <Option value={EnvironmentType.URBAN}>{translate('environment.environmentTypes.urban', '城市')}</Option>
                  <Option value={EnvironmentType.CUSTOM}>{translate('environment.environmentTypes.custom', '自定义')}</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="weatherType"
                label={translate('environment.weatherType', '天气类型')}
                rules={[{ required: true, message: translate('environment.weatherTypeRequired', '请选择天气类型') }]}
              >
                <Select>
                  <Option value={WeatherType.CLEAR}>{translate('environment.weatherTypes.clear', '晴天')}</Option>
                  <Option value={WeatherType.CLOUDY}>{translate('environment.weatherTypes.cloudy', '多云')}</Option>
                  <Option value={WeatherType.RAINY}>{translate('environment.weatherTypes.rainy', '雨天')}</Option>
                  <Option value={WeatherType.STORMY}>{translate('environment.weatherTypes.stormy', '暴风雨')}</Option>
                  <Option value={WeatherType.SNOWY}>{translate('environment.weatherTypes.snowy', '雪天')}</Option>
                  <Option value={WeatherType.FOGGY}>{translate('environment.weatherTypes.foggy', '雾天')}</Option>
                  <Option value={WeatherType.CUSTOM}>{translate('environment.weatherTypes.custom', '自定义')}</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="terrainType"
                label={translate('environment.terrainType', '地形类型')}
                rules={[{ required: true, message: translate('environment.terrainTypeRequired', '请选择地形类型') }]}
              >
                <Select>
                  <Option value={TerrainType.FLAT}>{translate('environment.terrainTypes.flat', '平地')}</Option>
                  <Option value={TerrainType.HILLS}>{translate('environment.terrainTypes.hills', '丘陵')}</Option>
                  <Option value={TerrainType.MOUNTAINS}>{translate('environment.terrainTypes.mountains', '山地')}</Option>
                  <Option value={TerrainType.WATER}>{translate('environment.terrainTypes.water', '水域')}</Option>
                  <Option value={TerrainType.URBAN}>{translate('environment.terrainTypes.urban', '城市')}</Option>
                  <Option value={TerrainType.CUSTOM}>{translate('environment.terrainTypes.custom', '自定义')}</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="timeOfDay"
                label={translate('environment.timeOfDay', '时间')}
                rules={[{ required: true, message: translate('environment.timeOfDayRequired', '请设置时间') }]}
              >
                <Slider
                  min={0}
                  max={24}
                  step={0.5}
                  marks={{
                    0: '0:00',
                    6: '6:00',
                    12: '12:00',
                    18: '18:00',
                    24: '24:00'
                  }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    );
  };

  /**
   * 渲染环境条件标签页
   */
  const renderConditionsTab = () => {
    return (
      <div className="conditions-tab">
        <Form
          form={form}
          layout="vertical"
          disabled={!isEditing}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="temperature"
                label={translate('environment.temperature', '温度')}
                rules={[{ required: true, message: translate('environment.temperatureRequired', '请设置温度') }]}
              >
                <Slider
                  min={-50}
                  max={50}
                  marks={{
                    '-50': '-50°C',
                    '0': '0°C',
                    '25': '25°C',
                    '50': '50°C'
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="humidity"
                label={translate('environment.humidity', '湿度')}
                rules={[{ required: true, message: translate('environment.humidityRequired', '请设置湿度') }]}
              >
                <Slider
                  min={0}
                  max={1}
                  step={0.01}
                  marks={{
                    0: '0%',
                    0.5: '50%',
                    1: '100%'
                  }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="windSpeed"
                label={translate('environment.windSpeed', '风速')}
                rules={[{ required: true, message: translate('environment.windSpeedRequired', '请设置风速') }]}
              >
                <Slider
                  min={0}
                  max={30}
                  marks={{
                    0: '0 m/s',
                    10: '10 m/s',
                    20: '20 m/s',
                    30: '30 m/s'
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="visibility"
                label={translate('environment.visibility', '能见度')}
                rules={[{ required: true, message: translate('environment.visibilityRequired', '请设置能见度') }]}
              >
                <Slider
                  min={0}
                  max={2000}
                  step={10}
                  marks={{
                    0: '0m',
                    500: '500m',
                    1000: '1km',
                    2000: '2km'
                  }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    );
  };

  /**
   * 渲染光照标签页
   */
  const renderLightingTab = () => {
    return (
      <div className="lighting-tab">
        <Form
          form={form}
          layout="vertical"
          disabled={!isEditing}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="lightIntensity"
                label={translate('environment.lightIntensity', '光照强度')}
                rules={[{ required: true, message: translate('environment.lightIntensityRequired', '请设置光照强度') }]}
              >
                <Slider
                  min={0}
                  max={1}
                  step={0.01}
                  marks={{
                    0: translate('environment.dark', '暗'),
                    0.5: translate('environment.medium', '中等'),
                    1: translate('environment.bright', '亮')
                  }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    );
  };

  /**
   * 渲染其他标签页
   */
  const renderOthersTab = () => {
    return (
      <div className="others-tab">
        <Form
          form={form}
          layout="vertical"
          disabled={!isEditing}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="noiseLevel"
                label={translate('environment.noiseLevel', '噪音等级')}
                rules={[{ required: true, message: translate('environment.noiseLevelRequired', '请设置噪音等级') }]}
              >
                <Slider
                  min={0}
                  max={1}
                  step={0.01}
                  marks={{
                    0: translate('environment.quiet', '安静'),
                    0.5: translate('environment.moderate', '中等'),
                    1: translate('environment.loud', '嘈杂')
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="airQuality"
                label={translate('environment.airQuality', '空气质量')}
                rules={[{ required: true, message: translate('environment.airQualityRequired', '请设置空气质量') }]}
              >
                <Slider
                  min={0}
                  max={1}
                  step={0.01}
                  marks={{
                    0: translate('environment.poor', '差'),
                    0.5: translate('environment.moderate', '中等'),
                    1: translate('environment.excellent', '优秀')
                  }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="waterLevel"
                label={translate('environment.waterLevel', '水位')}
                rules={[{ required: true, message: translate('environment.waterLevelRequired', '请设置水位') }]}
              >
                <Slider
                  min={0}
                  max={10}
                  step={0.1}
                  marks={{
                    0: '0m',
                    2.5: '2.5m',
                    5: '5m',
                    7.5: '7.5m',
                    10: '10m'
                  }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    );
  };

  /**
   * 渲染环境数据概览
   */
  const renderEnvironmentDataOverview = () => {
    if (!localEnvironmentData) return null;

    return (
      <div className="environment-data-overview">
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Card size="small" title={translate('environment.environmentType', '环境类型')}>
              <Tag color="blue">{localEnvironmentData.environmentType}</Tag>
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" title={translate('environment.weatherType', '天气类型')}>
              <Tag color="green">{localEnvironmentData.weatherType}</Tag>
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" title={translate('environment.terrainType', '地形类型')}>
              <Tag color="orange">{localEnvironmentData.terrainType}</Tag>
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={6}>
            <Card size="small" title={translate('environment.temperature', '温度')}>
              {localEnvironmentData.temperature.toFixed(1)}°C
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small" title={translate('environment.humidity', '湿度')}>
              {(localEnvironmentData.humidity * 100).toFixed(0)}%
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small" title={translate('environment.windSpeed', '风速')}>
              {localEnvironmentData.windSpeed.toFixed(1)} m/s
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small" title={translate('environment.timeOfDay', '时间')}>
              {Math.floor(localEnvironmentData.timeOfDay)}:{(localEnvironmentData.timeOfDay % 1 * 60).toFixed(0).padStart(2, '0')}
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={8}>
            <Card size="small" title={translate('environment.lightIntensity', '光照强度')}>
              {(localEnvironmentData.lightIntensity * 100).toFixed(0)}%
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" title={translate('environment.visibility', '能见度')}>
              {localEnvironmentData.visibility.toFixed(0)} m
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" title={translate('environment.waterLevel', '水位')}>
              {localEnvironmentData.waterLevel.toFixed(1)} m
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  return (
    <div className="environment-awareness-panel">
      <Card
        title={
          <Space>
            <EnvironmentOutlined />
            <span>{translate('environment.awarenessPanel', '环境感知面板')}</span>
          </Space>
        }
        extra={
          <Space>
            <Tooltip title={translate('environment.refresh', '刷新')}>
              <Button
                type="text"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              />
            </Tooltip>
            {isEditing ? (
              <>
                <Button onClick={handleCancel}>{translate('environment.cancel', '取消')}</Button>
                <Button type="primary" onClick={handleSave}>{translate('environment.save', '保存')}</Button>
              </>
            ) : (
              <Button type="primary" icon={<SettingOutlined />} onClick={handleEdit}>
                {translate('environment.edit', '编辑')}
              </Button>
            )}
          </Space>
        }
      >
        {localEnvironmentData ? (
          <>
            {!isEditing && renderEnvironmentDataOverview()}
            
            {isEditing && (
              <Tabs activeKey={activeTab} onChange={setActiveTab}>
                <TabPane
                  tab={
                    <span>
                      <EnvironmentOutlined />
                      {translate('environment.basic', '基本信息')}
                    </span>
                  }
                  key="basic"
                >
                  {renderBasicTab()}
                </TabPane>
                <TabPane
                  tab={
                    <span>
                      <CloudOutlined />
                      {translate('environment.conditions', '环境条件')}
                    </span>
                  }
                  key="conditions"
                >
                  {renderConditionsTab()}
                </TabPane>
                <TabPane
                  tab={
                    <span>
                      <BulbOutlined />
                      {translate('environment.lighting', '光照')}
                    </span>
                  }
                  key="lighting"
                >
                  {renderLightingTab()}
                </TabPane>
                <TabPane
                  tab={
                    <span>
                      <SoundOutlined />
                      {translate('environment.others', '其他')}
                    </span>
                  }
                  key="others"
                >
                  {renderOthersTab()}
                </TabPane>
              </Tabs>
            )}
          </>
        ) : (
          <div className="no-data">
            <p>{translate('environment.noData', '暂无环境数据')}</p>
            <Button type="primary" onClick={handleRefresh}>
              {translate('environment.refresh', '刷新')}
            </Button>
          </div>
        )}
      </Card>
    </div>
  );
};

export default EnvironmentAwarenessPanel;
