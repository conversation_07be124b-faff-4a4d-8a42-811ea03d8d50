/**
 * DL引擎视觉脚本系统全面节点分析
 * 统计底层引擎、编辑器、服务器端三个部分的所有节点
 */
const fs = require('fs');
const path = require('path');

// 节点统计结果
const nodeStats = {
  engine: { totalNodes: 0, files: [], nodesByCategory: {} },
  editor: { totalNodes: 0, files: [], nodesByCategory: {} },
  server: { totalNodes: 0, files: [], nodesByCategory: {} },
  total: 0,
  allNodes: []
};

/**
 * 分析单个节点文件
 */
function analyzeNodeFile(filePath, component) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fileName = path.basename(filePath, '.ts');
    const relativePath = path.relative(process.cwd(), filePath);
    
    // 查找类定义（节点类）
    const classRegex = /export\s+class\s+(\w+Node)\s+extends\s+/g;
    const nodes = [];
    let match;
    
    while ((match = classRegex.exec(content)) !== null) {
      nodes.push(match[1]);
    }
    
    // 查找其他节点定义模式
    const interfaceRegex = /export\s+interface\s+(\w+Node)\s+/g;
    while ((match = interfaceRegex.exec(content)) !== null) {
      nodes.push(match[1]);
    }
    
    // 查找注册函数中的节点类型
    const registerRegex = /registry\.registerNodeType\(\s*\{[^}]*type:\s*['"`]([^'"`]+)['"`]/g;
    const registeredTypes = [];
    
    while ((match = registerRegex.exec(content)) !== null) {
      registeredTypes.push(match[1]);
    }
    
    // 查找静态TYPE定义
    const typeRegex = /static\s+readonly\s+TYPE\s*=\s*['"`]([^'"`]+)['"`]/g;
    while ((match = typeRegex.exec(content)) !== null) {
      registeredTypes.push(match[1]);
    }
    
    return {
      fileName,
      relativePath,
      component,
      nodeClasses: nodes,
      registeredTypes,
      nodeCount: nodes.length,
      registeredCount: registeredTypes.length
    };
  } catch (error) {
    console.warn(`分析文件失败: ${filePath}`, error.message);
    return null;
  }
}

/**
 * 递归分析目录中的节点文件
 */
function analyzeDirectory(dirPath, component, basePath = '') {
  const results = [];

  if (!fs.existsSync(dirPath)) {
    return results;
  }

  const items = fs.readdirSync(dirPath);

  for (const item of items) {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);

    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      // 递归分析子目录
      const subResults = analyzeDirectory(itemPath, component, path.join(basePath, item));
      results.push(...subResults);
    } else if (item.endsWith('.ts') && !item.includes('test') && !item.includes('spec')) {
      // 分析TypeScript文件
      const analysis = analyzeNodeFile(itemPath, component);
      if (analysis && analysis.nodeCount > 0) {
        analysis.directory = basePath || 'root';
        results.push(analysis);
      }
    }
  }

  return results;
}

/**
 * 获取节点类别
 */
function getNodeCategory(fileName, directory) {
  const categoryMap = {
    'Core': '核心节点',
    'Math': '数学节点',
    'Debug': '调试节点',
    'AI': 'AI节点',
    'Network': '网络节点',
    'UI': 'UI节点',
    'Animation': '动画节点',
    'Physics': '物理节点',
    'Entity': '实体节点',
    'Audio': '音频节点',
    'Input': '输入节点',
    'Transform': '变换节点',
    'Spatial': '空间节点',
    'SmartCity': '智慧城市节点',
    'RAG': 'RAG应用节点',
    'Rendering': '渲染节点',
    'Particle': '粒子节点',
    'Terrain': '地形节点',
    'Mocap': '动作捕捉节点',
    'Learning': '学习记录节点',
    'Blockchain': '区块链节点',
    'Industrial': '工业自动化节点'
  };

  // 从文件名推断类别
  for (const [key, value] of Object.entries(categoryMap)) {
    if (fileName.toLowerCase().includes(key.toLowerCase())) {
      return value;
    }
  }

  // 从目录路径推断类别
  if (directory.includes('smartcity')) return '智慧城市节点';
  if (directory.includes('mocap')) return '动作捕捉节点';
  if (directory.includes('ai')) return 'AI节点';
  if (directory.includes('network')) return '网络节点';
  if (directory.includes('physics')) return '物理节点';
  if (directory.includes('rendering')) return '渲染节点';
  if (directory.includes('animation')) return '动画节点';
  if (directory.includes('audio')) return '音频节点';
  if (directory.includes('ui')) return 'UI节点';
  if (directory.includes('input')) return '输入节点';
  if (directory.includes('spatial')) return '空间节点';
  if (directory.includes('blockchain')) return '区块链节点';
  if (directory.includes('industrial')) return '工业自动化节点';

  return '其他节点';
}

/**
 * 主函数
 */
function main() {
  console.log('=== DL引擎视觉脚本系统全面节点分析 ===\n');

  // 分析底层引擎节点
  console.log('🔧 分析底层引擎节点...');
  const enginePaths = [
    'engine/src/visualscript',
    'engine/src/visual-script'
  ];

  let engineResults = [];
  enginePaths.forEach(enginePath => {
    if (fs.existsSync(enginePath)) {
      console.log(`   分析路径: ${enginePath}`);
      const results = analyzeDirectory(enginePath, 'engine');
      engineResults.push(...results);
      console.log(`   找到 ${results.length} 个节点文件`);
    } else {
      console.log(`   路径不存在: ${enginePath}`);
    }
  });

  // 分析编辑器节点
  console.log('🎨 分析编辑器节点...');
  const editorPaths = [
    'editor/src/components/visualscript',
    'editor/src/components/scripting',
    'editor/src/libs'
  ];

  let editorResults = [];
  editorPaths.forEach(editorPath => {
    if (fs.existsSync(editorPath)) {
      console.log(`   分析路径: ${editorPath}`);
      const results = analyzeDirectory(editorPath, 'editor');
      editorResults.push(...results);
      console.log(`   找到 ${results.length} 个节点文件`);
    } else {
      console.log(`   路径不存在: ${editorPath}`);
    }
  });

  // 分析服务器端节点
  console.log('🖥️ 分析服务器端节点...');
  const serverPaths = [
    'server/visual-script-service',
    'server/ai-service',
    'server/nlp-scene-service'
  ];

  let serverResults = [];
  serverPaths.forEach(serverPath => {
    if (fs.existsSync(serverPath)) {
      console.log(`   分析路径: ${serverPath}`);
      const results = analyzeDirectory(serverPath, 'server');
      serverResults.push(...results);
      console.log(`   找到 ${results.length} 个节点文件`);
    } else {
      console.log(`   路径不存在: ${serverPath}`);
    }
  });

  // 合并和统计结果
  const allResults = [...engineResults, ...editorResults, ...serverResults];
  
  // 统计各组件的节点
  [engineResults, editorResults, serverResults].forEach((results, index) => {
    const component = ['engine', 'editor', 'server'][index];
    
    results.forEach(analysis => {
      const category = getNodeCategory(analysis.fileName, analysis.directory);
      
      nodeStats[component].files.push(analysis);
      nodeStats[component].totalNodes += analysis.nodeCount;
      
      if (!nodeStats[component].nodesByCategory[category]) {
        nodeStats[component].nodesByCategory[category] = 0;
      }
      nodeStats[component].nodesByCategory[category] += analysis.nodeCount;
      
      nodeStats.allNodes.push(...analysis.nodeClasses.map(node => ({
        name: node,
        file: analysis.fileName,
        category: category,
        component: component,
        directory: analysis.directory
      })));
    });
  });

  nodeStats.total = nodeStats.engine.totalNodes + nodeStats.editor.totalNodes + nodeStats.server.totalNodes;

  // 输出统计结果
  console.log('\n=== 统计结果 ===\n');
  console.log(`📊 总节点数量: ${nodeStats.total}`);
  console.log(`🔧 底层引擎节点: ${nodeStats.engine.totalNodes}`);
  console.log(`🎨 编辑器节点: ${nodeStats.editor.totalNodes}`);
  console.log(`🖥️ 服务器端节点: ${nodeStats.server.totalNodes}\n`);

  // 检查是否达到640个节点目标
  const targetNodes = 640;
  const smartCityNodes = 7; // 新增的智慧城市节点
  const previousNodes = 633; // 之前的节点数量
  
  console.log('🎯 目标分析:');
  console.log(`   目标节点数: ${targetNodes}`);
  console.log(`   之前节点数: ${previousNodes}`);
  console.log(`   智慧城市新增: ${smartCityNodes}`);
  console.log(`   当前实际节点数: ${nodeStats.total}`);
  console.log(`   完成度: ${((nodeStats.total / targetNodes) * 100).toFixed(1)}%`);
  console.log(`   缺口: ${targetNodes - nodeStats.total} 个节点\n`);

  // 生成详细报告
  generateComprehensiveReport();
}

/**
 * 生成综合报告
 */
function generateComprehensiveReport() {
  const reportPath = 'docs/DL引擎视觉脚本系统节点全面分析报告.md';
  
  let report = '# DL引擎视觉脚本系统节点全面分析报告\n\n';
  report += `生成时间: ${new Date().toLocaleString('zh-CN')}\n\n`;
  
  // 执行摘要
  report += '## 执行摘要\n\n';
  report += `经过对DL引擎项目的全面分析，视觉脚本系统当前共有 **${nodeStats.total}个节点**，分布在底层引擎、编辑器、服务器端三个部分。\n\n`;
  
  const targetNodes = 640;
  const completionRate = ((nodeStats.total / targetNodes) * 100).toFixed(1);
  const gap = targetNodes - nodeStats.total;
  
  report += `- **目标节点数**: ${targetNodes}个\n`;
  report += `- **当前节点数**: ${nodeStats.total}个\n`;
  report += `- **完成度**: ${completionRate}%\n`;
  report += `- **缺口**: ${gap}个节点\n\n`;
  
  // 各组件统计
  report += '## 各组件节点统计\n\n';
  report += '| 组件 | 节点数量 | 占比 | 主要功能 |\n';
  report += '|------|----------|------|----------|\n';
  report += `| 底层引擎 | ${nodeStats.engine.totalNodes} | ${((nodeStats.engine.totalNodes / nodeStats.total) * 100).toFixed(1)}% | 核心功能、物理、渲染、AI等 |\n`;
  report += `| 编辑器 | ${nodeStats.editor.totalNodes} | ${((nodeStats.editor.totalNodes / nodeStats.total) * 100).toFixed(1)}% | 可视化编辑、UI交互 |\n`;
  report += `| 服务器端 | ${nodeStats.server.totalNodes} | ${((nodeStats.server.totalNodes / nodeStats.total) * 100).toFixed(1)}% | 网络服务、数据处理 |\n\n`;
  
  // 智慧城市节点分析
  const smartCityNodes = nodeStats.allNodes.filter(node => 
    node.category === '智慧城市节点' || 
    node.file.toLowerCase().includes('smartcity') ||
    node.directory.includes('smartcity')
  );
  
  report += '## 智慧城市节点分析\n\n';
  report += `智慧城市相关节点共 **${smartCityNodes.length}个**：\n\n`;
  smartCityNodes.forEach(node => {
    report += `- **${node.name}** (${node.component}/${node.file})\n`;
  });
  report += '\n';
  
  // 保存报告
  fs.writeFileSync(reportPath, report, 'utf8');
  console.log(`📄 综合分析报告已保存到: ${reportPath}`);
}

// 运行主函数
main();
