/**
 * 动画重定向页面
 * 用于展示和测试动画重定向功能
 */
import React, { useState, useEffect } from 'react';
import { Layout, Button, Card, List, Upload, message, Space, Spin, Modal, Tabs } from 'antd';
import {
  UploadOutlined,
  DeleteOutlined,
  EditOutlined,
  ExportOutlined,
  ImportOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { RetargetingEditorPanel, RetargetingResult } from '../components/animation';
import './AnimationRetargetingPage.less';

const { Header, Content } = Layout;

/**
 * 扩展的重定向结果接口，包含时间戳
 */
interface ExtendedRetargetingResult extends RetargetingResult {
  /** 时间戳 */
  timestamp?: number;
}

/**
 * 动画重定向页面组件
 */
const AnimationRetargetingPage: React.FC = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [editorVisible, setEditorVisible] = useState(false);
  const [currentRetargetingId, setCurrentRetargetingId] = useState<string | null>(null);
  const [retargetingResults, setRetargetingResults] = useState<Record<string, ExtendedRetargetingResult>>({});
  const [activeTab, setActiveTab] = useState('recent');

  // 加载保存的重定向结果
  useEffect(() => {
    loadRetargetingResults();
  }, []);

  // 加载重定向结果
  const loadRetargetingResults = () => {
    setLoading(true);
    
    // 从本地存储加载
    try {
      const savedResults = localStorage.getItem('retargetingResults');
      if (savedResults) {
        setRetargetingResults(JSON.parse(savedResults));
      }
    } catch (error) {
      console.error('加载重定向结果失败:', error);
      message.error(t('editor.animation.retargeting.loadResultsFailed'));
    }
    
    setLoading(false);
  };

  // 保存重定向结果
  const saveRetargetingResults = (results: Record<string, ExtendedRetargetingResult>) => {
    try {
      localStorage.setItem('retargetingResults', JSON.stringify(results));
    } catch (error) {
      console.error('保存重定向结果失败:', error);
      message.error(t('editor.animation.retargeting.saveResultsFailed'));
    }
  };

  // 打开编辑器
  const openEditor = (retargetingId?: string) => {
    setCurrentRetargetingId(retargetingId || null);
    setEditorVisible(true);
  };

  // 关闭编辑器
  const closeEditor = () => {
    setEditorVisible(false);
    setCurrentRetargetingId(null);
  };

  // 保存重定向结果
  const handleSaveRetargeting = (result: RetargetingResult) => {
    const id = currentRetargetingId || `retargeting_${Date.now()}`;
    const extendedResult: ExtendedRetargetingResult = {
      ...result,
      timestamp: Date.now()
    };
    const updatedResults = {
      ...retargetingResults,
      [id]: extendedResult
    };

    setRetargetingResults(updatedResults);
    saveRetargetingResults(updatedResults);

    message.success(t('editor.animation.retargeting.saveSuccess'));
    closeEditor();
  };

  // 删除重定向结果
  const handleDeleteRetargeting = (id: string) => {
    Modal.confirm({
      title: t('editor.animation.retargeting.confirmDelete'),
      content: t('editor.animation.retargeting.confirmDeleteContent'),
      okText: t('editor.delete'),
      okType: 'danger',
      cancelText: t('editor.cancel'),
      onOk: () => {
        const updatedResults = { ...retargetingResults };
        delete updatedResults[id];
        
        setRetargetingResults(updatedResults);
        saveRetargetingResults(updatedResults);
        
        message.success(t('editor.animation.retargeting.deleteSuccess'));
      }});
  };

  // 导出重定向结果
  const handleExportRetargeting = (id: string) => {
    const result = retargetingResults[id];
    if (!result) return;
    
    // 创建导出数据
    const exportData = {
      boneMapping: result.boneMapping,
      config: result.config,
      metadata: {
        sourceClipName: result.sourceClip?.name,
        targetClipName: result.retargetedClip?.name,
        timestamp: result.timestamp}};
    
    // 导出为JSON文件
    const data = JSON.stringify(exportData, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `retargeting_${id}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    message.success(t('editor.animation.retargeting.exportSuccess'));
  };

  // 导入重定向结果
  const handleImportRetargeting = (file: File) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      if (!event.target?.result) return;
      
      try {
        const data = JSON.parse(event.target.result as string);
        
        if (!data.boneMapping || !data.config) {
          message.error(t('editor.animation.retargeting.invalidFile'));
          return false;
        }
        
        // 创建新的重定向结果
        const id = `retargeting_${Date.now()}`;
        const newResult: ExtendedRetargetingResult = {
          boneMapping: data.boneMapping,
          config: data.config,
          timestamp: Date.now()
        };

        // 更新状态
        const updatedResults = {
          ...retargetingResults,
          [id]: newResult
        };
        
        setRetargetingResults(updatedResults);
        saveRetargetingResults(updatedResults);
        
        message.success(t('editor.animation.retargeting.importSuccess'));
      } catch (error) {
        console.error('导入重定向结果失败:', error);
        message.error(t('editor.animation.retargeting.importFailed'));
      }
    };
    
    reader.readAsText(file);
    return false; // 阻止默认上传行为
  };

  // 渲染重定向结果列表
  const renderRetargetingList = () => {
    const results = Object.entries(retargetingResults)
      .map(([id, result]) => ({ id, ...result }))
      .sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));
    
    if (results.length === 0) {
      return (
        <div className="empty-state">
          <div className="empty-icon">
            <UploadOutlined style={{ fontSize: 48 }} />
          </div>
          <div className="empty-text">{t('editor.animation.retargeting.noResults')}</div>
          <Button type="primary" icon={<PlusOutlined />} onClick={() => openEditor()}>
            {t('editor.animation.retargeting.createNew')}
          </Button>
        </div>
      );
    }
    
    return (
      <List
        grid={{ gutter: 16, xs: 1, sm: 2, md: 2, lg: 3, xl: 4, xxl: 4 }}
        dataSource={results}
        renderItem={(item) => (
          <List.Item>
            <Card
              title={item.sourceClip?.name || t('editor.animation.retargeting.untitled')}
              extra={
                <Button
                  type="text"
                  icon={<DeleteOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteRetargeting(item.id);
                  }}
                  danger
                />
              }
              actions={[
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={() => openEditor(item.id)}
                >
                  {t('editor.edit')}
                </Button>,
                <Button
                  type="text"
                  icon={<ExportOutlined />}
                  onClick={() => handleExportRetargeting(item.id)}
                >
                  {t('editor.export')}
                </Button>,
              ]}
              hoverable
              onClick={() => openEditor(item.id)}
              className="retargeting-card"
            >
              <div className="retargeting-card-content">
                <div className="retargeting-info">
                  <div className="info-item">
                    <span className="info-label">{t('editor.animation.retargeting.mappings')}:</span>
                    <span className="info-value">{item.boneMapping?.length || 0}</span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">{t('editor.animation.retargeting.date')}:</span>
                    <span className="info-value">
                      {item.timestamp ? new Date(item.timestamp).toLocaleDateString() : '-'}
                    </span>
                  </div>
                </div>
              </div>
            </Card>
          </List.Item>
        )}
      />
    );
  };

  return (
    <Layout className="animation-retargeting-page">
      <Header className="page-header">
        <div className="header-title">{t('editor.animation.retargeting.pageTitle')}</div>
        <div className="header-actions">
          <Space>
            <Upload
              beforeUpload={handleImportRetargeting}
              showUploadList={false}
              accept=".json"
            >
              <Button icon={<ImportOutlined />}>
                {t('editor.animation.retargeting.import')}
              </Button>
            </Upload>
            <Button type="primary" icon={<PlusOutlined />} onClick={() => openEditor()}>
              {t('editor.animation.retargeting.createNew')}
            </Button>
          </Space>
        </div>
      </Header>

      <Content className="page-content">
        <Spin spinning={loading}>
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={[
              {
                key: 'recent',
                label: t('editor.animation.retargeting.recent'),
                children: renderRetargetingList()
              }
            ]}
          />
        </Spin>
      </Content>

      <RetargetingEditorPanel
        visible={editorVisible}
        onClose={closeEditor}
        onSave={handleSaveRetargeting}
        initialData={currentRetargetingId ? retargetingResults[currentRetargetingId] : undefined}
      />
    </Layout>
  );
};

export default AnimationRetargetingPage;
