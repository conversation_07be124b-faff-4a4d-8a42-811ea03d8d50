/**
 * VirtualScrollList.module.css
 * 
 * 虚拟滚动列表组件样式
 */

.virtual-scroll-container {
  position: relative;
  overflow: hidden;
  border: 1px solid var(--border-color, #d9d9d9);
  border-radius: 6px;
  background: var(--background-color, #ffffff);
}

.virtual-scroll-wrapper {
  position: relative;
  overflow: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb-color, #c1c1c1) var(--scrollbar-track-color, #f1f1f1);
}

.virtual-scroll-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.virtual-scroll-wrapper::-webkit-scrollbar-track {
  background: var(--scrollbar-track-color, #f1f1f1);
  border-radius: 4px;
}

.virtual-scroll-wrapper::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-color, #c1c1c1);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.virtual-scroll-wrapper::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-color, #a8a8a8);
}

.virtual-scroll-spacer {
  position: relative;
  width: 100%;
}

.virtual-scroll-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  will-change: transform;
}

.virtual-scroll-item {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  border-bottom: 1px solid var(--item-border-color, #f0f0f0);
  transition: background-color 0.2s ease;
}

.virtual-scroll-item:last-child {
  border-bottom: none;
}

.virtual-scroll-item:hover {
  background-color: var(--item-hover-background, #f5f5f5);
}

.virtual-scroll-item:active {
  background-color: var(--item-active-background, #e6f7ff);
}

.virtual-scroll-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-color-secondary, #8c8c8c);
  font-size: 14px;
}

/* 加载状态 */
.virtual-scroll-container .ant-spin-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* 性能优化 */
.virtual-scroll-content {
  contain: layout style paint;
}

.virtual-scroll-item {
  contain: layout style paint;
}

/* 暗色主题 */
[data-theme="dark"] .virtual-scroll-container {
  --background-color: #141414;
  --border-color: #434343;
  --item-border-color: #303030;
  --item-hover-background: #262626;
  --item-active-background: #111b26;
  --text-color-secondary: #8c8c8c;
  --scrollbar-track-color: #262626;
  --scrollbar-thumb-color: #434343;
  --scrollbar-thumb-hover-color: #595959;
}

/* 紧凑模式 */
.virtual-scroll-container.compact .virtual-scroll-item {
  padding: 4px 8px;
  min-height: 32px;
}

/* 宽松模式 */
.virtual-scroll-container.comfortable .virtual-scroll-item {
  padding: 12px 16px;
  min-height: 48px;
}

/* 选中状态 */
.virtual-scroll-item.selected {
  background-color: var(--primary-color, #1890ff);
  color: white;
}

.virtual-scroll-item.selected:hover {
  background-color: var(--primary-color-hover, #40a9ff);
}

/* 禁用状态 */
.virtual-scroll-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 拖拽状态 */
.virtual-scroll-item.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
  z-index: 1000;
}

.virtual-scroll-item.drag-over {
  border-top: 2px solid var(--primary-color, #1890ff);
}

/* 动画效果 */
.virtual-scroll-item {
  transition: all 0.2s ease;
}

.virtual-scroll-item.entering {
  opacity: 0;
  transform: translateX(-20px);
}

.virtual-scroll-item.entered {
  opacity: 1;
  transform: translateX(0);
}

.virtual-scroll-item.exiting {
  opacity: 0;
  transform: translateX(20px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .virtual-scroll-wrapper::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
  
  .virtual-scroll-item {
    padding: 8px 12px;
    min-height: 40px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .virtual-scroll-container {
    border-width: 2px;
  }
  
  .virtual-scroll-item {
    border-bottom-width: 2px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .virtual-scroll-item {
    transition: none;
  }
  
  .virtual-scroll-content {
    transition: none;
  }
}

/* 焦点状态 */
.virtual-scroll-item:focus {
  outline: 2px solid var(--primary-color, #1890ff);
  outline-offset: -2px;
}

.virtual-scroll-item:focus:not(:focus-visible) {
  outline: none;
}

/* 键盘导航 */
.virtual-scroll-container:focus-within .virtual-scroll-item.keyboard-focused {
  background-color: var(--item-focus-background, #e6f7ff);
  outline: 2px solid var(--primary-color, #1890ff);
  outline-offset: -2px;
}

/* 加载骨架屏 */
.virtual-scroll-skeleton {
  padding: 12px 16px;
  border-bottom: 1px solid var(--item-border-color, #f0f0f0);
}

.virtual-scroll-skeleton-line {
  height: 16px;
  background: linear-gradient(
    90deg,
    var(--skeleton-color, #f2f2f2) 25%,
    var(--skeleton-highlight, #e6e6e6) 50%,
    var(--skeleton-color, #f2f2f2) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 8px;
}

.virtual-scroll-skeleton-line:last-child {
  margin-bottom: 0;
  width: 60%;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 错误状态 */
.virtual-scroll-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--error-color, #ff4d4f);
  text-align: center;
}

.virtual-scroll-error-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.virtual-scroll-error-message {
  font-size: 16px;
  margin-bottom: 8px;
}

.virtual-scroll-error-description {
  font-size: 14px;
  color: var(--text-color-secondary, #8c8c8c);
}

/* 性能监控 */
.virtual-scroll-performance-monitor {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
  z-index: 1000;
  pointer-events: none;
}

/* 调试模式 */
.virtual-scroll-container.debug .virtual-scroll-item {
  border: 1px dashed #ff4d4f;
  position: relative;
}

.virtual-scroll-container.debug .virtual-scroll-item::before {
  content: attr(data-index);
  position: absolute;
  top: 2px;
  right: 2px;
  background: #ff4d4f;
  color: white;
  padding: 2px 4px;
  font-size: 10px;
  border-radius: 2px;
}
