/* 虚拟化对象列表样式 */
.virtualized-object-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

/* 工具栏样式 */
.list-toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 6px 6px 0 0;
}

.toolbar-actions {
  display: flex;
  gap: 4px;
}

/* 列表容器 */
.list-container {
  flex: 1;
  overflow: hidden;
}

/* 对象列表项 */
.object-list-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  border-bottom: 1px solid #f5f5f5;
  transition: all 0.2s ease;
  user-select: none;
}

.object-list-item:hover {
  background-color: #f5f5f5;
}

.object-list-item.selected {
  background-color: #e6f7ff;
  border-color: #91d5ff;
}

.object-list-item.locked {
  opacity: 0.7;
}

.object-list-item.locked .object-name {
  color: #999;
}

/* 项目内容 */
.item-content {
  display: flex;
  align-items: center;
  width: 100%;
  height: 32px;
  padding: 0 8px;
  gap: 6px;
}

/* 展开按钮 */
.expand-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  cursor: pointer;
  border-radius: 2px;
  transition: background-color 0.2s ease;
}

.expand-button:hover {
  background-color: #e6f7ff;
}

.expand-icon {
  font-size: 10px;
  color: #666;
  transition: transform 0.2s ease;
  transform-origin: center;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.expand-placeholder {
  width: 10px;
  height: 10px;
}

/* 对象图标 */
.object-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  color: #666;
  font-size: 12px;
}

/* 对象名称 */
.object-name {
  flex: 1;
  font-size: 13px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
}

/* 操作按钮 */
.object-actions {
  display: flex;
  gap: 2px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.object-list-item:hover .object-actions {
  opacity: 1;
}

.action-button {
  width: 24px !important;
  height: 24px !important;
  min-width: 24px !important;
  padding: 0 !important;
  border: none !important;
  box-shadow: none !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.action-button .anticon {
  font-size: 12px;
}

.action-button.inactive {
  color: #bfbfbf;
}

.action-button.active {
  color: #1890ff;
  background-color: #e6f7ff;
}

.action-button:hover {
  background-color: #f0f0f0 !important;
}

.action-button.active:hover {
  background-color: #bae7ff !important;
}

/* 右键菜单覆盖层 */
.context-menu-overlay {
  position: fixed;
  z-index: 1000;
}

.context-menu-overlay .ant-menu {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #d9d9d9;
  min-width: 120px;
}

/* 空状态 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
  font-size: 14px;
  background: #fafafa;
  border-radius: 0 0 6px 6px;
}

/* 深色主题支持 */
.dark-theme .virtualized-object-list {
  background: #1f1f1f;
  border-color: #434343;
}

.dark-theme .list-toolbar {
  background: #2d2d2d;
  border-bottom-color: #434343;
}

.dark-theme .object-list-item {
  border-bottom-color: #434343;
}

.dark-theme .object-list-item:hover {
  background-color: #2d2d2d;
}

.dark-theme .object-list-item.selected {
  background-color: #1e3a8a;
  border-color: #3b82f6;
}

.dark-theme .object-name {
  color: #e5e5e5;
}

.dark-theme .object-list-item.locked .object-name {
  color: #888;
}

.dark-theme .object-icon {
  color: #999;
}

.dark-theme .expand-icon {
  color: #999;
}

.dark-theme .expand-button:hover {
  background-color: #3b82f6;
}

.dark-theme .action-button:hover {
  background-color: #2d2d2d !important;
}

.dark-theme .action-button.active {
  color: #3b82f6;
  background-color: #1e3a8a;
}

.dark-theme .action-button.active:hover {
  background-color: #1e40af !important;
}

.dark-theme .empty-state {
  background: #2d2d2d;
  color: #888;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .list-toolbar {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .toolbar-actions {
    justify-content: center;
  }
  
  .object-actions {
    opacity: 1; /* 在移动设备上始终显示操作按钮 */
  }
  
  .item-content {
    padding: 0 4px;
  }
  
  .object-name {
    font-size: 12px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .object-list-item {
    border-bottom-color: #000;
  }
  
  .object-list-item.selected {
    background-color: #0066cc;
    color: #fff;
  }
  
  .object-name {
    color: #000;
  }
  
  .dark-theme .object-name {
    color: #fff;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .object-list-item,
  .expand-icon,
  .action-button,
  .expand-button {
    transition: none;
  }
}

/* 滚动条样式 */
.list-container ::-webkit-scrollbar {
  width: 8px;
}

.list-container ::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.list-container ::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.list-container ::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.dark-theme .list-container ::-webkit-scrollbar-track {
  background: #2d2d2d;
}

.dark-theme .list-container ::-webkit-scrollbar-thumb {
  background: #555;
}

.dark-theme .list-container ::-webkit-scrollbar-thumb:hover {
  background: #666;
}

/* 焦点样式 */
.object-list-item:focus-visible {
  outline: 2px solid #1890ff;
  outline-offset: -2px;
}

.expand-button:focus-visible {
  outline: 2px solid #1890ff;
  outline-offset: 1px;
}

.action-button:focus-visible {
  outline: 2px solid #1890ff !important;
  outline-offset: 1px !important;
}

/* 拖拽样式 */
.object-list-item.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.object-list-item.drag-over {
  border-top: 2px solid #1890ff;
}

.object-list-item.drop-target {
  background-color: #e6f7ff;
  border: 2px dashed #1890ff;
}

/* 加载状态 */
.object-list-item.loading {
  opacity: 0.6;
  pointer-events: none;
}

.object-list-item.loading::after {
  content: '';
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translateY(-50%) rotate(0deg); }
  100% { transform: translateY(-50%) rotate(360deg); }
}

/* 性能优化 */
.object-list-item {
  contain: layout style paint;
  will-change: transform;
}

.list-container {
  contain: strict;
}
