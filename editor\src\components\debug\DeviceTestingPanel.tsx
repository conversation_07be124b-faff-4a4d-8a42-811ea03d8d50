/**
 * 设备测试面板
 * 用于测试不同设备和屏幕尺寸下的响应式设计
 */
import React, { useState, useEffect } from 'react';
import { Card, Select, Button, Divider, Space, Typography, Row, Col, Slider, Switch, Tag, Tooltip, Alert } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  MobileOutlined,
  TabletOutlined,
  LaptopOutlined,
  DesktopOutlined,
  RotateLeftOutlined,
  RotateRightOutlined,
  ReloadOutlined,
  EyeOutlined,
  BugOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import MobileDeviceService, { DeviceType, ScreenOrientation } from '../../services/MobileDeviceService';
import ResponsiveDesignService, { ResponsiveBreakpoint } from '../../services/ResponsiveDesignService';
import './DeviceTestingPanel.less';

const { Paragraph } = Typography;
const { Option, OptGroup } = Select;

/**
 * 设备预设
 */
interface DevicePreset {
  name: string;
  width: number;
  height: number;
  deviceType: DeviceType;
  pixelRatio: number;
  userAgent: string;
}

/**
 * 设备测试面板属性
 */
interface DeviceTestingPanelProps {
  /** 是否显示标题 */
  showTitle?: boolean;
}

/**
 * 设备测试面板
 */
const DeviceTestingPanel: React.FC<DeviceTestingPanelProps> = ({
  showTitle = true
}) => {
  const { t } = useTranslation();
  
  // 设备预设列表
  const devicePresets: DevicePreset[] = [
    // 手机
    { name: 'iPhone SE', width: 375, height: 667, deviceType: DeviceType.MOBILE, pixelRatio: 2, userAgent: 'iPhone' },
    { name: 'iPhone XR', width: 414, height: 896, deviceType: DeviceType.MOBILE, pixelRatio: 2, userAgent: 'iPhone' },
    { name: 'iPhone 12 Pro', width: 390, height: 844, deviceType: DeviceType.MOBILE, pixelRatio: 3, userAgent: 'iPhone' },
    { name: 'Google Pixel 5', width: 393, height: 851, deviceType: DeviceType.MOBILE, pixelRatio: 2.75, userAgent: 'Android' },
    { name: 'Samsung Galaxy S21', width: 360, height: 800, deviceType: DeviceType.MOBILE, pixelRatio: 3, userAgent: 'Android' },
    { name: 'Xiaomi Mi 11', width: 393, height: 873, deviceType: DeviceType.MOBILE, pixelRatio: 2.8, userAgent: 'Android' },
    
    // 平板
    { name: 'iPad Mini', width: 768, height: 1024, deviceType: DeviceType.TABLET, pixelRatio: 2, userAgent: 'iPad' },
    { name: 'iPad Air', width: 820, height: 1180, deviceType: DeviceType.TABLET, pixelRatio: 2, userAgent: 'iPad' },
    { name: 'iPad Pro 11"', width: 834, height: 1194, deviceType: DeviceType.TABLET, pixelRatio: 2, userAgent: 'iPad' },
    { name: 'Samsung Galaxy Tab S7', width: 800, height: 1280, deviceType: DeviceType.TABLET, pixelRatio: 2.5, userAgent: 'Android' },
    
    // 桌面
    { name: 'Laptop (1366x768)', width: 1366, height: 768, deviceType: DeviceType.DESKTOP, pixelRatio: 1, userAgent: 'Desktop' },
    { name: 'Laptop (1440x900)', width: 1440, height: 900, deviceType: DeviceType.DESKTOP, pixelRatio: 1, userAgent: 'Desktop' },
    { name: 'Desktop (1920x1080)', width: 1920, height: 1080, deviceType: DeviceType.DESKTOP, pixelRatio: 1, userAgent: 'Desktop' },
    { name: 'Desktop (2560x1440)', width: 2560, height: 1440, deviceType: DeviceType.DESKTOP, pixelRatio: 1, userAgent: 'Desktop' },
  ];
  
  // 状态
  const [selectedPreset, setSelectedPreset] = useState<string>('iPhone 12 Pro');
  const [customWidth, setCustomWidth] = useState<number>(390);
  const [customHeight, setCustomHeight] = useState<number>(844);
  const [orientation, setOrientation] = useState<ScreenOrientation>(ScreenOrientation.PORTRAIT);
  const [deviceType, setDeviceType] = useState<DeviceType>(DeviceType.MOBILE);
  const [pixelRatio, setPixelRatio] = useState<number>(3);
  const [userAgent, setUserAgent] = useState<string>('iPhone');
  const [isTouchEnabled, setIsTouchEnabled] = useState<boolean>(true);
  const [isTestMode, setIsTestMode] = useState<boolean>(false);
  const [testResults, setTestResults] = useState<{[key: string]: boolean}>({});
  
  // 初始化
  useEffect(() => {
    // 获取当前设备信息
    const deviceInfo = MobileDeviceService.getDeviceInfo();
    setDeviceType(deviceInfo.type);
    setOrientation(deviceInfo.orientation);
    setIsTouchEnabled(deviceInfo.isTouch);
    
    // 根据当前设备类型选择默认预设
    if (deviceInfo.type === DeviceType.DESKTOP) {
      setSelectedPreset('Laptop (1366x768)');
      setCustomWidth(1366);
      setCustomHeight(768);
      setPixelRatio(1);
      setUserAgent('Desktop');
    } else if (deviceInfo.type === DeviceType.TABLET) {
      setSelectedPreset('iPad Air');
      setCustomWidth(820);
      setCustomHeight(1180);
      setPixelRatio(2);
      setUserAgent('iPad');
    }
  }, []);
  
  // 处理预设选择
  const handlePresetChange = (value: string) => {
    setSelectedPreset(value);
    
    // 查找选中的预设
    const preset = devicePresets.find(p => p.name === value);
    if (preset) {
      setCustomWidth(preset.width);
      setCustomHeight(preset.height);
      setDeviceType(preset.deviceType);
      setPixelRatio(preset.pixelRatio);
      setUserAgent(preset.userAgent);
      
      // 根据设备类型设置默认方向
      if (preset.deviceType === DeviceType.DESKTOP) {
        setOrientation(ScreenOrientation.LANDSCAPE);
      } else if (preset.width > preset.height) {
        setOrientation(ScreenOrientation.LANDSCAPE);
      } else {
        setOrientation(ScreenOrientation.PORTRAIT);
      }
    }
  };
  
  // 切换方向
  const toggleOrientation = () => {
    const newOrientation = orientation === ScreenOrientation.PORTRAIT
      ? ScreenOrientation.LANDSCAPE
      : ScreenOrientation.PORTRAIT;
    setOrientation(newOrientation);
    
    // 交换宽高
    const temp = customWidth;
    setCustomWidth(customHeight);
    setCustomHeight(temp);
  };
  
  // 应用测试设置
  const applyTestSettings = () => {
    // 注意：这里只是模拟设置，实际的设备模拟需要在服务中实现
    console.log('应用测试设置:', {
      type: deviceType,
      orientation: orientation,
      width: customWidth,
      height: customHeight,
      pixelRatio: pixelRatio,
      isTouch: isTouchEnabled,
      userAgent: userAgent
    });

    // 可以在这里添加实际的设备模拟逻辑
    // 例如：修改 CSS 媒体查询、调整视口大小等
  };

  // 重置测试设置
  const resetTestSettings = () => {
    // 重置到默认设置
    const deviceInfo = MobileDeviceService.getDeviceInfo();
    setDeviceType(deviceInfo.type);
    setOrientation(deviceInfo.orientation);
    setCustomWidth(deviceInfo.screenWidth);
    setCustomHeight(deviceInfo.screenHeight);
    setPixelRatio(deviceInfo.pixelRatio);
    setIsTouchEnabled(deviceInfo.isTouch);
    setUserAgent(deviceInfo.userAgent);
  };
  
  // 运行测试
  const runTests = () => {
    setIsTestMode(true);
    
    // 初始化测试结果
    const results: {[key: string]: boolean} = {};
    
    // 测试响应式断点
    const breakpoint = ResponsiveDesignService.getCurrentBreakpoint();
    const expectedBreakpoint = getExpectedBreakpoint(customWidth);
    results['breakpoint'] = breakpoint === expectedBreakpoint;
    
    // 测试设备类型检测
    const detectedDeviceType = MobileDeviceService.getDeviceInfo().type;
    results['deviceType'] = detectedDeviceType === deviceType;
    
    // 测试屏幕方向检测
    const detectedOrientation = MobileDeviceService.getDeviceInfo().orientation;
    results['orientation'] = detectedOrientation === orientation;
    
    // 测试触控模式
    const touchMode = ResponsiveDesignService.isTouchMode();
    results['touchMode'] = touchMode === isTouchEnabled;
    
    // 更新测试结果
    setTestResults(results);
  };
  
  // 获取预期的响应式断点
  const getExpectedBreakpoint = (width: number): ResponsiveBreakpoint => {
    if (width < 480) return ResponsiveBreakpoint.XS;
    if (width < 576) return ResponsiveBreakpoint.XS;
    if (width < 768) return ResponsiveBreakpoint.SM;
    if (width < 992) return ResponsiveBreakpoint.MD;
    if (width < 1200) return ResponsiveBreakpoint.LG;
    if (width < 1600) return ResponsiveBreakpoint.XL;
    return ResponsiveBreakpoint.XXL;
  };
  
  // 渲染设备图标
  const renderDeviceIcon = (type: DeviceType) => {
    switch (type) {
      case DeviceType.MOBILE:
        return <MobileOutlined />;
      case DeviceType.TABLET:
        return <TabletOutlined />;
      case DeviceType.DESKTOP:
        return <LaptopOutlined />;
      default:
        return <DesktopOutlined />;
    }
  };
  
  // 渲染测试结果
  const renderTestResults = () => {
    if (!isTestMode) return null;
    
    return (
      <div className="test-results">
        <Divider>{t('deviceTesting.testResults')}</Divider>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Card size="small" title={t('deviceTesting.breakpointTest')}>
              <Space direction="vertical">
                <div>
                  {t('deviceTesting.expected')}: {getExpectedBreakpoint(customWidth)}
                </div>
                <div>
                  {t('deviceTesting.actual')}: {ResponsiveDesignService.getCurrentBreakpoint()}
                </div>
                <div>
                  {testResults['breakpoint'] ? (
                    <Tag color="success" icon={<CheckCircleOutlined />}>{t('deviceTesting.pass')}</Tag>
                  ) : (
                    <Tag color="error" icon={<CloseCircleOutlined />}>{t('deviceTesting.fail')}</Tag>
                  )}
                </div>
              </Space>
            </Card>
          </Col>
          <Col span={12}>
            <Card size="small" title={t('deviceTesting.deviceTypeTest')}>
              <Space direction="vertical">
                <div>
                  {t('deviceTesting.expected')}: {deviceType}
                </div>
                <div>
                  {t('deviceTesting.actual')}: {MobileDeviceService.getDeviceInfo().type}
                </div>
                <div>
                  {testResults['deviceType'] ? (
                    <Tag color="success" icon={<CheckCircleOutlined />}>{t('deviceTesting.pass')}</Tag>
                  ) : (
                    <Tag color="error" icon={<CloseCircleOutlined />}>{t('deviceTesting.fail')}</Tag>
                  )}
                </div>
              </Space>
            </Card>
          </Col>
          <Col span={12}>
            <Card size="small" title={t('deviceTesting.orientationTest')}>
              <Space direction="vertical">
                <div>
                  {t('deviceTesting.expected')}: {orientation}
                </div>
                <div>
                  {t('deviceTesting.actual')}: {MobileDeviceService.getDeviceInfo().orientation}
                </div>
                <div>
                  {testResults['orientation'] ? (
                    <Tag color="success" icon={<CheckCircleOutlined />}>{t('deviceTesting.pass')}</Tag>
                  ) : (
                    <Tag color="error" icon={<CloseCircleOutlined />}>{t('deviceTesting.fail')}</Tag>
                  )}
                </div>
              </Space>
            </Card>
          </Col>
          <Col span={12}>
            <Card size="small" title={t('deviceTesting.touchModeTest')}>
              <Space direction="vertical">
                <div>
                  {t('deviceTesting.expected')}: {isTouchEnabled ? t('enabled') : t('disabled')}
                </div>
                <div>
                  {t('deviceTesting.actual')}: {ResponsiveDesignService.isTouchMode() ? t('enabled') : t('disabled')}
                </div>
                <div>
                  {testResults['touchMode'] ? (
                    <Tag color="success" icon={<CheckCircleOutlined />}>{t('deviceTesting.pass')}</Tag>
                  ) : (
                    <Tag color="error" icon={<CloseCircleOutlined />}>{t('deviceTesting.fail')}</Tag>
                  )}
                </div>
              </Space>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };
  
  return (
    <div className="device-testing-panel">
      <Card
        title={showTitle ? (
          <Space>
            <MobileOutlined />
            {t('deviceTesting.title')}
          </Space>
        ) : undefined}
        extra={
          <Space>
            <Tooltip title={t('deviceTesting.resetSettings')}>
              <Button
                icon={<ReloadOutlined />}
                onClick={resetTestSettings}
                size="small"
              />
            </Tooltip>
            <Tooltip title={t('deviceTesting.runTests')}>
              <Button
                icon={<BugOutlined />}
                onClick={runTests}
                size="small"
                type={isTestMode ? 'primary' : 'default'}
              />
            </Tooltip>
          </Space>
        }
      >
        <Alert
          message={t('deviceTesting.simulationWarning')}
          description={t('deviceTesting.simulationWarningDescription')}
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Select
              value={selectedPreset}
              onChange={handlePresetChange}
              style={{ width: '100%' }}
            >
              <OptGroup label={t('deviceTesting.mobileDevices')}>
                {devicePresets.filter(p => p.deviceType === DeviceType.MOBILE).map(preset => (
                  <Option key={preset.name} value={preset.name}>
                    <Space>
                      <MobileOutlined />
                      {preset.name} ({preset.width}x{preset.height})
                    </Space>
                  </Option>
                ))}
              </OptGroup>
              <OptGroup label={t('deviceTesting.tabletDevices')}>
                {devicePresets.filter(p => p.deviceType === DeviceType.TABLET).map(preset => (
                  <Option key={preset.name} value={preset.name}>
                    <Space>
                      <TabletOutlined />
                      {preset.name} ({preset.width}x{preset.height})
                    </Space>
                  </Option>
                ))}
              </OptGroup>
              <OptGroup label={t('deviceTesting.desktopDevices')}>
                {devicePresets.filter(p => p.deviceType === DeviceType.DESKTOP).map(preset => (
                  <Option key={preset.name} value={preset.name}>
                    <Space>
                      <LaptopOutlined />
                      {preset.name} ({preset.width}x{preset.height})
                    </Space>
                  </Option>
                ))}
              </OptGroup>
            </Select>
          </Col>
          
          <Col span={12}>
            <div className="setting-label">{t('deviceTesting.screenWidth')}</div>
            <Slider
              min={320}
              max={3840}
              value={customWidth}
              onChange={setCustomWidth}
              marks={{
                320: '320px',
                768: '768px',
                1366: '1366px',
                1920: '1920px'
              }}
            />
          </Col>
          
          <Col span={12}>
            <div className="setting-label">{t('deviceTesting.screenHeight')}</div>
            <Slider
              min={240}
              max={2160}
              value={customHeight}
              onChange={setCustomHeight}
              marks={{
                240: '240px',
                480: '480px',
                900: '900px',
                1080: '1080px'
              }}
            />
          </Col>
          
          <Col span={12}>
            <div className="setting-label">{t('deviceTesting.deviceType')}</div>
            <Select
              value={deviceType}
              onChange={setDeviceType}
              style={{ width: '100%' }}
            >
              <Option value={DeviceType.MOBILE}>
                <Space>
                  <MobileOutlined />
                  {t('deviceTesting.mobile')}
                </Space>
              </Option>
              <Option value={DeviceType.TABLET}>
                <Space>
                  <TabletOutlined />
                  {t('deviceTesting.tablet')}
                </Space>
              </Option>
              <Option value={DeviceType.DESKTOP}>
                <Space>
                  <LaptopOutlined />
                  {t('deviceTesting.desktop')}
                </Space>
              </Option>
            </Select>
          </Col>
          
          <Col span={12}>
            <div className="setting-label">{t('deviceTesting.orientation')}</div>
            <Space>
              <Select
                value={orientation}
                onChange={setOrientation}
                style={{ width: 150 }}
              >
                <Option value={ScreenOrientation.PORTRAIT}>
                  {t('deviceTesting.portrait')}
                </Option>
                <Option value={ScreenOrientation.LANDSCAPE}>
                  {t('deviceTesting.landscape')}
                </Option>
              </Select>
              <Button
                icon={orientation === ScreenOrientation.PORTRAIT ? <RotateRightOutlined /> : <RotateLeftOutlined />}
                onClick={toggleOrientation}
              />
            </Space>
          </Col>
          
          <Col span={12}>
            <div className="setting-label">{t('deviceTesting.pixelRatio')}</div>
            <Slider
              min={1}
              max={4}
              step={0.5}
              value={pixelRatio}
              onChange={setPixelRatio}
              marks={{
                1: '1x',
                2: '2x',
                3: '3x',
                4: '4x'
              }}
            />
          </Col>
          
          <Col span={12}>
            <div className="setting-label">{t('deviceTesting.touchEnabled')}</div>
            <Switch
              checked={isTouchEnabled}
              onChange={setIsTouchEnabled}
            />
          </Col>
          
          <Col span={24}>
            <Space style={{ width: '100%', justifyContent: 'center' }}>
              <Button
                type="primary"
                icon={<EyeOutlined />}
                onClick={applyTestSettings}
              >
                {t('deviceTesting.applySettings')}
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={resetTestSettings}
              >
                {t('deviceTesting.resetSettings')}
              </Button>
            </Space>
          </Col>
        </Row>
        
        {renderTestResults()}
        
        <Divider />
        
        <div className="device-preview">
          <div className="device-frame" style={{
            width: Math.min(customWidth / 4, 200),
            height: Math.min(customHeight / 4, 300),
            transform: orientation === ScreenOrientation.LANDSCAPE ? 'rotate(90deg)' : 'none'
          }}>
            <div className="device-screen">
              {renderDeviceIcon(deviceType)}
              <div className="device-info">
                <div>{selectedPreset}</div>
                <div>{customWidth}x{customHeight}</div>
                <div>{pixelRatio}x</div>
              </div>
            </div>
          </div>
        </div>
        
        <Paragraph className="help-text">
          <InfoCircleOutlined /> {t('deviceTesting.helpText')}
        </Paragraph>
      </Card>
    </div>
  );
};

export default DeviceTestingPanel;
