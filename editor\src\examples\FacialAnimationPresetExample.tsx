/**
 * 面部动画预设管理器使用示例
 */
import React, { useEffect, useState } from 'react';
import { Card, Space, Button, message } from 'antd';
import { FacialAnimationPresetManager } from '../components/FacialAnimationEditor/FacialAnimationPresetManager';
import { facialAnimationPresetService } from '../services/FacialAnimationPresetService';

/**
 * 面部动画预设示例组件
 */
export const FacialAnimationPresetExample: React.FC = () => {
  const [isServiceReady, setIsServiceReady] = useState(false);

  // 初始化服务
  useEffect(() => {
    const initializeService = async () => {
      try {
        await facialAnimationPresetService.initialize();
        setIsServiceReady(true);
        message.success('面部动画预设服务初始化成功');
      } catch (error) {
        console.error('初始化服务失败:', error);
        message.error('面部动画预设服务初始化失败');
      }
    };

    initializeService();
  }, []);

  // 处理预设应用
  const handlePresetApply = (preset: any) => {
    console.log('应用预设:', preset);
    message.info(`应用预设: ${preset.name}`);
  };

  // 处理预设导入
  const handlePresetImport = (presets: any[]) => {
    console.log('导入预设:', presets);
    message.info(`导入了 ${presets.length} 个预设`);
  };

  // 处理预设导出
  const handlePresetExport = (presets: any[]) => {
    console.log('导出预设:', presets);
    message.info(`导出了 ${presets.length} 个预设`);
  };

  // 测试服务功能
  const testServiceFunctions = async () => {
    try {
      // 获取所有预设
      const presets = await facialAnimationPresetService.getAllPresets();
      console.log('所有预设:', presets);

      // 创建测试预设
      const testPreset = {
        id: `test_${Date.now()}`,
        name: '测试预设',
        type: 'standard' as any,
        description: '这是一个测试预设',
        tags: ['测试', '示例'],
        culture: 'global',
        expression: 'happy' as any,
        weight: 0.8,
        author: '测试用户',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // 添加预设
      const addResult = await facialAnimationPresetService.addPreset(testPreset);
      if (addResult) {
        message.success('测试预设创建成功');
      }

      // 应用预设
      const applyResult = await facialAnimationPresetService.applyPreset('test-entity', testPreset.id);
      if (applyResult) {
        message.success('测试预设应用成功');
      }

    } catch (error) {
      console.error('测试服务功能失败:', error);
      message.error('测试服务功能失败');
    }
  };

  if (!isServiceReady) {
    return (
      <Card title="面部动画预设示例" style={{ margin: 20 }}>
        <div style={{ textAlign: 'center', padding: 40 }}>
          正在初始化面部动画预设服务...
        </div>
      </Card>
    );
  }

  return (
    <div style={{ padding: 20 }}>
      <Card title="面部动画预设管理器示例" style={{ marginBottom: 20 }}>
        <Space style={{ marginBottom: 16 }}>
          <Button type="primary" onClick={testServiceFunctions}>
            测试服务功能
          </Button>
        </Space>
        
        <div style={{ border: '1px solid #d9d9d9', borderRadius: 6, padding: 16 }}>
          <FacialAnimationPresetManager
            entityId="example-entity"
            editable={true}
            onPresetApply={handlePresetApply}
            onPresetImport={handlePresetImport}
            onPresetExport={handlePresetExport}
          />
        </div>
      </Card>

      <Card title="使用说明">
        <div style={{ lineHeight: '1.6' }}>
          <h4>功能特性：</h4>
          <ul>
            <li>✅ 预设管理：创建、编辑、删除面部动画预设</li>
            <li>✅ 预设分类：支持标准、文化特定、情感组合、动画序列等类型</li>
            <li>✅ 预设预览：实时预览面部动画效果</li>
            <li>✅ 导入导出：支持JSON格式的预设文件导入导出</li>
            <li>✅ 搜索筛选：按名称、标签、类型等条件筛选预设</li>
            <li>✅ 引擎集成：与底层引擎系统无缝集成</li>
            <li>✅ 国际化：支持中英文界面</li>
          </ul>

          <h4>使用方法：</h4>
          <ol>
            <li>点击"创建预设"按钮创建新的面部动画预设</li>
            <li>使用搜索框和筛选器查找特定预设</li>
            <li>点击预设卡片上的按钮进行预览、编辑或应用操作</li>
            <li>使用导入/导出功能管理预设文件</li>
            <li>在预览模态框中可以实时查看动画效果</li>
          </ol>

          <h4>技术实现：</h4>
          <ul>
            <li>🔧 React + TypeScript + Ant Design UI框架</li>
            <li>🔧 Three.js 3D渲染引擎用于预览</li>
            <li>🔧 服务层架构，支持引擎集成</li>
            <li>🔧 完整的错误处理和用户反馈</li>
            <li>🔧 响应式设计，支持不同屏幕尺寸</li>
          </ul>
        </div>
      </Card>
    </div>
  );
};

export default FacialAnimationPresetExample;
