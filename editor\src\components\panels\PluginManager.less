/**
 * 插件管理器样式
 */

.plugin-manager {
  .plugin-manager-content {
    .plugin-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;
    }

    .ant-tabs {
      .ant-tabs-content-holder {
        max-height: 500px;
        overflow-y: auto;
      }
    }

    .ant-list-item {
      .ant-card {
        .ant-card-head {
          .ant-card-head-title {
            font-size: 14px;
            
            .ant-space {
              align-items: center;
            }
          }

          .ant-card-extra {
            .ant-space {
              align-items: center;
            }
          }
        }

        .ant-card-body {
          padding: 12px 16px;

          .plugin-card-content {
            .plugin-description {
              margin-bottom: 8px;
              color: #666666;
              font-size: 13px;
              line-height: 1.4;
            }

            .plugin-meta {
              .ant-tag {
                margin-bottom: 4px;
                font-size: 11px;
              }
            }
          }
        }

        .ant-card-actions {
          border-top: 1px solid #f0f0f0;
          
          li {
            margin: 0;
            
            .ant-btn-link {
              color: #1890ff;
              font-size: 12px;
              
              &:hover {
                color: #40a9ff;
              }
            }
          }
        }
      }
    }
  }

  .plugin-details {
    .ant-descriptions {
      .ant-descriptions-item-label {
        font-weight: 500;
        color: #333333;
      }

      .ant-descriptions-item-content {
        .ant-space {
          .ant-tag {
            margin-bottom: 4px;
          }
        }
      }
    }
  }
}

// 深色主题
.dark-theme {
  .plugin-manager {
    .plugin-manager-content {
      .plugin-toolbar {
        border-bottom-color: #404040;
      }

      .ant-list-item {
        .ant-card {
          background: #2d2d2d;
          border-color: #404040;

          .ant-card-head {
            background: #2d2d2d;
            border-bottom-color: #404040;

            .ant-card-head-title {
              color: #ffffff;
            }
          }

          .ant-card-body {
            .plugin-card-content {
              .plugin-description {
                color: #cccccc;
              }
            }
          }

          .ant-card-actions {
            background: #2d2d2d;
            border-top-color: #404040;
          }
        }
      }
    }

    .plugin-details {
      .ant-descriptions {
        background: #2d2d2d;
        border-color: #404040;

        .ant-descriptions-item-label {
          background: #2d2d2d;
          color: #ffffff;
        }

        .ant-descriptions-item-content {
          background: #2d2d2d;
          color: #cccccc;
        }
      }
    }
  }
}

// 紧凑模式
.compact-theme {
  .plugin-manager {
    .plugin-manager-content {
      .plugin-toolbar {
        margin-bottom: 12px;
        padding-bottom: 8px;
      }

      .ant-list-item {
        .ant-card {
          .ant-card-head {
            min-height: 36px;
            padding: 0 12px;
          }

          .ant-card-body {
            padding: 8px 12px;

            .plugin-card-content {
              .plugin-description {
                margin-bottom: 6px;
                font-size: 12px;
              }

              .plugin-meta {
                .ant-tag {
                  font-size: 10px;
                  padding: 0 4px;
                  margin-bottom: 2px;
                }
              }
            }
          }
        }
      }
    }

    .plugin-details {
      .ant-descriptions {
        .ant-descriptions-item {
          padding: 8px 12px;
        }
      }
    }
  }
}

// 动画效果
.plugin-manager {
  .ant-card {
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }
  }

  .ant-switch {
    transition: all 0.2s ease;
  }

  .ant-btn {
    transition: all 0.2s ease;
  }

  .ant-tag {
    transition: all 0.2s ease;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .plugin-manager {
    .ant-modal {
      width: 95% !important;
      max-width: none;
    }

    .plugin-manager-content {
      .plugin-toolbar {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
      }

      .ant-list-item {
        .ant-card {
          .ant-card-head {
            .ant-card-extra {
              .ant-space {
                flex-direction: column;
                gap: 4px;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .plugin-manager {
    .plugin-manager-content {
      .plugin-toolbar {
        .ant-space {
          flex-direction: column;
          align-items: stretch;
          width: 100%;
        }
      }
    }
  }
}

// 加载状态
.plugin-manager {
  .loading-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #999999;
  }
}

// 空状态
.plugin-manager {
  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #999999;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .empty-text {
      font-size: 14px;
      margin-bottom: 16px;
    }
  }
}

// 插件状态指示器
.plugin-manager {
  .plugin-status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;

    &.status-active {
      color: #52c41a;
    }

    &.status-inactive {
      color: #faad14;
    }

    &.status-error {
      color: #ff4d4f;
    }
  }
}
