/**
 * 企业级权限管理服务
 * 提供团队管理、角色权限、项目访问控制、审计日志等功能
 */
import { EventEmitter } from '../utils/EventEmitter';

// 用户角色枚举
export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  MANAGER = 'manager',
  EDITOR = 'editor',
  VIEWER = 'viewer',
  GUEST = 'guest'
}

// 权限类型枚举
export enum PermissionType {
  // 项目权限
  PROJECT_CREATE = 'project_create',
  PROJECT_READ = 'project_read',
  PROJECT_UPDATE = 'project_update',
  PROJECT_DELETE = 'project_delete',
  PROJECT_SHARE = 'project_share',
  PROJECT_EXPORT = 'project_export',
  
  // 团队权限
  TEAM_CREATE = 'team_create',
  TEAM_MANAGE = 'team_manage',
  TEAM_INVITE = 'team_invite',
  TEAM_REMOVE = 'team_remove',
  
  // 用户权限
  USER_MANAGE = 'user_manage',
  USER_INVITE = 'user_invite',
  USER_REMOVE = 'user_remove',
  USER_ROLE_ASSIGN = 'user_role_assign',
  
  // 系统权限
  SYSTEM_ADMIN = 'system_admin',
  SYSTEM_CONFIG = 'system_config',
  SYSTEM_AUDIT = 'system_audit',
  SYSTEM_BACKUP = 'system_backup',
  
  // 资源权限
  RESOURCE_UPLOAD = 'resource_upload',
  RESOURCE_DELETE = 'resource_delete',
  RESOURCE_SHARE = 'resource_share',
  
  // 协作权限
  COLLABORATION_EDIT = 'collaboration_edit',
  COLLABORATION_COMMENT = 'collaboration_comment',
  COLLABORATION_REVIEW = 'collaboration_review'
}

// 访问级别枚举
export enum AccessLevel {
  NONE = 'none',
  READ = 'read',
  WRITE = 'write',
  ADMIN = 'admin',
  OWNER = 'owner'
}

// 审计操作类型枚举
export enum AuditActionType {
  USER_LOGIN = 'user_login',
  USER_LOGOUT = 'user_logout',
  USER_CREATE = 'user_create',
  USER_UPDATE = 'user_update',
  USER_DELETE = 'user_delete',
  
  PROJECT_CREATE = 'project_create',
  PROJECT_UPDATE = 'project_update',
  PROJECT_DELETE = 'project_delete',
  PROJECT_SHARE = 'project_share',
  
  TEAM_CREATE = 'team_create',
  TEAM_UPDATE = 'team_update',
  TEAM_DELETE = 'team_delete',
  TEAM_MEMBER_ADD = 'team_member_add',
  TEAM_MEMBER_REMOVE = 'team_member_remove',
  
  PERMISSION_GRANT = 'permission_grant',
  PERMISSION_REVOKE = 'permission_revoke',
  
  SYSTEM_CONFIG_CHANGE = 'system_config_change',
  SECURITY_VIOLATION = 'security_violation'
}

// 用户接口
export interface User {
  id: string;
  username: string;
  email: string;
  displayName: string;
  avatar?: string;
  
  // 用户状态
  isActive: boolean;
  isVerified: boolean;
  lastLoginAt?: number;
  createdAt: number;
  updatedAt: number;
  
  // 角色和权限
  role: UserRole;
  permissions: PermissionType[];
  customPermissions: Record<string, boolean>;
  
  // 团队关系
  teams: string[];
  primaryTeam?: string;
  
  // 用户偏好
  preferences: {
    language: string;
    timezone: string;
    theme: string;
    notifications: {
      email: boolean;
      push: boolean;
      inApp: boolean;
    };
  };
  
  // 安全信息
  security: {
    twoFactorEnabled: boolean;
    lastPasswordChange: number;
    failedLoginAttempts: number;
    lockedUntil?: number;
  };
}

// 团队接口
export interface Team {
  id: string;
  name: string;
  description: string;
  avatar?: string;
  
  // 团队状态
  isActive: boolean;
  createdAt: number;
  updatedAt: number;
  
  // 团队成员
  members: TeamMember[];
  maxMembers: number;
  
  // 团队权限
  permissions: TeamPermission[];
  
  // 团队设置
  settings: {
    isPublic: boolean;
    allowInvites: boolean;
    requireApproval: boolean;
    defaultRole: UserRole;
  };
  
  // 团队统计
  stats: {
    memberCount: number;
    projectCount: number;
    storageUsed: number;
    storageLimit: number;
  };
}

// 团队成员接口
export interface TeamMember {
  userId: string;
  teamId: string;
  role: UserRole;
  permissions: PermissionType[];
  
  // 成员状态
  isActive: boolean;
  joinedAt: number;
  invitedBy: string;
  
  // 成员统计
  stats: {
    projectsCreated: number;
    lastActivity: number;
    contributionScore: number;
  };
}

// 团队权限接口
export interface TeamPermission {
  id: string;
  teamId: string;
  resourceType: 'project' | 'team' | 'user' | 'system';
  resourceId: string;
  accessLevel: AccessLevel;
  permissions: PermissionType[];
  
  // 权限元数据
  grantedBy: string;
  grantedAt: number;
  expiresAt?: number;
  
  // 权限条件
  conditions?: {
    ipWhitelist?: string[];
    timeRestrictions?: {
      startTime: string;
      endTime: string;
      daysOfWeek: number[];
    };
  };
}

// 项目权限接口
export interface ProjectPermission {
  id: string;
  projectId: string;
  userId?: string;
  teamId?: string;
  accessLevel: AccessLevel;
  permissions: PermissionType[];
  
  // 权限元数据
  grantedBy: string;
  grantedAt: number;
  expiresAt?: number;
  
  // 继承信息
  inheritedFrom?: {
    type: 'team' | 'role';
    id: string;
  };
}

// 审计日志接口
export interface AuditLog {
  id: string;
  actionType: AuditActionType;
  userId: string;
  targetType: 'user' | 'team' | 'project' | 'system';
  targetId: string;
  
  // 操作详情
  details: {
    action: string;
    oldValue?: any;
    newValue?: any;
    metadata?: Record<string, any>;
  };
  
  // 上下文信息
  context: {
    ipAddress: string;
    userAgent: string;
    sessionId: string;
    requestId: string;
  };
  
  // 时间信息
  timestamp: number;
  
  // 风险评估
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  
  // 结果
  result: 'success' | 'failure' | 'partial';
  errorMessage?: string;
}

// 权限策略接口
export interface PermissionPolicy {
  id: string;
  name: string;
  description: string;
  
  // 策略规则
  rules: PolicyRule[];
  
  // 策略状态
  isActive: boolean;
  priority: number;
  
  // 策略元数据
  createdBy: string;
  createdAt: number;
  updatedAt: number;
  
  // 应用范围
  scope: {
    teams?: string[];
    users?: string[];
    projects?: string[];
    global?: boolean;
  };
}

// 策略规则接口
export interface PolicyRule {
  id: string;
  condition: {
    type: 'role' | 'team' | 'user' | 'time' | 'ip' | 'custom';
    operator: 'equals' | 'contains' | 'in' | 'not_in' | 'greater' | 'less';
    value: any;
  };
  
  action: 'allow' | 'deny';
  permissions: PermissionType[];
  
  // 规则优先级
  priority: number;
  
  // 规则条件
  conditions?: {
    timeRange?: {
      start: string;
      end: string;
    };
    ipWhitelist?: string[];
    customConditions?: Record<string, any>;
  };
}

/**
 * 企业级权限管理服务类
 */
export class EnterprisePermissionService extends EventEmitter {
  private static instance: EnterprisePermissionService;
  private users: Map<string, User> = new Map();
  private teams: Map<string, Team> = new Map();
  // private projectPermissions: Map<string, ProjectPermission[]> = new Map(); // 暂时未使用
  private auditLogs: AuditLog[] = [];
  private policies: Map<string, PermissionPolicy> = new Map();
  private currentUser: User | null = null;

  private constructor() {
    super();
    this.initializeDefaultRoles();
    this.initializeDefaultPolicies();
  }

  public static getInstance(): EnterprisePermissionService {
    if (!EnterprisePermissionService.instance) {
      EnterprisePermissionService.instance = new EnterprisePermissionService();
    }
    return EnterprisePermissionService.instance;
  }

  /**
   * 初始化默认角色
   */
  private initializeDefaultRoles(): void {
    // 这里可以定义默认的角色权限映射
    const rolePermissions: Record<UserRole, PermissionType[]> = {
      [UserRole.SUPER_ADMIN]: Object.values(PermissionType),
      [UserRole.ADMIN]: [
        PermissionType.PROJECT_CREATE,
        PermissionType.PROJECT_READ,
        PermissionType.PROJECT_UPDATE,
        PermissionType.PROJECT_DELETE,
        PermissionType.PROJECT_SHARE,
        PermissionType.TEAM_CREATE,
        PermissionType.TEAM_MANAGE,
        PermissionType.USER_MANAGE,
        PermissionType.USER_INVITE,
        PermissionType.SYSTEM_AUDIT
      ],
      [UserRole.MANAGER]: [
        PermissionType.PROJECT_CREATE,
        PermissionType.PROJECT_READ,
        PermissionType.PROJECT_UPDATE,
        PermissionType.PROJECT_SHARE,
        PermissionType.TEAM_INVITE,
        PermissionType.USER_INVITE,
        PermissionType.COLLABORATION_EDIT,
        PermissionType.COLLABORATION_REVIEW
      ],
      [UserRole.EDITOR]: [
        PermissionType.PROJECT_READ,
        PermissionType.PROJECT_UPDATE,
        PermissionType.COLLABORATION_EDIT,
        PermissionType.COLLABORATION_COMMENT,
        PermissionType.RESOURCE_UPLOAD
      ],
      [UserRole.VIEWER]: [
        PermissionType.PROJECT_READ,
        PermissionType.COLLABORATION_COMMENT
      ],
      [UserRole.GUEST]: [
        PermissionType.PROJECT_READ
      ]
    };

    // 存储角色权限映射
    this.emit('defaultRolesInitialized', rolePermissions);
  }

  /**
   * 初始化默认策略
   */
  private initializeDefaultPolicies(): void {
    // 创建默认的安全策略
    const defaultPolicy: PermissionPolicy = {
      id: 'default_security_policy',
      name: 'Default Security Policy',
      description: 'Default security policy for all users',
      rules: [
        {
          id: 'rule_1',
          condition: {
            type: 'role',
            operator: 'equals',
            value: UserRole.GUEST
          },
          action: 'deny',
          permissions: [
            PermissionType.PROJECT_CREATE,
            PermissionType.PROJECT_UPDATE,
            PermissionType.PROJECT_DELETE
          ],
          priority: 100
        }
      ],
      isActive: true,
      priority: 1,
      createdBy: 'system',
      createdAt: Date.now(),
      updatedAt: Date.now(),
      scope: { global: true }
    };

    this.policies.set(defaultPolicy.id, defaultPolicy);
  }

  /**
   * 创建用户
   */
  public async createUser(userData: {
    username: string;
    email: string;
    displayName: string;
    role: UserRole;
    teamId?: string;
  }): Promise<User> {
    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const user: User = {
      id: userId,
      username: userData.username,
      email: userData.email,
      displayName: userData.displayName,
      isActive: true,
      isVerified: false,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      role: userData.role,
      permissions: this.getRolePermissions(userData.role),
      customPermissions: {},
      teams: userData.teamId ? [userData.teamId] : [],
      primaryTeam: userData.teamId,
      preferences: {
        language: 'en',
        timezone: 'UTC',
        theme: 'light',
        notifications: {
          email: true,
          push: true,
          inApp: true
        }
      },
      security: {
        twoFactorEnabled: false,
        lastPasswordChange: Date.now(),
        failedLoginAttempts: 0
      }
    };

    this.users.set(userId, user);

    // 记录审计日志
    await this.logAuditAction({
      actionType: AuditActionType.USER_CREATE,
      userId: this.currentUser?.id || 'system',
      targetType: 'user',
      targetId: userId,
      details: {
        action: 'User created',
        newValue: { username: userData.username, email: userData.email, role: userData.role }
      }
    });

    this.emit('userCreated', user);
    return user;
  }

  /**
   * 更新用户
   */
  public async updateUser(userId: string, updates: Partial<User>): Promise<User> {
    const user = this.users.get(userId);
    if (!user) {
      throw new Error('User not found');
    }

    const oldValue = { ...user };
    Object.assign(user, updates, { updatedAt: Date.now() });

    // 记录审计日志
    await this.logAuditAction({
      actionType: AuditActionType.USER_UPDATE,
      userId: this.currentUser?.id || 'system',
      targetType: 'user',
      targetId: userId,
      details: {
        action: 'User updated',
        oldValue: { role: oldValue.role, permissions: oldValue.permissions },
        newValue: { role: user.role, permissions: user.permissions }
      }
    });

    this.emit('userUpdated', user);
    return user;
  }

  /**
   * 删除用户
   */
  public async deleteUser(userId: string): Promise<void> {
    const user = this.users.get(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // 从所有团队中移除用户
    for (const teamId of user.teams) {
      await this.removeTeamMember(teamId, userId);
    }

    this.users.delete(userId);

    // 记录审计日志
    await this.logAuditAction({
      actionType: AuditActionType.USER_DELETE,
      userId: this.currentUser?.id || 'system',
      targetType: 'user',
      targetId: userId,
      details: {
        action: 'User deleted',
        oldValue: { username: user.username, email: user.email }
      }
    });

    this.emit('userDeleted', user);
  }

  /**
   * 创建团队
   */
  public async createTeam(teamData: {
    name: string;
    description: string;
    maxMembers?: number;
  }): Promise<Team> {
    const teamId = `team_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const team: Team = {
      id: teamId,
      name: teamData.name,
      description: teamData.description,
      isActive: true,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      members: [],
      maxMembers: teamData.maxMembers || 50,
      permissions: [],
      settings: {
        isPublic: false,
        allowInvites: true,
        requireApproval: false,
        defaultRole: UserRole.EDITOR
      },
      stats: {
        memberCount: 0,
        projectCount: 0,
        storageUsed: 0,
        storageLimit: 10 * 1024 * 1024 * 1024 // 10GB
      }
    };

    this.teams.set(teamId, team);

    // 记录审计日志
    await this.logAuditAction({
      actionType: AuditActionType.TEAM_CREATE,
      userId: this.currentUser?.id || 'system',
      targetType: 'team',
      targetId: teamId,
      details: {
        action: 'Team created',
        newValue: { name: teamData.name, description: teamData.description }
      }
    });

    this.emit('teamCreated', team);
    return team;
  }

  /**
   * 添加团队成员
   */
  public async addTeamMember(
    teamId: string,
    userId: string,
    role: UserRole = UserRole.EDITOR
  ): Promise<void> {
    const team = this.teams.get(teamId);
    const user = this.users.get(userId);

    if (!team || !user) {
      throw new Error('Team or user not found');
    }

    if (team.members.length >= team.maxMembers) {
      throw new Error('Team is full');
    }

    const member: TeamMember = {
      userId,
      teamId,
      role,
      permissions: this.getRolePermissions(role),
      isActive: true,
      joinedAt: Date.now(),
      invitedBy: this.currentUser?.id || 'system',
      stats: {
        projectsCreated: 0,
        lastActivity: Date.now(),
        contributionScore: 0
      }
    };

    team.members.push(member);
    team.stats.memberCount++;

    // 更新用户的团队信息
    if (!user.teams.includes(teamId)) {
      user.teams.push(teamId);
    }
    if (!user.primaryTeam) {
      user.primaryTeam = teamId;
    }

    // 记录审计日志
    await this.logAuditAction({
      actionType: AuditActionType.TEAM_MEMBER_ADD,
      userId: this.currentUser?.id || 'system',
      targetType: 'team',
      targetId: teamId,
      details: {
        action: 'Team member added',
        newValue: { userId, role }
      }
    });

    this.emit('teamMemberAdded', { team, member });
  }

  /**
   * 移除团队成员
   */
  public async removeTeamMember(teamId: string, userId: string): Promise<void> {
    const team = this.teams.get(teamId);
    const user = this.users.get(userId);

    if (!team || !user) {
      throw new Error('Team or user not found');
    }

    const memberIndex = team.members.findIndex(m => m.userId === userId);
    if (memberIndex === -1) {
      throw new Error('User is not a member of this team');
    }

    const member = team.members[memberIndex];
    team.members.splice(memberIndex, 1);
    team.stats.memberCount--;

    // 更新用户的团队信息
    const teamIndex = user.teams.indexOf(teamId);
    if (teamIndex !== -1) {
      user.teams.splice(teamIndex, 1);
    }
    if (user.primaryTeam === teamId) {
      user.primaryTeam = user.teams[0];
    }

    // 记录审计日志
    await this.logAuditAction({
      actionType: AuditActionType.TEAM_MEMBER_REMOVE,
      userId: this.currentUser?.id || 'system',
      targetType: 'team',
      targetId: teamId,
      details: {
        action: 'Team member removed',
        oldValue: { userId, role: member.role }
      }
    });

    this.emit('teamMemberRemoved', { team, member });
  }

  /**
   * 检查用户权限
   */
  public hasPermission(
    userId: string,
    permission: PermissionType,
    resourceId?: string,
    resourceType?: string
  ): boolean {
    const user = this.users.get(userId);
    if (!user || !user.isActive) {
      return false;
    }

    // 检查用户直接权限
    if (user.permissions.includes(permission)) {
      return true;
    }

    // 检查自定义权限
    const customPermKey = resourceId ? `${permission}:${resourceId}` : permission;
    if (user.customPermissions[customPermKey]) {
      return true;
    }

    // 检查团队权限
    for (const teamId of user.teams) {
      if (this.hasTeamPermission(teamId, permission, resourceId, resourceType)) {
        return true;
      }
    }

    // 检查策略权限
    return this.evaluatePolicies(user, permission, resourceId, resourceType);
  }

  /**
   * 检查团队权限
   */
  private hasTeamPermission(
    teamId: string,
    permission: PermissionType,
    resourceId?: string,
    _resourceType?: string
  ): boolean {
    const team = this.teams.get(teamId);
    if (!team || !team.isActive) {
      return false;
    }

    // 检查团队权限
    for (const teamPermission of team.permissions) {
      if (teamPermission.permissions.includes(permission)) {
        if (!resourceId || teamPermission.resourceId === resourceId) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * 评估策略权限
   */
  private evaluatePolicies(
    user: User,
    permission: PermissionType,
    _resourceId?: string,
    _resourceType?: string
  ): boolean {
    const applicablePolicies = Array.from(this.policies.values())
      .filter(policy => policy.isActive)
      .sort((a, b) => b.priority - a.priority);

    for (const policy of applicablePolicies) {
      // 检查策略范围
      if (!this.isPolicyApplicable(policy, user)) {
        continue;
      }

      // 评估策略规则
      for (const rule of policy.rules.sort((a, b) => b.priority - a.priority)) {
        if (rule.permissions.includes(permission)) {
          if (this.evaluateRuleCondition(rule, user)) {
            return rule.action === 'allow';
          }
        }
      }
    }

    return false;
  }

  /**
   * 检查策略是否适用
   */
  private isPolicyApplicable(policy: PermissionPolicy, user: User): boolean {
    if (policy.scope.global) {
      return true;
    }

    if (policy.scope.users?.includes(user.id)) {
      return true;
    }

    if (policy.scope.teams?.some(teamId => user.teams.includes(teamId))) {
      return true;
    }

    return false;
  }

  /**
   * 评估规则条件
   */
  private evaluateRuleCondition(rule: PolicyRule, user: User): boolean {
    const { condition } = rule;

    switch (condition.type) {
      case 'role':
        return this.evaluateCondition(user.role, condition.operator, condition.value);
      case 'user':
        return this.evaluateCondition(user.id, condition.operator, condition.value);
      case 'team':
        return user.teams.some(teamId =>
          this.evaluateCondition(teamId, condition.operator, condition.value)
        );
      default:
        return false;
    }
  }

  /**
   * 评估条件
   */
  private evaluateCondition(actual: any, operator: string, expected: any): boolean {
    switch (operator) {
      case 'equals':
        return actual === expected;
      case 'contains':
        return String(actual).includes(String(expected));
      case 'in':
        return Array.isArray(expected) && expected.includes(actual);
      case 'not_in':
        return Array.isArray(expected) && !expected.includes(actual);
      default:
        return false;
    }
  }

  /**
   * 获取所有用户
   */
  public getAllUsers(): User[] {
    return Array.from(this.users.values());
  }

  /**
   * 获取所有团队
   */
  public getAllTeams(): Team[] {
    return Array.from(this.teams.values());
  }

  /**
   * 获取用户的团队
   */
  public getUserTeams(userId: string): Team[] {
    const user = this.users.get(userId);
    if (!user) return [];

    return user.teams.map(teamId => this.teams.get(teamId)).filter(Boolean) as Team[];
  }

  /**
   * 获取团队成员
   */
  public getTeamMembers(teamId: string): User[] {
    const team = this.teams.get(teamId);
    if (!team) return [];

    return team.members.map(member => this.users.get(member.userId)).filter(Boolean) as User[];
  }

  /**
   * 获取审计日志
   */
  public getAuditLogs(filters?: {
    userId?: string;
    actionType?: AuditActionType;
    targetType?: string;
    targetId?: string;
    startDate?: number;
    endDate?: number;
    riskLevel?: string;
    limit?: number;
    offset?: number;
  }): AuditLog[] {
    let logs = this.auditLogs;

    if (filters) {
      logs = logs.filter(log => {
        if (filters.userId && log.userId !== filters.userId) return false;
        if (filters.actionType && log.actionType !== filters.actionType) return false;
        if (filters.targetType && log.targetType !== filters.targetType) return false;
        if (filters.targetId && log.targetId !== filters.targetId) return false;
        if (filters.startDate && log.timestamp < filters.startDate) return false;
        if (filters.endDate && log.timestamp > filters.endDate) return false;
        if (filters.riskLevel && log.riskLevel !== filters.riskLevel) return false;
        return true;
      });

      const offset = filters.offset || 0;
      const limit = filters.limit || 100;
      logs = logs.slice(offset, offset + limit);
    }

    return logs;
  }

  /**
   * 获取权限统计
   */
  public getPermissionStats(): {
    totalUsers: number;
    activeUsers: number;
    totalTeams: number;
    activeTeams: number;
    totalAuditLogs: number;
    recentHighRiskActions: number;
    usersByRole: Record<UserRole, number>;
  } {
    const users = Array.from(this.users.values());
    const teams = Array.from(this.teams.values());
    const recentLogs = this.auditLogs.filter(log =>
      Date.now() - log.timestamp < 24 * 60 * 60 * 1000 // 最近24小时
    );

    const usersByRole = users.reduce((acc, user) => {
      acc[user.role] = (acc[user.role] || 0) + 1;
      return acc;
    }, {} as Record<UserRole, number>);

    return {
      totalUsers: users.length,
      activeUsers: users.filter(u => u.isActive).length,
      totalTeams: teams.length,
      activeTeams: teams.filter(t => t.isActive).length,
      totalAuditLogs: this.auditLogs.length,
      recentHighRiskActions: recentLogs.filter(log =>
        log.riskLevel === 'high' || log.riskLevel === 'critical'
      ).length,
      usersByRole
    };
  }

  /**
   * 记录审计日志
   */
  private async logAuditAction(action: {
    actionType: AuditActionType;
    userId: string;
    targetType: string;
    targetId: string;
    details: any;
  }): Promise<void> {
    const auditLog: AuditLog = {
      id: `audit_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      actionType: action.actionType,
      userId: action.userId,
      targetType: action.targetType as 'user' | 'team' | 'project' | 'system',
      targetId: action.targetId,
      timestamp: Date.now(),
      details: action.details,
      context: {
        ipAddress: '127.0.0.1', // 在实际应用中应该获取真实IP
        userAgent: 'DL-Editor',
        sessionId: `session_${Date.now()}`,
        requestId: `req_${Date.now()}`
      },
      riskLevel: this.calculateRiskLevel(action.actionType),
      result: 'success'
    };

    this.auditLogs.push(auditLog);

    // 保持审计日志在合理范围内
    if (this.auditLogs.length > 10000) {
      this.auditLogs = this.auditLogs.slice(-5000);
    }
  }

  /**
   * 获取角色权限
   */
  private getRolePermissions(role: UserRole): PermissionType[] {
    const rolePermissions: Record<UserRole, PermissionType[]> = {
      [UserRole.SUPER_ADMIN]: Object.values(PermissionType),
      [UserRole.ADMIN]: [
        PermissionType.USER_MANAGE,
        PermissionType.TEAM_CREATE,
        PermissionType.TEAM_MANAGE,
        PermissionType.PROJECT_CREATE,
        PermissionType.PROJECT_READ,
        PermissionType.PROJECT_UPDATE,
        PermissionType.PROJECT_DELETE,
        PermissionType.PROJECT_SHARE,
        PermissionType.PROJECT_EXPORT,
        PermissionType.RESOURCE_UPLOAD,
        PermissionType.RESOURCE_DELETE,
        PermissionType.RESOURCE_SHARE,
        PermissionType.COLLABORATION_EDIT,
        PermissionType.COLLABORATION_COMMENT,
        PermissionType.COLLABORATION_REVIEW,
        PermissionType.SYSTEM_AUDIT
      ],
      [UserRole.MANAGER]: [
        PermissionType.TEAM_INVITE,
        PermissionType.PROJECT_CREATE,
        PermissionType.PROJECT_READ,
        PermissionType.PROJECT_UPDATE,
        PermissionType.PROJECT_SHARE,
        PermissionType.PROJECT_EXPORT,
        PermissionType.RESOURCE_UPLOAD,
        PermissionType.RESOURCE_SHARE,
        PermissionType.COLLABORATION_EDIT,
        PermissionType.COLLABORATION_COMMENT,
        PermissionType.COLLABORATION_REVIEW,
        PermissionType.USER_INVITE
      ],
      [UserRole.EDITOR]: [
        PermissionType.PROJECT_READ,
        PermissionType.PROJECT_UPDATE,
        PermissionType.PROJECT_SHARE,
        PermissionType.RESOURCE_UPLOAD,
        PermissionType.RESOURCE_SHARE,
        PermissionType.COLLABORATION_EDIT,
        PermissionType.COLLABORATION_COMMENT
      ],
      [UserRole.VIEWER]: [
        PermissionType.PROJECT_READ,
        PermissionType.COLLABORATION_COMMENT
      ],
      [UserRole.GUEST]: [
        PermissionType.PROJECT_READ
      ]
    };

    return rolePermissions[role] || [];
  }

  /**
   * 计算风险级别
   */
  private calculateRiskLevel(actionType: AuditActionType): 'low' | 'medium' | 'high' | 'critical' {
    const riskLevels: Record<AuditActionType, 'low' | 'medium' | 'high' | 'critical'> = {
      [AuditActionType.USER_LOGIN]: 'low',
      [AuditActionType.USER_LOGOUT]: 'low',
      [AuditActionType.USER_CREATE]: 'medium',
      [AuditActionType.USER_UPDATE]: 'medium',
      [AuditActionType.USER_DELETE]: 'high',
      [AuditActionType.PROJECT_CREATE]: 'low',
      [AuditActionType.PROJECT_UPDATE]: 'low',
      [AuditActionType.PROJECT_DELETE]: 'medium',
      [AuditActionType.PROJECT_SHARE]: 'medium',
      [AuditActionType.TEAM_CREATE]: 'medium',
      [AuditActionType.TEAM_UPDATE]: 'medium',
      [AuditActionType.TEAM_DELETE]: 'high',
      [AuditActionType.TEAM_MEMBER_ADD]: 'medium',
      [AuditActionType.TEAM_MEMBER_REMOVE]: 'medium',
      [AuditActionType.PERMISSION_GRANT]: 'high',
      [AuditActionType.PERMISSION_REVOKE]: 'high',
      [AuditActionType.SYSTEM_CONFIG_CHANGE]: 'critical',
      [AuditActionType.SECURITY_VIOLATION]: 'critical'
    };

    return riskLevels[actionType] || 'medium';
  }
}

export default EnterprisePermissionService;
