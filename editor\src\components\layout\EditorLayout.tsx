/**
 * 编辑器布局组件
 */
import React, { useState, useEffect, useRef } from 'react';
import { Layout, Button, Tooltip, Space, Dropdown, Modal, Input, Form } from 'antd';
import {
  FullscreenOutlined,
  FullscreenExitOutlined,
  SaveOutlined,
  UndoOutlined,
  RedoOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ArrowsAltOutlined,
  <PERSON>otateRightOutlined,
  ColumnWidthOutlined,
  BorderOutlined,
  DotChartOutlined,
  AppstoreOutlined,
  LayoutOutlined,
  ReloadOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  BranchesOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  setTransformMode,
  setTransformSpace,
  setSnapMode,
  setShowGrid,
  setShowAxes,
  setIsPlaying,
  TransformMode,
  TransformSpace,
  SnapMode,
  undo,
  redo} from '../../store/editor/editorSlice';
import { toggleFullscreen, PanelType } from '../../store/ui/uiSlice';
import {
  setLayout,
  saveLayout,
  loadLayout,
  resetLayout,
  toggleTheme,
  defaultLayout
} from '../../store/ui/layoutSlice';
import { setShowGitPanel } from '../../store/git/gitSlice';

import Toolbar from '../toolbar/Toolbar';
import DockLayout, { DockLayoutRef } from './DockLayout';
import MobileAdaptiveLayout from './MobileAdaptiveLayout';
import LayoutService from '../../services/LayoutService';
import MobileDeviceService from '../../services/MobileDeviceService';
import { registerPanelComponent } from '../panels/PanelRegistry';
import ScenePanel from '../panels/ScenePanel';
import HierarchyPanel from '../panels/HierarchyPanel';
import InspectorPanel from '../panels/InspectorPanel';
import AssetsPanel from '../panels/AssetsPanel';
import ConsolePanel from '../panels/ConsolePanel';
import CollaborationPanel from '../collaboration/CollaborationPanel';
import UserTestingPanel from '../testing/UserTestingPanel';
import DebugPanel from '../debug/DebugPanel';
import PerformanceOptimizationPanel from '../optimization/PerformanceOptimizationPanel';
import ResourceHotUpdatePanel from '../resources/ResourceHotUpdatePanel';

import { LayoutData } from 'rc-dock';

const { Header, Content } = Layout;

interface EditorLayoutProps {
  projectId: string;
  sceneId: string;
}

export const EditorLayout: React.FC<EditorLayoutProps> = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const dockLayoutRef = useRef<DockLayoutRef>(null);

  const {
    transformMode,
    transformSpace,
    snapMode,
    showGrid,
    showAxes,
    isPlaying} = useAppSelector((state) => state.editor);

  const { fullscreen } = useAppSelector((state) => state.ui);
  const { theme, savedLayouts } = useAppSelector((state) => state.ui);

  const [saveLayoutModalVisible, setSaveLayoutModalVisible] = useState(false);
  const [layoutName, setLayoutName] = useState('');
  const [form] = Form.useForm();

  // 初始化布局服务
  useEffect(() => {
    if (dockLayoutRef.current) {
      LayoutService.getInstance().setDockLayoutRef(dockLayoutRef.current);
    }

    // 注册面板组件
    registerPanelComponent(PanelType.HIERARCHY, HierarchyPanel);
    registerPanelComponent(PanelType.INSPECTOR, InspectorPanel);
    registerPanelComponent(PanelType.SCENE, ScenePanel);
    registerPanelComponent(PanelType.ASSETS, AssetsPanel);
    registerPanelComponent(PanelType.CONSOLE, ConsolePanel);
    registerPanelComponent(PanelType.COLLABORATION, CollaborationPanel);
    registerPanelComponent(PanelType.USER_TESTING, UserTestingPanel);
    registerPanelComponent(PanelType.DEBUG, DebugPanel);
    registerPanelComponent(PanelType.PERFORMANCE_OPTIMIZATION, PerformanceOptimizationPanel);

    // 注册面板内容组件到布局服务
    LayoutService.registerPanelContent(PanelType.HIERARCHY, HierarchyPanel);
    LayoutService.registerPanelContent(PanelType.INSPECTOR, InspectorPanel);
    LayoutService.registerPanelContent(PanelType.SCENE, ScenePanel);
    LayoutService.registerPanelContent(PanelType.ASSETS, AssetsPanel);
    LayoutService.registerPanelContent(PanelType.CONSOLE, ConsolePanel);
    LayoutService.registerPanelContent(PanelType.COLLABORATION, CollaborationPanel);
    LayoutService.registerPanelContent(PanelType.USER_TESTING, UserTestingPanel);
    LayoutService.registerPanelContent(PanelType.DEBUG, DebugPanel);
    LayoutService.registerPanelContent(PanelType.PERFORMANCE_OPTIMIZATION, PerformanceOptimizationPanel);
    LayoutService.registerPanelContent(PanelType.RESOURCE_HOT_UPDATE, ResourceHotUpdatePanel);
  }, [dockLayoutRef]);

  // 切换全屏
  const handleToggleFullscreen = () => {
    dispatch(toggleFullscreen());

    if (!fullscreen) {
      document.documentElement.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
  };

  // 切换播放状态
  const handleTogglePlay = () => {
    dispatch(setIsPlaying(!isPlaying));
  };

  // 设置变换模式
  const handleSetTransformMode = (mode: TransformMode) => {
    dispatch(setTransformMode(mode));
  };

  // 设置变换空间
  const handleSetTransformSpace = (space: TransformSpace) => {
    dispatch(setTransformSpace(space));
  };

  // 设置网格捕捉模式
  const handleSetSnapMode = (mode: SnapMode) => {
    dispatch(setSnapMode(mode));
  };

  // 切换网格显示
  const handleToggleGrid = () => {
    dispatch(setShowGrid(!showGrid));
  };

  // 切换坐标轴显示
  const handleToggleAxes = () => {
    dispatch(setShowAxes(!showAxes));
  };

  // 撤销操作
  const handleUndo = () => {
    dispatch(undo());
  };

  // 重做操作
  const handleRedo = () => {
    dispatch(redo());
  };

  // 处理布局变化
  const handleLayoutChange = (newLayout: LayoutData) => {
    dispatch(setLayout(newLayout));
  };

  // 重置布局
  const handleResetLayout = () => {
    dispatch(resetLayout());
    if (dockLayoutRef.current) {
      dockLayoutRef.current.loadLayout(defaultLayout);
    }
  };

  // 切换主题
  const handleToggleTheme = () => {
    dispatch(toggleTheme());
  };

  // 打开保存布局对话框
  const handleOpenSaveLayoutModal = () => {
    setLayoutName('');
    form.resetFields();
    setSaveLayoutModalVisible(true);
  };

  // 保存布局
  const handleSaveLayout = () => {
    if (!layoutName.trim()) {
      return;
    }

    if (dockLayoutRef.current) {
      const currentLayout = dockLayoutRef.current.saveLayout() as LayoutData;
      dispatch(saveLayout({ name: layoutName, layout: currentLayout }));
      setSaveLayoutModalVisible(false);
    }
  };

  // 加载布局
  const handleLoadLayout = (layoutName: string) => {
    dispatch(loadLayout(layoutName));
    if (dockLayoutRef.current && savedLayouts[layoutName]) {
      dockLayoutRef.current.loadLayout(savedLayouts[layoutName]);
    }
  };



  // 布局菜单项
  const layoutMenuItems = [
    {
      key: 'reset',
      label: t('editor.layout.reset') || '重置布局',
      icon: <ReloadOutlined />,
      onClick: handleResetLayout
    },
    {
      key: 'save',
      label: t('editor.layout.save') || '保存布局',
      icon: <SaveOutlined />,
      onClick: handleOpenSaveLayoutModal
    },
    {
      key: 'theme',
      label: theme === 'light' ? (t('editor.layout.darkTheme') || '暗色主题') : (t('editor.layout.lightTheme') || '亮色主题'),
      icon: theme === 'light' ? <EyeInvisibleOutlined /> : <EyeOutlined />,
      onClick: handleToggleTheme
    },
    {
      type: 'divider' as const
    },
    {
      key: 'layouts',
      label: t('editor.layout.loadLayout') || '加载布局',
      children: Object.keys(savedLayouts).map(name => ({
        key: `layout-${name}`,
        label: name,
        onClick: () => handleLoadLayout(name)
      }))
    }
  ];

  // 检查是否是移动设备
  const isMobileDevice = MobileDeviceService.isMobileDevice() || MobileDeviceService.isTabletDevice();

  return (
    <Layout style={{ height: '100vh', overflow: 'hidden' }}>
      {!isMobileDevice && (
        <Header style={{ padding: 0, height: 40, lineHeight: '40px', background: '#1e1e1e' }}>
          <Toolbar />
        </Header>
      )}
      <Content style={{ position: 'relative', height: isMobileDevice ? '100vh' : 'calc(100vh - 40px)' }}>
        <MobileAdaptiveLayout>
          <div style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0 }}>
            <DockLayout
              ref={dockLayoutRef}
              defaultLayout={defaultLayout}
              onLayoutChange={handleLayoutChange}
            />
          </div>
          {!isMobileDevice && (
            <>
              <div style={{ position: 'absolute', top: 10, left: 10, zIndex: 100 }}>
                <Space>
                  <Tooltip title={t('editor.translate') || '移动'}>
                    <Button
                      type={transformMode === TransformMode.TRANSLATE ? 'primary' : 'default'}
                      icon={<ArrowsAltOutlined />}
                      onClick={() => handleSetTransformMode(TransformMode.TRANSLATE)}
                    />
                  </Tooltip>
                  <Tooltip title={t('editor.rotate') || '旋转'}>
                    <Button
                      type={transformMode === TransformMode.ROTATE ? 'primary' : 'default'}
                      icon={<RotateRightOutlined />}
                      onClick={() => handleSetTransformMode(TransformMode.ROTATE)}
                    />
                  </Tooltip>
                  <Tooltip title={t('editor.scale') || '缩放'}>
                    <Button
                      type={transformMode === TransformMode.SCALE ? 'primary' : 'default'}
                      icon={<ColumnWidthOutlined />}
                      onClick={() => handleSetTransformMode(TransformMode.SCALE)}
                    />
                  </Tooltip>
                  <Tooltip title={t('editor.localSpace') || '本地空间'}>
                    <Button
                      type={transformSpace === TransformSpace.LOCAL ? 'primary' : 'default'}
                      onClick={() => handleSetTransformSpace(TransformSpace.LOCAL)}
                    >
                      {t('editor.local') || '本地'}
                    </Button>
                  </Tooltip>
                  <Tooltip title={t('editor.worldSpace') || '世界空间'}>
                    <Button
                      type={transformSpace === TransformSpace.WORLD ? 'primary' : 'default'}
                      onClick={() => handleSetTransformSpace(TransformSpace.WORLD)}
                    >
                      {t('editor.world') || '世界'}
                    </Button>
                  </Tooltip>
                </Space>
              </div>
              <div style={{ position: 'absolute', top: 10, right: 10, zIndex: 100 }}>
                <Space>
                  <Dropdown menu={{ items: layoutMenuItems }} placement="bottomRight">
                    <Tooltip title={t('editor.layout.manage') || '管理布局'}>
                      <Button icon={<LayoutOutlined />} />
                    </Tooltip>
                  </Dropdown>
                  <Tooltip title={t('editor.save') || '保存'}>
                    <Button icon={<SaveOutlined />} />
                  </Tooltip>
                  <Tooltip title={t('editor.undo') || '撤销'}>
                    <Button icon={<UndoOutlined />} onClick={handleUndo} />
                  </Tooltip>
                  <Tooltip title={t('editor.redo') || '重做'}>
                    <Button icon={<RedoOutlined />} onClick={handleRedo} />
                  </Tooltip>
                  <Tooltip title={isPlaying ? (t('editor.pause') || '暂停') : (t('editor.play') || '播放')}>
                    <Button
                      icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                      onClick={handleTogglePlay}
                    />
                  </Tooltip>
                  <Tooltip title={t('editor.git') || 'Git'}>
                    <Button
                      icon={<BranchesOutlined />}
                      onClick={() => dispatch(setShowGitPanel(true))}
                    />
                  </Tooltip>
                  <Tooltip title={fullscreen ? (t('editor.exitFullscreen') || '退出全屏') : (t('editor.fullscreen') || '全屏')}>
                    <Button
                      icon={fullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
                      onClick={handleToggleFullscreen}
                    />
                  </Tooltip>
                </Space>
              </div>
              <div style={{ position: 'absolute', bottom: 10, left: 10, zIndex: 100 }}>
                <Space>
                  <Tooltip title={t('editor.showGrid') || '显示网格'}>
                    <Button
                      type={showGrid ? 'primary' : 'default'}
                      icon={<BorderOutlined />}
                      onClick={handleToggleGrid}
                    />
                  </Tooltip>
                  <Tooltip title={t('editor.showAxes') || '显示坐标轴'}>
                    <Button
                      type={showAxes ? 'primary' : 'default'}
                      icon={<AppstoreOutlined />}
                      onClick={handleToggleAxes}
                    />
                  </Tooltip>
                  <Tooltip title={t('editor.snapToGrid') || '吸附到网格'}>
                    <Button
                      type={snapMode === SnapMode.GRID ? 'primary' : 'default'}
                      icon={<DotChartOutlined />}
                      onClick={() => handleSetSnapMode(snapMode === SnapMode.GRID ? SnapMode.DISABLED : SnapMode.GRID)}
                    />
                  </Tooltip>
                </Space>
              </div>
            </>
          )}
        </MobileAdaptiveLayout>
      </Content>

      {/* 保存布局对话框 */}
      <Modal
        title={t('editor.layout.saveLayout') || '保存布局'}
        open={saveLayoutModalVisible}
        onOk={handleSaveLayout}
        onCancel={() => setSaveLayoutModalVisible(false)}
      >
        <Form form={form}>
          <Form.Item
            label={t('editor.layout.layoutName') || '布局名称'}
            name="layoutName"
            rules={[{ required: true, message: t('editor.layout.nameRequired') || '请输入布局名称' }]}
          >
            <Input
              value={layoutName}
              onChange={(e) => setLayoutName(e.target.value)}
              placeholder={t('editor.layout.enterName') || '请输入布局名称'}
            />
          </Form.Item>
        </Form>
      </Modal>
    </Layout>
  );
};
