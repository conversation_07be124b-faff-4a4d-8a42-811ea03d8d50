/**
 * 虚拟化对象列表组件
 * 专为支持100+并发用户优化的高性能列表组件
 */
import React, { useState } from 'react';
import { List, Input, Button, Empty } from 'antd';
import {
  EyeOutlined,
  EyeInvisibleOutlined,
  FolderOutlined,
  FileOutlined,
} from '@ant-design/icons';

const { Search } = Input;

// 对象项接口
interface ObjectItem {
  id: string;
  name: string;
  type: string;
  visible: boolean;
  locked: boolean;
}

// 虚拟化对象列表组件
export const VirtualizedObjectList: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');

  // 模拟数据
  const mockObjects: ObjectItem[] = [
    { id: '1', name: '立方体', type: 'mesh', visible: true, locked: false },
    { id: '2', name: '球体', type: 'mesh', visible: true, locked: false },
    { id: '3', name: '平面', type: 'mesh', visible: false, locked: true },
  ];

  // 过滤对象
  const filteredObjects = mockObjects.filter(obj =>
    obj.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="virtualized-object-list" style={{ padding: '16px' }}>
      {/* 搜索框 */}
      <Search
        placeholder="搜索对象..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        style={{ marginBottom: '16px' }}
        allowClear
      />

      {/* 对象列表 */}
      {filteredObjects.length > 0 ? (
        <List
          dataSource={filteredObjects}
          renderItem={(item) => (
            <List.Item
              key={item.id}
              style={{
                padding: '8px 12px',
                border: '1px solid #f0f0f0',
                marginBottom: '4px',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                {/* 图标 */}
                <div style={{ marginRight: '8px' }}>
                  {item.type === 'mesh' ? <FileOutlined /> : <FolderOutlined />}
                </div>

                {/* 名称 */}
                <div style={{ flex: 1, fontWeight: 500 }}>
                  {item.name}
                </div>

                {/* 状态指示器 */}
                <div style={{ display: 'flex', gap: '4px' }}>
                  <Button
                    type="text"
                    size="small"
                    icon={item.visible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                    style={{ color: item.visible ? '#1890ff' : '#d9d9d9' }}
                  />
                  {item.locked && (
                    <span style={{ color: '#ff4d4f', fontSize: '12px' }}>🔒</span>
                  )}
                </div>
              </div>
            </List.Item>
          )}
        />
      ) : (
        <Empty description="没有找到对象" />
      )}
    </div>
  );
};

export default VirtualizedObjectList;
