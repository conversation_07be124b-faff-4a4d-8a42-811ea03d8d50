/**
 * 相机编辑器
 */
import React from 'react';
import { Card, Form, Select, InputNumber, Switch } from 'antd';

const { Option } = Select;

/**
 * 相机编辑器属性
 */
interface CameraEditorProps {
  /** 组件数据 */
  data?: any;
  /** 数据更新回调 */
  onChange?: (data: any) => void;
}

/**
 * 相机编辑器组件
 */
const CameraEditor: React.FC<CameraEditorProps> = ({ data, onChange }) => {
  const [form] = Form.useForm();

  // 默认数据
  const defaultData = {
    enabled: true,
    type: 'perspective',
    fov: 75,
    near: 0.1,
    far: 1000,
    ...data
  };

  // 处理数据变化
  const handleChange = (field: string, value: any) => {
    const newData = { ...defaultData, [field]: value };
    if (onChange) {
      onChange(newData);
    }
  };

  return (
    <Card title="相机" size="small">
      <Form
        form={form}
        layout="vertical"
        initialValues={defaultData}
        onValuesChange={(changedValues) => {
          Object.entries(changedValues).forEach(([field, value]) => {
            handleChange(field, value);
          });
        }}
      >
        <Form.Item label="启用" name="enabled" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item label="类型" name="type">
          <Select>
            <Option value="perspective">透视相机</Option>
            <Option value="orthographic">正交相机</Option>
          </Select>
        </Form.Item>

        <Form.Item label="视野角度" name="fov">
          <InputNumber min={1} max={180} step={1} />
        </Form.Item>

        <Form.Item label="近裁剪面" name="near">
          <InputNumber min={0.01} max={10} step={0.01} />
        </Form.Item>

        <Form.Item label="远裁剪面" name="far">
          <InputNumber min={1} max={1000} step={1} />
        </Form.Item>
      </Form>
    </Card>
  );
};

export default CameraEditor;
