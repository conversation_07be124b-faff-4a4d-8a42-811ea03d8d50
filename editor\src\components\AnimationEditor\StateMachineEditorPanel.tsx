/**
 * 状态机编辑器面板组件
 * 用于集成到编辑器中
 */
import React, { useState, useEffect } from 'react';
import { Button, Space, message, Spin, Modal } from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import { loadStateMachine, clearStateMachine } from '../../store/animations/stateMachineSlice';
import { stateMachineService } from '../../services/stateMachineService';
import StateMachineEditor from './StateMachineEditor';
import './StateMachineEditor.less';

const { confirm } = Modal;

/**
 * 状态机编辑器面板属性
 */
interface StateMachineEditorPanelProps {
  /** 实体ID */
  entityId: string;
}

/**
 * 状态机编辑器面板组件
 */
const StateMachineEditorPanel: React.FC<StateMachineEditorPanelProps> = ({ entityId }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  // 从Redux获取状态
  const { loading } = useAppSelector((state) => state.stateMachine);

  // 本地状态
  const [stateMachines, setStateMachines] = useState<string[]>([]);
  const [selectedStateMachine, setSelectedStateMachine] = useState<string | null>(null);
  const [isEditorVisible, setIsEditorVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  // 加载状态机列表
  useEffect(() => {
    loadStateMachineList();
  }, [entityId]);
  
  // 加载状态机列表
  const loadStateMachineList = async () => {
    try {
      setIsLoading(true);
      const list = await stateMachineService.callEngineMethod('getStateMachineList', entityId);
      setStateMachines(list || []);
    } catch (error) {
      console.error('加载状态机列表失败:', error);
      message.error(t('editor.animation.loadStateMachineListFailed'));
    } finally {
      setIsLoading(false);
    }
  };
  
  // 处理创建状态机
  const handleCreateStateMachine = () => {
    confirm({
      title: t('editor.animation.createStateMachine'),
      icon: <ExclamationCircleOutlined />,
      content: t('editor.animation.createStateMachineConfirm'),
      onOk: async () => {
        try {
          setIsLoading(true);
          const data = await stateMachineService.createStateMachine(entityId);

          if (data) {
            message.success(t('editor.animation.createStateMachineSuccess'));
            await loadStateMachineList();

            // 选中新创建的状态机 - 使用状态机列表中的第一个状态机名称
            if (data.states && data.states.length > 0) {
              const firstStateName = data.states[0].name;
              setSelectedStateMachine(firstStateName);
              handleEditStateMachine(firstStateName);
            }
          }
        } catch (error) {
          console.error('创建状态机失败:', error);
          message.error(t('editor.animation.createStateMachineFailed'));
        } finally {
          setIsLoading(false);
        }
      }
    });
  };
  
  // 处理编辑状态机
  const handleEditStateMachine = (name: string) => {
    setSelectedStateMachine(name);
    dispatch(loadStateMachine(entityId));
    setIsEditorVisible(true);
  };
  
  // 处理删除状态机
  const handleDeleteStateMachine = (name: string) => {
    confirm({
      title: t('editor.animation.deleteStateMachine'),
      icon: <ExclamationCircleOutlined />,
      content: t('editor.animation.deleteStateMachineConfirm'),
      onOk: async () => {
        try {
          setIsLoading(true);
          await stateMachineService.callEngineMethod('deleteStateMachine', entityId, name);
          
          message.success(t('editor.animation.deleteStateMachineSuccess'));
          
          // 如果删除的是当前选中的状态机，则清除选中
          if (selectedStateMachine === name) {
            setSelectedStateMachine(null);
          }
          
          await loadStateMachineList();
        } catch (error) {
          console.error('删除状态机失败:', error);
          message.error(t('editor.animation.deleteStateMachineFailed'));
        } finally {
          setIsLoading(false);
        }
      }
    });
  };
  
  // 处理编辑器保存
  const handleEditorSave = async () => {
    setIsEditorVisible(false);
    await loadStateMachineList();
    dispatch(clearStateMachine());
  };
  
  // 处理编辑器取消
  const handleEditorCancel = () => {
    setIsEditorVisible(false);
    dispatch(clearStateMachine());
  };
  
  // 渲染状态机列表
  const renderStateMachineList = () => {
    return (
      <div className="state-machine-list">
        <div className="list-header">
          <h2>{t('editor.animation.stateMachines')}</h2>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreateStateMachine}
          >
            {t('editor.animation.create')}
          </Button>
        </div>
        
        <div className="list-content">
          {stateMachines.length === 0 ? (
            <div className="empty-message">
              {t('editor.animation.noStateMachines')}
            </div>
          ) : (
            <ul className="state-machine-items">
              {stateMachines.map((name) => (
                <li key={name} className="state-machine-item">
                  <div className="item-name">{name}</div>
                  <div className="item-actions">
                    <Space>
                      <Button
                        icon={<EditOutlined />}
                        size="small"
                        onClick={() => handleEditStateMachine(name)}
                      >
                        {t('editor.edit')}
                      </Button>
                      <Button
                        icon={<DeleteOutlined />}
                        size="small"
                        danger
                        onClick={() => handleDeleteStateMachine(name)}
                      >
                        {t('editor.delete')}
                      </Button>
                    </Space>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
    );
  };
  
  return (
    <div className="state-machine-editor-panel">
      <Spin spinning={isLoading || loading}>
        {isEditorVisible ? (
          <StateMachineEditor
            entityId={entityId}
            onSave={handleEditorSave}
            onCancel={handleEditorCancel}
          />
        ) : (
          renderStateMachineList()
        )}
      </Spin>
    </div>
  );
};

export default StateMachineEditorPanel;
