/**
 * 场景编辑工作流演示
 * 展示 ScenePanel、PropertiesPanel 和 TransformEditor 的完整集成
 */
import React, { useState, useEffect } from 'react';
import { Layout, Card, Space, Button, Typography, Divider, Steps, message, Row, Col } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  EditOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { addEntity, selectEntity, updateEntity } from '../../store/scene/sceneSlice';
import { setSelectedObject, undo, redo } from '../../store/editor/editorSlice';
import ScenePanel from '../ScenePanel';
import PropertiesPanel from '../PropertiesPanel';
import TransformEditor from './TransformEditor';

const { Title, Text, Paragraph } = Typography;
const { Sider, Content } = Layout;
const { Step } = Steps;

/**
 * 场景编辑工作流演示组件
 */
const SceneEditingWorkflowDemo: React.FC = () => {
  const dispatch = useDispatch();
  const { entities, selectedEntityId } = useSelector((state: any) => ({
    entities: state.scene?.entities || [],
    selectedEntityId: state.scene?.selectedEntityId || null
  }));
  const { undoStack, redoStack } = useSelector((state: any) => ({
    undoStack: state.editor?.undoStack || [],
    redoStack: state.editor?.redoStack || []
  }));
  
  const [currentStep, setCurrentStep] = useState(0);
  const [isAutoDemo, setIsAutoDemo] = useState(false);

  // 演示步骤
  const demoSteps = [
    {
      title: '创建实体',
      description: '在场景面板中添加新实体',
      action: () => {
        const newEntity = {
          id: `demo-entity-${Date.now()}`,
          name: `演示立方体 ${entities.length + 1}`,
          type: 'mesh',
          parentId: null,
          visible: true,
          locked: false,
          transform: {
            position: [Math.random() * 10 - 5, Math.random() * 10 - 5, Math.random() * 10 - 5] as [number, number, number],
            rotation: [0, 0, 0] as [number, number, number],
            scale: [1, 1, 1] as [number, number, number]
          },
          components: {}
        };
        dispatch(addEntity(newEntity));
        message.success('已创建新实体');
      }
    },
    {
      title: '选择实体',
      description: '在场景面板中选择实体',
      action: () => {
        if (entities.length > 0) {
          const lastEntity = entities[entities.length - 1];
          dispatch(selectEntity(lastEntity.id));
          dispatch(setSelectedObject(lastEntity));
          message.success('已选择实体');
        }
      }
    },
    {
      title: '编辑变换',
      description: '使用 TransformEditor 修改实体变换',
      action: () => {
        if (selectedEntityId) {
          const randomTransform = {
            position: [Math.random() * 10 - 5, Math.random() * 10 - 5, Math.random() * 10 - 5] as [number, number, number],
            rotation: [Math.random() * 360 - 180, Math.random() * 360 - 180, Math.random() * 360 - 180] as [number, number, number],
            scale: [Math.random() * 2 + 0.5, Math.random() * 2 + 0.5, Math.random() * 2 + 0.5] as [number, number, number]
          };
          
          dispatch(updateEntity({
            id: selectedEntityId,
            changes: { transform: randomTransform }
          }));
          message.success('已更新变换数据');
        }
      }
    },
    {
      title: '撤销操作',
      description: '使用撤销功能恢复之前的状态',
      action: () => {
        if (undoStack.length > 0) {
          dispatch(undo());
          message.success('已撤销操作');
        } else {
          message.warning('没有可撤销的操作');
        }
      }
    },
    {
      title: '重做操作',
      description: '使用重做功能重新应用操作',
      action: () => {
        if (redoStack.length > 0) {
          dispatch(redo());
          message.success('已重做操作');
        } else {
          message.warning('没有可重做的操作');
        }
      }
    }
  ];

  // 自动演示
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (isAutoDemo && currentStep < demoSteps.length) {
      timer = setTimeout(() => {
        demoSteps[currentStep].action();
        setCurrentStep(prev => prev + 1);
      }, 2000);
    } else if (isAutoDemo && currentStep >= demoSteps.length) {
      setIsAutoDemo(false);
      setCurrentStep(0);
      message.success('自动演示完成！');
    }
    return () => clearTimeout(timer);
  }, [isAutoDemo, currentStep, demoSteps, entities.length, selectedEntityId, undoStack.length, redoStack.length]);

  // 开始自动演示
  const startAutoDemo = () => {
    setCurrentStep(0);
    setIsAutoDemo(true);
    message.info('开始自动演示...');
  };

  // 停止自动演示
  const stopAutoDemo = () => {
    setIsAutoDemo(false);
    message.info('已停止自动演示');
  };

  // 重置演示
  const resetDemo = () => {
    setCurrentStep(0);
    setIsAutoDemo(false);
    // 这里可以添加重置场景的逻辑
    message.info('已重置演示');
  };

  // 手动执行步骤
  const executeStep = (stepIndex: number) => {
    if (stepIndex < demoSteps.length) {
      demoSteps[stepIndex].action();
      setCurrentStep(stepIndex + 1);
    }
  };

  return (
    <div style={{ padding: '20px', height: '100vh', backgroundColor: '#f5f5f5' }}>
      <Title level={2}>场景编辑工作流演示</Title>
      <Paragraph>
        这个演示展示了 ScenePanel、PropertiesPanel 和 TransformEditor 的完整集成工作流。
        您可以手动操作或使用自动演示来了解各个组件如何协同工作。
      </Paragraph>

      {/* 控制面板 */}
      <Card title="演示控制" style={{ marginBottom: '20px' }}>
        <Row gutter={16}>
          <Col span={12}>
            <Space>
              <Button 
                type="primary" 
                icon={<PlayCircleOutlined />}
                onClick={startAutoDemo}
                disabled={isAutoDemo}
              >
                开始自动演示
              </Button>
              <Button 
                icon={<PauseCircleOutlined />}
                onClick={stopAutoDemo}
                disabled={!isAutoDemo}
              >
                停止演示
              </Button>
              <Button 
                icon={<ReloadOutlined />}
                onClick={resetDemo}
              >
                重置
              </Button>
            </Space>
          </Col>
          <Col span={12}>
            <div style={{ textAlign: 'right' }}>
              <Text type="secondary">
                实体数量: {entities.length} | 
                撤销栈: {undoStack.length} | 
                重做栈: {redoStack.length}
              </Text>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 演示步骤 */}
      <Card title="演示步骤" style={{ marginBottom: '20px' }}>
        <Steps current={currentStep} direction="horizontal">
          {demoSteps.map((step, index) => (
            <Step
              key={index}
              title={step.title}
              description={step.description}
              onClick={() => !isAutoDemo && executeStep(index)}
              style={{ cursor: isAutoDemo ? 'default' : 'pointer' }}
            />
          ))}
        </Steps>
        
        {!isAutoDemo && (
          <div style={{ marginTop: '16px', textAlign: 'center' }}>
            <Text type="secondary">点击步骤可手动执行</Text>
          </div>
        )}
      </Card>

      {/* 主要工作区域 */}
      <Layout style={{ backgroundColor: '#fff', minHeight: '600px' }}>
        {/* 场景面板 */}
        <Sider width={300} style={{ backgroundColor: '#fafafa', padding: '16px' }}>
          <Card title="场景层次" size="small" style={{ height: '100%' }}>
            <ScenePanel />
          </Card>
        </Sider>

        {/* 中央内容区域 */}
        <Content style={{ padding: '16px' }}>
          <Card title="3D 视口" style={{ height: '100%', textAlign: 'center' }}>
            <div style={{ 
              height: '400px', 
              backgroundColor: '#f0f0f0', 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              border: '2px dashed #d9d9d9'
            }}>
              <div>
                <EyeOutlined style={{ fontSize: '48px', color: '#999' }} />
                <div style={{ marginTop: '16px', color: '#999' }}>
                  3D 视口区域
                  <br />
                  <Text type="secondary">这里将显示 3D 场景</Text>
                </div>
              </div>
            </div>
            
            {/* 选中实体信息 */}
            {selectedEntityId && (
              <div style={{ marginTop: '16px', textAlign: 'left' }}>
                <Divider />
                <Title level={4}>当前选中实体</Title>
                <Text>ID: {selectedEntityId}</Text>
                <br />
                <Text>名称: {entities.find((e: any) => e.id === selectedEntityId)?.name}</Text>
              </div>
            )}
          </Card>
        </Content>

        {/* 属性面板 */}
        <Sider width={350} style={{ backgroundColor: '#fafafa', padding: '16px' }}>
          <Card title="属性面板" size="small" style={{ height: '100%' }}>
            {selectedEntityId ? (
              <div>
                <PropertiesPanel />
                <Divider />
                <Title level={5}>独立 TransformEditor</Title>
                <TransformEditor entityId={selectedEntityId} />
              </div>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
                <EditOutlined style={{ fontSize: '48px' }} />
                <div style={{ marginTop: '16px' }}>
                  请选择一个实体来编辑属性
                </div>
              </div>
            )}
          </Card>
        </Sider>
      </Layout>
    </div>
  );
};

export default SceneEditingWorkflowDemo;
