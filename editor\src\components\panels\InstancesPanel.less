/**
 * 实例面板样式
 */
.instances-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 8px;
  
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    
    h3 {
      margin: 0;
      font-size: 16px;
    }
  }
  
  .search-input {
    margin-bottom: 8px;
  }
  
  .ant-collapse {
    flex: 1;
    overflow: auto;
    
    .ant-collapse-content-box {
      padding: 8px;
    }
  }
  
  .templates-list {
    .template-card {
      transition: all 0.3s;
      
      &.selected {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
      
      .ant-card-cover {
        height: 100px;
        overflow: hidden;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      
      .ant-card-meta-title {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .ant-card-meta-description {
        height: 40px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
  }
  
  .instances-list {
    .instance-item {
      display: flex;
      align-items: center;
      padding: 8px;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.3s;
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }
      
      &.selected {
        background-color: rgba(24, 144, 255, 0.1);
      }
      
      .instance-info {
        flex: 1;
        overflow: hidden;
        
        .instance-name {
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .instance-meta {
          display: flex;
          align-items: center;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.45);
          margin-top: 2px;
          
          .ant-tag {
            margin-right: 8px;
            font-size: 10px;
            padding: 0 4px;
            height: 18px;
            line-height: 18px;
          }
          
          .instance-position {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
      
      .instance-actions {
        display: flex;
        align-items: center;
        
        .ant-btn {
          padding: 0 4px;
          
          &:hover {
            background-color: rgba(0, 0, 0, 0.05);
          }
        }
      }
    }
  }
}

// 暗色主题适配
.dark-theme {
  .instances-panel {
    .instance-item {
      &:hover {
        background-color: rgba(255, 255, 255, 0.05);
      }
      
      &.selected {
        background-color: rgba(24, 144, 255, 0.2);
      }
      
      .instance-info {
        .instance-meta {
          color: rgba(255, 255, 255, 0.45);
        }
      }
      
      .instance-actions {
        .ant-btn {
          &:hover {
            background-color: rgba(255, 255, 255, 0.05);
          }
        }
      }
    }
    
    .template-card {
      &.selected {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.4);
      }
    }
  }
}
