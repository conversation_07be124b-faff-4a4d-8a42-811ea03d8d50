/**
 * 自然语言场景生成功能测试
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../../i18n';
import uiReducer from '../../../store/ui/uiSlice';
import NLPSceneGenerationPanel from '../../../components/panels/NLPSceneGenerationPanel';
import ScenePreview from '../ScenePreview';
import GenerationHistory from '../GenerationHistory';

// 创建测试store
const createTestStore = () => {
  return configureStore({
    reducer: {
      ui: uiReducer,
    },
  });
};

// 测试包装器
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const store = createTestStore();
  
  return (
    <Provider store={store}>
      <I18nextProvider i18n={i18n}>
        {children}
      </I18nextProvider>
    </Provider>
  );
};

describe('自然语言场景生成面板', () => {
  test('应该正确渲染面板', () => {
    render(
      <TestWrapper>
        <NLPSceneGenerationPanel />
      </TestWrapper>
    );

    expect(screen.getByText('自然语言场景生成')).toBeInTheDocument();
    expect(screen.getByText('快速模板')).toBeInTheDocument();
    expect(screen.getByText('场景描述')).toBeInTheDocument();
    expect(screen.getByText('生成场景')).toBeInTheDocument();
  });

  test('应该显示快速模板按钮', () => {
    render(
      <TestWrapper>
        <NLPSceneGenerationPanel />
      </TestWrapper>
    );

    expect(screen.getByText('现代办公室')).toBeInTheDocument();
    expect(screen.getByText('温馨客厅')).toBeInTheDocument();
    expect(screen.getByText('图书馆')).toBeInTheDocument();
    expect(screen.getByText('咖啡厅')).toBeInTheDocument();
    expect(screen.getByText('科幻实验室')).toBeInTheDocument();
  });

  test('应该能够使用模板填充文本', () => {
    render(
      <TestWrapper>
        <NLPSceneGenerationPanel />
      </TestWrapper>
    );

    const templateButton = screen.getByText('现代办公室');
    fireEvent.click(templateButton);

    const textArea = screen.getByPlaceholderText(/请描述您想要创建的场景/);
    expect(textArea).toHaveValue('创建一个现代化的办公室，包含玻璃桌子、舒适的椅子和绿色植物');
  });

  test('应该能够输入自定义文本', () => {
    render(
      <TestWrapper>
        <NLPSceneGenerationPanel />
      </TestWrapper>
    );

    const textArea = screen.getByPlaceholderText(/请描述您想要创建的场景/);
    fireEvent.change(textArea, { target: { value: '创建一个美丽的花园' } });

    expect(textArea).toHaveValue('创建一个美丽的花园');
  });

  test('应该显示设置面板', () => {
    render(
      <TestWrapper>
        <NLPSceneGenerationPanel />
      </TestWrapper>
    );

    const settingsButton = screen.getByRole('button', { name: /设置/ });
    fireEvent.click(settingsButton);

    expect(screen.getByText('生成参数')).toBeInTheDocument();
    expect(screen.getByText('风格:')).toBeInTheDocument();
    expect(screen.getByText('质量等级:')).toBeInTheDocument();
    expect(screen.getByText('最大对象数:')).toBeInTheDocument();
  });
});

describe('场景预览组件', () => {
  const mockScene = {
    name: '测试场景',
    entities: [
      { id: '1', name: '桌子', type: 'furniture' },
      { id: '2', name: '椅子', type: 'furniture' },
    ],
  };

  test('应该正确渲染预览组件', () => {
    render(
      <TestWrapper>
        <ScenePreview scene={mockScene} />
      </TestWrapper>
    );

    expect(screen.getByText('场景预览')).toBeInTheDocument();
    expect(screen.getByText('重置')).toBeInTheDocument();
    expect(screen.getByText('全屏')).toBeInTheDocument();
  });

  test('应该显示场景统计信息', () => {
    render(
      <TestWrapper>
        <ScenePreview scene={mockScene} />
      </TestWrapper>
    );

    // 点击显示统计按钮
    const showStatsButton = screen.getByText('显示统计');
    fireEvent.click(showStatsButton);

    expect(screen.getByText('实体数量: 2')).toBeInTheDocument();
  });

  test('应该处理空场景', () => {
    render(
      <TestWrapper>
        <ScenePreview scene={null} />
      </TestWrapper>
    );

    expect(screen.getByText('暂无场景可预览')).toBeInTheDocument();
  });
});

describe('生成历史组件', () => {
  const mockHistory = [
    {
      id: '1',
      text: '创建一个现代办公室',
      style: 'realistic',
      quality: 80,
      maxObjects: 50,
      timestamp: new Date('2024-01-01'),
      metadata: {
        generationTime: 5000,
        objectCount: 10,
        polygonCount: 10000,
      },
    },
    {
      id: '2',
      text: '设计一个温馨客厅',
      style: 'cartoon',
      quality: 60,
      maxObjects: 30,
      timestamp: new Date('2024-01-02'),
      metadata: {
        generationTime: 3000,
        objectCount: 8,
        polygonCount: 8000,
      },
    },
  ];

  const mockHandlers = {
    onPreview: jest.fn(),
    onRegenerate: jest.fn(),
    onDelete: jest.fn(),
  };

  test('应该正确渲染历史记录', () => {
    render(
      <TestWrapper>
        <GenerationHistory history={mockHistory} {...mockHandlers} />
      </TestWrapper>
    );

    expect(screen.getByText('创建一个现代办公室')).toBeInTheDocument();
    expect(screen.getByText('设计一个温馨客厅')).toBeInTheDocument();
  });

  test('应该显示风格标签', () => {
    render(
      <TestWrapper>
        <GenerationHistory history={mockHistory} {...mockHandlers} />
      </TestWrapper>
    );

    expect(screen.getByText('realistic')).toBeInTheDocument();
    expect(screen.getByText('cartoon')).toBeInTheDocument();
  });

  test('应该处理预览点击', () => {
    render(
      <TestWrapper>
        <GenerationHistory history={mockHistory} {...mockHandlers} />
      </TestWrapper>
    );

    const previewButtons = screen.getAllByRole('button', { name: /预览/ });
    fireEvent.click(previewButtons[0]);

    expect(mockHandlers.onPreview).toHaveBeenCalledWith(mockHistory[0]);
  });

  test('应该处理重新生成点击', () => {
    render(
      <TestWrapper>
        <GenerationHistory history={mockHistory} {...mockHandlers} />
      </TestWrapper>
    );

    const regenerateButtons = screen.getAllByRole('button', { name: /重新生成/ });
    fireEvent.click(regenerateButtons[0]);

    expect(mockHandlers.onRegenerate).toHaveBeenCalledWith(mockHistory[0]);
  });

  test('应该处理删除确认', async () => {
    render(
      <TestWrapper>
        <GenerationHistory history={mockHistory} {...mockHandlers} />
      </TestWrapper>
    );

    const deleteButtons = screen.getAllByRole('button', { name: /删除/ });
    fireEvent.click(deleteButtons[0]);

    // 确认删除
    const confirmButton = await screen.findByText('删除');
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(mockHandlers.onDelete).toHaveBeenCalledWith('1');
    });
  });

  test('应该处理空历史记录', () => {
    render(
      <TestWrapper>
        <GenerationHistory history={[]} {...mockHandlers} />
      </TestWrapper>
    );

    expect(screen.getByText('暂无生成历史')).toBeInTheDocument();
  });
});

describe('集成测试', () => {
  test('应该能够完整的生成流程', async () => {
    // 这里可以添加端到端的集成测试
    // 模拟用户输入 -> 生成场景 -> 预览 -> 保存的完整流程
    
    render(
      <TestWrapper>
        <NLPSceneGenerationPanel />
      </TestWrapper>
    );

    // 1. 输入场景描述
    const textArea = screen.getByPlaceholderText(/请描述您想要创建的场景/);
    fireEvent.change(textArea, { target: { value: '创建一个美丽的花园' } });

    // 2. 点击生成按钮
    const generateButton = screen.getByText('生成场景');
    expect(generateButton).not.toBeDisabled();

    // 注意：实际的生成功能需要mock EngineService
    // 这里只是验证UI交互是否正常
  });
});
