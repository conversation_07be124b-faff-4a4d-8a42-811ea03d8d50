/**
 * 插件初始化文件
 * 负责注册所有内置插件
 */
import PanelPluginManager from '../services/PanelPluginManager';
import { ExamplePlugin } from './ExamplePlugin';

/**
 * 初始化所有插件
 */
export const initializePlugins = () => {
  const pluginManager = PanelPluginManager.getInstance();

  // 注册示例插件
  pluginManager.registerPlugin(ExamplePlugin);

  console.log('All plugins initialized');
};

/**
 * 获取所有可用插件
 */
export const getAvailablePlugins = () => {
  return [
    ExamplePlugin
  ];
};

export default {
  initializePlugins,
  getAvailablePlugins
};
