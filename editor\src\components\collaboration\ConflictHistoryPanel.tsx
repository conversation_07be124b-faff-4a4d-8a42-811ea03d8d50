/**
 * 冲突历史面板组件
 */
import React from 'react';
import {
  Card,
  List,
  Button,
  Space,
  Typography,
  Tag,
  Empty,
  Divider
} from 'antd';
import {
  HistoryOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  WarningOutlined
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { ConflictHistoryEntry } from '../../services/ConflictVisualizationService';
import { conflictVisualizationService } from '../../services/ConflictVisualizationService';
import {
  setSelectedHistoryEntryId
} from '../../store/collaboration/conflictVisualizationSlice';
import { ConflictType, ConflictStatus, ConflictResolutionStrategy } from '../../services/ConflictResolutionService';

const { Text, Title } = Typography;

/**
 * 冲突历史面板组件
 */
const ConflictHistoryPanel: React.FC = () => {
  const dispatch = useDispatch();
  const history = useSelector((state: RootState) => state.conflictVisualization.history);
  const selectedHistoryEntryId = useSelector((state: RootState) => state.conflictVisualization.selectedHistoryEntryId);
  
  // 获取选中的历史记录
  const selectedEntry = history.find(entry => entry.id === selectedHistoryEntryId);
  
  // 处理选择历史记录
  const handleSelectEntry = (entryId: string) => {
    dispatch(setSelectedHistoryEntryId(entryId));
  };
  
  // 处理清除历史记录
  const handleClearHistory = () => {
    conflictVisualizationService.clearHistory();
  };
  
  // 处理关闭面板
  const handleClose = () => {
    dispatch(setSelectedHistoryEntryId(null));
  };
  
  // 格式化时间
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };
  
  // 获取冲突类型名称
  const getConflictTypeName = (type: ConflictType) => {
    switch (type) {
      case ConflictType.ENTITY_CONFLICT:
        return '实体冲突';
      case ConflictType.COMPONENT_CONFLICT:
        return '组件冲突';
      case ConflictType.PROPERTY_CONFLICT:
        return '属性冲突';
      case ConflictType.DELETION_CONFLICT:
        return '删除冲突';
      case ConflictType.SCENE_CONFLICT:
        return '场景冲突';
      default:
        return '未知冲突';
    }
  };
  
  // 获取冲突状态标签
  const getConflictStatusTag = (status: ConflictStatus) => {
    switch (status) {
      case ConflictStatus.PENDING:
        return <Tag color="warning">待解决</Tag>;
      case ConflictStatus.RESOLVED:
        return <Tag color="success">已解决</Tag>;
      case ConflictStatus.IGNORED:
        return <Tag color="default">已忽略</Tag>;
      default:
        return <Tag>未知状态</Tag>;
    }
  };
  
  // 获取解决策略名称
  const getResolutionStrategyName = (strategy: ConflictResolutionStrategy) => {
    switch (strategy) {
      case ConflictResolutionStrategy.ACCEPT_LOCAL:
        return '采用本地版本';
      case ConflictResolutionStrategy.ACCEPT_REMOTE:
        return '采用远程版本';
      case ConflictResolutionStrategy.MERGE:
        return '合并两个版本';
      case ConflictResolutionStrategy.CUSTOM:
        return '自定义解决方案';
      default:
        return '未知策略';
    }
  };
  
  // 渲染历史记录项
  const renderHistoryItem = (entry: ConflictHistoryEntry) => {
    const { conflict } = entry;
    const isSelected = selectedHistoryEntryId === entry.id;
    
    return (
      <List.Item
        key={entry.id}
        className={`history-item ${isSelected ? 'selected' : ''}`}
        onClick={() => handleSelectEntry(entry.id)}
      >
        <List.Item.Meta
          avatar={
            <div className="conflict-icon">
              {conflict.status === ConflictStatus.RESOLVED ? (
                <CheckCircleOutlined style={{ color: '#52c41a', fontSize: 20 }} />
              ) : (
                <WarningOutlined style={{ color: '#faad14', fontSize: 20 }} />
              )}
            </div>
          }
          title={
            <Space>
              <Text strong>{getConflictTypeName(conflict.type)}</Text>
              {getConflictStatusTag(conflict.status)}
            </Space>
          }
          description={
            <div>
              <div>
                <ClockCircleOutlined /> 创建: {formatTime(entry.createdAt)}
              </div>
              {entry.resolvedAt && (
                <div>
                  <CheckCircleOutlined /> 解决: {formatTime(entry.resolvedAt)}
                </div>
              )}
              {conflict.entityId && (
                <div>
                  <Text type="secondary">实体: {conflict.entityId}</Text>
                </div>
              )}
            </div>
          }
        />
      </List.Item>
    );
  };
  
  // 渲染历史详情
  const renderHistoryDetail = () => {
    if (!selectedEntry) {
      return (
        <Empty description="请选择一个历史记录查看详情" />
      );
    }
    
    const { conflict } = selectedEntry;
    
    return (
      <div className="history-detail">
        <Title level={5}>冲突详情</Title>
        
        <div className="detail-section">
          <Text strong>类型:</Text> {getConflictTypeName(conflict.type)}
        </div>
        
        <div className="detail-section">
          <Text strong>状态:</Text> {getConflictStatusTag(conflict.status)}
        </div>
        
        <div className="detail-section">
          <Text strong>创建时间:</Text> {formatTime(conflict.createdAt)}
        </div>
        
        {conflict.resolvedAt && (
          <div className="detail-section">
            <Text strong>解决时间:</Text> {formatTime(conflict.resolvedAt)}
          </div>
        )}
        
        {conflict.entityId && (
          <div className="detail-section">
            <Text strong>实体ID:</Text> {conflict.entityId}
          </div>
        )}
        
        {conflict.componentId && (
          <div className="detail-section">
            <Text strong>组件ID:</Text> {conflict.componentId}
          </div>
        )}
        
        {conflict.propertyPath && (
          <div className="detail-section">
            <Text strong>属性路径:</Text> {conflict.propertyPath.join('.')}
          </div>
        )}
        
        {conflict.resolution && (
          <div className="detail-section">
            <Text strong>解决策略:</Text> {getResolutionStrategyName(conflict.resolution)}
          </div>
        )}
        
        <Divider />
        
        <div className="operation-section">
          <Title level={5}>操作信息</Title>
          
          <div className="operation-item">
            <Text strong>本地操作:</Text>
            <div>
              <Text>用户: {conflict.localOperation.userId}</Text>
            </div>
            <div>
              <Text>时间: {formatTime(conflict.localOperation.timestamp)}</Text>
            </div>
            <div>
              <Text>类型: {conflict.localOperation.type}</Text>
            </div>
          </div>
          
          <div className="operation-item">
            <Text strong>远程操作:</Text>
            <div>
              <Text>用户: {conflict.remoteOperation.userId}</Text>
            </div>
            <div>
              <Text>时间: {formatTime(conflict.remoteOperation.timestamp)}</Text>
            </div>
            <div>
              <Text>类型: {conflict.remoteOperation.type}</Text>
            </div>
          </div>
        </div>
      </div>
    );
  };
  
  return (
    <div className="conflict-history-panel">
      <Card
        title={
          <Space>
            <HistoryOutlined />
            <span>冲突历史记录</span>
          </Space>
        }
        extra={
          <Space>
            <Button 
              type="text" 
              icon={<CloseCircleOutlined />} 
              onClick={handleClearHistory}
              title="清除历史记录"
            />
            <Button 
              type="text" 
              icon={<CloseCircleOutlined />} 
              onClick={handleClose}
              title="关闭"
            />
          </Space>
        }
        className="history-card"
      >
        <div className="history-container">
          <div className="history-list-container">
            {history.length > 0 ? (
              <List
                className="history-list"
                dataSource={history}
                renderItem={renderHistoryItem}
              />
            ) : (
              <Empty description="暂无历史记录" />
            )}
          </div>
          
          <div className="history-detail-container">
            {renderHistoryDetail()}
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ConflictHistoryPanel;
