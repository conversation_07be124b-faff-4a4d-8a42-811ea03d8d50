/**
 * 实时协作面板样式
 */

.realtime-collaboration-panel {
  .ant-modal-body {
    padding: 16px;
    max-height: 70vh;
    overflow-y: auto;
  }

  // 用户列表
  .users-list {
    .users-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      .ant-typography {
        margin-bottom: 0;
      }
    }

    .user-item {
      padding: 8px 0;
      border-bottom: 1px solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .user-info {
        display: flex;
        align-items: center;
      }

      .user-actions {
        display: flex;
        gap: 4px;
        opacity: 0;
        transition: opacity 0.2s ease;
      }

      &:hover .user-actions {
        opacity: 1;
      }

      .ant-list-item-meta {
        .ant-list-item-meta-avatar {
          .ant-badge {
            .ant-avatar {
              border: 2px solid #ffffff;
            }
          }
        }

        .ant-list-item-meta-title {
          margin-bottom: 4px;
          font-size: 14px;
        }

        .ant-list-item-meta-description {
          font-size: 12px;
        }
      }
    }
  }

  // 编辑区域
  .editing-zones {
    margin-bottom: 24px;

    .ant-typography {
      margin-bottom: 12px;
    }

    .editing-zone-item {
      padding: 8px 0;
      border-bottom: 1px solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .ant-list-item-meta {
        .ant-list-item-meta-title {
          font-size: 13px;
          margin-bottom: 4px;
        }

        .ant-list-item-meta-description {
          font-size: 11px;
        }
      }
    }
  }

  // 活动历史
  .activity-history {
    .ant-typography {
      margin-bottom: 12px;
    }

    .ant-timeline {
      .ant-timeline-item {
        padding-bottom: 8px;

        .activity-item {
          .ant-space {
            align-items: flex-start;
          }
        }
      }
    }
  }

  // 连接状态
  .connection-status {
    .ant-card {
      .ant-card-body {
        padding: 16px;
      }

      .ant-statistic {
        .ant-statistic-title {
          font-size: 12px;
          color: #8c8c8c;
        }

        .ant-statistic-content {
          font-size: 14px;
          font-weight: 600;
        }
      }
    }
  }
}

// 用户状态指示器
.realtime-collaboration-panel {
  .user-status-online {
    .ant-badge-status-dot {
      background-color: #52c41a;
    }
  }

  .user-status-away {
    .ant-badge-status-dot {
      background-color: #faad14;
    }
  }

  .user-status-busy {
    .ant-badge-status-dot {
      background-color: #ff4d4f;
    }
  }

  .user-status-offline {
    .ant-badge-status-dot {
      background-color: #d9d9d9;
    }
  }
}

// 编辑区域状态
.realtime-collaboration-panel {
  .editing-zone-active {
    background-color: #f6ffed;
    border-left: 3px solid #52c41a;
    padding-left: 8px;
  }

  .editing-zone-inactive {
    background-color: #f5f5f5;
    border-left: 3px solid #d9d9d9;
    padding-left: 8px;
  }
}

// 深色主题
.dark-theme {
  .realtime-collaboration-panel {
    .ant-modal-content {
      background: #2d2d2d;
      color: #cccccc;
    }

    .users-list {
      .users-header {
        border-bottom-color: #404040;

        .ant-typography {
          color: #ffffff;
        }
      }

      .user-item {
        border-bottom-color: #404040;

        .ant-list-item-meta {
          .ant-list-item-meta-title {
            color: #ffffff;
          }

          .ant-list-item-meta-description {
            color: #cccccc;
          }
        }
      }
    }

    .editing-zones {
      .ant-typography {
        color: #ffffff;
      }

      .editing-zone-item {
        border-bottom-color: #404040;

        .ant-list-item-meta {
          .ant-list-item-meta-title {
            color: #ffffff;
          }

          .ant-list-item-meta-description {
            color: #cccccc;
          }
        }
      }
    }

    .activity-history {
      .ant-typography {
        color: #ffffff;
      }

      .ant-timeline {
        .ant-timeline-item {
          .activity-item {
            color: #cccccc;
          }
        }
      }
    }

    .connection-status {
      .ant-card {
        background: #2d2d2d;
        border-color: #404040;

        .ant-card-body {
          background: #2d2d2d;
        }

        .ant-statistic {
          .ant-statistic-title {
            color: #8c8c8c;
          }

          .ant-statistic-content {
            color: #ffffff;
          }
        }
      }
    }
  }
}

// 紧凑模式
.compact-theme {
  .realtime-collaboration-panel {
    .ant-modal-body {
      padding: 12px;
    }

    .users-list {
      .users-header {
        margin-bottom: 12px;
        padding-bottom: 6px;
      }

      .user-item {
        padding: 6px 0;

        .ant-list-item-meta {
          .ant-list-item-meta-title {
            font-size: 13px;
          }

          .ant-list-item-meta-description {
            font-size: 11px;
          }
        }
      }
    }

    .editing-zones {
      margin-bottom: 16px;

      .editing-zone-item {
        padding: 6px 0;
      }
    }

    .activity-history {
      .ant-timeline {
        .ant-timeline-item {
          padding-bottom: 6px;
        }
      }
    }

    .connection-status {
      .ant-card {
        .ant-card-body {
          padding: 12px;
        }

        .ant-statistic {
          .ant-statistic-content {
            font-size: 12px;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .realtime-collaboration-panel {
    .ant-modal {
      width: 95% !important;
      max-width: none;
    }

    .ant-row {
      .ant-col {
        margin-bottom: 16px;
      }
    }

    .users-list {
      .users-header {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
      }
    }

    .connection-status {
      .ant-row {
        .ant-col {
          flex: 0 0 50%;
          max-width: 50%;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .realtime-collaboration-panel {
    .connection-status {
      .ant-row {
        .ant-col {
          flex: 0 0 100%;
          max-width: 100%;
          margin-bottom: 8px;
        }
      }
    }
  }
}

// 动画效果
.realtime-collaboration-panel {
  .user-item {
    transition: all 0.2s ease;

    &:hover {
      background-color: #f5f5f5;
    }
  }

  .editing-zone-item {
    transition: all 0.2s ease;

    &:hover {
      background-color: #f5f5f5;
    }
  }

  .user-actions {
    transition: opacity 0.2s ease;
  }

  .ant-btn {
    transition: all 0.2s ease;
  }

  .ant-avatar {
    transition: all 0.2s ease;
  }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
  .realtime-collaboration-panel {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .realtime-collaboration-panel {
    .user-item {
      border: 1px solid #000;
      margin-bottom: 4px;
    }

    .editing-zone-item {
      border: 1px solid #000;
      margin-bottom: 4px;
    }

    .connection-status {
      .ant-card {
        border: 2px solid #000;
      }
    }
  }
}

// 协作特定样式
.realtime-collaboration-panel {
  .typing-indicator {
    animation: typing 1.5s infinite;
  }

  .cursor-indicator {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: -2px;
      width: 2px;
      height: 100%;
      background: currentColor;
      animation: blink 1s infinite;
    }
  }

  .selection-indicator {
    background: rgba(24, 144, 255, 0.2);
    border: 1px solid #1890ff;
  }

  .conflict-indicator {
    background: rgba(255, 77, 79, 0.2);
    border: 1px solid #ff4d4f;
  }

  .sync-indicator {
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, transparent, rgba(24, 144, 255, 0.3), transparent);
      animation: syncing 2s infinite;
    }
  }

  .user-presence-indicator {
    &.online {
      color: #52c41a;
    }

    &.away {
      color: #faad14;
    }

    &.busy {
      color: #ff4d4f;
    }

    &.offline {
      color: #d9d9d9;
    }
  }
}

@keyframes typing {
  0%, 60%, 100% {
    opacity: 1;
  }
  30% {
    opacity: 0.5;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

@keyframes syncing {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
