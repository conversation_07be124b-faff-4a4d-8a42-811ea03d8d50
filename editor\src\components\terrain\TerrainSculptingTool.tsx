/**
 * 地形雕刻工具组件
 * 用于雕刻地形
 */
import React, { useState } from 'react';
import { 
  Form, 
  InputNumber, 
  Button, 
  Select, 
  Slider, 
  Radio, 
  Card, 
  Row, 
  Col, 
  Tooltip, 
  Switch, 
  Typography 
} from 'antd';
import {
  PlusOutlined,
  MinusOutlined,
  SyncOutlined,
  DragOutlined,
  HighlightOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import { updateEditSettings } from '../../store/terrain/terrainSlice';

// 移除引擎直接导入
import './TerrainSculptingTool.less';

const { Option } = Select;
const { Text } = Typography;

// 笔刷类型枚举
enum BrushType {
  RAISE = 'raise',
  LOWER = 'lower',
  SMOOTH = 'smooth',
  FLATTEN = 'flatten',
  NOISE = 'noise',
  PAINT = 'paint'
}

// 笔刷形状枚举
enum BrushShape {
  CIRCLE = 'circle',
  SQUARE = 'square'
}

/**
 * 地形雕刻工具属性
 */
interface TerrainSculptingToolProps {
  /** 实体ID */
  entityId?: string;
  /** 是否可编辑 */
  editable?: boolean;
  /** 地形修改回调 */
  onTerrainModified?: () => void;
  /** 操作回调 */
  onOperation?: (operation: any) => void;
}

/**
 * 雕刻操作接口
 */
interface SculptingOperation {
  brushType: string;
  brushShape: string;
  brushSize: number;
  brushStrength: number;
  brushFalloff: number;
  targetHeight?: number;
  position: { x: number; z: number };
}

/**
 * 地形雕刻工具组件
 */
const TerrainSculptingTool: React.FC<TerrainSculptingToolProps> = ({
  entityId,
  editable = true,
  onTerrainModified,
  onOperation
}) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  // 从Redux获取地形数据
  const editSettings = useAppSelector(state => state.terrain.editSettings);

  // 状态
  const [brushType, setBrushType] = useState<BrushType>(BrushType.RAISE);
  const [brushShape, setBrushShape] = useState<BrushShape>(BrushShape.CIRCLE);
  const [brushSize, setBrushSize] = useState<number>(10);
  const [brushStrength, setBrushStrength] = useState<number>(0.5);
  const [brushFalloff, setBrushFalloff] = useState<number>(2);
  const [targetHeight, setTargetHeight] = useState<number>(0.5);
  const [noiseSeed, setNoiseSeed] = useState<number>(Math.random() * 1000);
  const [noiseScale, setNoiseScale] = useState<number>(0.1);
  const [showBrushPreview, setShowBrushPreview] = useState<boolean>(true);
  const [showHeightContours, setShowHeightContours] = useState<boolean>(false);
  const [showSlopeOverlay, setShowSlopeOverlay] = useState<boolean>(false);
  const [heightLimitEnabled, setHeightLimitEnabled] = useState<boolean>(false);
  const [minHeightLimit, setMinHeightLimit] = useState<number>(0);
  const [maxHeightLimit, setMaxHeightLimit] = useState<number>(1);
  const [slopeLimitEnabled, setSlopeLimitEnabled] = useState<boolean>(false);
  const [minSlopeLimit, setMinSlopeLimit] = useState<number>(0);
  const [maxSlopeLimit, setMaxSlopeLimit] = useState<number>(90);

  // 引用（暂时注释掉未使用的部分）
  // const brushPreviewRef = useRef<any>(null);

  // 同步Redux状态到本地状态
  React.useEffect(() => {
    if (editSettings) {
      setBrushType(editSettings.brushType as BrushType);
      setBrushShape(editSettings.brushShape as BrushShape);
      setBrushSize(editSettings.brushSize);
      setBrushStrength(editSettings.brushStrength);
      setBrushFalloff(editSettings.brushFalloff);
      setTargetHeight(editSettings.targetHeight);
      setShowBrushPreview(editSettings.showBrushPreview);
      setShowHeightContours(editSettings.showHeightContours);
      setShowSlopeOverlay(editSettings.showSlopeOverlay);
    }
  }, [editSettings]);


  
  // 处理笔刷类型变更
  const handleBrushTypeChange = (value: BrushType) => {
    setBrushType(value);
    dispatch(updateEditSettings({ brushType: value }));
  };

  // 处理笔刷形状变更
  const handleBrushShapeChange = (value: BrushShape) => {
    setBrushShape(value);
    dispatch(updateEditSettings({ brushShape: value }));
  };

  // 处理笔刷大小变更
  const handleBrushSizeChange = (value: number) => {
    setBrushSize(value);
    dispatch(updateEditSettings({ brushSize: value }));
  };

  // 处理笔刷强度变更
  const handleBrushStrengthChange = (value: number) => {
    setBrushStrength(value);
    dispatch(updateEditSettings({ brushStrength: value }));
  };

  // 处理笔刷衰减变更
  const handleBrushFalloffChange = (value: number) => {
    setBrushFalloff(value);
    dispatch(updateEditSettings({ brushFalloff: value }));
  };

  // 处理目标高度变更
  const handleTargetHeightChange = (value: number) => {
    setTargetHeight(value);
    dispatch(updateEditSettings({ targetHeight: value }));
  };
  
  // 处理噪声种子变更
  const handleNoiseSeedChange = (value: number | null) => {
    if (value !== null) {
      setNoiseSeed(value);
    }
  };
  
  // 处理噪声比例变更
  const handleNoiseScaleChange = (value: number) => {
    setNoiseScale(value);
  };
  
  // 处理显示笔刷预览变更
  const handleShowBrushPreviewChange = (checked: boolean) => {
    setShowBrushPreview(checked);
  };
  
  // 处理显示高度等高线变更
  const handleShowHeightContoursChange = (checked: boolean) => {
    setShowHeightContours(checked);
  };
  
  // 处理显示斜度覆盖变更
  const handleShowSlopeOverlayChange = (checked: boolean) => {
    setShowSlopeOverlay(checked);
  };
  
  // 处理高度限制启用变更
  const handleHeightLimitEnabledChange = (checked: boolean) => {
    setHeightLimitEnabled(checked);
  };
  
  // 处理最小高度限制变更
  const handleMinHeightLimitChange = (value: number) => {
    setMinHeightLimit(value);
  };
  
  // 处理最大高度限制变更
  const handleMaxHeightLimitChange = (value: number) => {
    setMaxHeightLimit(value);
  };
  
  // 处理斜度限制启用变更
  const handleSlopeLimitEnabledChange = (checked: boolean) => {
    setSlopeLimitEnabled(checked);
  };
  
  // 处理最小斜度限制变更
  const handleMinSlopeLimitChange = (value: number) => {
    setMinSlopeLimit(value);
  };
  
  // 处理最大斜度限制变更
  const handleMaxSlopeLimitChange = (value: number) => {
    setMaxSlopeLimit(value);
  };
  
  // 处理随机噪声种子
  const handleRandomNoiseSeed = () => {
    setNoiseSeed(Math.random() * 1000);
  };

  // 执行雕刻操作
  const performSculpting = async (position: { x: number; z: number }) => {
    if (!entityId || !editable) return;

    try {
      // 动态导入地形引擎服务
      const { terrainEngineService } = await import('../../services/TerrainEngineService');

      const operation: SculptingOperation = {
        brushType,
        brushShape,
        brushSize,
        brushStrength,
        brushFalloff,
        targetHeight: brushType === BrushType.FLATTEN ? targetHeight : undefined,
        position
      };

      // 执行雕刻
      await terrainEngineService.sculptTerrain(entityId, operation);

      // 通知回调
      if (onTerrainModified) {
        onTerrainModified();
      }

      if (onOperation) {
        onOperation(operation);
      }
    } catch (error: any) {
      console.error('地形雕刻失败:', error);
    }
  };

  // 处理地形点击（模拟雕刻操作）
  const handleTerrainClick = (event: React.MouseEvent) => {
    if (!editable) return;

    // 这里应该从3D视图获取实际的世界坐标
    // 暂时使用模拟坐标
    const rect = (event.target as HTMLElement).getBoundingClientRect();
    const x = ((event.clientX - rect.left) / rect.width - 0.5) * 1000; // 假设地形宽度1000
    const z = ((event.clientY - rect.top) / rect.height - 0.5) * 1000; // 假设地形高度1000

    performSculpting({ x, z });
  };
  
  // 获取笔刷图标（暂时注释掉未使用的函数）
  // const getBrushTypeIcon = (type: BrushType) => {
  //   switch (type) {
  //     case BrushType.RAISE:
  //       return <PlusOutlined />;
  //     case BrushType.LOWER:
  //       return <MinusOutlined />;
  //     case BrushType.SMOOTH:
  //       return <SyncOutlined />;
  //     case BrushType.FLATTEN:
  //       return <DragOutlined />;
  //     case BrushType.NOISE:
  //       return <HighlightOutlined />;
  //     case BrushType.PAINT:
  //       return <BgColorsOutlined />;
  //     default:
  //       return <PlusOutlined />;
  //   }
  // };
  
  // 获取笔刷类型选项
  const brushTypeOptions = [
    { value: BrushType.RAISE, label: t('terrain.brushTypes.raise'), icon: <PlusOutlined /> },
    { value: BrushType.LOWER, label: t('terrain.brushTypes.lower'), icon: <MinusOutlined /> },
    { value: BrushType.SMOOTH, label: t('terrain.brushTypes.smooth'), icon: <SyncOutlined /> },
    { value: BrushType.FLATTEN, label: t('terrain.brushTypes.flatten'), icon: <DragOutlined /> },
    { value: BrushType.NOISE, label: t('terrain.brushTypes.noise'), icon: <HighlightOutlined /> }
  ];
  
  // 获取笔刷形状选项
  const brushShapeOptions = [
    { value: BrushShape.CIRCLE, label: t('terrain.brushShapes.circle') },
    { value: BrushShape.SQUARE, label: t('terrain.brushShapes.square') }
  ];
  
  return (
    <div className="terrain-sculpting-tool">
      <Row gutter={[16, 16]}>
        <Col span={16}>
          <Card title={t('terrain.sculpting.brushSettings')} className="brush-settings-card">
            <Form layout="vertical">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Form.Item label={t('terrain.sculpting.brushType')}>
                    <Radio.Group 
                      value={brushType} 
                      onChange={(e) => handleBrushTypeChange(e.target.value)}
                      disabled={!editable}
                    >
                      {brushTypeOptions.map(option => (
                        <Tooltip key={option.value} title={option.label}>
                          <Radio.Button value={option.value}>
                            {option.icon}
                          </Radio.Button>
                        </Tooltip>
                      ))}
                    </Radio.Group>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label={t('terrain.sculpting.brushShape')}>
                    <Select 
                      value={brushShape} 
                      onChange={handleBrushShapeChange}
                      disabled={!editable}
                    >
                      {brushShapeOptions.map(option => (
                        <Option key={option.value} value={option.value}>
                          {option.label}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              
              <Form.Item 
                label={
                  <span>
                    {t('terrain.sculpting.brushSize')}
                    <Tooltip title={t('terrain.sculpting.brushSizeTooltip')}>
                      <InfoCircleOutlined style={{ marginLeft: 8 }} />
                    </Tooltip>
                  </span>
                }
              >
                <Slider
                  min={1}
                  max={100}
                  value={brushSize}
                  onChange={handleBrushSizeChange}
                  disabled={!editable}
                />
              </Form.Item>
              
              <Form.Item 
                label={
                  <span>
                    {t('terrain.sculpting.brushStrength')}
                    <Tooltip title={t('terrain.sculpting.brushStrengthTooltip')}>
                      <InfoCircleOutlined style={{ marginLeft: 8 }} />
                    </Tooltip>
                  </span>
                }
              >
                <Slider
                  min={0.01}
                  max={1}
                  step={0.01}
                  value={brushStrength}
                  onChange={handleBrushStrengthChange}
                  disabled={!editable}
                />
              </Form.Item>
              
              <Form.Item 
                label={
                  <span>
                    {t('terrain.sculpting.brushFalloff')}
                    <Tooltip title={t('terrain.sculpting.brushFalloffTooltip')}>
                      <InfoCircleOutlined style={{ marginLeft: 8 }} />
                    </Tooltip>
                  </span>
                }
              >
                <Slider
                  min={0.1}
                  max={5}
                  step={0.1}
                  value={brushFalloff}
                  onChange={handleBrushFalloffChange}
                  disabled={!editable}
                />
              </Form.Item>
              
              {brushType === BrushType.FLATTEN && (
                <Form.Item 
                  label={
                    <span>
                      {t('terrain.sculpting.targetHeight')}
                      <Tooltip title={t('terrain.sculpting.targetHeightTooltip')}>
                        <InfoCircleOutlined style={{ marginLeft: 8 }} />
                      </Tooltip>
                    </span>
                  }
                >
                  <Slider
                    min={0}
                    max={1}
                    step={0.01}
                    value={targetHeight}
                    onChange={handleTargetHeightChange}
                    disabled={!editable}
                  />
                </Form.Item>
              )}
              
              {brushType === BrushType.NOISE && (
                <>
                  <Form.Item 
                    label={
                      <span>
                        {t('terrain.sculpting.noiseSeed')}
                        <Tooltip title={t('terrain.sculpting.noiseSeedTooltip')}>
                          <InfoCircleOutlined style={{ marginLeft: 8 }} />
                        </Tooltip>
                      </span>
                    }
                  >
                    <InputNumber
                      value={noiseSeed}
                      onChange={handleNoiseSeedChange}
                      disabled={!editable}
                      style={{ width: '80%' }}
                    />
                    <Button
                      icon={<SyncOutlined />}
                      onClick={handleRandomNoiseSeed}
                      disabled={!editable}
                      style={{ marginLeft: 8 }}
                    />
                  </Form.Item>
                  
                  <Form.Item 
                    label={
                      <span>
                        {t('terrain.sculpting.noiseScale')}
                        <Tooltip title={t('terrain.sculpting.noiseScaleTooltip')}>
                          <InfoCircleOutlined style={{ marginLeft: 8 }} />
                        </Tooltip>
                      </span>
                    }
                  >
                    <Slider
                      min={0.01}
                      max={1}
                      step={0.01}
                      value={noiseScale}
                      onChange={handleNoiseScaleChange}
                      disabled={!editable}
                    />
                  </Form.Item>
                </>
              )}
            </Form>
          </Card>
        </Col>
        
        <Col span={8}>
          <Card title={t('terrain.sculpting.visualizationSettings')} className="visualization-settings-card">
            <Form layout="vertical">
              <Form.Item>
                <Switch
                  checked={showBrushPreview}
                  onChange={handleShowBrushPreviewChange}
                  disabled={!editable}
                />
                <Text style={{ marginLeft: 8 }}>{t('terrain.sculpting.showBrushPreview')}</Text>
              </Form.Item>
              
              <Form.Item>
                <Switch
                  checked={showHeightContours}
                  onChange={handleShowHeightContoursChange}
                  disabled={!editable}
                />
                <Text style={{ marginLeft: 8 }}>{t('terrain.sculpting.showHeightContours')}</Text>
              </Form.Item>
              
              <Form.Item>
                <Switch
                  checked={showSlopeOverlay}
                  onChange={handleShowSlopeOverlayChange}
                  disabled={!editable}
                />
                <Text style={{ marginLeft: 8 }}>{t('terrain.sculpting.showSlopeOverlay')}</Text>
              </Form.Item>
            </Form>
          </Card>
          
          <Card title={t('terrain.sculpting.limitSettings')} className="limit-settings-card" style={{ marginTop: 16 }}>
            <Form layout="vertical">
              <Form.Item>
                <Switch
                  checked={heightLimitEnabled}
                  onChange={handleHeightLimitEnabledChange}
                  disabled={!editable}
                />
                <Text style={{ marginLeft: 8 }}>{t('terrain.sculpting.enableHeightLimit')}</Text>
              </Form.Item>
              
              {heightLimitEnabled && (
                <>
                  <Form.Item label={t('terrain.sculpting.minHeightLimit')}>
                    <Slider
                      min={0}
                      max={1}
                      step={0.01}
                      value={minHeightLimit}
                      onChange={handleMinHeightLimitChange}
                      disabled={!editable}
                    />
                  </Form.Item>
                  
                  <Form.Item label={t('terrain.sculpting.maxHeightLimit')}>
                    <Slider
                      min={0}
                      max={1}
                      step={0.01}
                      value={maxHeightLimit}
                      onChange={handleMaxHeightLimitChange}
                      disabled={!editable}
                    />
                  </Form.Item>
                </>
              )}
              
              <Form.Item>
                <Switch
                  checked={slopeLimitEnabled}
                  onChange={handleSlopeLimitEnabledChange}
                  disabled={!editable}
                />
                <Text style={{ marginLeft: 8 }}>{t('terrain.sculpting.enableSlopeLimit')}</Text>
              </Form.Item>
              
              {slopeLimitEnabled && (
                <>
                  <Form.Item label={t('terrain.sculpting.minSlopeLimit')}>
                    <Slider
                      min={0}
                      max={90}
                      step={1}
                      value={minSlopeLimit}
                      onChange={handleMinSlopeLimitChange}
                      disabled={!editable}
                    />
                  </Form.Item>
                  
                  <Form.Item label={t('terrain.sculpting.maxSlopeLimit')}>
                    <Slider
                      min={0}
                      max={90}
                      step={1}
                      value={maxSlopeLimit}
                      onChange={handleMaxSlopeLimitChange}
                      disabled={!editable}
                    />
                  </Form.Item>
                </>
              )}
            </Form>
          </Card>
        </Col>
      </Row>

      {/* 测试区域 */}
      <Card title={t('terrain.sculpting.testArea')} style={{ marginTop: 16 }}>
        <div
          style={{
            width: '100%',
            height: 200,
            border: '1px dashed #d9d9d9',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: editable ? 'crosshair' : 'not-allowed',
            backgroundColor: '#fafafa'
          }}
          onClick={handleTerrainClick}
        >
          <Text type="secondary">
            {editable ? t('terrain.sculpting.clickToSculpt') : t('terrain.sculpting.readOnly')}
          </Text>
        </div>

        <div style={{ marginTop: 16, textAlign: 'center' }}>
          <Button
            type="primary"
            onClick={() => performSculpting({ x: 0, z: 0 })}
            disabled={!editable || !entityId}
          >
            {t('terrain.sculpting.testSculpt')}
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default TerrainSculptingTool;
