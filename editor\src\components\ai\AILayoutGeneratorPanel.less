/**
 * AI布局生成器面板样式
 */

.ai-layout-generator-panel {
  .ant-modal-body {
    padding: 16px;
    max-height: 80vh;
    overflow-y: auto;
  }

  .layout-generator-content {
    .ant-steps {
      margin-bottom: 24px;

      .ant-steps-item {
        .ant-steps-item-title {
          font-size: 14px;
        }
      }
    }

    .step-content {
      min-height: 400px;

      .step-card {
        .ant-card-head {
          .ant-card-head-title {
            font-size: 16px;
            font-weight: 600;
          }
        }

        .ant-card-body {
          padding: 24px;
        }
      }
    }
  }

  // 需求输入步骤
  .requirements-step {
    .ant-input {
      border-radius: 8px;
    }

    .ant-btn {
      border-radius: 8px;
      height: 40px;
      font-weight: 500;
    }
  }

  // 建议选择步骤
  .suggestions-step {
    .ant-list {
      .ant-list-item {
        padding: 16px;
        border: 2px solid transparent;
        border-radius: 8px;
        margin-bottom: 12px;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
        }

        &.selected {
          border-color: #1890ff;
          background-color: #e6f7ff;
        }

        .ant-list-item-meta {
          .ant-list-item-meta-avatar {
            margin-right: 16px;
          }

          .ant-list-item-meta-title {
            margin-bottom: 8px;
            font-size: 16px;

            .ant-rate {
              margin-left: 8px;
              font-size: 12px;
            }
          }

          .ant-list-item-meta-description {
            .ant-typography {
              margin-bottom: 8px;
            }

            .ant-space {
              margin-top: 8px;
            }
          }
        }
      }
    }
  }

  // 配置步骤
  .config-step {
    .ant-checkbox-group {
      .ant-checkbox-wrapper {
        display: block;
        margin-bottom: 8px;
        padding: 8px 0;

        &:hover {
          background-color: #f9f9f9;
          border-radius: 4px;
          padding-left: 8px;
          margin-left: -8px;
        }
      }
    }

    .ant-select {
      border-radius: 6px;
    }

    .generate-button {
      width: 100%;
      height: 48px;
      font-size: 16px;
      font-weight: 600;
      border-radius: 8px;
      background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
      border: none;
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);

      &:hover {
        background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
        box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
      }

      &:disabled {
        background: #f5f5f5;
        box-shadow: none;
      }
    }
  }

  // 结果步骤
  .results-step {
    .ant-list {
      .ant-list-item {
        .ant-card {
          border-radius: 8px;
          overflow: hidden;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
          }

          .ant-card-cover {
            position: relative;
            overflow: hidden;

            &::after {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
              transform: translateX(-100%);
              transition: transform 0.6s ease;
            }

            &:hover::after {
              transform: translateX(100%);
            }
          }

          .ant-card-actions {
            .anticon {
              font-size: 16px;
              color: #1890ff;
              transition: all 0.2s ease;

              &:hover {
                color: #40a9ff;
                transform: scale(1.1);
              }
            }
          }

          .ant-card-meta {
            .ant-card-meta-title {
              font-size: 16px;
              font-weight: 600;
              margin-bottom: 8px;
            }

            .ant-card-meta-description {
              .ant-space {
                margin-top: 8px;
              }
            }
          }
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;

    .empty-icon {
      font-size: 48px;
      color: #d9d9d9;
      margin-bottom: 16px;
    }

    .ant-typography {
      margin-bottom: 8px;
    }
  }

  .generating-alert {
    .ant-alert-icon {
      animation: spin 2s linear infinite;
    }
  }
}

// 深色主题
.dark-theme {
  .ai-layout-generator-panel {
    .ant-card {
      background: #2d2d2d;
      border-color: #404040;

      .ant-card-head {
        background: #2d2d2d;
        border-bottom-color: #404040;

        .ant-card-head-title {
          color: #ffffff;
        }
      }

      .ant-card-body {
        background: #2d2d2d;
        color: #cccccc;
      }
    }

    .suggestions-step {
      .ant-list {
        .ant-list-item {
          background: #2d2d2d;
          border-color: #404040;

          &:hover {
            border-color: #1890ff;
            background-color: #1f3a5f;
          }

          &.selected {
            background-color: #1f3a5f;
          }

          .ant-list-item-meta {
            .ant-list-item-meta-title {
              color: #ffffff;
            }

            .ant-list-item-meta-description {
              color: #cccccc;
            }
          }
        }
      }
    }

    .config-step {
      .ant-checkbox-group {
        .ant-checkbox-wrapper {
          color: #cccccc;

          &:hover {
            background-color: #404040;
          }
        }
      }
    }

    .results-step {
      .ant-list {
        .ant-list-item {
          .ant-card {
            background: #2d2d2d;
            border-color: #404040;

            .ant-card-meta {
              .ant-card-meta-title {
                color: #ffffff;
              }

              .ant-card-meta-description {
                color: #cccccc;
              }
            }
          }
        }
      }
    }

    .empty-state {
      .empty-icon {
        color: #666666;
      }
    }
  }
}

// 紧凑模式
.compact-theme {
  .ai-layout-generator-panel {
    .ant-modal-body {
      padding: 12px;
    }

    .layout-generator-content {
      .step-content {
        min-height: 350px;

        .step-card {
          .ant-card-body {
            padding: 16px;
          }
        }
      }
    }

    .suggestions-step {
      .ant-list {
        .ant-list-item {
          padding: 12px;
          margin-bottom: 8px;

          .ant-list-item-meta {
            .ant-list-item-meta-title {
              font-size: 14px;
            }
          }
        }
      }
    }

    .config-step {
      .generate-button {
        height: 40px;
        font-size: 14px;
      }
    }

    .results-step {
      .ant-list {
        .ant-list-item {
          .ant-card {
            .ant-card-meta {
              .ant-card-meta-title {
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .ai-layout-generator-panel {
    .ant-modal {
      width: 95% !important;
      max-width: none;
    }

    .layout-generator-content {
      .ant-steps {
        .ant-steps-item {
          .ant-steps-item-title {
            font-size: 12px;
          }
        }
      }

      .step-content {
        min-height: 300px;
      }
    }

    .config-step {
      .ant-row {
        .ant-col {
          margin-bottom: 16px;
        }
      }
    }

    .results-step {
      .ant-list {
        .ant-list-grid {
          // 移动端单列显示
          grid-template-columns: 1fr;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .ai-layout-generator-panel {
    .layout-generator-content {
      .ant-steps {
        .ant-steps-item {
          .ant-steps-item-title {
            display: none;
          }
        }
      }
    }

    .suggestions-step {
      .ant-list {
        .ant-list-item {
          .ant-list-item-meta {
            .ant-list-item-meta-title {
              font-size: 14px;

              .ant-rate {
                display: none;
              }
            }
          }
        }
      }
    }
  }
}

// 动画效果
.ai-layout-generator-panel {
  .suggestions-step {
    .ant-list-item {
      transition: all 0.3s ease;
    }
  }

  .results-step {
    .ant-card {
      transition: all 0.3s ease;
    }
  }

  .ant-btn {
    transition: all 0.2s ease;
  }

  .ant-steps {
    .ant-steps-item {
      transition: all 0.3s ease;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
  .ai-layout-generator-panel {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }

    .results-step {
      .ant-card {
        &:hover {
          transform: none;
        }
      }
    }

    .generating-alert {
      .ant-alert-icon {
        animation: none;
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .ai-layout-generator-panel {
    .ant-card {
      border: 2px solid #000;
    }

    .suggestions-step {
      .ant-list {
        .ant-list-item {
          border: 2px solid #000;

          &.selected {
            border-color: #0066cc;
            background-color: #e6f3ff;
          }
        }
      }
    }

    .ant-steps {
      .ant-steps-item {
        .ant-steps-item-icon {
          border: 2px solid #000;
        }
      }
    }
  }
}

// 布局生成器特定样式
.ai-layout-generator-panel {
  .layout-mode-grid {
    background: linear-gradient(45deg, #e6f7ff 0%, #f0f9ff 100%);
  }

  .layout-mode-flexbox {
    background: linear-gradient(45deg, #f6ffed 0%, #fcffe6 100%);
  }

  .layout-mode-dashboard {
    background: linear-gradient(45deg, #fff2e6 0%, #fff7e6 100%);
  }

  .confidence-high {
    color: #52c41a;
  }

  .confidence-medium {
    color: #faad14;
  }

  .confidence-low {
    color: #ff4d4f;
  }

  .device-icon {
    font-size: 16px;
    margin-right: 8px;
  }

  .layout-preview {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    overflow: hidden;
    background: #ffffff;
  }
}
