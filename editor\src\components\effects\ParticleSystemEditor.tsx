/**
 * 粒子系统编辑器
 */
import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Form,
  Input,
  InputNumber,
  Select,
  Button,
  Tabs,
  Switch,
  Slider,
  ColorPicker,
  Space,
  Divider,
  Upload,
  Row,
  Col
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  UploadOutlined
} from '@ant-design/icons';

const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 粒子系统编辑器属性
 */
interface ParticleSystemEditorProps {
  /** 组件数据 */
  data?: any;
  /** 数据更新回调 */
  onChange?: (data: any) => void;
  /** 是否显示预览 */
  showPreview?: boolean;
  /** 预览画布宽度 */
  previewWidth?: number;
  /** 预览画布高度 */
  previewHeight?: number;
}

/**
 * 粒子发射器形状类型
 */
enum EmitterShapeType {
  POINT = 'point',
  CIRCLE = 'circle',
  BOX = 'box',
  SPHERE = 'sphere',
  CONE = 'cone',
  HEMISPHERE = 'hemisphere'
}

/**
 * 粒子混合模式
 */
enum BlendMode {
  NORMAL = 'normal',
  ADD = 'add',
  MULTIPLY = 'multiply',
  SCREEN = 'screen',
  OVERLAY = 'overlay'
}

/**
 * 粒子渲染模式
 */
enum RenderMode {
  BILLBOARD = 'billboard',
  STRETCHED = 'stretched',
  MESH = 'mesh'
}

/**
 * 粒子系统数据接口
 */
interface ParticleSystemData {
  // 基础属性
  enabled: boolean;
  name: string;

  // 发射器属性
  maxParticles: number;
  emissionRate: number;
  duration: number;
  loop: boolean;
  prewarm: boolean;

  // 粒子生命周期
  startLifetime: { min: number; max: number };

  // 粒子外观
  startSize: { min: number; max: number };
  endSize: { min: number; max: number };
  startColor: string;
  endColor: string;

  // 粒子运动
  startSpeed: { min: number; max: number };
  gravity: { x: number; y: number; z: number };
  drag: number;

  // 发射器形状
  shapeType: EmitterShapeType;
  shapeRadius: number;
  shapeSize: { x: number; y: number; z: number };
  shapeAngle: number;

  // 渲染设置
  blendMode: BlendMode;
  renderMode: RenderMode;
  texture: string;

  // 高级设置
  simulationSpace: 'local' | 'world';
  enableCollision: boolean;
  enablePhysics: boolean;
  enableSorting: boolean;
}

/**
 * 预设效果配置
 */
const PARTICLE_PRESETS = {
  fire: {
    name: '火焰',
    maxParticles: 1000,
    emissionRate: 100,
    duration: 0,
    loop: true,
    prewarm: false,
    startLifetime: { min: 1.0, max: 2.0 },
    startSize: { min: 0.5, max: 1.0 },
    endSize: { min: 0.1, max: 0.3 },
    startColor: '#ff7700',
    endColor: '#ff000000',
    startSpeed: { min: 1.0, max: 2.0 },
    gravity: { x: 0, y: 1.0, z: 0 },
    drag: 0.1,
    shapeType: EmitterShapeType.POINT,
    shapeRadius: 0,
    shapeSize: { x: 0, y: 0, z: 0 },
    shapeAngle: 0,
    blendMode: BlendMode.ADD,
    renderMode: RenderMode.BILLBOARD,
    texture: '',
    simulationSpace: 'local' as const,
    enableCollision: false,
    enablePhysics: false,
    enableSorting: false
  },
  smoke: {
    name: '烟雾',
    maxParticles: 500,
    emissionRate: 50,
    duration: 0,
    loop: true,
    prewarm: false,
    startLifetime: { min: 2.0, max: 4.0 },
    startSize: { min: 0.3, max: 0.5 },
    endSize: { min: 1.0, max: 2.0 },
    startColor: '#555555',
    endColor: '#55555500',
    startSpeed: { min: 0.5, max: 1.0 },
    gravity: { x: 0, y: 0.2, z: 0 },
    drag: 0.2,
    shapeType: EmitterShapeType.CIRCLE,
    shapeRadius: 0.5,
    shapeSize: { x: 0, y: 0, z: 0 },
    shapeAngle: 0,
    blendMode: BlendMode.NORMAL,
    renderMode: RenderMode.BILLBOARD,
    texture: '',
    simulationSpace: 'local' as const,
    enableCollision: false,
    enablePhysics: false,
    enableSorting: false
  },
  snow: {
    name: '雪花',
    maxParticles: 2000,
    emissionRate: 200,
    duration: 0,
    loop: true,
    prewarm: false,
    startLifetime: { min: 5.0, max: 10.0 },
    startSize: { min: 0.05, max: 0.1 },
    endSize: { min: 0.05, max: 0.1 },
    startColor: '#ffffff',
    endColor: '#ffffff',
    startSpeed: { min: 0.5, max: 1.0 },
    gravity: { x: 0, y: -0.1, z: 0 },
    drag: 0.05,
    shapeType: EmitterShapeType.BOX,
    shapeRadius: 0,
    shapeSize: { x: 10, y: 0, z: 10 },
    shapeAngle: 0,
    blendMode: BlendMode.NORMAL,
    renderMode: RenderMode.BILLBOARD,
    texture: '',
    simulationSpace: 'world' as const,
    enableCollision: false,
    enablePhysics: false,
    enableSorting: false
  },
  explosion: {
    name: '爆炸',
    maxParticles: 1500,
    emissionRate: 500,
    duration: 0.5,
    loop: false,
    prewarm: false,
    startLifetime: { min: 0.5, max: 1.5 },
    startSize: { min: 0.2, max: 0.8 },
    endSize: { min: 0.05, max: 0.2 },
    startColor: '#ffaa00',
    endColor: '#ff000000',
    startSpeed: { min: 3.0, max: 8.0 },
    gravity: { x: 0, y: -2.0, z: 0 },
    drag: 0.3,
    shapeType: EmitterShapeType.SPHERE,
    shapeRadius: 0.2,
    shapeSize: { x: 0, y: 0, z: 0 },
    shapeAngle: 0,
    blendMode: BlendMode.ADD,
    renderMode: RenderMode.BILLBOARD,
    texture: '',
    simulationSpace: 'local' as const,
    enableCollision: false,
    enablePhysics: false,
    enableSorting: false
  }
};

/**
 * 默认粒子系统数据
 */
const DEFAULT_PARTICLE_DATA: ParticleSystemData = {
  enabled: true,
  name: '新粒子系统',
  maxParticles: 1000,
  emissionRate: 100,
  duration: 0,
  loop: true,
  prewarm: false,
  startLifetime: { min: 1.0, max: 2.0 },
  startSize: { min: 0.1, max: 0.3 },
  endSize: { min: 0.05, max: 0.1 },
  startColor: '#ffffff',
  endColor: '#ffffff00',
  startSpeed: { min: 1.0, max: 2.0 },
  gravity: { x: 0, y: -0.5, z: 0 },
  drag: 0,
  shapeType: EmitterShapeType.POINT,
  shapeRadius: 0,
  shapeSize: { x: 0, y: 0, z: 0 },
  shapeAngle: 0,
  blendMode: BlendMode.ADD,
  renderMode: RenderMode.BILLBOARD,
  texture: '',
  simulationSpace: 'local',
  enableCollision: false,
  enablePhysics: false,
  enableSorting: false
};

/**
 * 粒子系统编辑器组件
 */
const ParticleSystemEditor: React.FC<ParticleSystemEditorProps> = ({
  data,
  onChange,
  showPreview = true,
  previewWidth = 400,
  previewHeight = 300
}) => {
  const [form] = Form.useForm();
  const previewCanvasRef = useRef<HTMLCanvasElement>(null);
  const [isPlaying, setIsPlaying] = useState(true);
  const [activeTab, setActiveTab] = useState('emission');
  const [selectedPreset, setSelectedPreset] = useState<string>('');

  // 合并默认数据和传入数据
  const particleData = { ...DEFAULT_PARTICLE_DATA, ...data };

  // 初始化表单值
  useEffect(() => {
    form.setFieldsValue(particleData);
  }, [form, particleData]);

  // 处理数据变化
  const handleValuesChange = (_changedValues: any, allValues: any) => {
    const newData = { ...particleData, ...allValues };
    if (onChange) {
      onChange(newData);
    }
  };

  // 处理预设选择
  const handlePresetSelect = (presetKey: string) => {
    const preset = PARTICLE_PRESETS[presetKey as keyof typeof PARTICLE_PRESETS];
    if (preset) {
      const newData = { ...particleData, ...preset };
      form.setFieldsValue(newData);
      setSelectedPreset(presetKey);
      if (onChange) {
        onChange(newData);
      }
    }
  };

  // 处理播放/暂停
  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
    // 这里可以添加实际的粒子系统播放/暂停逻辑
  };

  // 处理重置
  const handleReset = () => {
    // 这里可以添加实际的粒子系统重置逻辑
  };

  // 渲染范围输入组件
  const renderRangeInput = (
    namePrefix: string[],
    label: string,
    min: number = 0,
    max?: number,
    step: number = 0.1,
    unit?: string
  ) => (
    <Form.Item label={label}>
      <Space.Compact style={{ width: '100%' }}>
        <Form.Item name={[...namePrefix, 'min']} noStyle>
          <InputNumber
            min={min}
            max={max}
            step={step}
            style={{ width: '50%' }}
            placeholder="最小值"
            addonAfter={unit}
          />
        </Form.Item>
        <Form.Item name={[...namePrefix, 'max']} noStyle>
          <InputNumber
            min={min}
            max={max}
            step={step}
            style={{ width: '50%' }}
            placeholder="最大值"
            addonAfter={unit}
          />
        </Form.Item>
      </Space.Compact>
    </Form.Item>
  );

  // 渲染向量输入组件
  const renderVectorInput = (
    name: string[],
    label: string,
    step: number = 0.1,
    labels: string[] = ['X', 'Y', 'Z']
  ) => (
    <Form.Item label={label}>
      <Space.Compact style={{ width: '100%' }}>
        {labels.map((axisLabel, index) => (
          <Form.Item key={index} name={[...name, index === 0 ? 'x' : index === 1 ? 'y' : 'z']} noStyle>
            <InputNumber
              step={step}
              style={{ width: `${100 / labels.length}%` }}
              placeholder={axisLabel}
            />
          </Form.Item>
        ))}
      </Space.Compact>
    </Form.Item>
  );

  return (
    <Card title="粒子系统编辑器" size="small">
      <div style={{ display: 'flex', gap: '16px' }}>
        {/* 预览区域 */}
        {showPreview && (
          <div style={{ width: previewWidth, flexShrink: 0 }}>
            <Card title="预览" size="small">
              <div style={{ position: 'relative' }}>
                <canvas
                  ref={previewCanvasRef}
                  width={previewWidth - 32}
                  height={previewHeight}
                  style={{
                    width: '100%',
                    height: previewHeight,
                    border: '1px solid #d9d9d9',
                    borderRadius: '6px',
                    backgroundColor: '#000'
                  }}
                />
                <div style={{ marginTop: '8px', textAlign: 'center' }}>
                  <Space>
                    <Button
                      icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                      onClick={handlePlayPause}
                      size="small"
                    >
                      {isPlaying ? '暂停' : '播放'}
                    </Button>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={handleReset}
                      size="small"
                    >
                      重置
                    </Button>
                  </Space>
                </div>
              </div>

              {/* 预设选择 */}
              <Divider orientation="left" style={{ margin: '16px 0 8px 0' }}>
                预设效果
              </Divider>
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
                {Object.entries(PARTICLE_PRESETS).map(([key, preset]) => (
                  <Button
                    key={key}
                    size="small"
                    type={selectedPreset === key ? 'primary' : 'default'}
                    onClick={() => handlePresetSelect(key)}
                  >
                    {preset.name}
                  </Button>
                ))}
              </div>
            </Card>
          </div>
        )}

        {/* 编辑区域 */}
        <div style={{ flex: 1 }}>
          <Form
            form={form}
            layout="vertical"
            initialValues={particleData}
            onValuesChange={handleValuesChange}
            size="small"
          >
            {/* 基础设置 */}
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="启用" name="enabled" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="名称" name="name" rules={[{ required: true, message: '请输入名称' }]}>
                  <Input placeholder="粒子系统名称" />
                </Form.Item>
              </Col>
            </Row>

            {/* 标签页内容 */}
            <Tabs activeKey={activeTab} onChange={setActiveTab} size="small">
              {/* 发射器设置 */}
              <TabPane tab="发射器" key="emission">
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item label="最大粒子数" name="maxParticles" rules={[{ required: true }]}>
                      <InputNumber min={1} max={10000} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="发射率" name="emissionRate" rules={[{ required: true }]}>
                      <InputNumber min={0} max={1000} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item label="持续时间" name="duration">
                      <InputNumber min={0} step={0.1} style={{ width: '100%' }} placeholder="0表示无限" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="循环" name="loop" valuePropName="checked">
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item label="预热" name="prewarm" valuePropName="checked">
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="模拟空间" name="simulationSpace">
                      <Select>
                        <Option value="local">本地空间</Option>
                        <Option value="world">世界空间</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>

                {renderRangeInput(['startLifetime'], '生命周期', 0, undefined, 0.1, '秒')}
              </TabPane>

              {/* 粒子外观 */}
              <TabPane tab="外观" key="appearance">
                {renderRangeInput(['startSize'], '起始大小', 0, undefined, 0.01)}
                {renderRangeInput(['endSize'], '结束大小', 0, undefined, 0.01)}

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item label="起始颜色" name="startColor">
                      <ColorPicker showText style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="结束颜色" name="endColor">
                      <ColorPicker showText style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item label="混合模式" name="blendMode">
                      <Select>
                        <Option value={BlendMode.NORMAL}>正常</Option>
                        <Option value={BlendMode.ADD}>相加</Option>
                        <Option value={BlendMode.MULTIPLY}>相乘</Option>
                        <Option value={BlendMode.SCREEN}>滤色</Option>
                        <Option value={BlendMode.OVERLAY}>叠加</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="渲染模式" name="renderMode">
                      <Select>
                        <Option value={RenderMode.BILLBOARD}>广告牌</Option>
                        <Option value={RenderMode.STRETCHED}>拉伸</Option>
                        <Option value={RenderMode.MESH}>网格</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item label="纹理" name="texture">
                  <Input
                    placeholder="纹理路径"
                    addonAfter={
                      <Upload
                        showUploadList={false}
                        beforeUpload={() => false}
                        onChange={(info) => {
                          if (info.file) {
                            form.setFieldValue('texture', info.file.name);
                          }
                        }}
                      >
                        <Button icon={<UploadOutlined />} size="small" />
                      </Upload>
                    }
                  />
                </Form.Item>
              </TabPane>

              {/* 粒子运动 */}
              <TabPane tab="运动" key="motion">
                {renderRangeInput(['startSpeed'], '起始速度', 0, undefined, 0.1, 'm/s')}
                {renderVectorInput(['gravity'], '重力', 0.1)}

                <Form.Item label="阻力" name="drag">
                  <Slider min={0} max={1} step={0.01} style={{ width: '100%' }} />
                </Form.Item>
              </TabPane>

              {/* 发射器形状 */}
              <TabPane tab="形状" key="shape">
                <Form.Item label="发射器形状" name="shapeType">
                  <Select>
                    <Option value={EmitterShapeType.POINT}>点</Option>
                    <Option value={EmitterShapeType.CIRCLE}>圆形</Option>
                    <Option value={EmitterShapeType.BOX}>矩形</Option>
                    <Option value={EmitterShapeType.SPHERE}>球体</Option>
                    <Option value={EmitterShapeType.CONE}>圆锥</Option>
                    <Option value={EmitterShapeType.HEMISPHERE}>半球</Option>
                  </Select>
                </Form.Item>

                {(form.getFieldValue('shapeType') === EmitterShapeType.CIRCLE ||
                  form.getFieldValue('shapeType') === EmitterShapeType.SPHERE ||
                  form.getFieldValue('shapeType') === EmitterShapeType.HEMISPHERE) && (
                  <Form.Item label="半径" name="shapeRadius">
                    <InputNumber min={0} step={0.1} style={{ width: '100%' }} />
                  </Form.Item>
                )}

                {form.getFieldValue('shapeType') === EmitterShapeType.BOX && (
                  renderVectorInput(['shapeSize'], '尺寸', 0.1)
                )}

                {form.getFieldValue('shapeType') === EmitterShapeType.CONE && (
                  <>
                    <Form.Item label="半径" name="shapeRadius">
                      <InputNumber min={0} step={0.1} style={{ width: '100%' }} />
                    </Form.Item>
                    <Form.Item label="角度" name="shapeAngle">
                      <InputNumber min={0} max={180} step={1} style={{ width: '100%' }} addonAfter="°" />
                    </Form.Item>
                  </>
                )}
              </TabPane>

              {/* 高级设置 */}
              <TabPane tab="高级" key="advanced">
                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item label="启用碰撞" name="enableCollision" valuePropName="checked">
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="启用物理" name="enablePhysics" valuePropName="checked">
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="启用排序" name="enableSorting" valuePropName="checked">
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>
              </TabPane>
            </Tabs>
          </Form>
        </div>
      </div>
    </Card>
  );
};

export default ParticleSystemEditor;
