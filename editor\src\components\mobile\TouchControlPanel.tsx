/**
 * 触控控制面板组件
 * 提供触控友好的操作界面
 */
import React, { useState, useEffect, useRef } from 'react';
import { Button, Slider, Row, Col, Space, Tooltip, Card, Tabs, Switch} from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  ArrowLeftOutlined,
  ArrowRightOutlined,
  PlusOutlined,
  MinusOutlined,
  RotateLeftOutlined,
  RotateRightOutlined,
  ExpandOutlined,
  DeleteOutlined,
  CopyOutlined,
  UndoOutlined,
  RedoOutlined,
  SaveOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  QuestionCircleOutlined,
  ZoomInOutlined,
  ZoomOutOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  setTransformMode,
  setShowGrid,
  setShowAxes,
  TransformMode,
  undo,
  redo} from '../../store/editor/editorSlice';
import TouchInteractionService, { GestureType, GestureState } from '../../services/TouchInteractionService';
import MobileDeviceService, { ScreenOrientation } from '../../services/MobileDeviceService';
import './TouchControlPanel.less';

// 使用新的 Tabs API

/**
 * 触控控制面板组件
 */
const TouchControlPanel: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const panelRef = useRef<HTMLDivElement>(null);
  
  // 从Redux获取状态
  const {
    transformMode,
    showGrid,
    showAxes} = useAppSelector((state) => state.editor);

  // 本地状态
  const [activeTab, setActiveTab] = useState('transform');
  const [orientation, setOrientation] = useState(ScreenOrientation.LANDSCAPE);
  const [translateStep, setTranslateStep] = useState(1);
  const [rotateStep, setRotateStep] = useState(15);
  const [scaleStep, setScaleStep] = useState(0.1);
  
  // 初始化
  useEffect(() => {
    // 获取设备信息
    const deviceInfo = MobileDeviceService.getDeviceInfo();
    setOrientation(deviceInfo.orientation);
    
    // 监听设备变化
    MobileDeviceService.on('orientationChanged', (info: any) => {
      setOrientation(info.orientation);
    });
    
    // 初始化触控交互服务
    if (panelRef.current) {
      TouchInteractionService.initialize(panelRef.current);
      
      // 监听手势事件
      TouchInteractionService.on(GestureType.TAP, handleTapGesture);
      TouchInteractionService.on(GestureType.DOUBLE_TAP, handleDoubleTapGesture);
      TouchInteractionService.on(GestureType.LONG_PRESS, handleLongPressGesture);
      TouchInteractionService.on(GestureType.SWIPE, handleSwipeGesture);
      TouchInteractionService.on(GestureType.PINCH, handlePinchGesture);
      TouchInteractionService.on(GestureType.ROTATE, handleRotateGesture);
    }
    
    return () => {
      // 清理事件监听
      MobileDeviceService.removeAllListeners();
      TouchInteractionService.destroy();
    };
  }, []);
  
  // 处理点击手势
  const handleTapGesture = (data: any) => {
    if (data.state !== GestureState.ENDED) return;
    
    // 在实际项目中，这里可以实现点击手势的处理逻辑
    console.log('点击手势', data);
  };
  
  // 处理双击手势
  const handleDoubleTapGesture = (data: any) => {
    if (data.state !== GestureState.ENDED) return;
    
    // 在实际项目中，这里可以实现双击手势的处理逻辑
    console.log('双击手势', data);
  };
  
  // 处理长按手势
  const handleLongPressGesture = (data: any) => {
    if (data.state !== GestureState.ENDED) return;
    
    // 在实际项目中，这里可以实现长按手势的处理逻辑
    console.log('长按手势', data);
  };
  
  // 处理滑动手势
  const handleSwipeGesture = (data: any) => {
    if (data.state !== GestureState.ENDED) return;
    
    // 在实际项目中，这里可以实现滑动手势的处理逻辑
    console.log('滑动手势', data);
  };
  
  // 处理捏合手势
  const handlePinchGesture = (data: any) => {
    if (data.state !== GestureState.UPDATED) return;
    
    // 在实际项目中，这里可以实现捏合手势的处理逻辑
    console.log('捏合手势', data);
  };
  
  // 处理旋转手势
  const handleRotateGesture = (data: any) => {
    if (data.state !== GestureState.UPDATED) return;
    
    // 在实际项目中，这里可以实现旋转手势的处理逻辑
    console.log('旋转手势', data);
  };
  
  // 处理平移操作
  const handleTranslate = (direction: 'up' | 'down' | 'left' | 'right') => {
    // 在实际项目中，这里应该调用引擎的平移方法
    console.log(`平移 ${direction}，步长 ${translateStep}`);
  };
  
  // 处理旋转操作
  const handleRotate = (direction: 'left' | 'right') => {
    // 在实际项目中，这里应该调用引擎的旋转方法
    console.log(`旋转 ${direction}，步长 ${rotateStep}°`);
  };
  
  // 处理缩放操作
  const handleScale = (direction: 'up' | 'down') => {
    // 在实际项目中，这里应该调用引擎的缩放方法
    console.log(`缩放 ${direction}，步长 ${scaleStep}`);
  };
  
  // 渲染变换控制面板
  const renderTransformPanel = () => {
    return (
      <div className="transform-panel">
        <Card title={t('editor.mobile.translation')} size="small">
          <div className="control-grid">
            <div className="grid-row">
              <div className="grid-cell"></div>
              <div className="grid-cell">
                <Button icon={<ArrowUpOutlined />} size="large" onTouchStart={() => handleTranslate('up')} />
              </div>
              <div className="grid-cell"></div>
            </div>
            <div className="grid-row">
              <div className="grid-cell">
                <Button icon={<ArrowLeftOutlined />} size="large" onTouchStart={() => handleTranslate('left')} />
              </div>
              <div className="grid-cell">
                <Slider
                  min={0.1}
                  max={10}
                  step={0.1}
                  value={translateStep}
                  onChange={setTranslateStep}
                  tooltip={{ formatter: (value) => `${value}` }}
                />
              </div>
              <div className="grid-cell">
                <Button icon={<ArrowRightOutlined />} size="large" onTouchStart={() => handleTranslate('right')} />
              </div>
            </div>
            <div className="grid-row">
              <div className="grid-cell"></div>
              <div className="grid-cell">
                <Button icon={<ArrowDownOutlined />} size="large" onTouchStart={() => handleTranslate('down')} />
              </div>
              <div className="grid-cell"></div>
            </div>
          </div>
        </Card>
        
        <Card title={t('editor.mobile.rotation')} size="small" className="mt-2">
          <div className="control-grid">
            <div className="grid-row">
              <div className="grid-cell">
                <Button icon={<RotateLeftOutlined />} size="large" onTouchStart={() => handleRotate('left')} />
              </div>
              <div className="grid-cell">
                <Slider
                  min={1}
                  max={90}
                  step={1}
                  value={rotateStep}
                  onChange={setRotateStep}
                  tooltip={{ formatter: (value) => `${value}°` }}
                />
              </div>
              <div className="grid-cell">
                <Button icon={<RotateRightOutlined />} size="large" onTouchStart={() => handleRotate('right')} />
              </div>
            </div>
          </div>
        </Card>
        
        <Card title={t('editor.mobile.scale')} size="small" className="mt-2">
          <div className="control-grid">
            <div className="grid-row">
              <div className="grid-cell">
                <Button icon={<MinusOutlined />} size="large" onTouchStart={() => handleScale('down')} />
              </div>
              <div className="grid-cell">
                <Slider
                  min={0.01}
                  max={1}
                  step={0.01}
                  value={scaleStep}
                  onChange={setScaleStep}
                  tooltip={{ formatter: (value) => `${value}` }}
                />
              </div>
              <div className="grid-cell">
                <Button icon={<PlusOutlined />} size="large" onTouchStart={() => handleScale('up')} />
              </div>
            </div>
          </div>
        </Card>
      </div>
    );
  };
  
  // 渲染工具面板
  const renderToolsPanel = () => {
    return (
      <div className="tools-panel">
        <Row gutter={[8, 8]}>
          <Col span={8}>
            <Tooltip title={t('editor.mobile.translate')}>
              <Button
                icon={<ArrowRightOutlined />}
                size="large"
                block
                type={transformMode === TransformMode.TRANSLATE ? 'primary' : 'default'}
                onClick={() => dispatch(setTransformMode(TransformMode.TRANSLATE))}
              />
            </Tooltip>
          </Col>
          <Col span={8}>
            <Tooltip title={t('editor.mobile.rotate')}>
              <Button
                icon={<RotateRightOutlined />}
                size="large"
                block
                type={transformMode === TransformMode.ROTATE ? 'primary' : 'default'}
                onClick={() => dispatch(setTransformMode(TransformMode.ROTATE))}
              />
            </Tooltip>
          </Col>
          <Col span={8}>
            <Tooltip title={t('editor.mobile.scale')}>
              <Button
                icon={<ExpandOutlined />}
                size="large"
                block
                type={transformMode === TransformMode.SCALE ? 'primary' : 'default'}
                onClick={() => dispatch(setTransformMode(TransformMode.SCALE))}
              />
            </Tooltip>
          </Col>
          <Col span={8}>
            <Tooltip title={t('editor.mobile.delete')}>
              <Button
                icon={<DeleteOutlined />}
                size="large"
                block
                danger
              />
            </Tooltip>
          </Col>
          <Col span={8}>
            <Tooltip title={t('editor.mobile.duplicate')}>
              <Button
                icon={<CopyOutlined />}
                size="large"
                block
              />
            </Tooltip>
          </Col>
          <Col span={8}>
            <Tooltip title={t('editor.mobile.undo')}>
              <Button
                icon={<UndoOutlined />}
                size="large"
                block
                onClick={() => dispatch(undo())}
              />
            </Tooltip>
          </Col>
          <Col span={8}>
            <Tooltip title={t('editor.mobile.redo')}>
              <Button
                icon={<RedoOutlined />}
                size="large"
                block
                onClick={() => dispatch(redo())}
              />
            </Tooltip>
          </Col>
          <Col span={8}>
            <Tooltip title={t('editor.mobile.save')}>
              <Button
                icon={<SaveOutlined />}
                size="large"
                block
                type="primary"
              />
            </Tooltip>
          </Col>
        </Row>
      </div>
    );
  };
  
  // 渲染视图面板
  const renderViewPanel = () => {
    return (
      <div className="view-panel">
        <Row gutter={[8, 8]}>
          <Col span={12}>
            <Card size="small" title={t('editor.mobile.grid')}>
              <div className="center-content">
                <Space>
                  <Switch checked={showGrid} onChange={(checked) => dispatch(setShowGrid(checked))} />
                  {showGrid ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                </Space>
              </div>
            </Card>
          </Col>
          <Col span={12}>
            <Card size="small" title={t('editor.mobile.axes')}>
              <div className="center-content">
                <Space>
                  <Switch checked={showAxes} onChange={(checked) => dispatch(setShowAxes(checked))} />
                  {showAxes ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                </Space>
              </div>
            </Card>
          </Col>
          <Col span={24}>
            <Card size="small" title={t('editor.mobile.camera')}>
              <Row gutter={[8, 8]}>
                <Col span={8}>
                  <Button icon={<ArrowUpOutlined />} size="large" block />
                </Col>
                <Col span={8}>
                  <Button icon={<ArrowDownOutlined />} size="large" block />
                </Col>
                <Col span={8}>
                  <Button icon={<RotateRightOutlined />} size="large" block />
                </Col>
                <Col span={12}>
                  <Button icon={<ZoomInOutlined />} size="large" block />
                </Col>
                <Col span={12}>
                  <Button icon={<ZoomOutOutlined />} size="large" block />
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };
  
  // 渲染帮助面板
  const renderHelpPanel = () => {
    return (
      <div className="help-panel">
        <Card title={t('editor.mobile.gestureGuide')} size="small">
          <ul className="gesture-list">
            <li>
              <strong>{t('editor.mobile.tap')}</strong>: {t('editor.mobile.tapDescription')}
            </li>
            <li>
              <strong>{t('editor.mobile.doubleTap')}</strong>: {t('editor.mobile.doubleTapDescription')}
            </li>
            <li>
              <strong>{t('editor.mobile.longPress')}</strong>: {t('editor.mobile.longPressDescription')}
            </li>
            <li>
              <strong>{t('editor.mobile.swipe')}</strong>: {t('editor.mobile.swipeDescription')}
            </li>
            <li>
              <strong>{t('editor.mobile.pinch')}</strong>: {t('editor.mobile.pinchDescription')}
            </li>
            <li>
              <strong>{t('editor.mobile.rotate')}</strong>: {t('editor.mobile.rotateDescription')}
            </li>
          </ul>
        </Card>
      </div>
    );
  };
  
  return (
    <div className={`touch-control-panel ${orientation === ScreenOrientation.PORTRAIT ? 'portrait' : 'landscape'}`} ref={panelRef}>
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        centered
        items={[
          {
            key: 'transform',
            label: t('editor.mobile.transform'),
            children: renderTransformPanel()
          },
          {
            key: 'tools',
            label: t('editor.mobile.tools'),
            children: renderToolsPanel()
          },
          {
            key: 'view',
            label: t('editor.mobile.view'),
            children: renderViewPanel()
          },
          {
            key: 'help',
            label: <QuestionCircleOutlined />,
            children: renderHelpPanel()
          }
        ]}
      />
    </div>
  );
};

export default TouchControlPanel;
