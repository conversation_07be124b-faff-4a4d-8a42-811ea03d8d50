/**
 * 性能监控服务
 * 提供实时性能数据收集、分析和监控功能
 */
import { EventEmitter } from '../utils/EventEmitter';

// 性能指标接口
export interface PerformanceMetrics {
  fps: number;
  frameTime: number;
  renderTime: number;
  updateTime: number;
  memoryUsage: MemoryInfo;
  cpuUsage: number;
  gpuUsage?: number;
  drawCalls: number;
  triangles: number;
  timestamp: number;
}

// 内存信息接口
export interface MemoryInfo {
  used: number;
  total: number;
  jsHeapSizeLimit: number;
  usedJSHeapSize: number;
  totalJSHeapSize: number;
}

// 性能警告接口
export interface PerformanceWarning {
  id: string;
  type: WarningType;
  severity: WarningSeverity;
  message: string;
  details: string;
  timestamp: number;
  value: number;
  threshold: number;
  suggestions: string[];
}

// 警告类型枚举
export enum WarningType {
  LOW_FPS = 'low_fps',
  HIGH_MEMORY = 'high_memory',
  MEMORY_LEAK = 'memory_leak',
  HIGH_DRAW_CALLS = 'high_draw_calls',
  HIGH_TRIANGLES = 'high_triangles',
  LONG_FRAME_TIME = 'long_frame_time',
  HIGH_CPU_USAGE = 'high_cpu_usage'
}

// 警告严重程度枚举
export enum WarningSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

// 性能配置接口
export interface PerformanceConfig {
  sampleInterval: number; // 采样间隔(ms)
  historySize: number; // 历史数据保存数量
  warningThresholds: {
    minFps: number;
    maxMemoryUsage: number;
    maxFrameTime: number;
    maxDrawCalls: number;
    maxTriangles: number;
    maxCpuUsage: number;
  };
}

/**
 * 性能监控服务类
 */
export class PerformanceMonitorService extends EventEmitter {
  private static instance: PerformanceMonitorService;
  private isMonitoring: boolean = false;
  private metricsHistory: PerformanceMetrics[] = [];
  private warnings: PerformanceWarning[] = [];
  private config: PerformanceConfig;
  
  // 性能计时器
  private frameStartTime: number = 0;
  private lastFrameTime: number = 0;
  private frameCount: number = 0;
  private fpsUpdateTime: number = 0;
  private currentFps: number = 0;
  
  // 监控定时器
  private monitoringInterval: NodeJS.Timeout | null = null;
  private memoryCheckInterval: NodeJS.Timeout | null = null;
  
  // 渲染统计
  private renderStats = {
    drawCalls: 0,
    triangles: 0,
    renderTime: 0,
    updateTime: 0
  };

  private constructor() {
    super();
    this.config = this.getDefaultConfig();
    this.setupPerformanceObserver();
  }

  public static getInstance(): PerformanceMonitorService {
    if (!PerformanceMonitorService.instance) {
      PerformanceMonitorService.instance = new PerformanceMonitorService();
    }
    return PerformanceMonitorService.instance;
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): PerformanceConfig {
    return {
      sampleInterval: 1000, // 1秒采样一次
      historySize: 300, // 保存5分钟历史数据
      warningThresholds: {
        minFps: 30,
        maxMemoryUsage: 512 * 1024 * 1024, // 512MB
        maxFrameTime: 33.33, // 30fps对应的帧时间
        maxDrawCalls: 1000,
        maxTriangles: 100000,
        maxCpuUsage: 80
      }
    };
  }

  /**
   * 设置性能观察器
   */
  private setupPerformanceObserver(): void {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach(entry => {
            if (entry.entryType === 'measure') {
              this.handlePerformanceMeasure(entry);
            }
          });
        });
        
        observer.observe({ entryTypes: ['measure', 'navigation', 'resource'] });
      } catch (error) {
        console.warn('PerformanceObserver not supported:', error);
      }
    }
  }

  /**
   * 处理性能测量
   */
  private handlePerformanceMeasure(entry: PerformanceEntry): void {
    if (entry.name.startsWith('render-')) {
      this.renderStats.renderTime = entry.duration;
    } else if (entry.name.startsWith('update-')) {
      this.renderStats.updateTime = entry.duration;
    }
  }

  /**
   * 开始监控
   */
  public startMonitoring(): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.frameStartTime = performance.now();
    this.fpsUpdateTime = this.frameStartTime;

    // 启动主监控循环
    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
    }, this.config.sampleInterval);

    // 启动内存检查
    this.memoryCheckInterval = setInterval(() => {
      this.checkMemoryLeaks();
    }, 5000); // 5秒检查一次内存

    // 启动帧率监控
    this.startFrameMonitoring();

    this.emit('monitoringStarted');
  }

  /**
   * 停止监控
   */
  public stopMonitoring(): void {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    if (this.memoryCheckInterval) {
      clearInterval(this.memoryCheckInterval);
      this.memoryCheckInterval = null;
    }

    this.emit('monitoringStopped');
  }

  /**
   * 启动帧率监控
   */
  private startFrameMonitoring(): void {
    const updateFps = () => {
      if (!this.isMonitoring) return;

      const now = performance.now();
      this.frameCount++;

      // 每秒更新一次FPS
      if (now - this.fpsUpdateTime >= 1000) {
        this.currentFps = Math.round((this.frameCount * 1000) / (now - this.fpsUpdateTime));
        this.frameCount = 0;
        this.fpsUpdateTime = now;
      }

      // 计算帧时间
      const frameTime = now - this.lastFrameTime;
      this.lastFrameTime = now;

      // 检查FPS警告
      if (this.currentFps < this.config.warningThresholds.minFps) {
        this.addWarning(WarningType.LOW_FPS, WarningSeverity.WARNING, 
          `Low FPS detected: ${this.currentFps}`, 
          `Current FPS (${this.currentFps}) is below threshold (${this.config.warningThresholds.minFps})`,
          this.currentFps, this.config.warningThresholds.minFps,
          ['Reduce scene complexity', 'Optimize materials', 'Use LOD models']
        );
      }

      // 检查帧时间警告
      if (frameTime > this.config.warningThresholds.maxFrameTime) {
        this.addWarning(WarningType.LONG_FRAME_TIME, WarningSeverity.WARNING,
          `Long frame time detected: ${frameTime.toFixed(2)}ms`,
          `Frame time (${frameTime.toFixed(2)}ms) exceeds threshold (${this.config.warningThresholds.maxFrameTime}ms)`,
          frameTime, this.config.warningThresholds.maxFrameTime,
          ['Optimize render pipeline', 'Reduce draw calls', 'Use instancing']
        );
      }

      requestAnimationFrame(updateFps);
    };

    requestAnimationFrame(updateFps);
  }

  /**
   * 收集性能指标
   */
  private collectMetrics(): void {
    const memoryInfo = this.getMemoryInfo();
    const cpuUsage = this.estimateCpuUsage();

    const metrics: PerformanceMetrics = {
      fps: this.currentFps,
      frameTime: this.lastFrameTime - this.frameStartTime,
      renderTime: this.renderStats.renderTime,
      updateTime: this.renderStats.updateTime,
      memoryUsage: memoryInfo,
      cpuUsage,
      drawCalls: this.renderStats.drawCalls,
      triangles: this.renderStats.triangles,
      timestamp: Date.now()
    };

    // 添加到历史记录
    this.metricsHistory.push(metrics);
    
    // 保持历史记录在配置的大小内
    if (this.metricsHistory.length > this.config.historySize) {
      this.metricsHistory.shift();
    }

    // 检查警告
    this.checkWarnings(metrics);

    // 触发事件
    this.emit('metricsUpdated', metrics);
  }

  /**
   * 获取内存信息
   */
  private getMemoryInfo(): MemoryInfo {
    const memory = (performance as any).memory;
    
    if (memory) {
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize
      };
    }

    // 如果不支持memory API，返回估算值
    return {
      used: 0,
      total: 0,
      jsHeapSizeLimit: 0,
      usedJSHeapSize: 0,
      totalJSHeapSize: 0
    };
  }

  /**
   * 估算CPU使用率
   */
  private estimateCpuUsage(): number {
    // 简单的CPU使用率估算，基于帧时间
    const frameTime = this.lastFrameTime - this.frameStartTime;
    const targetFrameTime = 16.67; // 60fps对应的帧时间
    
    return Math.min(100, (frameTime / targetFrameTime) * 100);
  }

  /**
   * 检查内存泄漏
   */
  private checkMemoryLeaks(): void {
    if (this.metricsHistory.length < 10) return;

    const recent = this.metricsHistory.slice(-10);
    const memoryTrend = this.calculateMemoryTrend(recent);

    // 如果内存持续增长且增长率超过阈值，可能存在内存泄漏
    if (memoryTrend > 1024 * 1024) { // 1MB/sample
      this.addWarning(WarningType.MEMORY_LEAK, WarningSeverity.ERROR,
        'Potential memory leak detected',
        `Memory usage is continuously increasing at ${(memoryTrend / 1024 / 1024).toFixed(2)}MB per sample`,
        memoryTrend, 1024 * 1024,
        ['Check for unreleased resources', 'Review event listeners', 'Analyze object references']
      );
    }
  }

  /**
   * 计算内存趋势
   */
  private calculateMemoryTrend(metrics: PerformanceMetrics[]): number {
    if (metrics.length < 2) return 0;

    const first = metrics[0].memoryUsage.used;
    const last = metrics[metrics.length - 1].memoryUsage.used;
    
    return (last - first) / metrics.length;
  }

  /**
   * 检查警告
   */
  private checkWarnings(metrics: PerformanceMetrics): void {
    const { warningThresholds } = this.config;

    // 检查内存使用
    if (metrics.memoryUsage.used > warningThresholds.maxMemoryUsage) {
      this.addWarning(WarningType.HIGH_MEMORY, WarningSeverity.WARNING,
        `High memory usage: ${(metrics.memoryUsage.used / 1024 / 1024).toFixed(2)}MB`,
        `Memory usage exceeds threshold`,
        metrics.memoryUsage.used, warningThresholds.maxMemoryUsage,
        ['Clear unused resources', 'Optimize textures', 'Use object pooling']
      );
    }

    // 检查绘制调用
    if (metrics.drawCalls > warningThresholds.maxDrawCalls) {
      this.addWarning(WarningType.HIGH_DRAW_CALLS, WarningSeverity.WARNING,
        `High draw calls: ${metrics.drawCalls}`,
        `Draw calls exceed threshold`,
        metrics.drawCalls, warningThresholds.maxDrawCalls,
        ['Batch similar objects', 'Use instancing', 'Merge geometries']
      );
    }

    // 检查三角形数量
    if (metrics.triangles > warningThresholds.maxTriangles) {
      this.addWarning(WarningType.HIGH_TRIANGLES, WarningSeverity.WARNING,
        `High triangle count: ${metrics.triangles}`,
        `Triangle count exceeds threshold`,
        metrics.triangles, warningThresholds.maxTriangles,
        ['Use LOD models', 'Simplify geometry', 'Implement frustum culling']
      );
    }

    // 检查CPU使用率
    if (metrics.cpuUsage > warningThresholds.maxCpuUsage) {
      this.addWarning(WarningType.HIGH_CPU_USAGE, WarningSeverity.WARNING,
        `High CPU usage: ${metrics.cpuUsage.toFixed(1)}%`,
        `CPU usage exceeds threshold`,
        metrics.cpuUsage, warningThresholds.maxCpuUsage,
        ['Optimize update loops', 'Use web workers', 'Reduce calculations']
      );
    }
  }

  /**
   * 添加警告
   */
  private addWarning(
    type: WarningType,
    severity: WarningSeverity,
    message: string,
    details: string,
    value: number,
    threshold: number,
    suggestions: string[]
  ): void {
    // 避免重复警告
    const recentWarning = this.warnings.find(w => 
      w.type === type && Date.now() - w.timestamp < 5000
    );
    
    if (recentWarning) return;

    const warning: PerformanceWarning = {
      id: `warning_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      severity,
      message,
      details,
      timestamp: Date.now(),
      value,
      threshold,
      suggestions
    };

    this.warnings.push(warning);
    
    // 保持警告列表在合理大小
    if (this.warnings.length > 100) {
      this.warnings = this.warnings.slice(-50);
    }

    this.emit('warningAdded', warning);
  }

  /**
   * 更新渲染统计
   */
  public updateRenderStats(stats: Partial<typeof this.renderStats>): void {
    Object.assign(this.renderStats, stats);
  }

  /**
   * 获取当前指标
   */
  public getCurrentMetrics(): PerformanceMetrics | null {
    return this.metricsHistory.length > 0 ? 
      this.metricsHistory[this.metricsHistory.length - 1] : null;
  }

  /**
   * 获取历史指标
   */
  public getMetricsHistory(count?: number): PerformanceMetrics[] {
    if (count) {
      return this.metricsHistory.slice(-count);
    }
    return [...this.metricsHistory];
  }

  /**
   * 获取警告列表
   */
  public getWarnings(severity?: WarningSeverity): PerformanceWarning[] {
    if (severity) {
      return this.warnings.filter(w => w.severity === severity);
    }
    return [...this.warnings];
  }

  /**
   * 清除警告
   */
  public clearWarnings(): void {
    this.warnings = [];
    this.emit('warningsCleared');
  }

  /**
   * 获取性能摘要
   */
  public getPerformanceSummary(): {
    avgFps: number;
    avgFrameTime: number;
    avgMemoryUsage: number;
    avgCpuUsage: number;
    warningCount: number;
    criticalWarningCount: number;
  } {
    if (this.metricsHistory.length === 0) {
      return {
        avgFps: 0,
        avgFrameTime: 0,
        avgMemoryUsage: 0,
        avgCpuUsage: 0,
        warningCount: 0,
        criticalWarningCount: 0
      };
    }

    const recent = this.metricsHistory.slice(-60); // 最近1分钟数据
    
    const avgFps = recent.reduce((sum, m) => sum + m.fps, 0) / recent.length;
    const avgFrameTime = recent.reduce((sum, m) => sum + m.frameTime, 0) / recent.length;
    const avgMemoryUsage = recent.reduce((sum, m) => sum + m.memoryUsage.used, 0) / recent.length;
    const avgCpuUsage = recent.reduce((sum, m) => sum + m.cpuUsage, 0) / recent.length;
    
    const warningCount = this.warnings.length;
    const criticalWarningCount = this.warnings.filter(w => 
      w.severity === WarningSeverity.CRITICAL || w.severity === WarningSeverity.ERROR
    ).length;

    return {
      avgFps: Math.round(avgFps),
      avgFrameTime: Math.round(avgFrameTime * 100) / 100,
      avgMemoryUsage: Math.round(avgMemoryUsage),
      avgCpuUsage: Math.round(avgCpuUsage * 100) / 100,
      warningCount,
      criticalWarningCount
    };
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<PerformanceConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('configUpdated', this.config);
  }

  /**
   * 获取配置
   */
  public getConfig(): PerformanceConfig {
    return { ...this.config };
  }
}

export default PerformanceMonitorService;
