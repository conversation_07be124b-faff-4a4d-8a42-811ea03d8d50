/**
 * 地形LOD工具组件
 * 用于配置地形LOD
 */
import React, { useState, useEffect, useRef } from 'react';
import {
  Form,
  InputNumber,
  Button,
  Slider,
  Space,
  Divider,
  Card,
  Row,
  Col,
  Tooltip,
  Switch,
  Typography,
  Table,
  message
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  SaveOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import './TerrainLODTool.less';

const { Text } = Typography;

/**
 * LOD级别
 */
interface LODLevel {
  /** 级别 */
  level: number;
  /** 距离 */
  distance: number;
  /** 分辨率比例 */
  resolutionScale: number;
  /** 是否启用 */
  enabled: boolean;
}

/**
 * 地形LOD工具属性
 */
interface TerrainLODToolProps {
  /** 实体ID */
  entityId?: string;
  /** 是否可编辑 */
  editable?: boolean;
  /** 地形修改回调 */
  onTerrainModified?: () => void;
  /** 操作回调 */
  onOperation?: (operation: any) => void;
}

/**
 * 地形LOD工具组件
 */
const TerrainLODTool: React.FC<TerrainLODToolProps> = ({
  entityId,
  editable = true,
  onTerrainModified,
  onOperation
}) => {
  const { t } = useTranslation();

  // 从Redux获取地形数据
  const terrainData = useSelector((state: RootState) => {
    if (!entityId) return null;
    const entity = state.scene.entities.find(e => e.id === entityId);
    return entity?.components?.TerrainComponent || null;
  });
  
  // 状态
  const [useLOD, setUseLOD] = useState<boolean>(false);
  const [lodLevels, setLodLevels] = useState<LODLevel[]>([]);
  const [editingLevel, setEditingLevel] = useState<LODLevel | null>(null);
  const [editingIndex, setEditingIndex] = useState<number>(-1);
  const [useQuadTree, setUseQuadTree] = useState<boolean>(true);
  const [maxLODLevels, setMaxLODLevels] = useState<number>(4);
  const [transitionRange, setTransitionRange] = useState<number>(0.2);
  const [showWireframe, setShowWireframe] = useState<boolean>(false);
  const [showLODColors, setShowLODColors] = useState<boolean>(false);
  const [showPerformanceMetrics, setShowPerformanceMetrics] = useState<boolean>(false);
  
  // 引用
  const performanceChartRef = useRef<any>(null);
  
  // 初始化LOD级别
  useEffect(() => {
    if (terrainData) {
      setUseLOD(terrainData.useLOD || false);
      
      if (terrainData.lodLevels && terrainData.lodDistances) {
        const levels: LODLevel[] = [];
        for (let i = 0; i < terrainData.lodLevels; i++) {
          levels.push({
            level: i,
            distance: terrainData.lodDistances[i] || 100 * (i + 1),
            resolutionScale: 1 / Math.pow(2, i),
            enabled: true
          });
        }
        setLodLevels(levels);
      } else {
        // 默认LOD级别
        setLodLevels([
          { level: 0, distance: 100, resolutionScale: 1.0, enabled: true },
          { level: 1, distance: 300, resolutionScale: 0.5, enabled: true },
          { level: 2, distance: 600, resolutionScale: 0.25, enabled: true },
          { level: 3, distance: 1200, resolutionScale: 0.125, enabled: true }
        ]);
      }
    }
  }, [terrainData]);
  
  // 处理使用LOD变更
  const handleUseLODChange = (checked: boolean) => {
    setUseLOD(checked);
    
    // 通知地形修改
    if (onTerrainModified) {
      onTerrainModified();
    }
    
    // 记录操作
    if (onOperation) {
      onOperation({
        type: 'SET_USE_LOD',
        useLOD: checked
      });
    }
  };
  
  // 处理使用四叉树变更
  const handleUseQuadTreeChange = (checked: boolean) => {
    setUseQuadTree(checked);
    
    // 通知地形修改
    if (onTerrainModified) {
      onTerrainModified();
    }
    
    // 记录操作
    if (onOperation) {
      onOperation({
        type: 'SET_USE_QUAD_TREE',
        useQuadTree: checked
      });
    }
  };
  
  // 处理最大LOD级别变更
  const handleMaxLODLevelsChange = (value: number) => {
    setMaxLODLevels(value);
    
    // 通知地形修改
    if (onTerrainModified) {
      onTerrainModified();
    }
    
    // 记录操作
    if (onOperation) {
      onOperation({
        type: 'SET_MAX_LOD_LEVELS',
        maxLODLevels: value
      });
    }
  };
  
  // 处理过渡范围变更
  const handleTransitionRangeChange = (value: number) => {
    setTransitionRange(value);
    
    // 通知地形修改
    if (onTerrainModified) {
      onTerrainModified();
    }
    
    // 记录操作
    if (onOperation) {
      onOperation({
        type: 'SET_TRANSITION_RANGE',
        transitionRange: value
      });
    }
  };
  
  // 处理显示线框变更
  const handleShowWireframeChange = (checked: boolean) => {
    setShowWireframe(checked);
  };
  
  // 处理显示LOD颜色变更
  const handleShowLODColorsChange = (checked: boolean) => {
    setShowLODColors(checked);
  };
  
  // 处理显示性能指标变更
  const handleShowPerformanceMetricsChange = (checked: boolean) => {
    setShowPerformanceMetrics(checked);
  };
  
  // 处理添加LOD级别
  const handleAddLODLevel = () => {
    if (lodLevels.length >= maxLODLevels) {
      message.warning(t('terrain.lod.maxLevelsReached', { max: maxLODLevels }));
      return;
    }
    
    const newLevel: LODLevel = {
      level: lodLevels.length,
      distance: lodLevels.length > 0 ? lodLevels[lodLevels.length - 1].distance * 2 : 100,
      resolutionScale: 1 / Math.pow(2, lodLevels.length),
      enabled: true
    };
    
    const newLevels = [...lodLevels, newLevel];
    setLodLevels(newLevels);
    
    // 通知地形修改
    if (onTerrainModified) {
      onTerrainModified();
    }
    
    // 记录操作
    if (onOperation) {
      onOperation({
        type: 'ADD_LOD_LEVEL',
        level: newLevel
      });
    }
  };
  
  // 处理删除LOD级别
  const handleDeleteLODLevel = (index: number) => {
    if (lodLevels.length <= 1) {
      message.warning(t('terrain.lod.atLeastOneLevel'));
      return;
    }
    
    const deletedLevel = lodLevels[index];
    const newLevels = lodLevels.filter((_, i) => i !== index).map((level, i) => ({
      ...level,
      level: i
    }));
    
    setLodLevels(newLevels);
    
    // 通知地形修改
    if (onTerrainModified) {
      onTerrainModified();
    }
    
    // 记录操作
    if (onOperation) {
      onOperation({
        type: 'DELETE_LOD_LEVEL',
        index,
        level: deletedLevel
      });
    }
  };
  
  // 处理编辑LOD级别
  const handleEditLODLevel = (level: LODLevel, index: number) => {
    setEditingLevel({ ...level });
    setEditingIndex(index);
  };
  
  // 处理保存LOD级别
  const handleSaveLODLevel = () => {
    if (!editingLevel || editingIndex < 0) return;
    
    const newLevels = [...lodLevels];
    newLevels[editingIndex] = editingLevel;
    
    // 确保距离是递增的
    newLevels.sort((a, b) => a.distance - b.distance);
    
    // 更新级别
    newLevels.forEach((level, i) => {
      level.level = i;
    });
    
    setLodLevels(newLevels);
    setEditingLevel(null);
    setEditingIndex(-1);
    
    // 通知地形修改
    if (onTerrainModified) {
      onTerrainModified();
    }
    
    // 记录操作
    if (onOperation) {
      onOperation({
        type: 'EDIT_LOD_LEVEL',
        index: editingIndex,
        level: editingLevel
      });
    }
  };
  
  // 处理取消编辑LOD级别
  const handleCancelEditLODLevel = () => {
    setEditingLevel(null);
    setEditingIndex(-1);
  };
  
  // 处理LOD级别启用变更
  const handleLODLevelEnabledChange = (checked: boolean, index: number) => {
    const newLevels = [...lodLevels];
    newLevels[index].enabled = checked;
    setLodLevels(newLevels);
    
    // 通知地形修改
    if (onTerrainModified) {
      onTerrainModified();
    }
    
    // 记录操作
    if (onOperation) {
      onOperation({
        type: 'SET_LOD_LEVEL_ENABLED',
        index,
        enabled: checked
      });
    }
  };
  
  // 处理应用LOD设置
  const handleApplyLODSettings = () => {
    if (!entityId || !terrainData) {
      message.error(t('terrain.errors.noTerrainSelected'));
      return;
    }
    
    // 这里需要实现应用LOD设置逻辑
    
    message.success(t('terrain.lod.settingsApplied'));
  };
  
  // 表格列
  const columns = [
    {
      title: t('terrain.lod.level'),
      dataIndex: 'level',
      key: 'level',
      render: (level: number) => `LOD ${level}`
    },
    {
      title: t('terrain.lod.distance'),
      dataIndex: 'distance',
      key: 'distance',
      render: (distance: number) => `${distance} m`
    },
    {
      title: t('terrain.lod.resolutionScale'),
      dataIndex: 'resolutionScale',
      key: 'resolutionScale',
      render: (scale: number) => `${(scale * 100).toFixed(0)}%`
    },
    {
      title: t('terrain.lod.enabled'),
      dataIndex: 'enabled',
      key: 'enabled',
      render: (enabled: boolean, _record: LODLevel, index: number) => (
        <Switch
          checked={enabled}
          onChange={(checked) => handleLODLevelEnabledChange(checked, index)}
          disabled={!editable}
        />
      )
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (_: any, record: LODLevel, index: number) => (
        <Space>
          <Button
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEditLODLevel(record, index)}
            disabled={!editable}
          />
          <Button
            icon={<DeleteOutlined />}
            size="small"
            onClick={() => handleDeleteLODLevel(index)}
            disabled={!editable || lodLevels.length <= 1}
            danger
          />
        </Space>
      )
    }
  ];
  
  return (
    <div className="terrain-lod-tool">
      <Row gutter={[16, 16]}>
        <Col span={16}>
          <Card title={t('terrain.lod.settings')} className="lod-settings-card">
            <Form layout="vertical">
              <Form.Item>
                <Switch
                  checked={useLOD}
                  onChange={handleUseLODChange}
                  disabled={!editable}
                />
                <Text style={{ marginLeft: 8 }}>{t('terrain.lod.useLOD')}</Text>
                <Tooltip title={t('terrain.lod.useLODTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </Form.Item>
              
              {useLOD && (
                <>
                  <Form.Item>
                    <Switch
                      checked={useQuadTree}
                      onChange={handleUseQuadTreeChange}
                      disabled={!editable}
                    />
                    <Text style={{ marginLeft: 8 }}>{t('terrain.lod.useQuadTree')}</Text>
                    <Tooltip title={t('terrain.lod.useQuadTreeTooltip')}>
                      <InfoCircleOutlined style={{ marginLeft: 8 }} />
                    </Tooltip>
                  </Form.Item>
                  
                  <Form.Item label={t('terrain.lod.maxLODLevels')}>
                    <Slider
                      min={1}
                      max={8}
                      step={1}
                      value={maxLODLevels}
                      onChange={handleMaxLODLevelsChange}
                      disabled={!editable}
                    />
                  </Form.Item>
                  
                  <Form.Item 
                    label={
                      <span>
                        {t('terrain.lod.transitionRange')}
                        <Tooltip title={t('terrain.lod.transitionRangeTooltip')}>
                          <InfoCircleOutlined style={{ marginLeft: 8 }} />
                        </Tooltip>
                      </span>
                    }
                  >
                    <Slider
                      min={0}
                      max={0.5}
                      step={0.01}
                      value={transitionRange}
                      onChange={handleTransitionRangeChange}
                      disabled={!editable}
                    />
                  </Form.Item>
                  
                  <Divider>{t('terrain.lod.lodLevels')}</Divider>
                  
                  <Table
                    dataSource={lodLevels}
                    columns={columns}
                    pagination={false}
                    rowKey="level"
                    size="small"
                  />
                  
                  <div className="table-actions">
                    <Button
                      type="dashed"
                      icon={<PlusOutlined />}
                      onClick={handleAddLODLevel}
                      disabled={!editable || lodLevels.length >= maxLODLevels}
                      style={{ marginTop: 16 }}
                    >
                      {t('terrain.lod.addLevel')}
                    </Button>
                  </div>
                  
                  {editingLevel && (
                    <div className="editing-form">
                      <Divider>{t('terrain.lod.editLevel', { level: editingIndex })}</Divider>
                      
                      <Form layout="vertical">
                        <Form.Item label={t('terrain.lod.distance')}>
                          <InputNumber
                            value={editingLevel.distance}
                            onChange={(value) => setEditingLevel({ ...editingLevel, distance: value as number })}
                            min={0}
                            disabled={!editable}
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                        
                        <Form.Item label={t('terrain.lod.resolutionScale')}>
                          <Slider
                            min={0.01}
                            max={1}
                            step={0.01}
                            value={editingLevel.resolutionScale}
                            onChange={(value) => setEditingLevel({ ...editingLevel, resolutionScale: value as number })}
                            disabled={!editable}
                          />
                        </Form.Item>
                        
                        <div className="form-actions">
                          <Button
                            type="primary"
                            icon={<SaveOutlined />}
                            onClick={handleSaveLODLevel}
                            disabled={!editable}
                          >
                            {t('common.save')}
                          </Button>
                          <Button
                            onClick={handleCancelEditLODLevel}
                          >
                            {t('common.cancel')}
                          </Button>
                        </div>
                      </Form>
                    </div>
                  )}
                </>
              )}
            </Form>
          </Card>
        </Col>
        
        <Col span={8}>
          <Card title={t('terrain.lod.visualization')} className="lod-visualization-card">
            <Form layout="vertical">
              <Form.Item>
                <Switch
                  checked={showWireframe}
                  onChange={handleShowWireframeChange}
                  disabled={!editable}
                />
                <Text style={{ marginLeft: 8 }}>{t('terrain.lod.showWireframe')}</Text>
              </Form.Item>
              
              <Form.Item>
                <Switch
                  checked={showLODColors}
                  onChange={handleShowLODColorsChange}
                  disabled={!editable}
                />
                <Text style={{ marginLeft: 8 }}>{t('terrain.lod.showLODColors')}</Text>
              </Form.Item>
              
              <Form.Item>
                <Switch
                  checked={showPerformanceMetrics}
                  onChange={handleShowPerformanceMetricsChange}
                  disabled={!editable}
                />
                <Text style={{ marginLeft: 8 }}>{t('terrain.lod.showPerformanceMetrics')}</Text>
              </Form.Item>
            </Form>
            
            {showPerformanceMetrics && (
              <div className="performance-metrics">
                <div className="performance-chart" ref={performanceChartRef}>
                  {/* 性能图表 */}
                </div>
                <div className="performance-stats">
                  <div className="stat-item">
                    <Text strong>{t('terrain.lod.triangleCount')}:</Text>
                    <Text>0</Text>
                  </div>
                  <div className="stat-item">
                    <Text strong>{t('terrain.lod.chunkCount')}:</Text>
                    <Text>0</Text>
                  </div>
                  <div className="stat-item">
                    <Text strong>{t('terrain.lod.drawCalls')}:</Text>
                    <Text>0</Text>
                  </div>
                  <div className="stat-item">
                    <Text strong>{t('terrain.lod.frameTime')}:</Text>
                    <Text>0 ms</Text>
                  </div>
                </div>
              </div>
            )}
          </Card>
          
          <Card title={t('terrain.lod.actions')} className="lod-actions-card" style={{ marginTop: 16 }}>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleApplyLODSettings}
              disabled={!editable || !useLOD}
              block
            >
              {t('terrain.lod.applySettings')}
            </Button>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default TerrainLODTool;
