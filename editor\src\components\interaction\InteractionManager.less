/**
 * 交互管理器样式
 */
.interaction-manager {
  height: 100%;
  display: flex;
  flex-direction: column;

  // 水平布局
  &.horizontal {
    .interaction-layout {
      flex-direction: row;
    }
  }

  // 垂直布局
  &.vertical {
    .interaction-layout {
      flex-direction: column;
    }
  }

  // 布局容器
  .interaction-layout {
    flex: 1;
    background: #fff;

    .interaction-content {
      padding: 0;
      background: #fff;

      .ant-tabs {
        height: 100%;

        .ant-tabs-content-holder {
          height: calc(100% - 46px);
          overflow: auto;
        }

        .ant-tabs-tabpane {
          height: 100%;
          padding: 16px;
        }

        .ant-tabs-tab {
          padding: 8px 16px;
          
          .anticon {
            margin-right: 6px;
          }
        }

        .ant-tabs-extra-content {
          .ant-btn {
            border: none;
            box-shadow: none;
            
            &:hover {
              background: #f5f5f5;
            }
          }
        }
      }
    }
  }

  // 调试面板
  .debug-panel {
    background: #fafafa;
    border-left: 1px solid #f0f0f0;

    .ant-card {
      height: 100%;
      border: none;
      border-radius: 0;

      .ant-card-head {
        background: #f5f5f5;
        border-bottom: 1px solid #e8e8e8;
        min-height: 40px;
        padding: 0 12px;

        .ant-card-head-title {
          font-size: 13px;
          font-weight: 600;
        }
      }

      .ant-card-body {
        padding: 12px;
        height: calc(100% - 40px);
        overflow-y: auto;
      }
    }

    .debug-content {
      .debug-section {
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
          margin-bottom: 0;
        }

        h5 {
          margin: 0 0 8px 0;
          font-size: 12px;
          font-weight: 600;
          color: #666;
          text-transform: uppercase;
        }

        p {
          margin: 0;
          font-size: 13px;
          color: #333;
        }

        .status-ok {
          color: #52c41a;
          font-weight: 500;
        }

        .status-error {
          color: #ff4d4f;
          font-weight: 500;
        }
      }
    }
  }

  // 统计信息模态框
  .stats-detail {
    .stats-summary {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
      margin-bottom: 24px;

      .stat-card {
        text-align: center;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;

        .stat-value {
          font-size: 24px;
          font-weight: 700;
          color: #1890ff;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 12px;
          color: #666;
          text-transform: uppercase;
          font-weight: 500;
        }
      }
    }

    .stats-breakdown {
      margin-bottom: 24px;

      h4 {
        margin-bottom: 12px;
        font-size: 14px;
        font-weight: 600;
        color: #333;
      }

      .type-stat {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        margin-bottom: 4px;
        background: #f8f9fa;
        border-radius: 4px;
        border: 1px solid #e9ecef;

        .type-name {
          font-size: 13px;
          color: #333;
        }

        .type-count {
          font-size: 13px;
          font-weight: 600;
          color: #1890ff;
          background: #e6f7ff;
          padding: 2px 8px;
          border-radius: 12px;
        }
      }
    }

    .recent-events-detail {
      h4 {
        margin-bottom: 12px;
        font-size: 14px;
        font-weight: 600;
        color: #333;
      }

      .events-timeline {
        max-height: 200px;
        overflow-y: auto;

        .event-timeline-item {
          display: flex;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .event-time {
            min-width: 80px;
            font-size: 11px;
            color: #999;
            font-family: monospace;
          }

          .event-details {
            flex: 1;
            margin-left: 12px;

            .event-type {
              display: inline-block;
              background: #1890ff;
              color: white;
              padding: 2px 6px;
              border-radius: 3px;
              font-size: 10px;
              margin-right: 8px;
            }

            .event-entity {
              font-size: 12px;
              color: #666;
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    &.horizontal {
      .debug-panel {
        width: 250px !important;
      }
    }
  }

  @media (max-width: 768px) {
    &.horizontal {
      .interaction-layout {
        flex-direction: column;
      }

      .debug-panel {
        width: 100% !important;
        height: 200px;
        border-left: none;
        border-top: 1px solid #f0f0f0;
      }
    }

    .stats-detail {
      .stats-summary {
        grid-template-columns: 1fr;
        gap: 8px;

        .stat-card {
          padding: 12px;
        }
      }
    }

    .interaction-content {
      .ant-tabs-tabpane {
        padding: 8px;
      }
    }
  }

  // 动画效果
  .ant-tabs-tab {
    transition: all 0.2s ease;

    &:hover {
      color: #1890ff;
    }

    &.ant-tabs-tab-active {
      .anticon {
        color: #1890ff;
      }
    }
  }

  .debug-section {
    transition: all 0.2s ease;

    &:hover {
      background: rgba(24, 144, 255, 0.02);
      border-radius: 4px;
      margin: 0 -4px 16px -4px;
      padding: 8px 4px 12px 4px;
    }
  }

  .stat-card {
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .type-stat {
    transition: all 0.2s ease;

    &:hover {
      background: #e6f7ff;
      border-color: #91d5ff;
    }
  }

  .event-timeline-item {
    transition: all 0.2s ease;

    &:hover {
      background: #f8f9fa;
      margin: 0 -8px;
      padding: 8px 8px;
      border-radius: 4px;
    }
  }

  // 滚动条样式
  .ant-tabs-content-holder,
  .debug-content,
  .events-timeline {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  // 加载状态
  &.loading {
    .interaction-content {
      opacity: 0.6;
      pointer-events: none;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      z-index: 1000;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  // 错误状态
  &.error {
    .interaction-content {
      background: #fff2f0;
    }

    .ant-tabs-tab {
      color: #ff4d4f;
    }
  }

  // 深色主题支持
  @media (prefers-color-scheme: dark) {
    .interaction-layout {
      background: #1f1f1f;

      .interaction-content {
        background: #1f1f1f;

        .ant-tabs {
          .ant-tabs-tab {
            color: #ccc;

            &:hover {
              color: #1890ff;
            }

            &.ant-tabs-tab-active {
              color: #1890ff;
            }
          }
        }
      }
    }

    .debug-panel {
      background: #2f2f2f;
      border-left-color: #434343;

      .ant-card {
        background: #2f2f2f;

        .ant-card-head {
          background: #3f3f3f;
          border-bottom-color: #434343;

          .ant-card-head-title {
            color: #fff;
          }
        }

        .ant-card-body {
          background: #2f2f2f;
        }
      }

      .debug-content {
        .debug-section {
          border-bottom-color: #434343;

          h5 {
            color: #ccc;
          }

          p {
            color: #fff;
          }
        }
      }
    }

    .stats-detail {
      .stat-card {
        background: #2f2f2f;
        border-color: #434343;

        .stat-value {
          color: #1890ff;
        }

        .stat-label {
          color: #ccc;
        }
      }

      .type-stat {
        background: #2f2f2f;
        border-color: #434343;

        .type-name {
          color: #fff;
        }
      }
    }
  }
}
