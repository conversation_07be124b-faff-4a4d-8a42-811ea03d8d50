/**
 * 空间信息系统主组件样式
 */
.spatial-info-system {
  height: 100vh;
  background: #f0f2f5;
  
  .ant-layout {
    background: transparent;
    
    .map-content {
      padding: 16px;
      background: transparent;
      
      .ant-card {
        height: 100%;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        
        .ant-card-head {
          border-bottom: 1px solid #f0f0f0;
          
          .ant-card-head-title {
            font-size: 16px;
            font-weight: 600;
          }
          
          .ant-card-extra {
            .ant-btn {
              margin-left: 8px;
            }
          }
        }
        
        .ant-card-body {
          height: calc(100% - 57px);
          overflow: hidden;
        }
      }
    }
    
    .spatial-sider {
      background: #fff;
      border-left: 1px solid #f0f0f0;
      box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
      
      .ant-layout-sider-trigger {
        background: #1890ff;
        color: #fff;
        
        &:hover {
          background: #40a9ff;
        }
      }
      
      .sider-content {
        height: 100%;
        display: flex;
        flex-direction: column;
        
        .ant-tabs {
          flex-shrink: 0;
          
          .ant-tabs-nav {
            margin-bottom: 0;
            
            .ant-tabs-nav-wrap {
              .ant-tabs-nav-list {
                .ant-tabs-tab {
                  padding: 8px 16px;
                  font-size: 12px;
                  
                  .anticon {
                    margin-right: 4px;
                  }
                }
              }
            }
          }
        }
        
        .tab-content {
          flex: 1;
          overflow: hidden;
          padding: 16px;
          
          .map-info-panel {
            .info-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 6px 0;
              border-bottom: 1px solid #f5f5f5;
              
              &:last-child {
                border-bottom: none;
              }
              
              .label {
                font-size: 12px;
                color: #666;
                font-weight: 500;
              }
              
              .value {
                font-size: 12px;
                color: #333;
                font-family: 'Courier New', monospace;
              }
            }
            
            .selected-feature {
              margin-top: 16px;
              padding-top: 16px;
              border-top: 1px solid #f0f0f0;
              
              h4 {
                margin-bottom: 8px;
                font-size: 13px;
                color: #1890ff;
              }
            }
          }
        }
      }
      
      &.ant-layout-sider-collapsed {
        .sider-content {
          .tab-content {
            padding: 8px;
          }
        }
      }
    }
  }
  
  .system-info {
    .info-section {
      margin-bottom: 20px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      h4 {
        margin-bottom: 12px;
        font-size: 14px;
        font-weight: 600;
        color: #1890ff;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 4px;
      }
      
      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;
        
        .label {
          font-size: 13px;
          color: #666;
        }
        
        .value {
          font-size: 13px;
          color: #333;
          font-weight: 500;
          
          &:contains('✓') {
            color: #52c41a;
          }
          
          &:contains('✗') {
            color: #ff4d4f;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .spatial-info-system {
    .ant-layout {
      .spatial-sider {
        width: 320px !important;
        min-width: 320px !important;
        max-width: 320px !important;
      }
    }
  }
}

@media (max-width: 768px) {
  .spatial-info-system {
    .ant-layout {
      flex-direction: column;
      
      .map-content {
        padding: 8px;
        order: 2;
        
        .ant-card {
          .ant-card-head {
            .ant-card-head-title {
              font-size: 14px;
            }
            
            .ant-card-extra {
              .ant-btn {
                margin-left: 4px;
                padding: 4px 8px;
              }
            }
          }
        }
      }
      
      .spatial-sider {
        order: 1;
        width: 100% !important;
        min-width: 100% !important;
        max-width: 100% !important;
        height: 300px;
        
        .sider-content {
          .tab-content {
            padding: 8px;
          }
        }
      }
    }
  }
}

// 暗色主题支持
.dark-theme {
  .spatial-info-system {
    background: #141414;
    
    .ant-layout {
      .map-content {
        .ant-card {
          background: #1f1f1f;
          border-color: #434343;
          
          .ant-card-head {
            background: #262626;
            border-bottom-color: #434343;
            
            .ant-card-head-title {
              color: #fff;
            }
          }
        }
      }
      
      .spatial-sider {
        background: #1f1f1f;
        border-left-color: #434343;
        
        .ant-layout-sider-trigger {
          background: #177ddc;
          
          &:hover {
            background: #1890ff;
          }
        }
        
        .sider-content {
          .tab-content {
            .map-info-panel {
              .info-item {
                border-bottom-color: #434343;
                
                .label {
                  color: #999;
                }
                
                .value {
                  color: #fff;
                }
              }
              
              .selected-feature {
                border-top-color: #434343;
                
                h4 {
                  color: #40a9ff;
                }
              }
            }
          }
        }
      }
    }
    
    .system-info {
      .info-section {
        h4 {
          color: #40a9ff;
          border-bottom-color: #434343;
        }
        
        .info-item {
          .label {
            color: #999;
          }
          
          .value {
            color: #fff;
          }
        }
      }
    }
  }
}

// 高对比度主题支持
.high-contrast-theme {
  .spatial-info-system {
    background: #fff;
    
    .ant-layout {
      .map-content {
        .ant-card {
          border: 2px solid #000;
          
          .ant-card-head {
            border-bottom: 2px solid #000;
            
            .ant-card-head-title {
              color: #000;
              font-weight: bold;
            }
          }
        }
      }
      
      .spatial-sider {
        background: #fff;
        border-left: 2px solid #000;
        
        .sider-content {
          .tab-content {
            .map-info-panel {
              .info-item {
                border-bottom: 1px solid #000;
                
                .label,
                .value {
                  color: #000;
                  font-weight: bold;
                }
              }
              
              .selected-feature {
                border-top: 2px solid #000;
                
                h4 {
                  color: #000;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 动画效果
.spatial-info-system {
  .ant-layout {
    .spatial-sider {
      transition: all 0.3s ease;
      
      .sider-content {
        .ant-tabs {
          .ant-tabs-tab {
            transition: all 0.2s ease;
            
            &:hover {
              background: rgba(24, 144, 255, 0.1);
            }
            
            &.ant-tabs-tab-active {
              transform: translateY(-1px);
            }
          }
        }
        
        .tab-content {
          .map-info-panel {
            .info-item {
              transition: all 0.2s ease;
              
              &:hover {
                background: rgba(0, 0, 0, 0.02);
                padding-left: 4px;
              }
            }
          }
        }
      }
    }
  }
}

// 加载状态
.spatial-info-system {
  &.loading {
    .ant-layout {
      .map-content {
        opacity: 0.6;
        pointer-events: none;
        
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 32px;
          height: 32px;
          margin: -16px 0 0 -16px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #1890ff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          z-index: 1000;
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
