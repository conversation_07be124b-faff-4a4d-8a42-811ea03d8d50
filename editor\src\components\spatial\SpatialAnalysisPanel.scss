/**
 * 空间分析面板组件样式
 */
.spatial-analysis-panel {
  .analysis-types {
    .ant-radio-group {
      .ant-radio-wrapper {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        border-radius: 6px;
        transition: all 0.2s ease;
        margin-bottom: 4px;
        
        &:hover {
          background: #f5f5f5;
        }
        
        &.ant-radio-wrapper-checked {
          background: #e6f7ff;
          border-color: #1890ff;
        }
        
        .ant-radio {
          margin-right: 8px;
        }
        
        .ant-space {
          flex: 1;
          
          .anticon {
            font-size: 16px;
          }
        }
      }
    }
  }
  
  .analysis-parameters {
    .ant-form-item {
      margin-bottom: 12px;
      
      .ant-form-item-label {
        padding-bottom: 4px;
        
        label {
          font-size: 12px;
          font-weight: 500;
        }
      }
      
      .ant-input-number-group-wrapper {
        width: 100%;
        
        .ant-input-number {
          width: 100%;
        }
      }
    }
    
    .ant-btn-primary {
      height: 36px;
      font-weight: 500;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
      }
    }
    
    .ant-alert {
      .ant-alert-message {
        font-size: 12px;
      }
      
      .ant-alert-description {
        font-size: 11px;
      }
    }
  }
  
  .analysis-history {
    .ant-list {
      .ant-list-item {
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .ant-list-item-meta {
          .ant-list-item-meta-title {
            margin-bottom: 4px;
            font-size: 13px;
            font-weight: 500;
            
            .ant-space {
              .ant-tag {
                font-size: 11px;
                padding: 2px 6px;
                border-radius: 10px;
              }
            }
          }
          
          .ant-list-item-meta-description {
            font-size: 11px;
            color: #666;
            line-height: 1.4;
            
            div {
              margin: 2px 0;
            }
            
            .ant-progress {
              margin-top: 4px;
              
              .ant-progress-text {
                font-size: 10px;
              }
            }
          }
        }
        
        .ant-list-item-action {
          margin-left: 8px;
          
          .ant-btn {
            border: none;
            box-shadow: none;
            
            &:hover {
              background: rgba(0, 0, 0, 0.04);
            }
            
            &.ant-btn-dangerous:hover {
              background: rgba(255, 77, 79, 0.1);
            }
          }
        }
      }
      
      .ant-list-empty-text {
        padding: 20px;
        color: #999;
        font-size: 12px;
      }
    }
  }
  
  .ant-collapse {
    .ant-collapse-header {
      padding: 8px 0;
      font-size: 13px;
      font-weight: 500;
      
      .ant-collapse-arrow {
        font-size: 12px;
      }
    }
    
    .ant-collapse-content {
      .ant-collapse-content-box {
        padding: 12px 0;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .spatial-analysis-panel {
    .analysis-types {
      .ant-radio-group {
        .ant-radio-wrapper {
          padding: 6px 8px;
          
          .ant-space {
            .anticon {
              font-size: 14px;
            }
          }
        }
      }
    }
    
    .analysis-parameters {
      .ant-form-item {
        margin-bottom: 8px;
      }
      
      .ant-btn-primary {
        height: 32px;
      }
    }
    
    .analysis-history {
      .ant-list {
        .ant-list-item {
          padding: 8px 0;
          
          .ant-list-item-action {
            flex-direction: column;
            gap: 4px;
            
            .ant-btn {
              min-width: 28px;
              padding: 4px 6px;
            }
          }
        }
      }
    }
  }
}

// 暗色主题支持
.dark-theme {
  .spatial-analysis-panel {
    .analysis-types {
      .ant-radio-group {
        .ant-radio-wrapper {
          &:hover {
            background: #262626;
          }
          
          &.ant-radio-wrapper-checked {
            background: #111b26;
            border-color: #177ddc;
          }
        }
      }
    }
    
    .analysis-history {
      .ant-list {
        .ant-list-item {
          border-bottom-color: #434343;
          
          .ant-list-item-meta-description {
            color: #999;
          }
        }
        
        .ant-list-empty-text {
          color: #666;
        }
      }
    }
  }
}

// 高对比度主题支持
.high-contrast-theme {
  .spatial-analysis-panel {
    .analysis-types {
      .ant-radio-group {
        .ant-radio-wrapper {
          border: 1px solid #000;
          margin-bottom: 2px;
          
          &:hover {
            background: #f0f0f0;
          }
          
          &.ant-radio-wrapper-checked {
            background: #000;
            color: #fff;
          }
        }
      }
    }
    
    .analysis-parameters {
      .ant-form-item {
        .ant-form-item-control-input {
          .ant-input,
          .ant-select-selector,
          .ant-input-number {
            border: 2px solid #000;
          }
        }
      }
      
      .ant-btn-primary {
        border: 2px solid #000;
        background: #000;
        color: #fff;
        
        &:hover {
          background: #333;
          border-color: #333;
        }
      }
    }
    
    .analysis-history {
      .ant-list {
        .ant-list-item {
          border: 1px solid #000;
          margin-bottom: 4px;
          padding: 8px;
        }
      }
    }
  }
}

// 动画效果
.spatial-analysis-panel {
  .analysis-types {
    .ant-radio-group {
      .ant-radio-wrapper {
        .ant-space {
          .anticon {
            transition: all 0.2s ease;
            
            &:hover {
              transform: scale(1.1);
            }
          }
        }
      }
    }
  }
  
  .analysis-parameters {
    .ant-btn-primary {
      transition: all 0.2s ease;
      
      &.ant-btn-loading {
        .ant-btn-loading-icon {
          animation: spin 1s linear infinite;
        }
      }
    }
  }
  
  .analysis-history {
    .ant-list {
      .ant-list-item {
        transition: all 0.2s ease;
        
        &:hover {
          background: rgba(0, 0, 0, 0.02);
          transform: translateX(2px);
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 状态指示器
.spatial-analysis-panel {
  .analysis-history {
    .ant-list {
      .ant-list-item {
        .ant-list-item-meta {
          .ant-list-item-meta-title {
            .ant-tag {
              &.ant-tag-processing {
                animation: pulse 1.5s ease-in-out infinite;
              }
            }
          }
        }
      }
    }
  }
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}
