/**
 * 粒子编辑器组件测试
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../i18n';
import ParticleEditor from './index';

// 创建测试用的store
const createTestStore = () => {
  return configureStore({
    reducer: {
      // 这里可以添加需要的reducer
    },
  });
};

// 模拟Canvas API
Object.defineProperty(HTMLCanvasElement.prototype, 'getContext', {
  value: () => ({
    fillRect: jest.fn(),
    clearRect: jest.fn(),
    getImageData: jest.fn(() => ({ data: new Array(4) })),
    putImageData: jest.fn(),
    createImageData: jest.fn(() => []),
    setTransform: jest.fn(),
    drawImage: jest.fn(),
    save: jest.fn(),
    fillText: jest.fn(),
    restore: jest.fn(),
    beginPath: jest.fn(),
    moveTo: jest.fn(),
    lineTo: jest.fn(),
    closePath: jest.fn(),
    stroke: jest.fn(),
    translate: jest.fn(),
    scale: jest.fn(),
    rotate: jest.fn(),
    arc: jest.fn(),
    fill: jest.fn(),
    measureText: jest.fn(() => ({ width: 0 })),
    transform: jest.fn(),
    rect: jest.fn(),
    clip: jest.fn(),
  }),
});

// 模拟ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// 模拟requestAnimationFrame
global.requestAnimationFrame = jest.fn((cb) => setTimeout(cb, 16));
global.cancelAnimationFrame = jest.fn();

const renderWithProviders = (component: React.ReactElement) => {
  const store = createTestStore();
  
  return render(
    <Provider store={store}>
      <I18nextProvider i18n={i18n}>
        {component}
      </I18nextProvider>
    </Provider>
  );
};

describe('ParticleEditor', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('应该正确渲染粒子编辑器', () => {
    renderWithProviders(<ParticleEditor />);
    
    // 检查标题是否存在
    expect(screen.getByRole('heading')).toBeInTheDocument();
    
    // 检查预览画布是否存在
    expect(screen.getByRole('img')).toBeInTheDocument();
    
    // 检查控制按钮是否存在
    expect(screen.getByText(/播放|暂停/)).toBeInTheDocument();
    expect(screen.getByText(/重置/)).toBeInTheDocument();
  });

  it('应该正确渲染表单字段', () => {
    renderWithProviders(<ParticleEditor />);
    
    // 检查基本表单字段
    expect(screen.getByLabelText(/名称/)).toBeInTheDocument();
    expect(screen.getByLabelText(/最大粒子数/)).toBeInTheDocument();
    expect(screen.getByLabelText(/发射率/)).toBeInTheDocument();
  });

  it('应该正确处理播放/暂停切换', async () => {
    renderWithProviders(<ParticleEditor />);
    
    const playPauseButton = screen.getByText(/播放|暂停/);
    
    // 点击播放/暂停按钮
    fireEvent.click(playPauseButton);
    
    // 等待状态更新
    await waitFor(() => {
      expect(playPauseButton).toBeInTheDocument();
    });
  });

  it('应该正确处理预设选择', async () => {
    renderWithProviders(<ParticleEditor />);
    
    // 查找火焰预设按钮
    const firePresetButton = screen.getByText(/火焰/);
    
    // 点击火焰预设
    fireEvent.click(firePresetButton);
    
    // 等待预设应用
    await waitFor(() => {
      expect(firePresetButton).toHaveClass('ant-btn-primary');
    });
  });

  it('应该正确处理表单值变化', async () => {
    renderWithProviders(<ParticleEditor />);
    
    // 查找最大粒子数输入框
    const maxParticlesInput = screen.getByLabelText(/最大粒子数/);
    
    // 修改值
    fireEvent.change(maxParticlesInput, { target: { value: '2000' } });
    
    // 验证值已更新
    await waitFor(() => {
      expect(maxParticlesInput).toHaveValue(2000);
    });
  });

  it('应该正确处理保存操作', async () => {
    const mockOnSave = jest.fn();
    renderWithProviders(<ParticleEditor onSave={mockOnSave} />);
    
    // 查找保存按钮
    const saveButton = screen.getByText(/保存/);
    
    // 点击保存
    fireEvent.click(saveButton);
    
    // 验证保存回调被调用
    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalled();
    });
  });

  it('应该正确处理取消操作', () => {
    const mockOnCancel = jest.fn();
    renderWithProviders(<ParticleEditor onCancel={mockOnCancel} />);
    
    // 查找取消按钮
    const cancelButton = screen.getByText(/取消/);
    
    // 点击取消
    fireEvent.click(cancelButton);
    
    // 验证取消回调被调用
    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('应该在组件卸载时正确清理资源', () => {
    const { unmount } = renderWithProviders(<ParticleEditor />);
    
    // 卸载组件
    unmount();
    
    // 验证清理函数被调用（通过检查没有错误抛出）
    expect(true).toBe(true);
  });
});
