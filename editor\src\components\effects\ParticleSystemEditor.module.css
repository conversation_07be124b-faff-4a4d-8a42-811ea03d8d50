/**
 * 粒子系统编辑器样式
 */

.particleSystemEditor {
  width: 100%;
  height: 100%;
}

.editorContainer {
  display: flex;
  gap: 16px;
  height: 100%;
}

.previewSection {
  flex-shrink: 0;
  min-width: 300px;
}

.previewCanvas {
  width: 100%;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #000;
  display: block;
}

.previewControls {
  margin-top: 8px;
  text-align: center;
}

.presetSection {
  margin-top: 16px;
}

.presetButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.presetButton {
  font-size: 12px;
}

.editorSection {
  flex: 1;
  min-width: 0;
}

.basicSettings {
  margin-bottom: 16px;
}

.tabContent {
  padding: 16px 0;
}

.rangeInput {
  display: flex;
  gap: 8px;
}

.rangeInput .ant-input-number {
  flex: 1;
}

.vectorInput {
  display: flex;
  gap: 4px;
}

.vectorInput .ant-input-number {
  flex: 1;
}

.colorPicker {
  width: 100%;
}

.formItem {
  margin-bottom: 16px;
}

.formItem:last-child {
  margin-bottom: 0;
}

.sliderContainer {
  padding: 0 8px;
}

.uploadButton {
  border: none;
  box-shadow: none;
}

.shapeControls {
  margin-top: 16px;
}

.advancedSettings {
  padding: 16px 0;
}

.switchGroup {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .editorContainer {
    flex-direction: column;
  }
  
  .previewSection {
    min-width: auto;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .editorContainer {
    gap: 8px;
  }
  
  .presetButtons {
    gap: 2px;
  }
  
  .presetButton {
    font-size: 11px;
    padding: 2px 6px;
  }
  
  .vectorInput {
    flex-direction: column;
    gap: 8px;
  }
  
  .rangeInput {
    flex-direction: column;
    gap: 8px;
  }
}

/* 深色主题支持 */
[data-theme='dark'] .previewCanvas {
  border-color: #434343;
  background-color: #141414;
}

[data-theme='dark'] .particleSystemEditor {
  color: #fff;
}

/* 动画效果 */
.previewCanvas {
  transition: border-color 0.3s ease;
}

.previewCanvas:hover {
  border-color: #1890ff;
}

.presetButton {
  transition: all 0.3s ease;
}

.presetButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 自定义滚动条 */
.editorSection {
  overflow-y: auto;
}

.editorSection::-webkit-scrollbar {
  width: 6px;
}

.editorSection::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.editorSection::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.editorSection::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 表单项间距优化 */
.ant-form-item {
  margin-bottom: 12px;
}

.ant-tabs-content-holder {
  padding-top: 8px;
}

/* 颜色选择器样式 */
.ant-color-picker-trigger {
  width: 100% !important;
}

/* 输入框组样式 */
.ant-input-group-compact .ant-input-number {
  border-radius: 0;
}

.ant-input-group-compact .ant-input-number:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.ant-input-group-compact .ant-input-number:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

/* 标签页样式优化 */
.ant-tabs-tab {
  font-size: 12px;
  padding: 8px 12px;
}

.ant-tabs-content {
  padding: 0;
}

/* 卡片样式 */
.ant-card-head {
  padding: 0 16px;
  min-height: 40px;
}

.ant-card-head-title {
  font-size: 14px;
  font-weight: 500;
}

.ant-card-body {
  padding: 16px;
}

/* 按钮组样式 */
.ant-btn-group .ant-btn {
  font-size: 12px;
  height: 28px;
  padding: 0 8px;
}

/* 开关样式 */
.ant-switch {
  min-width: 44px;
}

/* 选择器样式 */
.ant-select {
  font-size: 12px;
}

.ant-select-selector {
  height: 28px !important;
}

.ant-select-selection-item {
  line-height: 26px !important;
}

/* 输入数字框样式 */
.ant-input-number {
  font-size: 12px;
  height: 28px;
}

.ant-input-number-input {
  height: 26px;
  padding: 0 8px;
}

/* 滑块样式 */
.ant-slider {
  margin: 8px 0;
}

.ant-slider-rail {
  height: 4px;
}

.ant-slider-track {
  height: 4px;
}

.ant-slider-handle {
  width: 14px;
  height: 14px;
  margin-top: -5px;
}
