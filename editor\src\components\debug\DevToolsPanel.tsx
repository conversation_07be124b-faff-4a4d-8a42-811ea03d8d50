/**
 * 开发工具面板
 * 包含服务状态监控、集成测试等开发调试功能
 */
import React, { useState } from 'react';
import { Drawer, Tabs, FloatButton } from 'antd';

import {
  BugOutlined,
  SettingOutlined,
  ApiOutlined,
  MonitorOutlined,
  ExperimentOutlined
} from '@ant-design/icons';
import ServiceStatusMonitor from './ServiceStatusMonitor';
import { config } from '../../config/environment';
import './DevToolsPanel.less';

interface DevToolsPanelProps {
  visible?: boolean;
  onClose?: () => void;
}

/**
 * 开发工具面板组件
 */
const DevToolsPanel: React.FC<DevToolsPanelProps> = ({
  visible = false,
  onClose
}) => {
  const [activeTab, setActiveTab] = useState('services');

  // 只在开发环境或启用调试时显示
  if (!config.enableDebug) {
    return null;
  }

  // 定义标签页配置
  const tabItems = [
    {
      key: 'services',
      label: (
        <span>
          <MonitorOutlined />
          服务监控
        </span>
      ),
      children: <ServiceStatusMonitor visible={true} />
    },
    {
      key: 'api',
      label: (
        <span>
          <ApiOutlined />
          API测试
        </span>
      ),
      children: (
        <div className="api-tester">
          <h3>API测试工具</h3>
          <p>这里可以添加API测试功能</p>
          {/* TODO: 实现API测试工具 */}
        </div>
      )
    },
    {
      key: 'config',
      label: (
        <span>
          <SettingOutlined />
          配置信息
        </span>
      ),
      children: (
        <div className="config-viewer">
          <h3>当前配置</h3>
          <div className="config-section">
            <h4>环境配置</h4>
            <div className="config-item">
              <strong>API URL:</strong> {config.apiUrl}
            </div>
            <div className="config-item">
              <strong>协作服务器:</strong> {config.collaborationServerUrl}
            </div>
            <div className="config-item">
              <strong>调试模式:</strong> {config.enableDebug ? '启用' : '禁用'}
            </div>
            <div className="config-item">
              <strong>模拟数据:</strong> {config.enableMockData ? '启用' : '禁用'}
            </div>
            <div className="config-item">
              <strong>分析统计:</strong> {config.enableAnalytics ? '启用' : '禁用'}
            </div>
          </div>

          <div className="config-section">
            <h4>性能配置</h4>
            <div className="config-item">
              <strong>请求超时:</strong> {config.requestTimeout}ms
            </div>
            <div className="config-item">
              <strong>缓存超时:</strong> {config.cacheTimeout}ms
            </div>
            <div className="config-item">
              <strong>最大重试:</strong> {config.maxRetries}
            </div>
          </div>

          <div className="config-section">
            <h4>日志配置</h4>
            <div className="config-item">
              <strong>日志级别:</strong> {config.logLevel}
            </div>
            <div className="config-item">
              <strong>控制台日志:</strong> {config.enableConsoleLog ? '启用' : '禁用'}
            </div>
            <div className="config-item">
              <strong>远程日志:</strong> {config.enableRemoteLog ? '启用' : '禁用'}
            </div>
          </div>

          <div className="config-section">
            <h4>微服务地址</h4>
            {Object.entries(config.services).map(([key, value]) => (
              <div key={key} className="config-item">
                <strong>{key}:</strong> {value}
              </div>
            ))}
          </div>
        </div>
      )
    },
    {
      key: 'experiments',
      label: (
        <span>
          <ExperimentOutlined />
          实验功能
        </span>
      ),
      children: (
        <div className="experiments">
          <h3>实验性功能</h3>
          <p>这里可以添加实验性功能的开关和测试</p>
          {/* TODO: 实现实验性功能管理 */}
        </div>
      )
    }
  ];

  return (
    <Drawer
      title={
        <div className="devtools-header">
          <BugOutlined style={{ marginRight: 8 }} />
          开发工具
        </div>
      }
      placement="right"
      width={800}
      open={visible}
      onClose={onClose}
      className="devtools-panel"
    >
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        type="card"
        items={tabItems}
      />
    </Drawer>
  );
};

/**
 * 开发工具浮动按钮
 */
export const DevToolsFloatButton: React.FC = () => {
  const [visible, setVisible] = useState(false);

  // 只在开发环境或启用调试时显示
  if (!config.enableDebug) {
    return null;
  }

  return (
    <>
      <FloatButton
        icon={<BugOutlined />}
        tooltip="开发工具"
        onClick={() => setVisible(true)}
        style={{
          right: 24,
          bottom: 80,
        }}
      />
      <DevToolsPanel
        visible={visible}
        onClose={() => setVisible(false)}
      />
    </>
  );
};

export default DevToolsPanel;
