/**
 * AI聊天面板样式
 */
.ai-chat-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  // 聊天头部
  .chat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;

    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .ai-avatar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      .header-info {
        .ai-name {
          font-weight: 600;
          font-size: 16px;
          color: #262626;
          display: block;
        }

        .connection-status {
          display: flex;
          align-items: center;
          gap: 6px;
          margin-top: 2px;

          .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            
            &.connected {
              background: #52c41a;
              box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
            }

            &.disconnected {
              background: #ff4d4f;
              box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
            }
          }

          .status-text {
            font-size: 12px;
            color: #8c8c8c;
          }
        }
      }
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }

  // 消息列表
  .chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;

    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }

    // 正在输入指示器
    .typing-indicator {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      background: #f6f7ff;
      border-radius: 12px;
      border: 1px solid #e6f7ff;

      .typing-content {
        display: flex;
        align-items: center;
        gap: 8px;

        span {
          color: #1890ff;
          font-size: 14px;
        }
      }
    }
  }

  // 输入区域
  .chat-input {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    padding: 16px;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;

    .ant-input {
      border-radius: 20px;
      border: 1px solid #d9d9d9;
      padding: 8px 16px;
      font-size: 14px;
      resize: none;

      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }

      &:disabled {
        background: #f5f5f5;
        color: #bfbfbf;
      }
    }

    .send-button {
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      &:disabled {
        background: #f5f5f5;
        border-color: #d9d9d9;
        color: #bfbfbf;
      }
    }
  }

  // 快捷操作
  .quick-actions {
    padding: 12px 16px;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;

    .ant-btn {
      border-radius: 16px;
      font-size: 12px;
      height: 28px;
      padding: 0 12px;
      border-color: #d9d9d9;
      color: #595959;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .chat-header {
      padding: 12px;

      .header-info .ai-name {
        font-size: 14px;
      }
    }

    .chat-messages {
      padding: 12px;
      gap: 12px;
    }

    .chat-input {
      padding: 12px;
      gap: 8px;

      .send-button {
        width: 36px;
        height: 36px;
      }
    }

    .quick-actions {
      padding: 8px 12px;

      .ant-btn {
        font-size: 11px;
        height: 24px;
        padding: 0 8px;
      }
    }
  }

  // 暗色主题支持
  &.dark-theme {
    background: #1f1f1f;
    color: #ffffff;

    .chat-header {
      background: #262626;
      border-bottom-color: #434343;

      .ai-name {
        color: #ffffff;
      }

      .status-text {
        color: #8c8c8c;
      }
    }

    .chat-messages {
      .typing-indicator {
        background: #262626;
        border-color: #434343;

        span {
          color: #1890ff;
        }
      }
    }

    .chat-input {
      background: #262626;
      border-top-color: #434343;

      .ant-input {
        background: #1f1f1f;
        border-color: #434343;
        color: #ffffff;

        &::placeholder {
          color: #8c8c8c;
        }

        &:focus {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
    }

    .quick-actions {
      background: #262626;
      border-top-color: #434343;

      .ant-btn {
        background: #1f1f1f;
        border-color: #434343;
        color: #ffffff;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }
      }
    }
  }

  // 动画效果
  .chat-messages > * {
    animation: fadeInUp 0.3s ease-out;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  // 加载状态
  &.loading {
    .chat-input {
      pointer-events: none;
      opacity: 0.6;
    }
  }

  // 错误状态
  &.error {
    .chat-header {
      background: #fff2f0;
      border-bottom-color: #ffccc7;

      .status-dot.disconnected {
        animation: pulse 1.5s infinite;
      }
    }
  }

  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(255, 77, 79, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(255, 77, 79, 0);
    }
  }
}
