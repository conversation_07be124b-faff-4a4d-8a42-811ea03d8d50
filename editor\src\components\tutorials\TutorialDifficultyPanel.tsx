/**
 * 教程难度级别面板组件
 * 展示不同难度级别的教程
 */
import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Tabs, 
  List, 
  Tag, 
  Button, 
  Typography, 
  Tooltip, 
  Empty, 
  Skeleton, 
  Badge,
  Progress,
  Collapse
} from 'antd';
import {
  PlayCircleOutlined,
  ReadOutlined,
  CodeOutlined,
  ClockCircleOutlined,
  TrophyOutlined,
  FireOutlined,
  BookOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { 
  tutorialRecommendationService, 
  TutorialItem, 
  TutorialType, 
  TutorialDifficulty 
} from '../../services/TutorialRecommendationService';
import { TutorialService } from '../../services/TutorialService';
import { VideoTutorialService } from '../../services/VideoTutorialService';
import { ExampleProjectService } from '../../services/ExampleProjectService';
import './TutorialDifficultyPanel.less';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;

/**
 * 教程难度级别面板属性接口
 */
interface TutorialDifficultyPanelProps {
  onTutorialSelect?: (item: TutorialItem) => void;
}

/**
 * 教程难度级别面板组件
 */
export const TutorialDifficultyPanel: React.FC<TutorialDifficultyPanelProps> = ({
  onTutorialSelect}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<string>(TutorialDifficulty.BEGINNER);
  const [tutorials, setTutorials] = useState<Map<string, TutorialItem[]>>(new Map());
  const [loading, setLoading] = useState<boolean>(true);
  const [stats, setStats] = useState<{
    total: number;
    completed: number;
    byDifficulty: Map<string, { total: number; completed: number }>;
  }>({
    total: 0,
    completed: 0,
    byDifficulty: new Map()});

  // 初始化
  useEffect(() => {
    loadTutorials();

    // 监听教程完成事件
    const handleItemCompleted = () => {
      loadTutorials();
    };

    tutorialRecommendationService.on('itemCompleted', handleItemCompleted);

    return () => {
      tutorialRecommendationService.off('itemCompleted', handleItemCompleted);
    };
  }, []);

  /**
   * 加载教程数据
   */
  const loadTutorials = () => {
    setLoading(true);
    
    // 获取所有教程项
    const allTutorials = tutorialRecommendationService.getAllTutorialItems();
    
    // 按难度分组
    const tutorialsByDifficulty = new Map<string, TutorialItem[]>();
    const difficultyStats = new Map<string, { total: number; completed: number }>();
    
    // 初始化难度分组
    Object.values(TutorialDifficulty).forEach(difficulty => {
      tutorialsByDifficulty.set(difficulty, []);
      difficultyStats.set(difficulty, { total: 0, completed: 0 });
    });
    
    // 分组教程
    let totalTutorials = 0;
    let completedTutorials = 0;
    
    allTutorials.forEach(tutorial => {
      const difficulty = tutorial.difficulty;
      const tutorialsInDifficulty = tutorialsByDifficulty.get(difficulty) || [];
      tutorialsInDifficulty.push(tutorial);
      tutorialsByDifficulty.set(difficulty, tutorialsInDifficulty);
      
      // 更新统计数据
      totalTutorials++;
      if (tutorial.completed) {
        completedTutorials++;
      }
      
      const stats = difficultyStats.get(difficulty) || { total: 0, completed: 0 };
      stats.total++;
      if (tutorial.completed) {
        stats.completed++;
      }
      difficultyStats.set(difficulty, stats);
    });
    
    // 排序每个难度内的教程
    tutorialsByDifficulty.forEach((tutorialsInDifficulty, difficulty) => {
      // 按完成状态和类别排序
      tutorialsInDifficulty.sort((a, b) => {
        // 首先按完成状态排序（未完成的排在前面）
        if (a.completed !== b.completed) {
          return a.completed ? 1 : -1;
        }
        
        // 然后按类别排序
        if (a.category !== b.category) {
          return a.category.localeCompare(b.category);
        }
        
        // 最后按标题排序
        return a.title.localeCompare(b.title);
      });
      
      tutorialsByDifficulty.set(difficulty, tutorialsInDifficulty);
    });
    
    setTutorials(tutorialsByDifficulty);
    setStats({
      total: totalTutorials,
      completed: completedTutorials,
      byDifficulty: difficultyStats});
    
    setLoading(false);
  };

  /**
   * 处理教程项点击
   */
  const handleTutorialItemClick = (item: TutorialItem) => {
    if (onTutorialSelect) {
      onTutorialSelect(item);
      return;
    }

    // 根据教程类型打开相应的教程
    switch (item.type) {
      case TutorialType.INTERACTIVE:
        const tutorialId = item.id.split(':')[1];
        TutorialService.getInstance().startTutorial(tutorialId);
        break;
      case TutorialType.VIDEO:
        const videoId = item.id.split(':')[1];
        VideoTutorialService.getInstance().playTutorial(videoId);
        break;
      case TutorialType.EXAMPLE:
        const exampleId = item.id.split(':')[1];
        ExampleProjectService.getInstance().markExampleAsViewed(exampleId);
        window.open(`/examples/${exampleId}/`, '_blank');
        break;
    }
  };

  /**
   * 获取教程类型图标
   */
  const getTutorialTypeIcon = (type: TutorialType) => {
    switch (type) {
      case TutorialType.INTERACTIVE:
        return <ReadOutlined />;
      case TutorialType.VIDEO:
        return <PlayCircleOutlined />;
      case TutorialType.EXAMPLE:
        return <CodeOutlined />;
      default:
        return <BookOutlined />;
    }
  };

  /**
   * 获取教程类型文本
   */
  const getTutorialTypeText = (type: TutorialType) => {
    switch (type) {
      case TutorialType.INTERACTIVE:
        return t('tutorials.types.interactive');
      case TutorialType.VIDEO:
        return t('tutorials.types.video');
      case TutorialType.EXAMPLE:
        return t('tutorials.types.example');
      default:
        return t('tutorials.types.unknown');
    }
  };

  /**
   * 获取难度标签颜色
   */
  const getDifficultyColor = (difficulty: TutorialDifficulty) => {
    switch (difficulty) {
      case TutorialDifficulty.BEGINNER:
        return 'green';
      case TutorialDifficulty.INTERMEDIATE:
        return 'blue';
      case TutorialDifficulty.ADVANCED:
        return 'orange';
      case TutorialDifficulty.EXPERT:
        return 'red';
      default:
        return 'default';
    }
  };

  /**
   * 获取难度文本
   */
  const getDifficultyText = (difficulty: TutorialDifficulty) => {
    switch (difficulty) {
      case TutorialDifficulty.BEGINNER:
        return t('tutorials.difficulty.beginner');
      case TutorialDifficulty.INTERMEDIATE:
        return t('tutorials.difficulty.intermediate');
      case TutorialDifficulty.ADVANCED:
        return t('tutorials.difficulty.advanced');
      case TutorialDifficulty.EXPERT:
        return t('tutorials.difficulty.expert');
      default:
        return t('tutorials.difficulty.unknown');
    }
  };

  /**
   * 获取难度描述
   */
  const getDifficultyDescription = (difficulty: TutorialDifficulty) => {
    switch (difficulty) {
      case TutorialDifficulty.BEGINNER:
        return t('tutorials.difficulty.beginnerDescription');
      case TutorialDifficulty.INTERMEDIATE:
        return t('tutorials.difficulty.intermediateDescription');
      case TutorialDifficulty.ADVANCED:
        return t('tutorials.difficulty.advancedDescription');
      case TutorialDifficulty.EXPERT:
        return t('tutorials.difficulty.expertDescription');
      default:
        return '';
    }
  };

  /**
   * 渲染教程项卡片
   */
  const renderTutorialItem = (item: TutorialItem) => {
    return (
      <Card 
        className="tutorial-item-card"
        hoverable
        onClick={() => handleTutorialItemClick(item)}
        cover={
          item.thumbnailUrl ? (
            <div className="tutorial-item-cover">
              <img alt={item.title} src={item.thumbnailUrl} />
              {item.completed && (
                <div className="tutorial-completed-badge">
                  <TrophyOutlined />
                </div>
              )}
            </div>
          ) : null
        }
      >
        <Card.Meta
          title={
            <div className="tutorial-item-title">
              <span>{item.title}</span>
              {item.popularity && item.popularity > 80 && (
                <Tooltip title={t('tutorials.popular')}>
                  <FireOutlined className="popular-icon" />
                </Tooltip>
              )}
            </div>
          }
          description={
            <div className="tutorial-item-description">
              <Paragraph ellipsis={{ rows: 2 }}>{item.description}</Paragraph>
              
              <div className="tutorial-item-meta">
                <Tooltip title={getTutorialTypeText(item.type)}>
                  <Tag icon={getTutorialTypeIcon(item.type)}>
                    {getTutorialTypeText(item.type)}
                  </Tag>
                </Tooltip>
                
                <Tooltip title={t('tutorials.category')}>
                  <Tag>
                    {item.category}
                  </Tag>
                </Tooltip>
                
                {item.duration && (
                  <Tooltip title={t('tutorials.duration')}>
                    <Tag icon={<ClockCircleOutlined />}>
                      {item.duration} {t('tutorials.minutes')}
                    </Tag>
                  </Tooltip>
                )}
              </div>
              
              {item.progress !== undefined && item.progress > 0 && !item.completed && (
                <div className="tutorial-progress">
                  <div className="progress-bar">
                    <div 
                      className="progress-fill" 
                      style={{ width: `${item.progress}%` }}
                    />
                  </div>
                  <Text type="secondary">{item.progress}%</Text>
                </div>
              )}
              
              <Button 
                type="primary" 
                className="start-tutorial-button"
                onClick={(e) => {
                  e.stopPropagation();
                  handleTutorialItemClick(item);
                }}
              >
                {item.completed ? t('tutorials.review') : t('tutorials.start')}
              </Button>
            </div>
          }
        />
      </Card>
    );
  };

  /**
   * 渲染难度标签
   */
  const renderDifficultyTab = (difficulty: TutorialDifficulty) => {
    const difficultyStats = stats.byDifficulty.get(difficulty);
    const total = difficultyStats?.total || 0;
    const completed = difficultyStats?.completed || 0;
    
    return (
      <Badge 
        count={total > 0 ? `${completed}/${total}` : 0} 
        style={{ backgroundColor: getDifficultyColor(difficulty) }}
      >
        {getDifficultyText(difficulty)}
      </Badge>
    );
  };

  /**
   * 按类别分组教程
   */
  const groupTutorialsByCategory = (tutorialsInDifficulty: TutorialItem[]) => {
    const categories = new Map<string, TutorialItem[]>();
    
    tutorialsInDifficulty.forEach(tutorial => {
      const category = tutorial.category;
      const tutorialsInCategory = categories.get(category) || [];
      tutorialsInCategory.push(tutorial);
      categories.set(category, tutorialsInCategory);
    });
    
    return categories;
  };

  /**
   * 渲染难度级别内容
   */
  const renderDifficultyContent = (difficulty: TutorialDifficulty) => {
    const tutorialsInDifficulty = tutorials.get(difficulty) || [];
    
    if (tutorialsInDifficulty.length === 0) {
      return (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={t('tutorials.noTutorialsInDifficulty')}
        />
      );
    }
    
    // 按类别分组
    const categoriesMap = groupTutorialsByCategory(tutorialsInDifficulty);
    const categories = Array.from(categoriesMap.entries()).sort((a, b) => a[0].localeCompare(b[0]));
    
    return (
      <div className="difficulty-content">
        <Paragraph className="difficulty-description">
          {getDifficultyDescription(difficulty)}
        </Paragraph>
        
        <div className="difficulty-stats">
          <div className="stats-item">
            <Text strong>{t('tutorials.totalTutorials')}:</Text>
            <Text>{tutorialsInDifficulty.length}</Text>
          </div>
          <div className="stats-item">
            <Text strong>{t('tutorials.completedTutorials')}:</Text>
            <Text>{tutorialsInDifficulty.filter(t => t.completed).length}</Text>
          </div>
          <div className="stats-item">
            <Text strong>{t('tutorials.progress')}:</Text>
            <Progress 
              percent={Math.round((tutorialsInDifficulty.filter(t => t.completed).length / tutorialsInDifficulty.length) * 100)} 
              size="small" 
              style={{ width: 120 }}
            />
          </div>
        </div>
        
        <Collapse 
          defaultActiveKey={categories.map(([category]) => category)}
          className="categories-collapse"
        >
          {categories.map(([category, tutorialsInCategory]) => (
            <Panel 
              key={category} 
              header={
                <div className="category-header">
                  <span>{category}</span>
                  <Badge 
                    count={`${tutorialsInCategory.filter(t => t.completed).length}/${tutorialsInCategory.length}`} 
                    style={{ backgroundColor: '#1890ff' }}
                  />
                </div>
              }
            >
              <List
                grid={{ gutter: 16, xs: 1, sm: 2, md: 2, lg: 3, xl: 3, xxl: 4 }}
                dataSource={tutorialsInCategory}
                renderItem={item => (
                  <List.Item>
                    {renderTutorialItem(item)}
                  </List.Item>
                )}
              />
            </Panel>
          ))}
        </Collapse>
      </div>
    );
  };

  return (
    <div className="tutorial-difficulty-panel">
      <div className="difficulty-header">
        <Title level={3}>{t('tutorials.difficultyLevels')}</Title>
        <Tooltip title={t('tutorials.difficultyLevelsHelp')}>
          <QuestionCircleOutlined className="help-icon" />
        </Tooltip>
      </div>
      
      <div className="overall-progress">
        <Text strong>{t('tutorials.overallProgress')}:</Text>
        <Progress 
          percent={Math.round((stats.completed / stats.total) * 100)} 
          format={percent => `${percent}% (${stats.completed}/${stats.total})`}
        />
      </div>
      
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={renderDifficultyTab(TutorialDifficulty.BEGINNER)} key={TutorialDifficulty.BEGINNER}>
          {loading ? (
            <div className="tutorial-skeleton">
              <Skeleton active avatar paragraph={{ rows: 3 }} />
              <Skeleton active avatar paragraph={{ rows: 3 }} />
            </div>
          ) : (
            renderDifficultyContent(TutorialDifficulty.BEGINNER)
          )}
        </TabPane>
        
        <TabPane tab={renderDifficultyTab(TutorialDifficulty.INTERMEDIATE)} key={TutorialDifficulty.INTERMEDIATE}>
          {loading ? (
            <div className="tutorial-skeleton">
              <Skeleton active avatar paragraph={{ rows: 3 }} />
              <Skeleton active avatar paragraph={{ rows: 3 }} />
            </div>
          ) : (
            renderDifficultyContent(TutorialDifficulty.INTERMEDIATE)
          )}
        </TabPane>
        
        <TabPane tab={renderDifficultyTab(TutorialDifficulty.ADVANCED)} key={TutorialDifficulty.ADVANCED}>
          {loading ? (
            <div className="tutorial-skeleton">
              <Skeleton active avatar paragraph={{ rows: 3 }} />
              <Skeleton active avatar paragraph={{ rows: 3 }} />
            </div>
          ) : (
            renderDifficultyContent(TutorialDifficulty.ADVANCED)
          )}
        </TabPane>
        
        <TabPane tab={renderDifficultyTab(TutorialDifficulty.EXPERT)} key={TutorialDifficulty.EXPERT}>
          {loading ? (
            <div className="tutorial-skeleton">
              <Skeleton active avatar paragraph={{ rows: 3 }} />
              <Skeleton active avatar paragraph={{ rows: 3 }} />
            </div>
          ) : (
            renderDifficultyContent(TutorialDifficulty.EXPERT)
          )}
        </TabPane>
      </Tabs>
    </div>
  );
};
