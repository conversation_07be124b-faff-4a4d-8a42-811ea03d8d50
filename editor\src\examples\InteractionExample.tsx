/**
 * 交互系统使用示例
 * 展示如何在编辑器中集成和使用交互功能
 */
import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Button, Space, message, Divider } from 'antd';
import { PlayCircleOutlined, StopOutlined, ReloadOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import {
  InteractionManager,
  InteractionEditor,
  InteractionPreview,
  interactionService,
  createDefaultInteractionConfig,
  INTERACTION_PRESETS
} from '../components/interaction';
import './InteractionExample.less';

/**
 * 交互示例组件
 */
const InteractionExample: React.FC = () => {
  const { t } = useTranslation();
  const [isRunning, setIsRunning] = useState(false);
  const [selectedPreset, setSelectedPreset] = useState<string>('BUTTON');
  const [currentConfig, setCurrentConfig] = useState(createDefaultInteractionConfig());

  // 模拟实体数据
  const mockEntity = {
    id: 'example-entity-1',
    name: '示例对象',
    components: {
      Transform: {
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 }
      },
      Interactable: currentConfig
    }
  };

  useEffect(() => {
    // 初始化交互服务（模拟）
    const initService = async () => {
      try {
        // 在实际应用中，这里会传入真实的世界实例
        // await interactionService.initialize(world);
        console.log('Interaction service initialized for example');
      } catch (error) {
        console.error('Failed to initialize interaction service:', error);
      }
    };

    initService();
  }, []);

  /**
   * 应用预设配置
   */
  const applyPreset = (presetName: string) => {
    const preset = INTERACTION_PRESETS[presetName as keyof typeof INTERACTION_PRESETS];
    if (preset) {
      const newConfig = {
        ...createDefaultInteractionConfig(),
        ...preset
      };
      setCurrentConfig(newConfig);
      setSelectedPreset(presetName);
      message.success(t('interaction.presetApplied', { name: presetName }));
    }
  };

  /**
   * 开始交互演示
   */
  const startDemo = () => {
    setIsRunning(true);
    message.info(t('interaction.demoStarted'));
    
    // 模拟交互事件
    setTimeout(() => {
      interactionService.emit('interaction', {
        type: 'interaction',
        entityId: mockEntity.id,
        interactionType: currentConfig.interactionType,
        timestamp: Date.now(),
        data: { config: currentConfig }
      });
    }, 1000);
  };

  /**
   * 停止交互演示
   */
  const stopDemo = () => {
    setIsRunning(false);
    message.info(t('interaction.demoStopped'));
  };

  /**
   * 重置演示
   */
  const resetDemo = () => {
    setIsRunning(false);
    setCurrentConfig(createDefaultInteractionConfig());
    setSelectedPreset('BUTTON');
    interactionService.clearStats();
    message.success(t('interaction.demoReset'));
  };

  return (
    <div className="interaction-example">
      <Card title="交互系统示例" className="example-header">
        <p>
          这个示例展示了如何在DL引擎编辑器中使用交互系统。
          您可以配置不同的交互类型、预览效果，并查看统计信息。
        </p>
        
        <Space>
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            onClick={startDemo}
            disabled={isRunning}
          >
            开始演示
          </Button>
          
          <Button
            icon={<StopOutlined />}
            onClick={stopDemo}
            disabled={!isRunning}
          >
            停止演示
          </Button>
          
          <Button
            icon={<ReloadOutlined />}
            onClick={resetDemo}
          >
            重置演示
          </Button>
        </Space>
      </Card>

      <Row gutter={16} style={{ marginTop: 16 }}>
        {/* 预设配置 */}
        <Col span={6}>
          <Card title="预设配置" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              {Object.keys(INTERACTION_PRESETS).map(presetName => (
                <Button
                  key={presetName}
                  type={selectedPreset === presetName ? 'primary' : 'default'}
                  block
                  onClick={() => applyPreset(presetName)}
                >
                  {presetName}
                </Button>
              ))}
            </Space>
          </Card>
        </Col>

        {/* 交互编辑器 */}
        <Col span={12}>
          <Card title="交互编辑器" size="small">
            <InteractionEditor
              data={currentConfig}
              onChange={setCurrentConfig}
              readonly={isRunning}
            />
          </Card>
        </Col>

        {/* 预览和统计 */}
        <Col span={6}>
          <Card title="预览" size="small" style={{ marginBottom: 16 }}>
            <InteractionPreview
              config={currentConfig}
              autoPlay={isRunning}
              size="small"
              showControls={false}
              showStats={true}
            />
          </Card>
        </Col>
      </Row>

      <Divider />

      {/* 完整的交互管理器示例 */}
      <Card title="完整交互管理器" style={{ marginTop: 16 }}>
        <p>下面是完整的交互管理器组件，包含所有功能：</p>
        <div style={{ height: 600, border: '1px solid #f0f0f0', borderRadius: 6 }}>
          <InteractionManager
            showToolbar={true}
            showPreview={true}
            defaultActiveTab="editor"
            layout="horizontal"
          />
        </div>
      </Card>

      {/* 代码示例 */}
      <Card title="代码示例" style={{ marginTop: 16 }}>
        <pre className="code-example">
{`// 1. 导入交互组件
import {
  InteractionManager,
  InteractionEditor,
  interactionService,
  createDefaultInteractionConfig
} from '../components/interaction';

// 2. 创建交互配置
const config = createDefaultInteractionConfig();

// 3. 使用交互编辑器
<InteractionEditor
  data={config}
  onChange={handleConfigChange}
/>

// 4. 使用完整管理器
<InteractionManager
  showToolbar={true}
  showPreview={true}
  layout="horizontal"
/>

// 5. 监听交互事件
interactionService.on('interaction', (event) => {
  console.log('Interaction occurred:', event);
});

// 6. 创建可交互组件
await interactionService.createInteractableComponent(entity, config);`}
        </pre>
      </Card>

      {/* 功能特性 */}
      <Card title="功能特性" style={{ marginTop: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <h4>🎯 交互类型</h4>
            <ul>
              <li>点击交互</li>
              <li>悬停交互</li>
              <li>接近交互</li>
              <li>抓取交互</li>
              <li>触摸交互</li>
            </ul>
          </Col>
          
          <Col span={8}>
            <h4>✨ 视觉效果</h4>
            <ul>
              <li>轮廓高亮</li>
              <li>发光效果</li>
              <li>颜色变化</li>
              <li>缩放动画</li>
              <li>自定义提示</li>
            </ul>
          </Col>
          
          <Col span={8}>
            <h4>🔧 高级功能</h4>
            <ul>
              <li>条件系统</li>
              <li>事件处理</li>
              <li>统计分析</li>
              <li>调试工具</li>
              <li>预览模式</li>
            </ul>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default InteractionExample;
