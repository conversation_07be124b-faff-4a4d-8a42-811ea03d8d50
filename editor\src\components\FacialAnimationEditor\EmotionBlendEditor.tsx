/**
 * 情感混合编辑器组件
 * 用于编辑情感混合配置
 */
import React, { useState, useEffect, useRef } from 'react';
import { Form, InputNumber, Select, Slider, Button, Collapse, Divider, Space, Row, Col, Tag } from 'antd';
import { PlusOutlined, MinusOutlined, EditOutlined, LineChartOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Panel } = Collapse;
const { Option } = Select;

/**
 * 情感混合配置
 */
export interface EmotionBlendConfig {
  /** 主要情感 */
  primaryEmotion: string;
  /** 主要情感强度 */
  primaryIntensity: number;
  /** 次要情感 */
  secondaryEmotion?: string;
  /** 次要情感强度 */
  secondaryIntensity?: number;
  /** 混合模式 */
  blendMode: 'add' | 'multiply' | 'override' | 'weighted';
  /** 混合权重 */
  blendWeight?: number;
  /** 过渡时间 */
  transitionTime: number;
  /** 情感变化 */
  emotionChanges?: EmotionChange[];
}

/**
 * 情感变化
 */
export interface EmotionChange {
  /** 时间点 */
  time: number;
  /** 情感 */
  emotion: string;
  /** 强度 */
  intensity: number;
}

/**
 * 情感混合编辑器属性
 */
interface EmotionBlendEditorProps {
  /** 可用情感 */
  availableEmotions: string[];
  /** 初始配置 */
  initialConfig?: EmotionBlendConfig;
  /** 变更回调 */
  onChange?: (config: EmotionBlendConfig) => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否显示图表 */
  showChart?: boolean;
}

/**
 * 默认情感混合配置
 */
const DEFAULT_BLEND_CONFIG: EmotionBlendConfig = {
  primaryEmotion: 'happy',
  primaryIntensity: 0.8,
  secondaryEmotion: 'surprised',
  secondaryIntensity: 0.3,
  blendMode: 'weighted',
  blendWeight: 0.7,
  transitionTime: 0.3,
  emotionChanges: [
    { time: 0.0, emotion: 'neutral', intensity: 0.8 },
    { time: 0.3, emotion: 'happy', intensity: 0.5 },
    { time: 0.7, emotion: 'surprised', intensity: 0.3 },
    { time: 1.0, emotion: 'happy', intensity: 0.8 }
  ]
};

/**
 * 情感颜色映射
 */
const EMOTION_COLORS: Record<string, string> = {
  happy: '#52c41a',
  sad: '#1890ff',
  angry: '#f5222d',
  surprised: '#faad14',
  fear: '#722ed1',
  disgust: '#eb2f96',
  neutral: '#bfbfbf',
  excited: '#fa541c',
  anxious: '#13c2c2',
  content: '#52c41a',
  bored: '#8c8c8c',
  confused: '#fadb14',
  disappointed: '#1890ff',
  proud: '#fa8c16',
  grateful: '#52c41a',
  hopeful: '#1890ff',
  lonely: '#722ed1',
  loving: '#eb2f96',
  nostalgic: '#faad14'
};

/**
 * 情感混合编辑器组件
 */
const EmotionBlendEditor: React.FC<EmotionBlendEditorProps> = ({
  availableEmotions,
  initialConfig,
  onChange,
  disabled = false,
  showChart = true
}) => {
  const { t } = useTranslation();
  
  // 状态
  const [config, setConfig] = useState<EmotionBlendConfig>(
    initialConfig || { ...DEFAULT_BLEND_CONFIG }
  );
  const [showEmotionChanges, setShowEmotionChanges] = useState<boolean>(false);
  const [editingChange, setEditingChange] = useState<number | null>(null);
  
  // 图表引用
  const chartRef = useRef<HTMLCanvasElement>(null);
  
  // 当配置变更时，调用回调
  useEffect(() => {
    onChange?.(config);
  }, [config, onChange]);
  
  // 当配置变更或图表显示状态变更时，重绘图表
  useEffect(() => {
    if (showChart && chartRef.current) {
      drawChart();
    }
  }, [config, showChart]);
  
  // 处理配置变更
  const handleConfigChange = (field: keyof EmotionBlendConfig, value: any) => {
    setConfig({ ...config, [field]: value });
  };
  
  // 处理情感变化变更
  const handleEmotionChangeUpdate = (index: number, field: keyof EmotionChange, value: any) => {
    const newChanges = [...(config.emotionChanges || [])];
    newChanges[index] = { ...newChanges[index], [field]: value };
    
    // 排序变化
    if (field === 'time') {
      newChanges.sort((a, b) => a.time - b.time);
      
      // 更新编辑索引
      if (editingChange !== null) {
        const newIndex = newChanges.findIndex(change => 
          change.emotion === config.emotionChanges?.[editingChange].emotion && 
          change.intensity === config.emotionChanges?.[editingChange].intensity
        );
        setEditingChange(newIndex);
      }
    }
    
    handleConfigChange('emotionChanges', newChanges);
  };
  
  // 添加情感变化
  const handleAddEmotionChange = () => {
    const changes = [...(config.emotionChanges || [])];
    const lastTime = changes.length > 0 ? changes[changes.length - 1].time : 0;
    const newTime = Math.min(lastTime + 0.2, 1.0);
    
    const newChange: EmotionChange = {
      time: newTime,
      emotion: config.primaryEmotion,
      intensity: 0.5
    };
    
    changes.push(newChange);
    changes.sort((a, b) => a.time - b.time);
    
    handleConfigChange('emotionChanges', changes);
    setEditingChange(changes.length - 1);
  };
  
  // 删除情感变化
  const handleRemoveEmotionChange = (index: number) => {
    const newChanges = [...(config.emotionChanges || [])];
    newChanges.splice(index, 1);
    
    handleConfigChange('emotionChanges', newChanges);
    
    if (editingChange === index) {
      setEditingChange(null);
    } else if (editingChange !== null && editingChange > index) {
      setEditingChange(editingChange - 1);
    }
  };
  
  // 重置为默认配置
  const handleReset = () => {
    setConfig({ ...DEFAULT_BLEND_CONFIG });
    setEditingChange(null);
  };
  
  // 绘制图表
  const drawChart = () => {
    const canvas = chartRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // 设置画布大小
    const width = canvas.width;
    const height = canvas.height;
    
    // 绘制背景
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, width, height);
    
    // 绘制网格
    ctx.strokeStyle = '#e8e8e8';
    ctx.lineWidth = 1;
    
    // 水平网格线
    for (let i = 0; i <= 10; i++) {
      const y = height - (i / 10) * height;
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }
    
    // 垂直网格线
    for (let i = 0; i <= 10; i++) {
      const x = (i / 10) * width;
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
    }
    
    // 绘制情感变化
    const changes = config.emotionChanges || [];
    if (changes.length < 2) return;
    
    // 按情感分组
    const emotionGroups: Record<string, EmotionChange[]> = {};
    
    for (const change of changes) {
      if (!emotionGroups[change.emotion]) {
        emotionGroups[change.emotion] = [];
      }
      emotionGroups[change.emotion].push(change);
    }
    
    // 绘制每个情感的曲线
    for (const emotion in emotionGroups) {
      const emotionChanges = emotionGroups[emotion].sort((a, b) => a.time - b.time);
      
      // 设置线条颜色
      ctx.strokeStyle = EMOTION_COLORS[emotion] || '#000000';
      ctx.lineWidth = 2;
      
      // 开始绘制路径
      ctx.beginPath();
      
      // 移动到第一个点
      const firstChange = emotionChanges[0];
      const firstX = firstChange.time * width;
      const firstY = height - firstChange.intensity * height;
      ctx.moveTo(firstX, firstY);
      
      // 绘制到其他点
      for (let i = 1; i < emotionChanges.length; i++) {
        const change = emotionChanges[i];
        const x = change.time * width;
        const y = height - change.intensity * height;
        
        // 使用贝塞尔曲线平滑过渡
        const prevChange = emotionChanges[i - 1];
        const prevX = prevChange.time * width;
        const prevY = height - prevChange.intensity * height;
        
        const cpX1 = prevX + (x - prevX) / 3;
        const cpX2 = prevX + (x - prevX) * 2 / 3;
        
        ctx.bezierCurveTo(cpX1, prevY, cpX2, y, x, y);
      }
      
      // 绘制线条
      ctx.stroke();
      
      // 绘制点
      ctx.fillStyle = EMOTION_COLORS[emotion] || '#000000';
      for (const change of emotionChanges) {
        const x = change.time * width;
        const y = height - change.intensity * height;
        
        ctx.beginPath();
        ctx.arc(x, y, 4, 0, Math.PI * 2);
        ctx.fill();
      }
    }
  };
  
  // 渲染基本混合设置
  const renderBasicBlendSettings = () => (
    <div className="basic-blend-settings">
      <Form layout="vertical">
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label={t('editor.emotion.primaryEmotion')}>
              <Select
                value={config.primaryEmotion}
                onChange={(value) => handleConfigChange('primaryEmotion', value)}
                disabled={disabled}
                style={{ width: '100%' }}
              >
                {availableEmotions.map((emotion) => (
                  <Option key={emotion} value={emotion}>
                    <Tag color={EMOTION_COLORS[emotion] || '#000000'}>{emotion}</Tag>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label={t('editor.emotion.primaryIntensity')}>
              <Slider
                value={config.primaryIntensity}
                min={0}
                max={1}
                step={0.1}
                onChange={(value) => handleConfigChange('primaryIntensity', value)}
                disabled={disabled}
                marks={{ 0: '0', 0.5: '0.5', 1: '1' }}
              />
            </Form.Item>
          </Col>
        </Row>
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label={t('editor.emotion.secondaryEmotion')}>
              <Select
                value={config.secondaryEmotion}
                onChange={(value) => handleConfigChange('secondaryEmotion', value)}
                disabled={disabled}
                style={{ width: '100%' }}
                allowClear
              >
                {availableEmotions.map((emotion) => (
                  <Option key={emotion} value={emotion}>
                    <Tag color={EMOTION_COLORS[emotion] || '#000000'}>{emotion}</Tag>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label={t('editor.emotion.secondaryIntensity')}>
              <Slider
                value={config.secondaryIntensity || 0}
                min={0}
                max={1}
                step={0.1}
                onChange={(value) => handleConfigChange('secondaryIntensity', value)}
                disabled={disabled || !config.secondaryEmotion}
                marks={{ 0: '0', 0.5: '0.5', 1: '1' }}
              />
            </Form.Item>
          </Col>
        </Row>
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label={t('editor.emotion.blendMode')}>
              <Select
                value={config.blendMode}
                onChange={(value) => handleConfigChange('blendMode', value)}
                disabled={disabled}
                style={{ width: '100%' }}
              >
                <Option value="add">{t('editor.emotion.blendModes.add')}</Option>
                <Option value="multiply">{t('editor.emotion.blendModes.multiply')}</Option>
                <Option value="override">{t('editor.emotion.blendModes.override')}</Option>
                <Option value="weighted">{t('editor.emotion.blendModes.weighted')}</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label={t('editor.emotion.blendWeight')}>
              <Slider
                value={config.blendWeight || 0.5}
                min={0}
                max={1}
                step={0.1}
                onChange={(value) => handleConfigChange('blendWeight', value)}
                disabled={disabled || config.blendMode !== 'weighted'}
                marks={{ 0: '0', 0.5: '0.5', 1: '1' }}
              />
            </Form.Item>
          </Col>
        </Row>
        
        <Form.Item label={t('editor.emotion.transitionTime')}>
          <InputNumber
            value={config.transitionTime}
            onChange={(value) => handleConfigChange('transitionTime', value)}
            disabled={disabled}
            min={0}
            max={5}
            step={0.1}
            style={{ width: '100%' }}
          />
        </Form.Item>
      </Form>
    </div>
  );
  
  // 渲染情感变化设置
  const renderEmotionChangesSettings = () => (
    <div className="emotion-changes-settings">
      {showChart && (
        <div className="emotion-chart" style={{ marginBottom: 16 }}>
          <canvas
            ref={chartRef}
            width={400}
            height={200}
            style={{ width: '100%', height: 200 }}
          />
        </div>
      )}
      
      <div className="emotion-changes-list">
        {(config.emotionChanges || []).map((change, index) => (
          <div key={index} className="emotion-change-item">
            <Row align="middle" gutter={8}>
              <Col span={4}>
                <InputNumber
                  value={change.time}
                  onChange={(value) => handleEmotionChangeUpdate(index, 'time', value)}
                  disabled={disabled}
                  min={0}
                  max={1}
                  step={0.1}
                  style={{ width: '100%' }}
                />
              </Col>
              <Col span={8}>
                <Select
                  value={change.emotion}
                  onChange={(value) => handleEmotionChangeUpdate(index, 'emotion', value)}
                  disabled={disabled}
                  style={{ width: '100%' }}
                >
                  {availableEmotions.map((emotion) => (
                    <Option key={emotion} value={emotion}>
                      <Tag color={EMOTION_COLORS[emotion] || '#000000'}>{emotion}</Tag>
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col span={8}>
                <Slider
                  value={change.intensity}
                  min={0}
                  max={1}
                  step={0.1}
                  onChange={(value) => handleEmotionChangeUpdate(index, 'intensity', value)}
                  disabled={disabled}
                />
              </Col>
              <Col span={4}>
                <Space>
                  <Button
                    type="text"
                    icon={<EditOutlined />}
                    onClick={() => setEditingChange(editingChange === index ? null : index)}
                    disabled={disabled}
                  />
                  <Button
                    type="text"
                    danger
                    icon={<MinusOutlined />}
                    onClick={() => handleRemoveEmotionChange(index)}
                    disabled={disabled}
                  />
                </Space>
              </Col>
            </Row>
          </div>
        ))}
      </div>
      
      <div className="emotion-changes-actions" style={{ marginTop: 16 }}>
        <Button
          type="dashed"
          icon={<PlusOutlined />}
          onClick={handleAddEmotionChange}
          disabled={disabled}
          block
        >
          {t('editor.emotion.addEmotionChange')}
        </Button>
      </div>
    </div>
  );
  
  return (
    <div className="emotion-blend-editor">
      {renderBasicBlendSettings()}
      
      <Divider />
      
      <Collapse
        bordered={false}
        activeKey={showEmotionChanges ? ['emotionChanges'] : []}
        onChange={(key) => setShowEmotionChanges(key.includes('emotionChanges'))}
      >
        <Panel
          header={
            <span>
              <LineChartOutlined /> {t('editor.emotion.emotionChanges')}
            </span>
          }
          key="emotionChanges"
        >
          {renderEmotionChangesSettings()}
        </Panel>
      </Collapse>
      
      <div className="settings-actions" style={{ marginTop: 16 }}>
        <Button onClick={handleReset} disabled={disabled}>
          {t('editor.emotion.resetToDefaults')}
        </Button>
      </div>
    </div>
  );
};

export default EmotionBlendEditor;
