/**
 * 数字人路径编辑器模块导出
 */

// 主要组件
export { AvatarPathEditor } from './AvatarPathEditor';
export { PathCanvas } from './PathCanvas';
export { PathPointEditor } from './PathPointEditor';
export { PathPropertiesPanel } from './PathPropertiesPanel';
export { PathPreview } from './PathPreview';
export { PathValidator } from './PathValidator';

// 类型定义
export type { PathCanvasRef } from './PathCanvas';
export type { PathPreviewRef } from './PathPreview';

/**
 * 路径编辑器工具函数
 */
export const PathEditorUtils = {
  /**
   * 创建默认路径数据
   */
  createDefaultPath: (name: string, avatarId: string) => ({
    id: `path_${Date.now()}`,
    name,
    avatarId,
    points: [],
    loopMode: 'none' as const,
    interpolation: 'linear' as const,
    enabled: true,
    totalDuration: 0,
    metadata: {
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      creator: 'user',
      version: 1,
      description: '',
      tags: []
    }
  }),

  /**
   * 创建默认路径点
   */
  createDefaultPoint: (position: { x: number; y: number; z: number }) => ({
    id: `point_${Date.now()}`,
    position,
    waitTime: 0,
    speed: 1.0,
    animation: 'walk',
    triggers: []
  }),

  /**
   * 验证路径数据
   */
  validatePathData: (path: any) => {
    const errors: string[] = [];

    if (!path.id) errors.push('路径ID不能为空');
    if (!path.name) errors.push('路径名称不能为空');
    if (!path.avatarId) errors.push('数字人ID不能为空');
    if (!Array.isArray(path.points)) errors.push('路径点必须是数组');
    if (path.points.length < 2) errors.push('路径至少需要2个点');

    // 验证路径点
    path.points?.forEach((point: any, index: number) => {
      if (!point.id) errors.push(`路径点${index + 1}缺少ID`);
      if (!point.position) errors.push(`路径点${index + 1}缺少位置信息`);
      if (typeof point.speed !== 'number' || point.speed <= 0) {
        errors.push(`路径点${index + 1}速度无效`);
      }
      if (typeof point.waitTime !== 'number' || point.waitTime < 0) {
        errors.push(`路径点${index + 1}等待时间无效`);
      }
    });

    return {
      valid: errors.length === 0,
      errors
    };
  },

  /**
   * 计算路径长度
   */
  calculatePathLength: (points: any[]) => {
    if (points.length < 2) return 0;

    let totalLength = 0;
    for (let i = 0; i < points.length - 1; i++) {
      const p1 = points[i].position;
      const p2 = points[i + 1].position;
      const dx = p2.x - p1.x;
      const dy = p2.y - p1.y;
      const dz = p2.z - p1.z;
      totalLength += Math.sqrt(dx * dx + dy * dy + dz * dz);
    }

    return totalLength;
  },

  /**
   * 计算路径持续时间
   */
  calculatePathDuration: (points: any[]) => {
    if (points.length === 0) return 0;

    let totalDuration = 0;

    for (let i = 0; i < points.length; i++) {
      const point = points[i];
      totalDuration += point.waitTime || 0;

      if (i < points.length - 1) {
        const nextPoint = points[i + 1];
        const dx = nextPoint.position.x - point.position.x;
        const dy = nextPoint.position.y - point.position.y;
        const dz = nextPoint.position.z - point.position.z;
        const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);
        const speed = point.speed || 1;
        totalDuration += distance / speed;
      }
    }

    return totalDuration;
  },

  /**
   * 优化路径点
   */
  optimizePath: (points: any[], tolerance: number = 0.1) => {
    if (points.length < 3) return points;

    const optimized = [points[0]];

    for (let i = 1; i < points.length - 1; i++) {
      const prev = optimized[optimized.length - 1];
      const curr = points[i];
      const next = points[i + 1];

      // 计算当前点到前后两点连线的距离
      const dx1 = next.position.x - prev.position.x;
      const dy1 = next.position.y - prev.position.y;
      const dz1 = next.position.z - prev.position.z;

      const dx2 = curr.position.x - prev.position.x;
      const dy2 = curr.position.y - prev.position.y;
      const dz2 = curr.position.z - prev.position.z;

      // 计算投影长度
      const dot = dx1 * dx2 + dy1 * dy2 + dz1 * dz2;
      const len1Sq = dx1 * dx1 + dy1 * dy1 + dz1 * dz1;

      if (len1Sq === 0) {
        optimized.push(curr);
        continue;
      }

      const t = Math.max(0, Math.min(1, dot / len1Sq));
      const projX = prev.position.x + t * dx1;
      const projY = prev.position.y + t * dy1;
      const projZ = prev.position.z + t * dz1;

      // 计算距离
      const distX = curr.position.x - projX;
      const distY = curr.position.y - projY;
      const distZ = curr.position.z - projZ;
      const distance = Math.sqrt(distX * distX + distY * distY + distZ * distZ);

      if (distance > tolerance) {
        optimized.push(curr);
      }
    }

    optimized.push(points[points.length - 1]);
    return optimized;
  },

  /**
   * 平滑路径
   */
  smoothPath: (points: any[], factor: number = 0.5) => {
    if (points.length < 3) return points;

    const smoothed = [...points];

    for (let i = 1; i < smoothed.length - 1; i++) {
      const prev = points[i - 1];
      const curr = points[i];
      const next = points[i + 1];

      smoothed[i] = {
        ...curr,
        position: {
          x: curr.position.x + factor * (prev.position.x + next.position.x - 2 * curr.position.x) * 0.5,
          y: curr.position.y + factor * (prev.position.y + next.position.y - 2 * curr.position.y) * 0.5,
          z: curr.position.z + factor * (prev.position.z + next.position.z - 2 * curr.position.z) * 0.5
        }
      };
    }

    return smoothed;
  },

  /**
   * 导出路径为JSON
   */
  exportPathToJSON: (path: any) => {
    return JSON.stringify(path, null, 2);
  },

  /**
   * 从JSON导入路径
   */
  importPathFromJSON: (jsonString: string) => {
    try {
      const path = JSON.parse(jsonString);
      const validation = PathEditorUtils.validatePathData(path);
      
      if (!validation.valid) {
        throw new Error(`路径数据无效: ${validation.errors.join(', ')}`);
      }

      return path;
    } catch (error) {
      throw new Error(`导入失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }
};

/**
 * 路径编辑器常量
 */
export const PathEditorConstants = {
  // 默认值
  DEFAULT_SPEED: 1.0,
  DEFAULT_WAIT_TIME: 0,
  DEFAULT_ANIMATION: 'walk',
  
  // 限制值
  MIN_SPEED: 0.1,
  MAX_SPEED: 20.0,
  MIN_WAIT_TIME: 0,
  MAX_WAIT_TIME: 300,
  MIN_POINTS: 2,
  MAX_POINTS: 1000,
  
  // 动画选项
  ANIMATION_OPTIONS: [
    'idle',
    'walk',
    'run',
    'jump',
    'wave',
    'talk',
    'sit',
    'stand',
    'dance',
    'custom'
  ],
  
  // 循环模式
  LOOP_MODES: [
    { value: 'none', label: '不循环' },
    { value: 'loop', label: '循环' },
    { value: 'pingpong', label: '来回循环' }
  ],
  
  // 插值类型
  INTERPOLATION_TYPES: [
    { value: 'linear', label: '线性' },
    { value: 'smooth', label: '平滑' },
    { value: 'bezier', label: '贝塞尔' },
    { value: 'spline', label: '样条' }
  ],
  
  // 触发器类型
  TRIGGER_TYPES: [
    { value: 'dialogue', label: '对话' },
    { value: 'animation', label: '动画' },
    { value: 'sound', label: '声音' },
    { value: 'event', label: '事件' },
    { value: 'custom', label: '自定义' }
  ]
};
