{"smartCity": {"name": "智慧城市演示", "version": "1.0.0", "description": "基于DL引擎的智慧城市管理系统演示", "config": {"cityBounds": {"north": 40.0, "south": 39.8, "east": 116.6, "west": 116.2}, "coordinateSystem": "WGS84", "timeZone": "Asia/Shanghai", "updateInterval": 5000}}, "iotDevices": [{"id": "temp_sensor_001", "name": "温度传感器-市中心", "type": "temperature_sensor", "protocol": "MQTT", "endpoint": "mqtt://iot.smartcity.local:1883", "location": {"lat": 39.9042, "lng": 116.4074, "address": "北京市东城区"}, "config": {"sampleRate": 30, "accuracy": 0.1, "range": {"min": -40, "max": 60}}}, {"id": "air_quality_001", "name": "空气质量监测站-CBD", "type": "air_quality_sensor", "protocol": "HTTP", "endpoint": "https://api.smartcity.local/sensors/air", "location": {"lat": 39.9088, "lng": 116.3974, "address": "北京市朝阳区CBD"}, "config": {"sampleRate": 60, "parameters": ["PM2.5", "PM10", "NO2", "SO2", "O3", "CO"]}}, {"id": "traffic_camera_001", "name": "交通摄像头-长安街", "type": "traffic_camera", "protocol": "RTSP", "endpoint": "rtsp://cameras.smartcity.local/traffic/001", "location": {"lat": 39.9063, "lng": 116.3974, "address": "长安街与王府井大街交叉口"}, "config": {"resolution": "1920x1080", "fps": 25, "aiAnalysis": true}}], "trafficIntersections": [{"id": "intersection_001", "name": "长安街-王府井路口", "location": {"lat": 39.9063, "lng": 116.3974}, "phases": [{"name": "north_south", "duration": 45, "description": "南北向通行"}, {"name": "east_west", "duration": 40, "description": "东西向通行"}, {"name": "left_turn", "duration": 20, "description": "左转通行"}, {"name": "pedestrian", "duration": 15, "description": "行人通行"}], "optimization": {"mode": "flow", "adaptiveControl": true, "peakHourAdjustment": true}}, {"id": "intersection_002", "name": "二环路-朝阳门路口", "location": {"lat": 39.9289, "lng": 116.4274}, "phases": [{"name": "north_south", "duration": 50, "description": "南北向通行"}, {"name": "east_west", "duration": 45, "description": "东西向通行"}, {"name": "left_turn", "duration": 25, "description": "左转通行"}], "optimization": {"mode": "delay", "adaptiveControl": true, "peakHourAdjustment": true}}], "streetLights": [{"id": "light_001", "name": "路灯-王府井大街1号", "location": {"lat": 39.9063, "lng": 116.3974}, "config": {"maxPower": 50, "motionSensor": true, "ambientLightSensor": true, "energyMode": "eco", "schedule": {"on": "18:00", "off": "06:00"}}}, {"id": "light_002", "name": "路灯-长安街东段", "location": {"lat": 39.9063, "lng": 116.4074}, "config": {"maxPower": 60, "motionSensor": true, "ambientLightSensor": true, "energyMode": "normal", "schedule": {"on": "18:30", "off": "05:30"}}}], "emergencyServices": [{"id": "fire_station_001", "name": "东城消防站", "type": "fire_department", "location": {"lat": 39.9142, "lng": 116.4074}, "resources": [{"type": "fire_truck", "count": 3, "status": "available"}, {"type": "ladder_truck", "count": 1, "status": "available"}, {"type": "rescue_vehicle", "count": 2, "status": "available"}], "responseTime": {"target": 8, "average": 7.5}}, {"id": "hospital_001", "name": "协和医院", "type": "hospital", "location": {"lat": 39.9122, "lng": 116.4174}, "resources": [{"type": "ambulance", "count": 5, "status": "available"}, {"type": "medical_team", "count": 10, "status": "available"}, {"type": "emergency_bed", "count": 20, "status": "available"}], "responseTime": {"target": 6, "average": 6.2}}], "visualScripts": [{"name": "iot_monitoring", "description": "IoT设备监控脚本", "nodes": [{"id": "device_connect_1", "type": "iot_device_connect", "config": {"deviceId": "temp_sensor_001", "protocol": "MQTT", "endpoint": "mqtt://iot.smartcity.local:1883"}, "position": {"x": 100, "y": 100}}, {"id": "data_process_1", "type": "iot_data_processing", "config": {"processingType": "anomaly_detect", "timeWindow": 300, "threshold": {"temperature": {"min": -10, "max": 45}, "humidity": {"min": 0, "max": 100}}}, "position": {"x": 300, "y": 100}}], "connections": [{"from": {"nodeId": "device_connect_1", "output": "onDataReceived"}, "to": {"nodeId": "data_process_1", "input": "rawData"}}]}, {"name": "traffic_management", "description": "交通管理脚本", "nodes": [{"id": "flow_monitor_1", "type": "traffic_flow_monitor", "config": {"roadId": "main_street_001", "analysisInterval": 60}, "position": {"x": 100, "y": 200}}, {"id": "traffic_light_1", "type": "smart_traffic_light", "config": {"intersectionId": "intersection_001", "optimizationMode": "flow"}, "position": {"x": 300, "y": 200}}], "connections": [{"from": {"nodeId": "flow_monitor_1", "output": "trafficStatus"}, "to": {"nodeId": "traffic_light_1", "input": "trafficData"}}]}], "dashboardConfig": {"layout": "grid", "refreshInterval": 5000, "widgets": [{"id": "city_overview", "type": "3d_scene", "title": "城市3D概览", "size": {"width": 8, "height": 6}, "position": {"x": 0, "y": 0}, "config": {"camera": {"position": {"x": 0, "y": 1000, "z": 1000}, "target": {"x": 0, "y": 0, "z": 0}}, "layers": ["buildings", "roads", "iot_devices", "traffic"]}}, {"id": "iot_status", "type": "status_grid", "title": "IoT设备状态", "size": {"width": 4, "height": 3}, "position": {"x": 8, "y": 0}, "config": {"deviceTypes": ["temperature_sensor", "air_quality_sensor", "traffic_camera"], "showAlerts": true}}, {"id": "traffic_flow", "type": "line_chart", "title": "交通流量趋势", "size": {"width": 4, "height": 3}, "position": {"x": 8, "y": 3}, "config": {"timeRange": "24h", "metrics": ["vehicle_count", "average_speed", "congestion_level"]}}, {"id": "air_quality", "type": "gauge_chart", "title": "空气质量指数", "size": {"width": 4, "height": 3}, "position": {"x": 0, "y": 6}, "config": {"metric": "aqi", "ranges": [{"min": 0, "max": 50, "color": "green", "label": "优"}, {"min": 51, "max": 100, "color": "yellow", "label": "良"}, {"min": 101, "max": 150, "color": "orange", "label": "轻度污染"}, {"min": 151, "max": 200, "color": "red", "label": "中度污染"}, {"min": 201, "max": 300, "color": "purple", "label": "重度污染"}, {"min": 301, "max": 500, "color": "maroon", "label": "严重污染"}]}}, {"id": "emergency_alerts", "type": "alert_list", "title": "应急事件", "size": {"width": 4, "height": 3}, "position": {"x": 4, "y": 6}, "config": {"maxItems": 10, "autoRefresh": true, "severityFilter": ["high", "critical"]}}, {"id": "energy_consumption", "type": "bar_chart", "title": "能耗统计", "size": {"width": 4, "height": 3}, "position": {"x": 8, "y": 6}, "config": {"timeRange": "7d", "categories": ["street_lights", "traffic_lights", "buildings", "infrastructure"]}}]}, "alerts": {"rules": [{"id": "high_aqi_alert", "name": "空气质量预警", "condition": "aqi > 150", "severity": "high", "actions": ["notify_admin", "publish_health_advice", "activate_emergency_plan"]}, {"id": "traffic_congestion_alert", "name": "交通拥堵预警", "condition": "congestion_level == 'severe'", "severity": "medium", "actions": ["optimize_traffic_lights", "suggest_alternative_routes"]}, {"id": "device_offline_alert", "name": "设备离线预警", "condition": "device_status == 'offline' AND offline_duration > 300", "severity": "low", "actions": ["notify_maintenance", "log_incident"]}]}}