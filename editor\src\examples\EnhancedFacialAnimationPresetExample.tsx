/**
 * 增强版面部动画预设面板使用示例
 * 展示完善后的面部动画预设面板功能
 */
import React, { useState, useEffect } from 'react';
import { Card, Space, Button, message, Switch, Divider, Typography, Alert } from 'antd';
import {
  StarOutlined,
  SettingOutlined,
  PlayCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { FacialAnimationPresetPanel } from '../panels/FacialAnimationPresetPanel';
import { FacialAnimationPresetType } from '../components/FacialAnimationEditor/FacialAnimationPresetManager';

const { Title, Paragraph, Text } = Typography;

/**
 * 增强版面部动画预设示例组件
 */
const EnhancedFacialAnimationPresetExample: React.FC = () => {
  // 状态管理
  const [entityId, setEntityId] = useState<string>('demo-character-001');
  const [showStatistics, setShowStatistics] = useState<boolean>(true);
  const [showQuickActions, setShowQuickActions] = useState<boolean>(true);
  const [showFavorites, setShowFavorites] = useState<boolean>(true);
  const [showHistory, setShowHistory] = useState<boolean>(true);
  const [enableBatchOperations, setEnableBatchOperations] = useState<boolean>(true);
  const [isEditable, setIsEditable] = useState<boolean>(true);

  // 模拟数据
  const [appliedPresets, setAppliedPresets] = useState<string[]>([]);
  const [favoritePresets, setFavoritePresets] = useState<string[]>([]);

  // 初始化
  useEffect(() => {
    // 模拟加载用户偏好设置
    const savedSettings = localStorage.getItem('facial-animation-panel-settings');
    if (savedSettings) {
      try {
        const settings = JSON.parse(savedSettings);
        setShowStatistics(settings.showStatistics ?? true);
        setShowQuickActions(settings.showQuickActions ?? true);
        setShowFavorites(settings.showFavorites ?? true);
        setShowHistory(settings.showHistory ?? true);
        setEnableBatchOperations(settings.enableBatchOperations ?? true);
      } catch (error) {
        console.warn('加载设置失败:', error);
      }
    }
  }, []);

  // 保存设置
  const saveSettings = () => {
    const settings = {
      showStatistics,
      showQuickActions,
      showFavorites,
      showHistory,
      enableBatchOperations
    };
    localStorage.setItem('facial-animation-panel-settings', JSON.stringify(settings));
    message.success('设置已保存');
  };

  // 处理预设应用
  const handlePresetApply = (preset: any) => {
    console.log('应用预设:', preset);
    setAppliedPresets(prev => [preset.id, ...prev.slice(0, 9)]); // 保留最近10个
    message.success(`已应用预设: ${preset.name}`);
  };

  // 处理模板应用
  const handleTemplateApply = (template: any, parameters: any) => {
    console.log('应用模板:', template, '参数:', parameters);
    message.success(`已应用模板: ${template.name}`);
  };

  // 处理收藏状态变更
  const handlePresetFavoriteChange = (presetId: string, isFavorite: boolean) => {
    if (isFavorite) {
      setFavoritePresets(prev => [...prev, presetId]);
      message.success('已添加到收藏');
    } else {
      setFavoritePresets(prev => prev.filter(id => id !== presetId));
      message.success('已从收藏中移除');
    }
  };

  // 处理批量操作
  const handleBatchOperation = (operation: string, presetIds: string[]) => {
    console.log('批量操作:', operation, '预设IDs:', presetIds);
    
    switch (operation) {
      case 'export':
        // 模拟导出操作
        setTimeout(() => {
          message.success(`已导出 ${presetIds.length} 个预设`);
        }, 1000);
        break;
      case 'delete':
        // 模拟删除操作
        setTimeout(() => {
          message.success(`已删除 ${presetIds.length} 个预设`);
        }, 1000);
        break;
      case 'favorite':
        // 模拟批量收藏操作
        setFavoritePresets(prev => [...new Set([...prev, ...presetIds])]);
        message.success(`已将 ${presetIds.length} 个预设添加到收藏`);
        break;
    }
  };

  // 重置演示数据
  const resetDemo = () => {
    setAppliedPresets([]);
    setFavoritePresets([]);
    localStorage.removeItem('facial-animation-preset-history');
    message.success('演示数据已重置');
  };

  return (
    <div style={{ padding: 20, background: '#f5f5f5', minHeight: '100vh' }}>
      <Card style={{ marginBottom: 20 }}>
        <Title level={2}>
          <PlayCircleOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          增强版面部动画预设面板
        </Title>
        
        <Alert
          message="功能演示"
          description="这是一个完善后的面部动画预设面板，包含统计信息、快速操作、收藏功能、历史记录、批量操作等增强功能。"
          type="info"
          icon={<InfoCircleOutlined />}
          style={{ marginBottom: 16 }}
        />

        <Paragraph>
          <Text strong>主要功能特性：</Text>
        </Paragraph>
        <ul>
          <li>📊 <Text strong>统计信息</Text>：显示预设总数、模板总数、收藏数量、最近使用等统计数据</li>
          <li>🔍 <Text strong>智能搜索</Text>：支持按名称、类型、文化等条件搜索和筛选预设</li>
          <li>⭐ <Text strong>收藏功能</Text>：可以收藏常用预设，快速访问</li>
          <li>📝 <Text strong>历史记录</Text>：自动记录最近使用的预设，支持快速重新应用</li>
          <li>📦 <Text strong>批量操作</Text>：支持批量导出、删除、收藏等操作</li>
          <li>🎯 <Text strong>快速预览</Text>：鼠标悬停或点击即可快速预览预设效果</li>
          <li>📱 <Text strong>响应式设计</Text>：适配不同屏幕尺寸，移动端友好</li>
          <li>🎨 <Text strong>主题支持</Text>：支持明暗主题切换</li>
        </ul>
      </Card>

      <Card title="面板配置" style={{ marginBottom: 20 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Space wrap>
            <Text>实体ID:</Text>
            <Button 
              size="small" 
              onClick={() => setEntityId(`demo-character-${String(Date.now()).slice(-3)}`)}
            >
              {entityId}
            </Button>
            
            <Divider type="vertical" />
            
            <Text>可编辑:</Text>
            <Switch checked={isEditable} onChange={setIsEditable} />
          </Space>

          <Space wrap>
            <Text>显示统计信息:</Text>
            <Switch checked={showStatistics} onChange={setShowStatistics} />
            
            <Text>显示快速操作:</Text>
            <Switch checked={showQuickActions} onChange={setShowQuickActions} />
            
            <Text>显示收藏功能:</Text>
            <Switch checked={showFavorites} onChange={setShowFavorites} />
            
            <Text>显示历史记录:</Text>
            <Switch checked={showHistory} onChange={setShowHistory} />
            
            <Text>启用批量操作:</Text>
            <Switch checked={enableBatchOperations} onChange={setEnableBatchOperations} />
          </Space>

          <Space>
            <Button onClick={saveSettings} icon={<SettingOutlined />}>
              保存设置
            </Button>
            <Button onClick={resetDemo} danger>
              重置演示数据
            </Button>
          </Space>
        </Space>
      </Card>

      <Card
        title="面部动画预设面板"
        style={{ height: 'calc(100vh - 400px)', minHeight: 600 }}
        styles={{ body: { height: 'calc(100% - 57px)', padding: 0 } }}
      >
        <FacialAnimationPresetPanel
          entityId={entityId}
          editable={isEditable}
          defaultActiveTab="presets"
          defaultPresetType={FacialAnimationPresetType.STANDARD}
          defaultCulture="global"
          showStatistics={showStatistics}
          showQuickActions={showQuickActions}
          showFavorites={showFavorites}
          showHistory={showHistory}
          enableBatchOperations={enableBatchOperations}
          onPresetApply={handlePresetApply}
          onTemplateApply={handleTemplateApply}
          onPresetFavoriteChange={handlePresetFavoriteChange}
          onBatchOperation={handleBatchOperation}
        />
      </Card>

      <Card title="演示状态" style={{ marginTop: 20 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>已应用的预设 ({appliedPresets.length}):</Text>
            <div style={{ marginTop: 8 }}>
              {appliedPresets.length === 0 ? (
                <Text type="secondary">暂无应用记录</Text>
              ) : (
                appliedPresets.map((id, index) => (
                  <Button key={index} size="small" style={{ margin: '2px 4px 2px 0' }}>
                    {id}
                  </Button>
                ))
              )}
            </div>
          </div>
          
          <div>
            <Text strong>收藏的预设 ({favoritePresets.length}):</Text>
            <div style={{ marginTop: 8 }}>
              {favoritePresets.length === 0 ? (
                <Text type="secondary">暂无收藏</Text>
              ) : (
                favoritePresets.map((id, index) => (
                  <Button 
                    key={index} 
                    size="small" 
                    icon={<StarOutlined />}
                    style={{ margin: '2px 4px 2px 0' }}
                  >
                    {id}
                  </Button>
                ))
              )}
            </div>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default EnhancedFacialAnimationPresetExample;
