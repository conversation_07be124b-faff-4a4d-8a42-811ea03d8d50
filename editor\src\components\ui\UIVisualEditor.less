.ui-visual-editor {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;

  .toolbar {
    background: #ffffff;
    padding: 8px 16px;
    border-bottom: 1px solid #e8e8e8;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .editor-content {
    display: flex;
    flex: 1;
    overflow: hidden;

    .component-library {
      width: 250px;
      background: #ffffff;
      border-right: 1px solid #e8e8e8;
      padding: 16px;
      overflow-y: auto;

      h3 {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }

      .component-list {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;

        .component-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 12px 8px;
          border: 1px solid #e8e8e8;
          border-radius: 6px;
          cursor: grab;
          transition: all 0.2s ease;
          background: #fafafa;

          &:hover {
            border-color: #1890ff;
            background: #f0f8ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(24, 144, 255, 0.2);
          }

          &.dragging {
            opacity: 0.5;
            cursor: grabbing;
          }

          .component-icon {
            font-size: 20px;
            color: #1890ff;
            margin-bottom: 4px;
          }

          .component-name {
            font-size: 12px;
            color: #666666;
            text-align: center;
          }
        }
      }
    }

    .canvas-container {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: flex-start;
      padding: 20px;
      background: #f0f0f0;

      .design-canvas {
        position: relative;
        width: 800px;
        height: 600px;
        background: #ffffff;
        border: 1px solid #d9d9d9;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        overflow: hidden;

        &.drag-over {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .element-controls {
          position: absolute;
          top: -30px;
          right: 0;
          display: flex;
          gap: 4px;
          background: #ffffff;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          padding: 2px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

          .ant-btn {
            border: none;
            box-shadow: none;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover {
              background: #f0f0f0;
            }
          }
        }
      }
    }

    .property-container {
      width: 300px;
      background: #ffffff;
      border-left: 1px solid #e8e8e8;
      overflow-y: auto;

      .property-panel {
        padding: 16px;

        h3 {
          margin: 0 0 16px 0;
          font-size: 16px;
          font-weight: 600;
          color: #262626;
          border-bottom: 1px solid #e8e8e8;
          padding-bottom: 8px;
        }

        .no-selection {
          text-align: center;
          color: #999999;
          padding: 40px 20px;
          font-style: italic;
        }

        .property-section {
          margin-bottom: 24px;

          h4 {
            margin: 0 0 12px 0;
            font-size: 14px;
            font-weight: 500;
            color: #595959;
          }

          label {
            display: block;
            margin-bottom: 4px;
            font-size: 12px;
            color: #666666;
            font-weight: 500;
          }

          .ant-input,
          .ant-select,
          .ant-slider {
            margin-bottom: 8px;
          }

          .ant-input[type="number"] {
            width: 100%;
          }

          .ant-color-picker {
            width: 100%;
          }
        }
      }
    }
  }
}

// 拖拽元素样式
.ui-element {
  position: absolute;
  border: 1px solid transparent;
  transition: border-color 0.2s ease;

  &:hover {
    border-color: #1890ff;
  }

  &.selected {
    border-color: #1890ff !important;
    border-width: 2px !important;
  }

  &.dragging {
    opacity: 0.7;
    z-index: 1000;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .ui-visual-editor {
    .editor-content {
      .component-library {
        width: 200px;

        .component-list {
          grid-template-columns: 1fr;
        }
      }

      .property-container {
        width: 250px;
      }

      .canvas-container {
        .design-canvas {
          width: 600px;
          height: 450px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .ui-visual-editor {
    .editor-content {
      flex-direction: column;

      .component-library {
        width: 100%;
        height: 150px;
        border-right: none;
        border-bottom: 1px solid #e8e8e8;

        .component-list {
          grid-template-columns: repeat(4, 1fr);
        }
      }

      .canvas-container {
        flex: 1;
        padding: 10px;

        .design-canvas {
          width: 100%;
          height: 400px;
        }
      }

      .property-container {
        width: 100%;
        height: 200px;
        border-left: none;
        border-top: 1px solid #e8e8e8;
      }
    }
  }
}

// 动画效果
@keyframes elementAppear {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.ui-element {
  animation: elementAppear 0.3s ease;
}

// 拖拽指示器
.drag-indicator {
  position: absolute;
  border: 2px dashed #1890ff;
  background: rgba(24, 144, 255, 0.1);
  pointer-events: none;
  z-index: 999;
}

// 选择框
.selection-box {
  position: absolute;
  border: 1px solid #1890ff;
  background: rgba(24, 144, 255, 0.1);
  pointer-events: none;
  z-index: 998;
}

// 网格背景
.design-canvas.show-grid {
  background-image: 
    linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

// 标尺
.ruler {
  background: #f8f8f8;
  border: 1px solid #e8e8e8;
  font-size: 10px;
  color: #666666;

  &.horizontal {
    height: 20px;
    border-bottom: none;
  }

  &.vertical {
    width: 20px;
    border-right: none;
  }
}

// 缩放控制
.zoom-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  .ant-btn {
    margin: 0 2px;
  }

  .zoom-level {
    margin: 0 8px;
    font-size: 12px;
    color: #666666;
  }
}
