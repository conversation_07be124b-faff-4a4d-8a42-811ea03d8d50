/**
 * NestedComponentEditor.module.css
 * 
 * 嵌套组件编辑器样式
 */

.nested-component-editor {
  display: flex;
  height: 100%;
  background: var(--background-color, #ffffff);
  border: 1px solid var(--border-color, #d9d9d9);
  border-radius: 8px;
  overflow: hidden;
}

/* 组件库面板 */
.component-library-panel {
  width: 200px;
  border-right: 1px solid var(--border-color, #d9d9d9);
  background: var(--surface-color, #fafafa);
  display: flex;
  flex-direction: column;
}

.component-library-panel .panel-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color, #d9d9d9);
  background: var(--header-background, #ffffff);
}

.component-library-panel .panel-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color, #000000);
}

.component-types {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
}

.component-type-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 4px;
  border: 1px solid var(--border-color, #d9d9d9);
  border-radius: 6px;
  background: var(--background-color, #ffffff);
  cursor: grab;
  transition: all 0.2s ease;
  user-select: none;
}

.component-type-item:hover {
  border-color: var(--primary-color, #1890ff);
  background: var(--primary-background, #e6f7ff);
}

.component-type-item.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.component-type-item .component-icon {
  margin-right: 8px;
  color: var(--icon-color, #666666);
}

.component-type-item .component-name {
  font-size: 12px;
  color: var(--text-color, #000000);
}

/* 组件树面板 */
.component-tree-panel {
  width: 250px;
  border-right: 1px solid var(--border-color, #d9d9d9);
  background: var(--surface-color, #fafafa);
  display: flex;
  flex-direction: column;
}

.component-tree-panel .panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color, #d9d9d9);
  background: var(--header-background, #ffffff);
}

.component-tree-panel .panel-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color, #000000);
}

.tree-container {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
}

.empty-tree {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-color-secondary, #8c8c8c);
}

.empty-tree p {
  margin-bottom: 16px;
  font-size: 14px;
}

/* 树节点 */
.tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 8px;
  margin-bottom: 2px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.tree-node:hover {
  background: var(--item-hover-background, #f5f5f5);
}

.tree-node.selected {
  background: var(--primary-color, #1890ff);
  color: white;
}

.tree-node.drop-over {
  background: var(--drop-over-background, #e6f7ff);
  border: 2px dashed var(--primary-color, #1890ff);
}

.node-content {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.node-icon {
  margin-right: 6px;
  color: var(--icon-color, #666666);
}

.tree-node.selected .node-icon {
  color: white;
}

.node-name {
  font-size: 12px;
  font-weight: 500;
  margin-right: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.node-type {
  font-size: 10px;
  color: var(--text-color-secondary, #8c8c8c);
  margin-right: 6px;
}

.tree-node.selected .node-type {
  color: rgba(255, 255, 255, 0.8);
}

.node-hidden,
.node-locked {
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
  margin-left: 4px;
}

.node-hidden {
  background: var(--warning-color, #faad14);
  color: white;
}

.node-locked {
  background: var(--error-color, #ff4d4f);
  color: white;
}

.node-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.tree-node:hover .node-actions,
.tree-node.selected .node-actions {
  opacity: 1;
}

/* 可视化编辑区域 */
.visual-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--editor-background, #f8f9fa);
  position: relative;
}

.visual-editor.drop-over {
  background: var(--drop-over-background, #e6f7ff);
}

.visual-editor.drop-over::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px dashed var(--primary-color, #1890ff);
  pointer-events: none;
}

.editor-canvas {
  flex: 1;
  position: relative;
  overflow: auto;
  padding: 16px;
}

.selection-indicator {
  position: absolute;
  top: 8px;
  left: 8px;
  padding: 4px 8px;
  background: var(--primary-color, #1890ff);
  color: white;
  font-size: 12px;
  border-radius: 4px;
  z-index: 1000;
}

.component-preview {
  width: 100%;
  height: 100%;
  min-height: 400px;
  border: 1px dashed var(--border-color, #d9d9d9);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background-color, #ffffff);
}

.preview-message {
  color: var(--text-color-secondary, #8c8c8c);
  font-size: 14px;
}

/* 自定义滚动条 */
.component-types::-webkit-scrollbar,
.tree-container::-webkit-scrollbar,
.editor-canvas::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.component-types::-webkit-scrollbar-track,
.tree-container::-webkit-scrollbar-track,
.editor-canvas::-webkit-scrollbar-track {
  background: var(--scrollbar-track-color, #f1f1f1);
  border-radius: 3px;
}

.component-types::-webkit-scrollbar-thumb,
.tree-container::-webkit-scrollbar-thumb,
.editor-canvas::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-color, #c1c1c1);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.component-types::-webkit-scrollbar-thumb:hover,
.tree-container::-webkit-scrollbar-thumb:hover,
.editor-canvas::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-color, #a8a8a8);
}

/* 暗色主题 */
[data-theme="dark"] .nested-component-editor {
  --background-color: #141414;
  --surface-color: #1f1f1f;
  --header-background: #262626;
  --border-color: #434343;
  --text-color: #ffffff;
  --text-color-secondary: #a6a6a6;
  --icon-color: #a6a6a6;
  --item-hover-background: #262626;
  --primary-color: #177ddc;
  --primary-background: #111b26;
  --drop-over-background: #111b26;
  --editor-background: #0f1419;
  --warning-color: #d89614;
  --error-color: #d32029;
  --scrollbar-track-color: #262626;
  --scrollbar-thumb-color: #434343;
  --scrollbar-thumb-hover-color: #595959;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .component-library-panel {
    width: 180px;
  }
  
  .component-tree-panel {
    width: 220px;
  }
}

@media (max-width: 768px) {
  .nested-component-editor {
    flex-direction: column;
  }
  
  .component-library-panel,
  .component-tree-panel {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid var(--border-color, #d9d9d9);
  }
  
  .component-types,
  .tree-container {
    max-height: 150px;
  }
}

/* 拖拽状态 */
.component-type-item:active {
  cursor: grabbing;
}

.tree-node.dragging {
  opacity: 0.5;
  transform: rotate(2deg);
}

/* 动画效果 */
.tree-node,
.component-type-item {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.node-actions {
  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 焦点状态 */
.tree-node:focus,
.component-type-item:focus {
  outline: 2px solid var(--primary-color, #1890ff);
  outline-offset: -2px;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .nested-component-editor {
    border-width: 2px;
  }
  
  .tree-node.selected {
    outline: 2px solid var(--primary-color, #1890ff);
    outline-offset: -2px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .tree-node,
  .component-type-item,
  .node-actions {
    transition: none;
  }
}

/* 打印样式 */
@media print {
  .component-library-panel,
  .component-tree-panel {
    display: none;
  }
  
  .visual-editor {
    border: none;
  }
  
  .selection-indicator {
    display: none;
  }
}
