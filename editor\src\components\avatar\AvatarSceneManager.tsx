/**
 * 虚拟化身场景管理器组件
 * 
 * 集成虚拟化身保存、场景加载、控制等功能的统一管理界面
 */

import React, { useState, useCallback, useEffect } from 'react';
import { Card, Tabs, Button, Select, Input, InputNumber, Switch, message, Space, Divider } from 'antd';
import { 
  SaveOutlined, 
  PlayCircleOutlined, 
  ControlOutlined,
  EnvironmentOutlined,
  UserOutlined,
  SettingOutlined
} from '@ant-design/icons';

const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 虚拟化身数据接口
 */
interface AvatarData {
  id: string;
  userId?: string;
  name?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 场景信息接口
 */
interface SceneInfo {
  id: string;
  name: string;
  description: string;
  tags: string[];
  thumbnail?: string;
}

/**
 * 组件状态接口
 */
interface ManagerState {
  // 虚拟化身相关
  currentAvatar: AvatarData | null;
  savedAvatars: AvatarData[];
  
  // 场景相关
  availableScenes: SceneInfo[];
  currentScene: string | null;
  
  // 控制相关
  isControlEnabled: boolean;
  activeAvatarId: string | null;
  
  // 保存相关
  saveFormat: 'json' | 'binary' | 'gltf' | 'fbx';
  saveLocation: 'filesystem' | 'database' | 'cloud';
  enableCompression: boolean;
  enableEncryption: boolean;
  
  // 场景加载相关
  spawnPosition: { x: number; y: number; z: number };
  enablePhysics: boolean;
  enableAI: boolean;
  enableVoice: boolean;
  
  // 控制配置
  moveSpeed: number;
  rotationSpeed: number;
  jumpForce: number;
  enableGravity: boolean;
  enableCollision: boolean;
  
  // 状态
  isProcessing: boolean;
  processingStep: string;
}

/**
 * 组件属性接口
 */
interface AvatarSceneManagerProps {
  onAvatarSaved?: (result: any) => void;
  onSceneLoaded?: (result: any) => void;
  onControlEnabled?: (avatarId: string) => void;
}

/**
 * 虚拟化身场景管理器组件
 */
export const AvatarSceneManager: React.FC<AvatarSceneManagerProps> = ({
  onAvatarSaved,
  onSceneLoaded,
  onControlEnabled
}) => {
  const [state, setState] = useState<ManagerState>({
    currentAvatar: null,
    savedAvatars: [],
    availableScenes: [
      {
        id: 'default',
        name: '默认场景',
        description: '基础的虚拟环境场景',
        tags: ['basic', 'indoor']
      },
      {
        id: 'medical_hall',
        name: '医疗展厅',
        description: '医疗健康展示场景',
        tags: ['medical', 'exhibition', 'educational']
      },
      {
        id: 'classroom',
        name: '虚拟教室',
        description: '教育培训场景',
        tags: ['education', 'indoor', 'learning']
      },
      {
        id: 'outdoor_park',
        name: '户外公园',
        description: '自然环境场景',
        tags: ['outdoor', 'nature', 'relaxing']
      }
    ],
    currentScene: null,
    isControlEnabled: false,
    activeAvatarId: null,
    saveFormat: 'json',
    saveLocation: 'filesystem',
    enableCompression: true,
    enableEncryption: false,
    spawnPosition: { x: 0, y: 0, z: 0 },
    enablePhysics: true,
    enableAI: false,
    enableVoice: false,
    moveSpeed: 5.0,
    rotationSpeed: 2.0,
    jumpForce: 10.0,
    enableGravity: true,
    enableCollision: true,
    isProcessing: false,
    processingStep: ''
  });

  /**
   * 更新状态
   */
  const updateState = useCallback((updates: Partial<ManagerState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  /**
   * 保存虚拟化身
   */
  const handleSaveAvatar = useCallback(async () => {
    if (!state.currentAvatar) {
      message.error('没有可保存的虚拟化身');
      return;
    }

    updateState({
      isProcessing: true,
      processingStep: '保存虚拟化身...'
    });

    try {
      // 模拟保存过程
      await new Promise(resolve => setTimeout(resolve, 1500));

      const saveResult = {
        success: true,
        saveId: `save_${Date.now()}`,
        fileSize: Math.floor(Math.random() * 1000000) + 100000,
        timestamp: new Date(),
        format: state.saveFormat,
        location: state.saveLocation
      };

      message.success('虚拟化身保存成功');
      onAvatarSaved?.(saveResult);

    } catch (error) {
      console.error('保存虚拟化身失败:', error);
      message.error('保存虚拟化身失败');
    } finally {
      updateState({
        isProcessing: false,
        processingStep: ''
      });
    }
  }, [state.currentAvatar, state.saveFormat, state.saveLocation, onAvatarSaved, updateState]);

  /**
   * 一键进入场景
   */
  const handleQuickEnterScene = useCallback(async () => {
    if (!state.currentAvatar) {
      message.error('请先选择虚拟化身');
      return;
    }

    if (!state.currentScene) {
      message.error('请先选择场景');
      return;
    }

    updateState({
      isProcessing: true,
      processingStep: '加载场景...'
    });

    try {
      // 模拟场景加载过程
      await new Promise(resolve => setTimeout(resolve, 2000));

      const loadResult = {
        success: true,
        sceneId: state.currentScene,
        avatarId: state.currentAvatar.id,
        loadTime: Math.floor(Math.random() * 1000) + 500,
        spawnPosition: state.spawnPosition
      };

      message.success('成功进入虚拟场景');
      onSceneLoaded?.(loadResult);

      // 自动启用控制
      updateState({
        isControlEnabled: true,
        activeAvatarId: state.currentAvatar.id
      });

      onControlEnabled?.(state.currentAvatar.id);

    } catch (error) {
      console.error('进入场景失败:', error);
      message.error('进入场景失败');
    } finally {
      updateState({
        isProcessing: false,
        processingStep: ''
      });
    }
  }, [state.currentAvatar, state.currentScene, state.spawnPosition, onSceneLoaded, onControlEnabled, updateState]);

  /**
   * 启用虚拟化身控制
   */
  const handleEnableControl = useCallback(() => {
    if (!state.currentAvatar) {
      message.error('请先选择虚拟化身');
      return;
    }

    updateState({
      isControlEnabled: true,
      activeAvatarId: state.currentAvatar.id
    });

    onControlEnabled?.(state.currentAvatar.id);
    message.success('虚拟化身控制已启用');
  }, [state.currentAvatar, onControlEnabled, updateState]);

  /**
   * 模拟创建虚拟化身
   */
  const createMockAvatar = useCallback(() => {
    const mockAvatar: AvatarData = {
      id: `avatar_${Date.now()}`,
      userId: 'user123',
      name: `虚拟化身_${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    updateState({
      currentAvatar: mockAvatar,
      savedAvatars: [...state.savedAvatars, mockAvatar]
    });

    message.success('虚拟化身创建成功');
  }, [state.savedAvatars, updateState]);

  /**
   * 渲染保存配置面板
   */
  const renderSavePanel = () => (
    <Card title="虚拟化身保存" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <label>保存格式：</label>
          <Select
            value={state.saveFormat}
            onChange={(value) => updateState({ saveFormat: value })}
            style={{ width: 120, marginLeft: 8 }}
          >
            <Option value="json">JSON</Option>
            <Option value="binary">Binary</Option>
            <Option value="gltf">GLTF</Option>
            <Option value="fbx">FBX</Option>
          </Select>
        </div>

        <div>
          <label>保存位置：</label>
          <Select
            value={state.saveLocation}
            onChange={(value) => updateState({ saveLocation: value })}
            style={{ width: 120, marginLeft: 8 }}
          >
            <Option value="filesystem">文件系统</Option>
            <Option value="database">数据库</Option>
            <Option value="cloud">云存储</Option>
          </Select>
        </div>

        <div>
          <Switch
            checked={state.enableCompression}
            onChange={(checked) => updateState({ enableCompression: checked })}
          />
          <span style={{ marginLeft: 8 }}>启用压缩</span>
        </div>

        <div>
          <Switch
            checked={state.enableEncryption}
            onChange={(checked) => updateState({ enableEncryption: checked })}
          />
          <span style={{ marginLeft: 8 }}>启用加密</span>
        </div>

        <Button
          type="primary"
          icon={<SaveOutlined />}
          onClick={handleSaveAvatar}
          disabled={!state.currentAvatar || state.isProcessing}
          loading={state.isProcessing && state.processingStep.includes('保存')}
        >
          保存虚拟化身
        </Button>
      </Space>
    </Card>
  );

  /**
   * 渲染场景加载面板
   */
  const renderScenePanel = () => (
    <Card title="一键进入场景" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <label>选择场景：</label>
          <Select
            value={state.currentScene}
            onChange={(value) => updateState({ currentScene: value })}
            placeholder="请选择场景"
            style={{ width: 200, marginLeft: 8 }}
          >
            {state.availableScenes.map(scene => (
              <Option key={scene.id} value={scene.id}>
                {scene.name}
              </Option>
            ))}
          </Select>
        </div>

        <div>
          <label>生成位置：</label>
          <Space>
            <InputNumber
              value={state.spawnPosition.x}
              onChange={(value) => updateState({
                spawnPosition: { ...state.spawnPosition, x: value || 0 }
              })}
              placeholder="X"
              style={{ width: 60 }}
            />
            <InputNumber
              value={state.spawnPosition.y}
              onChange={(value) => updateState({
                spawnPosition: { ...state.spawnPosition, y: value || 0 }
              })}
              placeholder="Y"
              style={{ width: 60 }}
            />
            <InputNumber
              value={state.spawnPosition.z}
              onChange={(value) => updateState({
                spawnPosition: { ...state.spawnPosition, z: value || 0 }
              })}
              placeholder="Z"
              style={{ width: 60 }}
            />
          </Space>
        </div>

        <div>
          <Switch
            checked={state.enablePhysics}
            onChange={(checked) => updateState({ enablePhysics: checked })}
          />
          <span style={{ marginLeft: 8 }}>启用物理</span>
        </div>

        <div>
          <Switch
            checked={state.enableAI}
            onChange={(checked) => updateState({ enableAI: checked })}
          />
          <span style={{ marginLeft: 8 }}>启用AI</span>
        </div>

        <div>
          <Switch
            checked={state.enableVoice}
            onChange={(checked) => updateState({ enableVoice: checked })}
          />
          <span style={{ marginLeft: 8 }}>启用语音</span>
        </div>

        <Button
          type="primary"
          icon={<PlayCircleOutlined />}
          onClick={handleQuickEnterScene}
          disabled={!state.currentAvatar || !state.currentScene || state.isProcessing}
          loading={state.isProcessing && state.processingStep.includes('场景')}
        >
          一键进入场景
        </Button>
      </Space>
    </Card>
  );

  /**
   * 渲染控制面板
   */
  const renderControlPanel = () => (
    <Card title="虚拟化身控制" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <label>移动速度：</label>
          <InputNumber
            value={state.moveSpeed}
            onChange={(value) => updateState({ moveSpeed: value || 5.0 })}
            min={0.1}
            max={20}
            step={0.1}
            style={{ width: 100, marginLeft: 8 }}
          />
        </div>

        <div>
          <label>旋转速度：</label>
          <InputNumber
            value={state.rotationSpeed}
            onChange={(value) => updateState({ rotationSpeed: value || 2.0 })}
            min={0.1}
            max={10}
            step={0.1}
            style={{ width: 100, marginLeft: 8 }}
          />
        </div>

        <div>
          <label>跳跃力度：</label>
          <InputNumber
            value={state.jumpForce}
            onChange={(value) => updateState({ jumpForce: value || 10.0 })}
            min={1}
            max={50}
            step={1}
            style={{ width: 100, marginLeft: 8 }}
          />
        </div>

        <div>
          <Switch
            checked={state.enableGravity}
            onChange={(checked) => updateState({ enableGravity: checked })}
          />
          <span style={{ marginLeft: 8 }}>启用重力</span>
        </div>

        <div>
          <Switch
            checked={state.enableCollision}
            onChange={(checked) => updateState({ enableCollision: checked })}
          />
          <span style={{ marginLeft: 8 }}>启用碰撞检测</span>
        </div>

        <Button
          type="primary"
          icon={<ControlOutlined />}
          onClick={handleEnableControl}
          disabled={!state.currentAvatar || state.isControlEnabled}
        >
          {state.isControlEnabled ? '控制已启用' : '启用控制'}
        </Button>

        {state.isControlEnabled && (
          <div style={{ marginTop: 16, padding: 12, backgroundColor: '#f0f0f0', borderRadius: 4 }}>
            <h4>控制说明：</h4>
            <p>• WASD 或 方向键：移动</p>
            <p>• 空格键：跳跃</p>
            <p>• Shift：跑步</p>
            <p>• E键：交互</p>
            <p>• 鼠标：视角旋转</p>
          </div>
        )}
      </Space>
    </Card>
  );

  /**
   * 渲染状态面板
   */
  const renderStatusPanel = () => (
    <Card title="状态信息" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <strong>当前虚拟化身：</strong>
          {state.currentAvatar ? (
            <span style={{ color: '#52c41a' }}>{state.currentAvatar.name || state.currentAvatar.id}</span>
          ) : (
            <span style={{ color: '#ff4d4f' }}>未选择</span>
          )}
        </div>

        <div>
          <strong>当前场景：</strong>
          {state.currentScene ? (
            <span style={{ color: '#52c41a' }}>
              {state.availableScenes.find(s => s.id === state.currentScene)?.name || state.currentScene}
            </span>
          ) : (
            <span style={{ color: '#ff4d4f' }}>未加载</span>
          )}
        </div>

        <div>
          <strong>控制状态：</strong>
          {state.isControlEnabled ? (
            <span style={{ color: '#52c41a' }}>已启用</span>
          ) : (
            <span style={{ color: '#ff4d4f' }}>未启用</span>
          )}
        </div>

        <div>
          <strong>激活虚拟化身：</strong>
          {state.activeAvatarId ? (
            <span style={{ color: '#52c41a' }}>{state.activeAvatarId}</span>
          ) : (
            <span style={{ color: '#ff4d4f' }}>无</span>
          )}
        </div>

        {state.isProcessing && (
          <div style={{ color: '#1890ff' }}>
            <strong>处理中：</strong>{state.processingStep}
          </div>
        )}
      </Space>
    </Card>
  );

  return (
    <div style={{ padding: 16 }}>
      <Card title="虚拟化身场景管理器" extra={
        <Button onClick={createMockAvatar} icon={<UserOutlined />}>
          创建测试虚拟化身
        </Button>
      }>
        <Tabs defaultActiveKey="save" type="card">
          <TabPane tab={<span><SaveOutlined />保存管理</span>} key="save">
            {renderSavePanel()}
          </TabPane>

          <TabPane tab={<span><EnvironmentOutlined />场景加载</span>} key="scene">
            {renderScenePanel()}
          </TabPane>

          <TabPane tab={<span><ControlOutlined />控制设置</span>} key="control">
            {renderControlPanel()}
          </TabPane>

          <TabPane tab={<span><SettingOutlined />状态信息</span>} key="status">
            {renderStatusPanel()}
          </TabPane>
        </Tabs>

        <Divider />

        <div style={{ textAlign: 'center' }}>
          <Space size="large">
            <Button
              type="primary"
              size="large"
              icon={<SaveOutlined />}
              onClick={handleSaveAvatar}
              disabled={!state.currentAvatar || state.isProcessing}
              loading={state.isProcessing && state.processingStep.includes('保存')}
            >
              保存虚拟化身
            </Button>

            <Button
              type="primary"
              size="large"
              icon={<PlayCircleOutlined />}
              onClick={handleQuickEnterScene}
              disabled={!state.currentAvatar || !state.currentScene || state.isProcessing}
              loading={state.isProcessing && state.processingStep.includes('场景')}
            >
              一键进入场景
            </Button>

            <Button
              type="primary"
              size="large"
              icon={<ControlOutlined />}
              onClick={handleEnableControl}
              disabled={!state.currentAvatar || state.isControlEnabled}
            >
              启用控制
            </Button>
          </Space>
        </div>
      </Card>
    </div>
  );
};
