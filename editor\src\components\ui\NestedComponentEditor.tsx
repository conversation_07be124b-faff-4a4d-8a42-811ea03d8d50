/**
 * NestedComponentEditor.tsx
 * 
 * 嵌套组件编辑器，支持容器组件内的子组件编辑
 */

import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import {
  Tree,
  Button,
  Dropdown,
  Modal,
  Input,
  Select,
  Space,
  Tooltip,
  message
} from 'antd';
import type { Key } from 'react';
import {
  PlusOutlined,
  DeleteOutlined,
  CopyOutlined,
  EditOutlined,
  FolderOutlined,
  FileOutlined,
  MoreOutlined
} from '@ant-design/icons';
import './NestedComponentEditor.module.css';

/**
 * 组件节点接口
 */
export interface ComponentNode {
  id: string;
  type: string;
  name: string;
  parentId?: string;
  children?: ComponentNode[];
  properties: Record<string, any>;
  style: Record<string, any>;
  position: { x: number; y: number };
  size: { width: number; height: number };
  zIndex: number;
  visible: boolean;
  locked: boolean;
  expanded?: boolean;
}

/**
 * 组件类型定义
 */
export interface ComponentType {
  type: string;
  name: string;
  icon: React.ReactNode;
  category: string;
  isContainer: boolean;
  defaultProps: Record<string, any>;
  allowedChildren?: string[];
  maxChildren?: number;
}

/**
 * 拖拽项目类型
 */
const ItemTypes = {
  COMPONENT: 'component',
  NODE: 'node'
};

/**
 * 预定义组件类型
 */
const COMPONENT_TYPES: ComponentType[] = [
  {
    type: 'panel',
    name: '面板',
    icon: <FolderOutlined />,
    category: '容器',
    isContainer: true,
    defaultProps: { backgroundColor: '#f5f5f5', padding: 16 }
  },
  {
    type: 'window',
    name: '窗口',
    icon: <FolderOutlined />,
    category: '容器',
    isContainer: true,
    defaultProps: { title: '新窗口', resizable: true },
    allowedChildren: ['button', 'text', 'input', 'panel']
  },
  {
    type: 'button',
    name: '按钮',
    icon: <FileOutlined />,
    category: '控件',
    isContainer: false,
    defaultProps: { text: '按钮', variant: 'primary' }
  },
  {
    type: 'text',
    name: '文本',
    icon: <FileOutlined />,
    category: '控件',
    isContainer: false,
    defaultProps: { content: '文本内容', fontSize: 14 }
  },
  {
    type: 'input',
    name: '输入框',
    icon: <FileOutlined />,
    category: '控件',
    isContainer: false,
    defaultProps: { placeholder: '请输入...', value: '' }
  }
];

/**
 * 嵌套组件编辑器属性
 */
export interface NestedComponentEditorProps {
  /** 根组件节点 */
  rootNode?: ComponentNode;
  /** 组件变化回调 */
  onChange?: (rootNode: ComponentNode) => void;
  /** 选中节点变化回调 */
  onSelectionChange?: (selectedNode: ComponentNode | null) => void;
  /** 样式类名 */
  className?: string;
  /** 是否只读 */
  readonly?: boolean;
}

/**
 * 可拖拽组件项
 */
const DraggableComponentType: React.FC<{ componentType: ComponentType }> = ({ componentType }) => {
  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.COMPONENT,
    item: { componentType },
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  });

  return (
    <div
      ref={drag}
      className={`component-type-item ${isDragging ? 'dragging' : ''}`}
      style={{ opacity: isDragging ? 0.5 : 1 }}
    >
      <span className="component-icon">{componentType.icon}</span>
      <span className="component-name">{componentType.name}</span>
    </div>
  );
};

/**
 * 可拖拽树节点
 */
const DraggableTreeNode: React.FC<{
  node: ComponentNode;
  onSelect: (node: ComponentNode) => void;
  onUpdate: (node: ComponentNode) => void;
  onDelete: (nodeId: string) => void;
  isSelected: boolean;
}> = ({ node, onSelect, onUpdate, onDelete, isSelected }) => {
  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.NODE,
    item: { node },
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  });

  const [{ isOver }, drop] = useDrop({
    accept: [ItemTypes.COMPONENT, ItemTypes.NODE],
    drop: (item: any) => {
      // 处理拖拽放置逻辑
      console.log('Drop on node:', node.id, item);
    },
    collect: (monitor) => ({
      isOver: monitor.isOver()
    })
  });

  const componentType = COMPONENT_TYPES.find(t => t.type === node.type);
  const isContainer = componentType?.isContainer || false;

  const handleContextMenu = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    // 显示右键菜单
  }, []);

  const menuItems = [
    {
      key: 'edit',
      icon: <EditOutlined />,
      label: '编辑',
      onClick: () => onSelect(node)
    },
    {
      key: 'copy',
      icon: <CopyOutlined />,
      label: '复制',
      onClick: () => {
        // 复制节点逻辑
        const newNode = { ...node, id: `${node.id}_copy_${Date.now()}` };
        onUpdate(newNode);
      }
    },
    {
      key: 'delete',
      icon: <DeleteOutlined />,
      label: '删除',
      danger: true,
      onClick: () => onDelete(node.id)
    }
  ];

  return (
    <div
      ref={(el) => {
        drag(el);
        drop(el);
      }}
      className={`tree-node ${isSelected ? 'selected' : ''} ${isOver ? 'drop-over' : ''}`}
      style={{ opacity: isDragging ? 0.5 : 1 }}
      onClick={() => onSelect(node)}
      onContextMenu={handleContextMenu}
    >
      <div className="node-content">
        <span className="node-icon">
          {isContainer ? <FolderOutlined /> : <FileOutlined />}
        </span>
        <span className="node-name">{node.name}</span>
        <span className="node-type">({node.type})</span>
        {!node.visible && <span className="node-hidden">隐藏</span>}
        {node.locked && <span className="node-locked">锁定</span>}
      </div>
      
      <div className="node-actions">
        <Dropdown
          menu={{ items: menuItems }}
          trigger={['click']}
          placement="bottomRight"
        >
          <Button
            type="text"
            size="small"
            icon={<MoreOutlined />}
            onClick={(e) => e.stopPropagation()}
          />
        </Dropdown>
      </div>
    </div>
  );
};

/**
 * 嵌套组件编辑器
 */
export const NestedComponentEditor: React.FC<NestedComponentEditorProps> = ({
  rootNode,
  onChange,
  onSelectionChange,
  className,
  readonly = false
}) => {
  const [selectedNode, setSelectedNode] = useState<ComponentNode | null>(null);
  const [expandedKeys, setExpandedKeys] = useState<Key[]>([]);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [newComponentType, setNewComponentType] = useState<string>('');
  const [newComponentName, setNewComponentName] = useState<string>('');
  const [targetParentId, setTargetParentId] = useState<string>('');

  const editorRef = useRef<HTMLDivElement>(null);

  // 生成树数据
  const treeData = useMemo(() => {
    const buildTreeData = (node: ComponentNode): any => {
      return {
        key: node.id,
        title: (
          <DraggableTreeNode
            node={node}
            onSelect={setSelectedNode}
            onUpdate={(updatedNode) => {
              // 更新节点逻辑
              if (onChange && rootNode) {
                const updateNodeInTree = (tree: ComponentNode, nodeId: string, updates: Partial<ComponentNode>): ComponentNode => {
                  if (tree.id === nodeId) {
                    return { ...tree, ...updates };
                  }
                  if (tree.children) {
                    return {
                      ...tree,
                      children: tree.children.map(child => updateNodeInTree(child, nodeId, updates))
                    };
                  }
                  return tree;
                };
                
                const updatedRoot = updateNodeInTree(rootNode, updatedNode.id, updatedNode);
                onChange(updatedRoot);
              }
            }}
            onDelete={(nodeId) => {
              if (onChange && rootNode) {
                const removeNodeFromTree = (tree: ComponentNode, nodeId: string): ComponentNode | null => {
                  if (tree.id === nodeId) {
                    return null;
                  }
                  if (tree.children) {
                    const filteredChildren = tree.children
                      .map(child => removeNodeFromTree(child, nodeId))
                      .filter(Boolean) as ComponentNode[];
                    return { ...tree, children: filteredChildren };
                  }
                  return tree;
                };
                
                const updatedRoot = removeNodeFromTree(rootNode, nodeId);
                if (updatedRoot) {
                  onChange(updatedRoot);
                }
              }
            }}
            isSelected={selectedNode?.id === node.id}
          />
        ),
        children: node.children?.map(child => buildTreeData(child)) || []
      };
    };

    return rootNode ? [buildTreeData(rootNode)] : [];
  }, [rootNode, selectedNode, onChange]);



  // 添加组件
  const handleAddComponent = useCallback(() => {
    if (!newComponentType || !newComponentName) {
      message.error('请填写完整信息');
      return;
    }

    const componentType = COMPONENT_TYPES.find(t => t.type === newComponentType);
    if (!componentType) {
      message.error('无效的组件类型');
      return;
    }

    const newNode: ComponentNode = {
      id: `component_${Date.now()}`,
      type: newComponentType,
      name: newComponentName,
      parentId: targetParentId || undefined,
      children: componentType.isContainer ? [] : undefined,
      properties: { ...componentType.defaultProps },
      style: {},
      position: { x: 0, y: 0 },
      size: { width: 100, height: 50 },
      zIndex: 1,
      visible: true,
      locked: false
    };

    if (onChange && rootNode) {
      if (!targetParentId) {
        // 添加为根节点的子节点
        const updatedRoot = {
          ...rootNode,
          children: [...(rootNode.children || []), newNode]
        };
        onChange(updatedRoot);
      } else {
        // 添加到指定父节点
        const addNodeToTree = (tree: ComponentNode, parentId: string, node: ComponentNode): ComponentNode => {
          if (tree.id === parentId) {
            return {
              ...tree,
              children: [...(tree.children || []), node]
            };
          }
          if (tree.children) {
            return {
              ...tree,
              children: tree.children.map(child => addNodeToTree(child, parentId, node))
            };
          }
          return tree;
        };
        
        const updatedRoot = addNodeToTree(rootNode, targetParentId, newNode);
        onChange(updatedRoot);
      }
    }

    setIsAddModalVisible(false);
    setNewComponentType('');
    setNewComponentName('');
    setTargetParentId('');
    message.success('组件添加成功');
  }, [newComponentType, newComponentName, targetParentId, onChange, rootNode]);

  // 获取可作为父节点的容器组件
  const getContainerNodes = useCallback((node: ComponentNode): ComponentNode[] => {
    const containers: ComponentNode[] = [];
    
    const traverse = (n: ComponentNode) => {
      const componentType = COMPONENT_TYPES.find(t => t.type === n.type);
      if (componentType?.isContainer) {
        containers.push(n);
      }
      if (n.children) {
        n.children.forEach(traverse);
      }
    };
    
    traverse(node);
    return containers;
  }, []);

  const containerNodes = rootNode ? getContainerNodes(rootNode) : [];

  // 处理拖拽放置
  const [{ isOver }, drop] = useDrop({
    accept: [ItemTypes.COMPONENT, ItemTypes.NODE],
    drop: (item: any, monitor) => {
      if (!monitor.didDrop()) {
        // 处理拖拽到编辑器根部的逻辑
        console.log('Drop to editor root:', item);
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true })
    })
  });

  useEffect(() => {
    if (selectedNode) {
      onSelectionChange?.(selectedNode);
    }
  }, [selectedNode, onSelectionChange]);

  return (
    <DndProvider backend={HTML5Backend}>
      <div className={`nested-component-editor ${className || ''}`}>
        {/* 组件库面板 */}
        <div className="component-library-panel">
          <div className="panel-header">
            <h3>组件库</h3>
          </div>
          <div className="component-types">
            {COMPONENT_TYPES.map(componentType => (
              <DraggableComponentType
                key={componentType.type}
                componentType={componentType}
              />
            ))}
          </div>
        </div>

        {/* 组件树面板 */}
        <div className="component-tree-panel">
          <div className="panel-header">
            <h3>组件树</h3>
            <Space>
              <Tooltip title="添加组件">
                <Button
                  type="primary"
                  size="small"
                  icon={<PlusOutlined />}
                  onClick={() => setIsAddModalVisible(true)}
                  disabled={readonly}
                />
              </Tooltip>
            </Space>
          </div>
          
          <div className="tree-container">
            {treeData.length > 0 ? (
              <Tree
                treeData={treeData}
                expandedKeys={expandedKeys}
                onExpand={setExpandedKeys}
                showLine
                showIcon={false}
                selectable={false}
              />
            ) : (
              <div className="empty-tree">
                <p>暂无组件</p>
                <Button
                  type="dashed"
                  icon={<PlusOutlined />}
                  onClick={() => setIsAddModalVisible(true)}
                  disabled={readonly}
                >
                  添加组件
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* 可视化编辑区域 */}
        <div
          ref={(el) => {
            drop(el);
            if (editorRef.current !== el) {
              (editorRef as any).current = el;
            }
          }}
          className={`visual-editor ${isOver ? 'drop-over' : ''}`}
        >
          <div className="editor-canvas">
            {selectedNode && (
              <div className="selection-indicator">
                当前选中: {selectedNode.name} ({selectedNode.type})
              </div>
            )}
            
            {/* 这里可以渲染实际的组件预览 */}
            <div className="component-preview">
              {rootNode && (
                <div className="preview-message">
                  组件预览区域 - {rootNode.name}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 添加组件模态框 */}
        <Modal
          title="添加组件"
          open={isAddModalVisible}
          onOk={handleAddComponent}
          onCancel={() => {
            setIsAddModalVisible(false);
            setNewComponentType('');
            setNewComponentName('');
            setTargetParentId('');
          }}
          okText="添加"
          cancelText="取消"
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <label>组件类型:</label>
              <Select
                style={{ width: '100%' }}
                placeholder="选择组件类型"
                value={newComponentType}
                onChange={setNewComponentType}
              >
                {COMPONENT_TYPES.map(type => (
                  <Select.Option key={type.type} value={type.type}>
                    {type.icon} {type.name}
                  </Select.Option>
                ))}
              </Select>
            </div>
            
            <div>
              <label>组件名称:</label>
              <Input
                placeholder="输入组件名称"
                value={newComponentName}
                onChange={(e) => setNewComponentName(e.target.value)}
              />
            </div>
            
            <div>
              <label>父容器:</label>
              <Select
                style={{ width: '100%' }}
                placeholder="选择父容器（可选）"
                value={targetParentId}
                onChange={setTargetParentId}
                allowClear
              >
                {containerNodes.map(node => (
                  <Select.Option key={node.id} value={node.id}>
                    {node.name} ({node.type})
                  </Select.Option>
                ))}
              </Select>
            </div>
          </Space>
        </Modal>
      </div>
    </DndProvider>
  );
};

export default NestedComponentEditor;
