/**
 * 层级面板组件
 */
import React, { useState, useEffect, useCallback } from 'react';
import { Button, Dropdown, Empty, Space, message, Input, Tree, Modal } from 'antd';
import {
  FolderOutlined,
  PlusOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  LockOutlined,
  UnlockOutlined,
  DeleteOutlined,
  CameraOutlined,
  BulbOutlined,
  AppstoreOutlined,
  ApartmentOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import { setSelectedObject } from '../../store/editor/editorSlice';

// 导入服务
import EngineService, { SelectionMode } from '../../services/EngineService';
import SceneService from '../../services/SceneService';

const { Search } = Input;
const { DirectoryTree } = Tree;
const { confirm } = Modal;

// 本地类型定义
interface Entity {
  id: string;
  name: string;
  hasComponent(type: string): boolean;
  getComponent(type: string): any;
  addComponent(type: string): any;
  getComponents(): Map<string, any>;
  isActive(): boolean;
  setParent(parent: Entity): void;
  getTransform(): any;
}

interface TreeSelectInfo {
  node: {
    entityId?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

interface RightClickInfo {
  event: React.MouseEvent;
  node: {
    entityId?: string;
    [key: string]: any;
  };
}

// 树节点接口
interface TreeNode {
  key: string;
  title: string;
  icon?: React.ReactNode;
  isLeaf?: boolean;
  children?: TreeNode[];
  visible?: boolean;
  locked?: boolean;
  entityId?: string;
  entityType?: string;
}

const HierarchyPanel: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const { sceneGraph, selectedObject } = useAppSelector((state) => state.editor);

  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [treeData, setTreeData] = useState<TreeNode[]>([]);

  // 递归构建树节点
  const buildTreeNode = useCallback((node: any): TreeNode => {
    // 确定图标
    let icon;
    if (node.components && node.components.includes('Camera')) {
      icon = <CameraOutlined />;
    } else if (node.components && node.components.some((c: any) => c.includes('Light'))) {
      icon = <BulbOutlined />;
    } else if (node.children && node.children.length > 0) {
      icon = <FolderOutlined />;
    } else {
      icon = <AppstoreOutlined />;
    }

    // 构建树节点
    const treeNode: TreeNode = {
      key: node.id,
      title: node.name,
      icon,
      isLeaf: !node.children || node.children.length === 0,
      visible: node.visible !== undefined ? node.visible : true,
      locked: node.locked !== undefined ? node.locked : false,
      entityId: node.id,
      entityType: node.type
    };

    // 处理子节点
    if (node.children && node.children.length > 0) {
      treeNode.children = node.children.map((child: any) => buildTreeNode(child));
    }

    return treeNode;
  }, []);

  // 将场景图转换为树数据
  const convertSceneGraphToTreeData = useCallback((sceneGraph: any): TreeNode[] => {
    if (!sceneGraph) {
      return [];
    }

    // 如果是数组，处理数组中的每个节点
    if (Array.isArray(sceneGraph)) {
      return sceneGraph.map((node: any) => buildTreeNode(node));
    }

    // 如果是单个节点，直接处理
    return [buildTreeNode(sceneGraph)];
  }, [buildTreeNode]);

  // 监听场景图变化
  useEffect(() => {
    if (sceneGraph) {
      const newTreeData = convertSceneGraphToTreeData(sceneGraph);
      setTreeData(newTreeData);

      // 默认展开根节点
      if (newTreeData.length > 0) {
        setExpandedKeys([newTreeData[0].key]);
      }
    } else {
      setTreeData([]);
    }
  }, [sceneGraph, convertSceneGraphToTreeData]);

  // 查找实体路径（暂时未使用，但保留以备将来使用）
  // const findEntityPath = useCallback((entityId: string): string[] => {
  //   const path: string[] = [];

  //   const findPath = (nodes: TreeNode[], id: string, currentPath: string[]): boolean => {
  //     for (const node of nodes) {
  //       const newPath = [...currentPath, node.key as string];

  //       if (node.entityId === id) {
  //         path.push(...newPath);
  //         return true;
  //       }

  //       if (node.children && findPath(node.children, id, newPath)) {
  //         return true;
  //       }
  //     }

  //     return false;
  //   };

  //   findPath(treeData, entityId, []);
  //   return path;
  // }, [treeData]);

  // 处理搜索
  const handleSearch = (value: string) => {
    if (!value) {
      setExpandedKeys([]);
      setAutoExpandParent(false);
      return;
    }

    // 查找匹配的节点并展开其父节点
    const expandKeys: React.Key[] = [];

    const searchTree = (nodes: TreeNode[]) => {
      nodes.forEach((node) => {
        if (node.title.toLowerCase().includes(value.toLowerCase())) {
          expandKeys.push(node.key);
        }

        if (node.children) {
          searchTree(node.children);
        }
      });
    };

    searchTree(treeData);

    setExpandedKeys(expandKeys);
    setAutoExpandParent(true);
  };

  // 处理展开/折叠
  const handleExpand = (keys: React.Key[]) => {
    setExpandedKeys(keys);
    setAutoExpandParent(false);
  };

  // 处理选择
  const handleSelect = (selectedKeys: React.Key[], info: TreeSelectInfo) => {
    if (selectedKeys.length > 0) {
      const entityId = info.node.entityId;
      if (entityId) {
        // 获取实体并选择
        const scene = EngineService.getActiveScene();
        if (scene) {
          const entities = scene.getEntities();
          const entity = entities.find((e: Entity) => e.id === entityId);
          if (entity) {
            EngineService.selectEntity(entity, SelectionMode.SINGLE);
          }
        }
      }
    } else {
      EngineService.clearSelection();
      dispatch(setSelectedObject(null));
    }
  };

  // 处理右键菜单
  const handleRightClick = ({ event, node }: RightClickInfo) => {
    event.preventDefault();

    const entityId = node.entityId;
    if (entityId) {
      // 获取实体并选择
      const scene = EngineService.getActiveScene();
      if (scene) {
        const entities = scene.getEntities();
        const entity = entities.find((e: Entity) => e.id === entityId);
        if (entity) {
          EngineService.selectEntity(entity, SelectionMode.SINGLE);
        }
      }
    }
  };

  // 处理删除对象
  const handleDeleteObject = () => {
    if (!selectedObject || !selectedObject.entityId) return;

    confirm({
      title: t('editor.confirmDelete'),
      content: t('editor.deleteObjectConfirm', { name: selectedObject.title }),
      okText: t('common.delete'),
      okType: 'danger',
      cancelText: t('common.cancel'),
      onOk() {
        const scene = EngineService.getActiveScene();
        if (scene) {
          const entities = scene.getEntities();
          const entity = entities.find((e: Entity) => e.id === selectedObject.entityId);
          if (entity) {
            EngineService.removeEntity(entity);
            message.success(t('editor.objectDeleted'));
          }
        }
      }
    });
  };

  // 处理添加对象
  const handleAddObject = (type: string) => {
    const scene = EngineService.getActiveScene();
    if (!scene) return;

    let entity;
    let parentEntity;

    // 如果有选中的对象，使用它作为父对象
    if (selectedObject && selectedObject.entityId) {
      const entities = scene.getEntities();
      parentEntity = entities.find((e: Entity) => e.id === selectedObject.entityId);
    }

    // 根据类型创建不同的对象
    switch (type) {
      case 'empty':
        entity = EngineService.createEntity(t('editor.newObject'), parentEntity);
        break;

      case 'cube':
        entity = EngineService.createEntity(t('editor.cube'), parentEntity);
        // 添加立方体组件
        // entity.addComponent('MeshRenderer', { geometry: 'cube' });
        break;

      case 'sphere':
        entity = EngineService.createEntity(t('editor.sphere'), parentEntity);
        // 添加球体组件
        // entity.addComponent('MeshRenderer', { geometry: 'sphere' });
        break;

      case 'plane':
        entity = EngineService.createEntity(t('editor.plane'), parentEntity);
        // 添加平面组件
        // entity.addComponent('MeshRenderer', { geometry: 'plane' });
        break;

      case 'directionalLight':
        entity = EngineService.createEntity(t('editor.directionalLight'), parentEntity);
        // 添加定向光组件
        // entity.addComponent('DirectionalLight');
        break;

      case 'pointLight':
        entity = EngineService.createEntity(t('editor.pointLight'), parentEntity);
        // 添加点光源组件
        // entity.addComponent('PointLight');
        break;

      case 'spotLight':
        entity = EngineService.createEntity(t('editor.spotLight'), parentEntity);
        // 添加聚光灯组件
        // entity.addComponent('SpotLight');
        break;

      case 'ambientLight':
        entity = EngineService.createEntity(t('editor.ambientLight'), parentEntity);
        // 添加环境光组件
        // entity.addComponent('AmbientLight');
        break;

      case 'camera':
        entity = EngineService.createEntity(t('editor.camera'), parentEntity);
        // 添加相机组件
        // entity.addComponent('Camera');
        break;
    }

    if (entity) {
      // 选择新创建的实体
      EngineService.selectEntity(entity);
      message.success(t('editor.objectCreated'));
    }
  };

  // 处理可见性切换
  const handleToggleVisibility = (node: TreeNode, e: React.MouseEvent) => {
    e.stopPropagation();

    if (!node.entityId) return;

    const scene = EngineService.getActiveScene();
    if (scene) {
      const entities = scene.getEntities();
      const entity = entities.find((e: Entity) => e.id === node.entityId);
      if (entity) {
        // entity.setVisible(!node.visible); // 暂时注释，因为 Entity 接口中没有此方法
        // 更新场景图
        SceneService.updateSceneGraph();
      }
    }
  };

  // 处理锁定切换
  const handleToggleLock = (node: TreeNode, e: React.MouseEvent) => {
    e.stopPropagation();

    if (!node.entityId) return;

    const scene = EngineService.getActiveScene();
    if (scene) {
      const entities = scene.getEntities();
      const entity = entities.find((e: Entity) => e.id === node.entityId);
      if (entity) {
        // entity.setLocked(!node.locked); // 暂时注释，因为 Entity 接口中没有此方法
        // 更新场景图
        SceneService.updateSceneGraph();
      }
    }
  };

  // 右键菜单 (暂时未使用)
  // const contextMenu = (
  //   <Menu>
  //     <Menu.Item key="rename" icon={<FileOutlined />}>
  //       {t('editor.rename')}
  //     </Menu.Item>
  //     <Menu.Item key="duplicate" icon={<CopyOutlined />}>
  //       {t('editor.duplicate')}
  //     </Menu.Item>
  //     <Menu.Divider />
  //     <Menu.Item key="cut" icon={<ScissorOutlined />}>
  //       {t('editor.cut')}
  //     </Menu.Item>
  //     <Menu.Item key="copy" icon={<CopyOutlined />}>
  //       {t('editor.copy')}
  //     </Menu.Item>
  //     <Menu.Item key="paste" icon={<SnippetsOutlined />}>
  //       {t('editor.paste')}
  //     </Menu.Item>
  //     <Menu.Divider />
  //     <Menu.Item key="delete" icon={<DeleteOutlined />} onClick={handleDeleteObject}>
  //       {t('editor.delete')}
  //     </Menu.Item>
  //   </Menu>
  // );

  // 添加菜单项
  const addMenuItems = [
    {
      key: 'empty',
      icon: <ApartmentOutlined />,
      label: t('editor.emptyObject'),
      onClick: () => handleAddObject('empty')
    },
    { type: 'divider' as const },
    {
      key: 'cube',
      icon: <AppstoreOutlined />,
      label: t('editor.cube'),
      onClick: () => handleAddObject('cube')
    },
    {
      key: 'sphere',
      icon: <AppstoreOutlined />,
      label: t('editor.sphere'),
      onClick: () => handleAddObject('sphere')
    },
    {
      key: 'plane',
      icon: <AppstoreOutlined />,
      label: t('editor.plane'),
      onClick: () => handleAddObject('plane')
    },
    { type: 'divider' as const },
    {
      key: 'directionalLight',
      icon: <BulbOutlined />,
      label: t('editor.directionalLight'),
      onClick: () => handleAddObject('directionalLight')
    },
    {
      key: 'pointLight',
      icon: <BulbOutlined />,
      label: t('editor.pointLight'),
      onClick: () => handleAddObject('pointLight')
    },
    {
      key: 'spotLight',
      icon: <BulbOutlined />,
      label: t('editor.spotLight'),
      onClick: () => handleAddObject('spotLight')
    },
    {
      key: 'ambientLight',
      icon: <BulbOutlined />,
      label: t('editor.ambientLight'),
      onClick: () => handleAddObject('ambientLight')
    },
    { type: 'divider' as const },
    {
      key: 'camera',
      icon: <CameraOutlined />,
      label: t('editor.camera'),
      onClick: () => handleAddObject('camera')
    }
  ];

  // 自定义节点渲染
  const renderTreeNode = (nodeData: TreeNode): React.ReactElement => {
    const { title, visible, locked } = nodeData;

    const isSelected = selectedObject && selectedObject.entityId === nodeData.entityId;

    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '0 4px',
          backgroundColor: isSelected ? 'rgba(24, 144, 255, 0.1)' : 'transparent'
        }}
      >
        <span>{title}</span>
        <Space size={4}>
          <Button
            type="text"
            size="small"
            icon={visible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
            style={{ padding: 0 }}
            onClick={(e) => handleToggleVisibility(nodeData, e)}
          />
          <Button
            type="text"
            size="small"
            icon={locked ? <LockOutlined /> : <UnlockOutlined />}
            style={{ padding: 0 }}
            onClick={(e) => handleToggleLock(nodeData, e)}
          />
        </Space>
      </div>
    );
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <div style={{ padding: '8px', borderBottom: '1px solid #f0f0f0' }}>
        <Search
          placeholder={t('editor.searchObjects') as string}
          allowClear
          onChange={(e) => handleSearch(e.target.value)}
          style={{ width: '100%' }}
        />
      </div>
      <div style={{ display: 'flex', justifyContent: 'space-between', padding: '8px', borderBottom: '1px solid #f0f0f0' }}>
        <Dropdown menu={{ items: addMenuItems }} placement="bottomLeft">
          <Button type="primary" icon={<PlusOutlined />} size="small">
            {t('editor.add')}
          </Button>
        </Dropdown>
        <Button
          type="text"
          icon={<DeleteOutlined />}
          size="small"
          disabled={!selectedObject}
          onClick={handleDeleteObject}
        />
      </div>
      <div style={{ flex: 1, overflow: 'auto' }}>
        {treeData.length > 0 ? (
          <DirectoryTree
            showIcon
            expandedKeys={expandedKeys}
            autoExpandParent={autoExpandParent}
            onExpand={handleExpand}
            onSelect={handleSelect}
            onRightClick={handleRightClick}
            titleRender={renderTreeNode}
            treeData={treeData}
            blockNode
            draggable
          />
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={t('editor.noObjects')}
            style={{ margin: '20px 0' }}
          />
        )}
      </div>
    </div>
  );
};

export default HierarchyPanel;
