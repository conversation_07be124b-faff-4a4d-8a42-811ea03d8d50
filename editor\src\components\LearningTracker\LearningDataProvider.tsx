import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { LearningDataCollector, CollectorConfig, LearnerInfo } from '../../services/LearningDataCollector';
import { LearningStats, RealtimeLearningState } from '../../types/xapi.types';

/**
 * 学习数据上下文接口
 */
interface LearningDataContextType {
  collector: LearningDataCollector | null;
  isInitialized: boolean;
  stats: LearningStats | null;
  realtimeState: RealtimeLearningState | null;
  recordAvatarInteraction: (data: any) => Promise<void>;
  recordPathFollowing: (data: any) => Promise<void>;
  recordRecommendationReceived: (data: any) => Promise<void>;
  recordEmotionExpression: (data: any) => Promise<void>;
  recordSceneExploration: (data: any) => Promise<void>;
  getCollectorStats: () => any;
  startLearningSession: () => void;
  endLearningSession: () => void;
}

/**
 * 学习数据上下文
 */
const LearningDataContext = createContext<LearningDataContextType | null>(null);

/**
 * 学习数据提供者属性
 */
interface LearningDataProviderProps {
  children: ReactNode;
  userId: string;
  userName?: string;
  email?: string;
  config?: Partial<CollectorConfig>;
}

/**
 * 学习数据提供者组件
 * 为整个应用提供学习数据采集功能
 */
export const LearningDataProvider: React.FC<LearningDataProviderProps> = ({
  children,
  userId,
  userName,
  email,
  config = {}
}) => {
  const [collector, setCollector] = useState<LearningDataCollector | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [stats, setStats] = useState<LearningStats | null>(null);
  const [realtimeState, setRealtimeState] = useState<RealtimeLearningState | null>(null);
  const [sessionId] = useState(() => `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);

  /**
   * 初始化学习数据采集器
   */
  useEffect(() => {
    if (!userId) return;

    const defaultConfig: Partial<CollectorConfig> = {
      apiEndpoint: '/api/learning-tracking/statements',
      batchSize: 10,
      flushInterval: 5000,
      maxRetries: 3,
      retryDelay: 1000,
      enableLocalStorage: true,
      debug: process.env.NODE_ENV === 'development',
      ...config
    };

    const learnerInfo: LearnerInfo = {
      userId,
      userName,
      email,
      sessionId
    };

    try {
      const newCollector = new LearningDataCollector(defaultConfig, learnerInfo);
      
      // 监听采集器事件
      newCollector.on('avatarInteraction', handleAvatarInteraction);
      newCollector.on('pathFollowing', handlePathFollowing);
      newCollector.on('recommendationReceived', handleRecommendationReceived);
      newCollector.on('emotionExpression', handleEmotionExpression);
      newCollector.on('sceneExploration', handleSceneExploration);
      newCollector.on('flush', handleFlush);

      setCollector(newCollector);
      setIsInitialized(true);

      // 初始化实时状态
      setRealtimeState({
        isActive: true,
        attentionLevel: 1,
        engagementLevel: 0.8,
        difficultyLevel: 'medium',
        recommendedBreak: false
      });

      console.log('学习数据采集器初始化成功', { userId, sessionId });
    } catch (error) {
      console.error('学习数据采集器初始化失败:', error);
    }

    return () => {
      if (collector) {
        collector.destroy();
      }
    };
  }, [userId, userName, email, sessionId]);

  /**
   * 处理数字人交互事件
   */
  const handleAvatarInteraction = (data: any) => {
    updateStats('avatarInteraction', data);
    updateRealtimeState({ currentActivity: 'avatar_interaction' });
  };

  /**
   * 处理路径跟踪事件
   */
  const handlePathFollowing = (data: any) => {
    updateStats('pathFollowing', data);
    updateRealtimeState({ currentActivity: 'path_following' });
  };

  /**
   * 处理推荐接收事件
   */
  const handleRecommendationReceived = (data: any) => {
    updateStats('recommendationReceived', data);
  };

  /**
   * 处理情感表达事件
   */
  const handleEmotionExpression = (data: any) => {
    updateStats('emotionExpression', data);
    updateRealtimeState({ currentEmotion: data.emotion });
  };

  /**
   * 处理场景探索事件
   */
  const handleSceneExploration = (data: any) => {
    updateStats('sceneExploration', data);
    updateRealtimeState({ currentActivity: 'scene_exploration' });
  };

  /**
   * 处理数据刷新事件
   */
  const handleFlush = (data: { count: number; success: boolean; error?: any }) => {
    if (data.success) {
      console.log(`成功发送 ${data.count} 条学习记录`);
    } else {
      console.error('学习记录发送失败:', data.error);
    }
  };

  /**
   * 更新统计信息
   */
  const updateStats = (eventType: string, data: any) => {
    setStats(prevStats => {
      if (!prevStats) {
        return {
          totalEvents: 1,
          eventsByType: { [eventType]: 1 },
          sessionDuration: 0,
          averageInteractionTime: 0,
          emotionalStates: {},
          knowledgeAreas: {},
          completionRates: {}
        };
      }

      const newStats = { ...prevStats };
      newStats.totalEvents++;
      newStats.eventsByType[eventType] = (newStats.eventsByType[eventType] || 0) + 1;

      // 更新情感状态
      if (data.emotion) {
        newStats.emotionalStates[data.emotion] = (newStats.emotionalStates[data.emotion] || 0) + 1;
      }

      // 更新知识领域
      if (data.knowledgeArea) {
        newStats.knowledgeAreas[data.knowledgeArea] = (newStats.knowledgeAreas[data.knowledgeArea] || 0) + 1;
      }

      // 更新完成率
      if (data.completionRate !== undefined) {
        newStats.completionRates[data.pathId || data.contentId] = data.completionRate;
      }

      return newStats;
    });
  };

  /**
   * 更新实时状态
   */
  const updateRealtimeState = (updates: Partial<RealtimeLearningState>) => {
    setRealtimeState(prevState => {
      if (!prevState) {
        // 如果之前的状态为 null，创建默认状态
        const defaultState: RealtimeLearningState = {
          isActive: false,
          attentionLevel: 0,
          engagementLevel: 0,
          difficultyLevel: 'medium',
          recommendedBreak: false
        };
        return { ...defaultState, ...updates };
      }
      return { ...prevState, ...updates };
    });
  };

  /**
   * 记录数字人交互
   */
  const recordAvatarInteraction = async (data: any) => {
    if (!collector) return;
    await collector.recordAvatarInteraction(data);
  };

  /**
   * 记录路径跟踪
   */
  const recordPathFollowing = async (data: any) => {
    if (!collector) return;
    await collector.recordPathFollowing(data);
  };

  /**
   * 记录推荐接收
   */
  const recordRecommendationReceived = async (data: any) => {
    if (!collector) return;
    await collector.recordRecommendationReceived(data);
  };

  /**
   * 记录情感表达
   */
  const recordEmotionExpression = async (data: any) => {
    if (!collector) return;
    await collector.recordEmotionExpression(data);
  };

  /**
   * 记录场景探索
   */
  const recordSceneExploration = async (data: any) => {
    if (!collector) return;
    await collector.recordSceneExploration(data);
  };

  /**
   * 获取采集器统计信息
   */
  const getCollectorStats = () => {
    return collector?.getStats() || null;
  };

  /**
   * 开始学习会话
   */
  const startLearningSession = () => {
    updateRealtimeState({ isActive: true });
    console.log('学习会话开始:', sessionId);
  };

  /**
   * 结束学习会话
   */
  const endLearningSession = () => {
    updateRealtimeState({ isActive: false });
    
    // 记录会话结束事件
    if (collector && stats) {
      const sessionData = {
        sessionId,
        startTime: new Date(Date.now() - (stats.sessionDuration * 60 * 1000)),
        endTime: new Date(),
        duration: stats.sessionDuration,
        activitiesCount: stats.totalEvents,
        knowledgeAreas: Object.keys(stats.knowledgeAreas),
        emotions: stats.emotionalStates,
        averageSatisfaction: 4.0 // 可以基于实际数据计算
      };

      // 这里可以调用专门的会话记录方法
      console.log('学习会话结束:', sessionData);
    }
  };

  const contextValue: LearningDataContextType = {
    collector,
    isInitialized,
    stats,
    realtimeState,
    recordAvatarInteraction,
    recordPathFollowing,
    recordRecommendationReceived,
    recordEmotionExpression,
    recordSceneExploration,
    getCollectorStats,
    startLearningSession,
    endLearningSession
  };

  return (
    <LearningDataContext.Provider value={contextValue}>
      {children}
    </LearningDataContext.Provider>
  );
};

/**
 * 使用学习数据上下文的Hook
 */
export const useLearningData = (): LearningDataContextType => {
  const context = useContext(LearningDataContext);
  if (!context) {
    throw new Error('useLearningData must be used within a LearningDataProvider');
  }
  return context;
};

/**
 * 学习数据采集Hook
 * 提供便捷的数据采集方法
 */
export const useLearningTracker = () => {
  const {
    recordAvatarInteraction,
    recordPathFollowing,
    recordRecommendationReceived,
    recordEmotionExpression,
    recordSceneExploration,
    stats,
    realtimeState
  } = useLearningData();

  return {
    // 数据采集方法
    trackAvatarInteraction: recordAvatarInteraction,
    trackPathFollowing: recordPathFollowing,
    trackRecommendationReceived: recordRecommendationReceived,
    trackEmotionExpression: recordEmotionExpression,
    trackSceneExploration: recordSceneExploration,
    
    // 状态信息
    learningStats: stats,
    realtimeState,
    
    // 便捷方法
    trackQuestionAsked: (avatarId: string, question: string, answer: string, emotion?: string) => {
      return recordAvatarInteraction({
        avatarId,
        question,
        answer,
        emotion,
        duration: 0,
        satisfaction: 4
      });
    },
    
    trackContentCompleted: (contentId: string, completionRate: number, timeSpent: number) => {
      return recordSceneExploration({
        sceneId: contentId,
        startTime: new Date(Date.now() - timeSpent * 1000),
        endTime: new Date(),
        areasVisited: ['main'],
        interactionsCount: 1,
        completionRate
      });
    },
    
    trackEmotionChange: (emotion: string, intensity: number, trigger: string) => {
      return recordEmotionExpression({
        emotion,
        intensity,
        trigger,
        context: 'learning_session'
      });
    }
  };
};
