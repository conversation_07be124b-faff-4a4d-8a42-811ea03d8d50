/**
 * AI内容推荐面板样式
 */

.ai-content-recommendation-panel {
  .ant-card {
    .ant-card-head {
      .ant-card-head-title {
        font-size: 16px;
        font-weight: 600;
      }
    }

    .ant-card-body {
      padding: 16px;
    }
  }

  // 过滤器区域
  .filters-row {
    margin-bottom: 16px;

    .ant-input-search {
      .ant-input {
        border-radius: 6px;
      }

      .ant-btn {
        border-radius: 0 6px 6px 0;
      }
    }

    .ant-select {
      .ant-select-selector {
        border-radius: 6px;
      }
    }

    .ant-btn {
      border-radius: 6px;
    }
  }

  // 建议标签区域
  .suggestions-card {
    .ant-card-body {
      padding: 12px 16px;
    }

    .ant-typography {
      margin-bottom: 8px;
    }

    .ant-tag {
      margin-bottom: 4px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background-color: #40a9ff;
        border-color: #40a9ff;
        color: #ffffff;
      }
    }
  }

  // 推荐列表
  .recommendation-list {
    .ant-list-item {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #f9f9f9;
      }

      .ant-list-item-meta {
        .ant-list-item-meta-avatar {
          margin-right: 16px;

          .ant-avatar {
            border: 1px solid #d9d9d9;
          }
        }

        .ant-list-item-meta-title {
          margin-bottom: 8px;
          font-size: 14px;

          .ant-progress {
            margin-left: 8px;
          }
        }

        .ant-list-item-meta-description {
          .ant-typography {
            margin-bottom: 8px;
          }

          .ant-space {
            margin-top: 8px;
          }

          .ant-rate {
            font-size: 12px;
          }
        }
      }

      .ant-list-item-action {
        margin-left: 16px;

        .ant-btn {
          margin-left: 4px;
          border: none;
          box-shadow: none;

          &:hover {
            background-color: #f0f0f0;
          }
        }
      }
    }
  }

  // 分类标签页
  .category-tabs {
    .ant-tabs-tab {
      .ant-badge {
        .ant-badge-count {
          font-size: 10px;
          min-width: 16px;
          height: 16px;
          line-height: 16px;
        }
      }
    }

    .ant-tabs-content {
      .ant-tabs-tabpane {
        padding-top: 16px;
      }
    }
  }

  // 空状态
  .empty-state {
    padding: 40px 0;
    text-align: center;

    .ant-empty-image {
      margin-bottom: 16px;
    }
  }

  // 加载状态
  .loading-state {
    .ant-spin {
      .ant-spin-dot {
        font-size: 20px;
      }
    }
  }
}

// 深色主题
.dark-theme {
  .ai-content-recommendation-panel {
    .ant-card {
      background: #2d2d2d;
      border-color: #404040;

      .ant-card-head {
        background: #2d2d2d;
        border-bottom-color: #404040;

        .ant-card-head-title {
          color: #ffffff;
        }
      }

      .ant-card-body {
        background: #2d2d2d;
        color: #cccccc;
      }
    }

    .suggestions-card {
      background: #2d2d2d;
      border-color: #404040;

      .ant-tag {
        background: #404040;
        border-color: #404040;
        color: #cccccc;

        &:hover {
          background-color: #1890ff;
          border-color: #1890ff;
          color: #ffffff;
        }
      }
    }

    .recommendation-list {
      .ant-list-item {
        border-bottom-color: #404040;

        &:hover {
          background-color: #404040;
        }

        .ant-list-item-meta {
          .ant-list-item-meta-title {
            color: #ffffff;
          }

          .ant-list-item-meta-description {
            color: #cccccc;
          }
        }

        .ant-list-item-action {
          .ant-btn {
            color: #cccccc;

            &:hover {
              background-color: #555555;
              color: #ffffff;
            }
          }
        }
      }
    }

    .empty-state {
      color: #cccccc;
    }
  }
}

// 紧凑模式
.compact-theme {
  .ai-content-recommendation-panel {
    .ant-card {
      .ant-card-body {
        padding: 12px;
      }
    }

    .filters-row {
      margin-bottom: 12px;
    }

    .suggestions-card {
      .ant-card-body {
        padding: 8px 12px;
      }
    }

    .recommendation-list {
      .ant-list-item {
        padding: 12px;

        .ant-list-item-meta {
          .ant-list-item-meta-title {
            font-size: 13px;
          }

          .ant-list-item-meta-description {
            font-size: 12px;
          }
        }
      }
    }

    .category-tabs {
      .ant-tabs-content {
        .ant-tabs-tabpane {
          padding-top: 12px;
        }
      }
    }

    .empty-state {
      padding: 30px 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .ai-content-recommendation-panel {
    .filters-row {
      .ant-row {
        .ant-col {
          margin-bottom: 8px;
        }
      }
    }

    .recommendation-list {
      .ant-list-item {
        .ant-list-item-action {
          margin-left: 8px;

          .ant-btn {
            margin-left: 2px;
            padding: 4px 8px;
          }
        }
      }
    }

    .category-tabs {
      .ant-tabs-tab {
        padding: 8px 12px;
        font-size: 12px;
      }
    }
  }
}

@media (max-width: 480px) {
  .ai-content-recommendation-panel {
    .filters-row {
      .ant-row {
        .ant-col {
          flex: 0 0 100%;
          max-width: 100%;
        }
      }
    }

    .recommendation-list {
      .ant-list-item {
        .ant-list-item-meta {
          .ant-list-item-meta-avatar {
            margin-right: 12px;

            .ant-avatar {
              width: 48px;
              height: 48px;
            }
          }

          .ant-list-item-meta-title {
            font-size: 13px;

            .ant-progress {
              display: none;
            }
          }
        }

        .ant-list-item-action {
          .ant-btn {
            font-size: 12px;
          }
        }
      }
    }

    .suggestions-card {
      .ant-space {
        .ant-tag {
          font-size: 11px;
          padding: 2px 6px;
        }
      }
    }
  }
}

// 动画效果
.ai-content-recommendation-panel {
  .recommendation-list {
    .ant-list-item {
      transition: background-color 0.2s ease;
    }
  }

  .suggestions-card {
    .ant-tag {
      transition: all 0.2s ease;
    }
  }

  .ant-btn {
    transition: all 0.2s ease;
  }

  .ant-tabs {
    .ant-tabs-tab {
      transition: all 0.2s ease;
    }
  }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
  .ai-content-recommendation-panel {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .ai-content-recommendation-panel {
    .ant-card {
      border: 2px solid #000;
    }

    .recommendation-list {
      .ant-list-item {
        border-bottom: 2px solid #000;
      }
    }

    .suggestions-card {
      border: 2px solid #000;

      .ant-tag {
        border: 1px solid #000;
      }
    }

    .ant-btn {
      border: 1px solid #000;
    }
  }
}

// 推荐特定样式
.ai-content-recommendation-panel {
  .confidence-high {
    color: #52c41a;
  }

  .confidence-medium {
    color: #faad14;
  }

  .confidence-low {
    color: #ff4d4f;
  }

  .popularity-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 4px;

    &.high {
      background-color: #52c41a;
    }

    &.medium {
      background-color: #faad14;
    }

    &.low {
      background-color: #ff4d4f;
    }
  }

  .recommendation-type-component {
    border-left: 4px solid #1890ff;
  }

  .recommendation-type-template {
    border-left: 4px solid #52c41a;
  }

  .recommendation-type-icon {
    border-left: 4px solid #faad14;
  }

  .recommendation-type-image {
    border-left: 4px solid #722ed1;
  }

  .recommendation-type-color_palette {
    border-left: 4px solid #eb2f96;
  }

  .bookmarked-item {
    background-color: #fff7e6;
    border: 1px solid #ffd591;
  }

  .recently-used-item {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
  }
}
