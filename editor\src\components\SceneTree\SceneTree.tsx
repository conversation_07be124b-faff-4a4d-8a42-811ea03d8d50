/**
 * 场景树组件
 * 用于显示场景中的实体层级结构
 */
import React, { useState, useCallback } from 'react';
import { Tree, Button, Tooltip, Space } from 'antd';
import {
  EyeOutlined,
  EyeInvisibleOutlined,
  LockOutlined,
  UnlockOutlined,
  AppstoreOutlined,
  BulbOutlined,
  CameraOutlined,
  SoundOutlined,
  CaretRightOutlined,
  CaretDownOutlined
} from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { selectEntity, updateEntity } from '../../store/scene/sceneSlice';
import './SceneTree.less';

/**
 * 实体类型图标映射
 */
const entityTypeIcons = {
  mesh: <AppstoreOutlined data-testid="mesh-icon" />,
  light: <BulbOutlined data-testid="light-icon" />,
  camera: <CameraOutlined data-testid="camera-icon" />,
  audio: <SoundOutlined data-testid="audio-icon" />,
  default: <AppstoreOutlined data-testid="default-icon" />
};

/**
 * 场景树组件属性
 */
interface SceneTreeProps {
  /** 选择实体回调 */
  onSelectEntity?: (entityId: string) => void;
  /** 展开/折叠节点回调 */
  onToggleNode?: (entityId: string) => void;
}

/**
 * 场景树组件
 */
export const SceneTree: React.FC<SceneTreeProps> = ({
  onSelectEntity,
  onToggleNode
}) => {
  const dispatch = useDispatch();
  const { entities, selectedEntityId } = useSelector((state: RootState) => ({
    entities: state.scene?.entities || [],
    selectedEntityId: state.scene?.selectedEntityId || null
  }));
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);

  /**
   * 转换实体数据为树节点数据
   */
  const convertToTreeData = useCallback((entities: any[]): any[] => {
    if (!entities) return [];

    return entities.map(entity => ({
      key: entity.id,
      title: (
        <div
          className={`scene-tree-node ${selectedEntityId === entity.id ? 'selected' : ''}`}
          data-testid={`entity-item-${entity.id}`}
        >
          <Space size="small">
            {/* 实体类型图标 */}
            {entityTypeIcons[entity.type as keyof typeof entityTypeIcons] || entityTypeIcons.default}
            
            {/* 实体名称 */}
            <span className="entity-name">{entity.name}</span>
            
            {/* 可见性切换 */}
            <Tooltip title={entity.visible ? "隐藏" : "显示"}>
              <Button
                type="text"
                size="small"
                icon={entity.visible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  dispatch(updateEntity({
                    id: entity.id,
                    changes: { visible: !entity.visible }
                  }));
                }}
              />
            </Tooltip>
            
            {/* 锁定切换 */}
            <Tooltip title={entity.locked ? "解锁" : "锁定"}>
              <Button
                type="text"
                size="small"
                icon={entity.locked ? <LockOutlined /> : <UnlockOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  dispatch(updateEntity({
                    id: entity.id,
                    changes: { locked: !entity.locked }
                  }));
                }}
              />
            </Tooltip>
          </Space>
        </div>
      ),
      children: entity.children ? convertToTreeData(entity.children) : [],
      isLeaf: !entity.children || entity.children.length === 0,
      switcherIcon: ({ expanded }: { expanded: boolean }) => (
        <Button
          type="text"
          size="small"
          icon={expanded ? <CaretDownOutlined /> : <CaretRightOutlined />}
          data-testid="expand-icon"
          onClick={(e) => {
            e.stopPropagation();
            if (onToggleNode) {
              onToggleNode(entity.id);
            }
          }}
        />
      )
    }));
  }, [selectedEntityId, dispatch, onToggleNode]);

  /**
   * 处理节点选择
   */
  const handleSelect = useCallback((selectedKeys: React.Key[]) => {
    if (selectedKeys.length > 0) {
      const entityId = selectedKeys[0] as string;
      dispatch(selectEntity(entityId));
      if (onSelectEntity) {
        onSelectEntity(entityId);
      }
    } else {
      dispatch(selectEntity(null));
    }
  }, [dispatch, onSelectEntity]);

  /**
   * 处理节点展开/折叠
   */
  const handleExpand = useCallback((expandedKeys: React.Key[]) => {
    setExpandedKeys(expandedKeys as string[]);
  }, []);

  // 如果没有实体，显示空状态
  if (!entities || entities.length === 0) {
    return (
      <div className="scene-tree-empty">
        <p>没有加载的场景</p>
      </div>
    );
  }

  const treeData = convertToTreeData(entities);

  return (
    <div className="scene-tree">
      {/* 场景名称 */}
      <div className="scene-header">
        <h4>场景层次</h4>
      </div>
      
      {/* 实体树 */}
      <Tree
        treeData={treeData}
        selectedKeys={selectedEntityId ? [selectedEntityId] : []}
        expandedKeys={expandedKeys}
        onSelect={handleSelect}
        onExpand={handleExpand}
        showIcon={false}
        blockNode
        className="entity-tree"
      />
    </div>
  );
};

export default SceneTree;
