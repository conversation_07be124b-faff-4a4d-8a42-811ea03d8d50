/**
 * 实时协作覆盖层样式
 */

.realtime-collaboration-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;

  // 用户光标
  .collaboration-cursor {
    position: absolute;
    pointer-events: none;
    z-index: 1001;
    animation: cursorFadeIn 0.2s ease-in-out;

    .cursor-pointer {
      width: 4px;
      height: 4px;
      border-radius: 50%;
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: inherit;
        opacity: 0.3;
        animation: cursorPulse 2s infinite;
      }
    }

    .cursor-label {
      position: absolute;
      top: 8px;
      left: 8px;
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 2px 6px;
      border-radius: 12px;
      color: white;
      font-size: 11px;
      font-weight: 500;
      white-space: nowrap;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      max-width: 120px;
      overflow: hidden;

      .cursor-name {
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .typing-indicator {
        animation: typingBlink 1s infinite;
      }
    }
  }

  // 用户选择区域
  .collaboration-selection {
    position: absolute;
    border: 2px solid;
    border-radius: 4px;
    pointer-events: none;
    animation: selectionFadeIn 0.3s ease-in-out;
    
    &::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      border: 1px solid;
      border-color: inherit;
      border-radius: 6px;
      opacity: 0.5;
      animation: selectionPulse 2s infinite;
    }
  }

  // 编辑区域
  .collaboration-editing-zone {
    position: absolute;
    border: 2px dashed;
    border-radius: 4px;
    pointer-events: none;
    animation: editingZoneFadeIn 0.3s ease-in-out;

    .zone-indicator {
      position: absolute;
      top: -12px;
      left: -2px;
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 2px 6px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      font-size: 12px;
      color: #666;
    }
  }

  // 在线用户面板
  .online-users-panel {
    position: fixed;
    top: 80px;
    right: 16px;
    width: 60px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    pointer-events: auto;
    z-index: 1002;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      width: 200px;
      
      .panel-header {
        opacity: 1;
        transform: translateX(0);
      }
    }

    .panel-header {
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
      background: #fafafa;
      opacity: 0;
      transform: translateX(20px);
      transition: all 0.3s ease;
      white-space: nowrap;
    }

    .users-list {
      padding: 8px;
      max-height: 300px;
      overflow-y: auto;

      .user-item {
        position: relative;
        margin-bottom: 8px;
        cursor: pointer;
        transition: transform 0.2s ease;

        &:hover {
          transform: scale(1.05);
        }

        &:last-child {
          margin-bottom: 0;
        }

        .typing-indicator-badge {
          position: absolute;
          top: -4px;
          right: -4px;
          width: 16px;
          height: 16px;
          background: #52c41a;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 8px;
          animation: typingBlink 1s infinite;
        }

        .editing-indicator-badge {
          position: absolute;
          bottom: -4px;
          right: -4px;
          width: 16px;
          height: 16px;
          background: #1890ff;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 8px;
        }
      }
    }
  }
}

// 动画定义
@keyframes cursorFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes cursorPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.1;
  }
}

@keyframes selectionFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes selectionPulse {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes editingZoneFadeIn {
  from {
    opacity: 0;
    transform: scale(0.98);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes typingBlink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

// 深色主题
.dark-theme {
  .realtime-collaboration-overlay {
    .collaboration-cursor {
      .cursor-label {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      }
    }

    .collaboration-editing-zone {
      .zone-indicator {
        background: #2d2d2d;
        color: #cccccc;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      }
    }

    .online-users-panel {
      background: #2d2d2d;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);

      .panel-header {
        background: #404040;
        border-bottom-color: #555555;
        color: #ffffff;
      }
    }
  }
}

// 紧凑模式
.compact-theme {
  .realtime-collaboration-overlay {
    .collaboration-cursor {
      .cursor-label {
        padding: 1px 4px;
        font-size: 10px;
        border-radius: 8px;
      }
    }

    .online-users-panel {
      width: 48px;
      top: 60px;

      &:hover {
        width: 160px;
      }

      .panel-header {
        padding: 8px 12px;
        font-size: 12px;
      }

      .users-list {
        padding: 6px;

        .user-item {
          margin-bottom: 6px;

          .ant-avatar {
            width: 28px !important;
            height: 28px !important;
            font-size: 12px;
          }

          .typing-indicator-badge,
          .editing-indicator-badge {
            width: 14px;
            height: 14px;
            font-size: 7px;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .realtime-collaboration-overlay {
    .online-users-panel {
      width: 48px;
      top: 60px;
      right: 8px;

      &:hover {
        width: 140px;
      }

      .panel-header {
        padding: 8px 12px;
        font-size: 12px;
      }

      .users-list {
        .user-item {
          .ant-avatar {
            width: 28px !important;
            height: 28px !important;
          }
        }
      }
    }

    .collaboration-cursor {
      .cursor-label {
        font-size: 10px;
        padding: 1px 4px;
        max-width: 80px;
      }
    }
  }
}

@media (max-width: 480px) {
  .realtime-collaboration-overlay {
    .online-users-panel {
      width: 40px;
      
      &:hover {
        width: 120px;
      }

      .users-list {
        .user-item {
          .ant-avatar {
            width: 24px !important;
            height: 24px !important;
            font-size: 10px;
          }

          .typing-indicator-badge,
          .editing-indicator-badge {
            width: 12px;
            height: 12px;
            font-size: 6px;
          }
        }
      }
    }
  }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
  .realtime-collaboration-overlay {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .realtime-collaboration-overlay {
    .collaboration-cursor {
      .cursor-pointer {
        border: 2px solid #000;
      }

      .cursor-label {
        border: 1px solid #000;
      }
    }

    .collaboration-selection {
      border-width: 3px;
    }

    .collaboration-editing-zone {
      border-width: 3px;
    }

    .online-users-panel {
      border: 2px solid #000;
    }
  }
}
