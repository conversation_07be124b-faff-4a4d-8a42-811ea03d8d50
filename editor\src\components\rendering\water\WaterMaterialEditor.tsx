/**
 * 水体材质编辑器组件
 * 用于编辑水体材质属性
 */
import React, { useState, useEffect, useRef } from 'react';
import { Form, InputNumber, Select, Switch, Slider, Button, Card, Tabs, Row, Col, Tooltip, Space, message } from 'antd';
import {
  DropboxOutlined,
  BgColorsOutlined,
  HighlightOutlined,
  EyeOutlined,
  AppstoreOutlined,
  SyncOutlined,
  SaveOutlined,
  EyeInvisibleOutlined,
  UploadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../../store';
import Vector2Input from '../../common/Vector2Input';
import ColorPicker from '../../common/ColorPicker';
import TextureSelector from '../../common/TextureSelector';
import WaterMaterialPresetSelector from './WaterMaterialPresetSelector';
import {
  WaterBodyType,
  updateWaterMaterial
} from '../../../store/rendering/waterMaterialSlice';

const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 水体材质编辑器属性
 */
interface WaterMaterialEditorProps {
  /** 实体ID */
  entityId: string;
}

/**
 * 水体材质编辑器组件
 */
const WaterMaterialEditor: React.FC<WaterMaterialEditorProps> = ({ entityId }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const dispatch = useDispatch<AppDispatch>();
  const previewCanvasRef = useRef<HTMLCanvasElement>(null);

  // 状态
  const [activeTab, setActiveTab] = useState<string>('basic');
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isPreviewVisible, setIsPreviewVisible] = useState<boolean>(true);
  const [isPresetSelectorVisible, setIsPresetSelectorVisible] = useState<boolean>(false);
  const [previewMode, setPreviewMode] = useState<'static' | 'realtime'>('static');
  const [previewLoading, setPreviewLoading] = useState<boolean>(false);

  // 从Redux获取水体材质数据
  const waterMaterial = useSelector((state: RootState) =>
    state.waterMaterial.waterMaterials.find(wm => wm.entityId === entityId)
  );

  // 初始化表单
  useEffect(() => {
    if (waterMaterial) {
      form.setFieldsValue({
        // 基本属性
        type: waterMaterial.type,
        color: waterMaterial.color,
        opacity: waterMaterial.opacity,
        reflectivity: waterMaterial.reflectivity,
        refractionRatio: waterMaterial.refractionRatio,

        // 波动属性
        waveStrength: waterMaterial.waveStrength,
        waveSpeed: waterMaterial.waveSpeed,
        waveScale: waterMaterial.waveScale,
        waveDirection: waterMaterial.waveDirection,

        // 深度属性
        depth: waterMaterial.depth,
        depthColor: waterMaterial.depthColor,
        shallowColor: waterMaterial.shallowColor,

        // 特效属性
        enableCaustics: waterMaterial.enableCaustics,
        causticsIntensity: waterMaterial.causticsIntensity,
        enableFoam: waterMaterial.enableFoam,
        foamIntensity: waterMaterial.foamIntensity,
        enableUnderwaterFog: waterMaterial.enableUnderwaterFog,
        underwaterFogDensity: waterMaterial.underwaterFogDensity,
        enableUnderwaterDistortion: waterMaterial.enableUnderwaterDistortion,
        underwaterDistortionStrength: waterMaterial.underwaterDistortionStrength,

        // 贴图属性
        normalMap: waterMaterial.normalMap,
        reflectionMap: waterMaterial.reflectionMap,
        refractionMap: waterMaterial.refractionMap,
        depthMap: waterMaterial.depthMap,
        causticsMap: waterMaterial.causticsMap,
        foamMap: waterMaterial.foamMap});
    }
  }, [waterMaterial, form]);

  // 处理表单变更
  const handleValuesChange = (changedValues: any, allValues: any) => {
    // 更新Redux状态
    if (entityId) {
      dispatch(updateWaterMaterial({ entityId, properties: changedValues }));

      // 如果是实时预览模式，则更新预览
      if (previewMode === 'realtime') {
        updatePreview(allValues);
      }
    }
  };

  // 处理保存
  const handleSave = () => {
    form.validateFields().then(values => {
      // 保存到Redux
      dispatch(updateWaterMaterial({ entityId, properties: values }));
      setIsEditing(false);
      message.success(t('editor.rendering.water.saveSuccess'));
    });
  };

  // 处理取消
  const handleCancel = () => {
    // 重置表单
    if (waterMaterial) {
      form.setFieldsValue({
        // 重置为原始值
        type: waterMaterial.type,
        color: waterMaterial.color,
        opacity: waterMaterial.opacity,
        reflectivity: waterMaterial.reflectivity,
        refractionRatio: waterMaterial.refractionRatio,
        // ... 其他属性
      });
    }
    setIsEditing(false);
  };

  // 更新预览
  const updatePreview = (values: any = null) => {
    if (!previewCanvasRef.current) return;

    setPreviewLoading(true);

    // 使用当前表单值或传入的值
    const previewValues = values || form.getFieldsValue();

    // 这里应该调用引擎的预览渲染功能
    // 实际实现应该通过Redux action或服务来调用

    // 模拟预览渲染
    setTimeout(() => {
      // 在实际实现中，这里应该渲染水体预览到canvas
      const canvas = previewCanvasRef.current;
      if (canvas) {
        const ctx = canvas.getContext('2d');
        if (ctx) {
          // 清除画布
          ctx.clearRect(0, 0, canvas.width, canvas.height);

          // 绘制简单的水体效果
          const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
          gradient.addColorStop(0, previewValues.shallowColor || '#66ccff');
          gradient.addColorStop(1, previewValues.depthColor || '#0055aa');

          ctx.fillStyle = gradient;
          ctx.fillRect(0, 0, canvas.width, canvas.height);

          // 绘制波浪
          const waveStrength = previewValues.waveStrength || 0.1;
          const waveScale = previewValues.waveScale || 4.0;

          ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
          ctx.lineWidth = 2;

          for (let i = 0; i < 3; i++) {
            ctx.beginPath();
            for (let x = 0; x < canvas.width; x += 5) {
              const y = Math.sin(x / (waveScale * 10) + i * 0.5) * (waveStrength * 50) + canvas.height / 2;
              if (x === 0) {
                ctx.moveTo(x, y);
              } else {
                ctx.lineTo(x, y);
              }
            }
            ctx.stroke();
          }
        }
      }

      setPreviewLoading(false);
    }, 300);
  };

  // 切换预览模式
  const togglePreviewMode = () => {
    const newMode = previewMode === 'static' ? 'realtime' : 'static';
    setPreviewMode(newMode);

    if (newMode === 'realtime') {
      updatePreview();
    }

    message.info(
      newMode === 'realtime'
        ? t('editor.rendering.water.realtimePreviewEnabled')
        : t('editor.rendering.water.realtimePreviewDisabled')
    );
  };

  // 打开预设选择器
  const openPresetSelector = () => {
    setIsPresetSelectorVisible(true);
  };

  // 关闭预设选择器
  const closePresetSelector = () => {
    setIsPresetSelectorVisible(false);
  };

  // 应用预设
  const handlePresetSelect = (presetId: string) => {
    // 这里应该dispatch一个action来应用预设
    console.log(`Applying water material preset: ${presetId} for entity: ${entityId}`);
    // TODO: 实现预设应用逻辑
    // dispatch(applyWaterMaterialPreset({ entityId, presetId }));

    // 关闭预设选择器
    setIsPresetSelectorVisible(false);

    // 更新预览
    if (previewMode === 'realtime') {
      updatePreview();
    }

    message.success(t('editor.rendering.water.presetApplied'));
  };

  // 渲染基本属性标签页
  const renderBasicTab = () => {
    return (
      <div className="basic-tab">
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleValuesChange}
          disabled={!isEditing}
        >
          <Form.Item
            name="type"
            label={t('editor.rendering.water.type')}
            tooltip={t('editor.rendering.water.typeTooltip')}
          >
            <Select>
              <Option value={WaterBodyType.OCEAN}>{t('editor.rendering.water.typeOcean')}</Option>
              <Option value={WaterBodyType.LAKE}>{t('editor.rendering.water.typeLake')}</Option>
              <Option value={WaterBodyType.RIVER}>{t('editor.rendering.water.typeRiver')}</Option>
              <Option value={WaterBodyType.POOL}>{t('editor.rendering.water.typePool')}</Option>
              <Option value={WaterBodyType.PUDDLE}>{t('editor.rendering.water.typePuddle')}</Option>
              <Option value={WaterBodyType.UNDERGROUND}>{t('editor.rendering.water.typeUnderground')}</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="color"
            label={t('editor.rendering.water.color')}
            tooltip={t('editor.rendering.water.colorTooltip')}
          >
            <ColorPicker />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="opacity"
                label={t('editor.rendering.water.opacity')}
                tooltip={t('editor.rendering.water.opacityTooltip')}
              >
                <Slider min={0} max={1} step={0.01} />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="reflectivity"
                label={t('editor.rendering.water.reflectivity')}
                tooltip={t('editor.rendering.water.reflectivityTooltip')}
              >
                <Slider min={0} max={1} step={0.01} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="refractionRatio"
            label={t('editor.rendering.water.refractionRatio')}
            tooltip={t('editor.rendering.water.refractionRatioTooltip')}
          >
            <Slider min={0} max={1} step={0.01} />
          </Form.Item>
        </Form>
      </div>
    );
  };

  // 渲染波动属性标签页
  const renderWavesTab = () => {
    return (
      <div className="waves-tab">
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleValuesChange}
          disabled={!isEditing}
        >
          <Form.Item
            name="waveStrength"
            label={t('editor.rendering.water.waveStrength')}
            tooltip={t('editor.rendering.water.waveStrengthTooltip')}
          >
            <Slider min={0} max={1} step={0.01} />
          </Form.Item>

          <Form.Item
            name="waveSpeed"
            label={t('editor.rendering.water.waveSpeed')}
            tooltip={t('editor.rendering.water.waveSpeedTooltip')}
          >
            <Slider min={0} max={2} step={0.01} />
          </Form.Item>

          <Form.Item
            name="waveScale"
            label={t('editor.rendering.water.waveScale')}
            tooltip={t('editor.rendering.water.waveScaleTooltip')}
          >
            <Slider min={0.1} max={10} step={0.1} />
          </Form.Item>

          <Form.Item
            name="waveDirection"
            label={t('editor.rendering.water.waveDirection')}
            tooltip={t('editor.rendering.water.waveDirectionTooltip')}
          >
            <Vector2Input />
          </Form.Item>
        </Form>
      </div>
    );
  };

  // 渲染深度属性标签页
  const renderDepthTab = () => {
    return (
      <div className="depth-tab">
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleValuesChange}
          disabled={!isEditing}
        >
          <Form.Item
            name="depth"
            label={t('editor.rendering.water.depth')}
            tooltip={t('editor.rendering.water.depthTooltip')}
          >
            <InputNumber min={0.1} max={100} step={0.1} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="depthColor"
            label={t('editor.rendering.water.depthColor')}
            tooltip={t('editor.rendering.water.depthColorTooltip')}
          >
            <ColorPicker />
          </Form.Item>

          <Form.Item
            name="shallowColor"
            label={t('editor.rendering.water.shallowColor')}
            tooltip={t('editor.rendering.water.shallowColorTooltip')}
          >
            <ColorPicker />
          </Form.Item>
        </Form>
      </div>
    );
  };

  // 渲染特效属性标签页
  const renderEffectsTab = () => {
    return (
      <div className="effects-tab">
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleValuesChange}
          disabled={!isEditing}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enableCaustics"
                label={t('editor.rendering.water.enableCaustics')}
                valuePropName="checked"
                tooltip={t('editor.rendering.water.enableCausticsTooltip')}
              >
                <Switch />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="causticsIntensity"
                label={t('editor.rendering.water.causticsIntensity')}
                tooltip={t('editor.rendering.water.causticsIntensityTooltip')}
              >
                <Slider min={0} max={1} step={0.01} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enableFoam"
                label={t('editor.rendering.water.enableFoam')}
                valuePropName="checked"
                tooltip={t('editor.rendering.water.enableFoamTooltip')}
              >
                <Switch />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="foamIntensity"
                label={t('editor.rendering.water.foamIntensity')}
                tooltip={t('editor.rendering.water.foamIntensityTooltip')}
              >
                <Slider min={0} max={1} step={0.01} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enableUnderwaterFog"
                label={t('editor.rendering.water.enableUnderwaterFog')}
                valuePropName="checked"
                tooltip={t('editor.rendering.water.enableUnderwaterFogTooltip')}
              >
                <Switch />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="underwaterFogDensity"
                label={t('editor.rendering.water.underwaterFogDensity')}
                tooltip={t('editor.rendering.water.underwaterFogDensityTooltip')}
              >
                <Slider min={0} max={1} step={0.01} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enableUnderwaterDistortion"
                label={t('editor.rendering.water.enableUnderwaterDistortion')}
                valuePropName="checked"
                tooltip={t('editor.rendering.water.enableUnderwaterDistortionTooltip')}
              >
                <Switch />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="underwaterDistortionStrength"
                label={t('editor.rendering.water.underwaterDistortionStrength')}
                tooltip={t('editor.rendering.water.underwaterDistortionStrengthTooltip')}
              >
                <Slider min={0} max={1} step={0.01} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    );
  };

  // 渲染贴图属性标签页
  const renderTexturesTab = () => {
    return (
      <div className="textures-tab">
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleValuesChange}
          disabled={!isEditing}
        >
          <Form.Item
            name="normalMap"
            label={t('editor.rendering.water.normalMap')}
            tooltip={t('editor.rendering.water.normalMapTooltip')}
          >
            <TextureSelector />
          </Form.Item>

          <Form.Item
            name="reflectionMap"
            label={t('editor.rendering.water.reflectionMap')}
            tooltip={t('editor.rendering.water.reflectionMapTooltip')}
          >
            <TextureSelector />
          </Form.Item>

          <Form.Item
            name="refractionMap"
            label={t('editor.rendering.water.refractionMap')}
            tooltip={t('editor.rendering.water.refractionMapTooltip')}
          >
            <TextureSelector />
          </Form.Item>

          <Form.Item
            name="depthMap"
            label={t('editor.rendering.water.depthMap')}
            tooltip={t('editor.rendering.water.depthMapTooltip')}
          >
            <TextureSelector />
          </Form.Item>

          <Form.Item
            name="causticsMap"
            label={t('editor.rendering.water.causticsMap')}
            tooltip={t('editor.rendering.water.causticsMapTooltip')}
          >
            <TextureSelector />
          </Form.Item>

          <Form.Item
            name="foamMap"
            label={t('editor.rendering.water.foamMap')}
            tooltip={t('editor.rendering.water.foamMapTooltip')}
          >
            <TextureSelector />
          </Form.Item>
        </Form>
      </div>
    );
  };

  // 初始化预览
  useEffect(() => {
    if (isPreviewVisible && previewCanvasRef.current) {
      updatePreview();
    }
  }, [isPreviewVisible]); // updatePreview 函数在组件内部定义，不需要添加到依赖项

  return (
    <div className="component-editor water-material-editor">
      <Card
        title={
          <Space>
            <DropboxOutlined />
            {t('editor.rendering.water.title')}
          </Space>
        }
        extra={
          <Space>
            <Tooltip title={t('editor.rendering.water.presetTooltip')}>
              <Button
                icon={<AppstoreOutlined />}
                onClick={openPresetSelector}
              >
                {t('editor.rendering.water.presets')}
              </Button>
            </Tooltip>

            <Tooltip title={t('editor.rendering.water.previewModeTooltip')}>
              <Button
                icon={previewMode === 'realtime' ? <SyncOutlined spin /> : <SyncOutlined />}
                onClick={togglePreviewMode}
              >
                {previewMode === 'realtime'
                  ? t('editor.rendering.water.realtimePreview')
                  : t('editor.rendering.water.staticPreview')}
              </Button>
            </Tooltip>

            <Tooltip title={t('editor.rendering.water.togglePreviewTooltip')}>
              <Button
                icon={isPreviewVisible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                onClick={() => setIsPreviewVisible(!isPreviewVisible)}
              />
            </Tooltip>

            {isEditing ? (
              <>
                <Button onClick={handleCancel}>{t('editor.common.cancel')}</Button>
                <Button type="primary" icon={<SaveOutlined />} onClick={handleSave}>{t('editor.common.save')}</Button>
              </>
            ) : (
              <Button type="primary" onClick={() => setIsEditing(true)}>{t('editor.common.edit')}</Button>
            )}
          </Space>
        }
      >
        <Row gutter={16}>
          <Col span={isPreviewVisible ? 16 : 24}>
            <Tabs activeKey={activeTab} onChange={setActiveTab}>
              <TabPane
                tab={
                  <span>
                    <BgColorsOutlined />
                    {t('editor.rendering.water.basicTab')}
                  </span>
                }
                key="basic"
              >
                {renderBasicTab()}
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <DropboxOutlined />
                    {t('editor.rendering.water.wavesTab')}
                  </span>
                }
                key="waves"
              >
                {renderWavesTab()}
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <HighlightOutlined />
                    {t('editor.rendering.water.depthTab')}
                  </span>
                }
                key="depth"
              >
                {renderDepthTab()}
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <EyeOutlined />
                    {t('editor.rendering.water.effectsTab')}
                  </span>
                }
                key="effects"
              >
                {renderEffectsTab()}
              </TabPane>

          <TabPane
                tab={
                  <span>
                    <UploadOutlined />
                    {t('editor.rendering.water.texturesTab')}
                  </span>
                }
                key="textures"
              >
                {renderTexturesTab()}
              </TabPane>
            </Tabs>
          </Col>

          {isPreviewVisible && (
            <Col span={8}>
              <Card
                title={t('editor.rendering.water.preview')}
                className="preview-card"
                styles={{ body: { padding: 0 } }}
              >
                <div className="preview-container" style={{ position: 'relative' }}>
                  <canvas
                    ref={previewCanvasRef}
                    width={300}
                    height={200}
                    style={{ width: '100%', height: 'auto' }}
                  />
                  {previewLoading && (
                    <div
                      style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        background: 'rgba(0, 0, 0, 0.5)'
                      }}
                    >
                      <SyncOutlined spin style={{ fontSize: 24, color: '#fff' }} />
                    </div>
                  )}
                </div>
                <div className="preview-controls" style={{ padding: '12px' }}>
                  <Button
                    type="primary"
                    block
                    onClick={() => updatePreview()}
                    icon={<SyncOutlined />}
                  >
                    {t('editor.rendering.water.updatePreview')}
                  </Button>
                </div>
              </Card>
            </Col>
          )}
        </Row>
      </Card>

      {/* 水体材质预设选择器 */}
      <WaterMaterialPresetSelector
        visible={isPresetSelectorVisible}
        onClose={closePresetSelector}
        onSelect={handlePresetSelect}
        entityId={entityId}
      />
    </div>
  );
};

export default WaterMaterialEditor;
