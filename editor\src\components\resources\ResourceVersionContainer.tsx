/**
 * 资源版本容器组件
 * 用于集成资源版本历史面板和版本比较面板
 */
import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  selectResourceVersions,
  selectCurrentVersionId,
  selectShowVersionPanel,
  selectIsRollingBack,
  selectComparisonResult,
  selectShowComparePanel,
  selectCurrentResourceId,
  setShowResourceVersionPanel,
  setShowComparePanel
} from '../../store/resources/resourceVersionSlice';
import { resourceVersionService } from '../../services/ResourceVersionService';
import ResourceVersionHistoryPanel from './ResourceVersionHistoryPanel';
import ResourceVersionComparePanel from './ResourceVersionComparePanel';

// 组件属性接口
interface ResourceVersionContainerProps {
  resourceId: string;
  resourceName: string;
}

/**
 * 资源版本容器组件
 */
const ResourceVersionContainer: React.FC<ResourceVersionContainerProps> = ({
  resourceId,
  resourceName
}) => {
  const dispatch = useDispatch();
  
  // 从Redux获取状态
  const versions = useSelector(selectResourceVersions);
  const currentVersionId = useSelector(selectCurrentVersionId);
  const showVersionPanel = useSelector(selectShowVersionPanel);
  const isRollingBack = useSelector(selectIsRollingBack);
  const comparisonResult = useSelector(selectComparisonResult);
  const showComparePanel = useSelector(selectShowComparePanel);
  const currentResourceId = useSelector(selectCurrentResourceId);
  
  // 初始化资源版本服务
  useEffect(() => {
    resourceVersionService.initialize();
  }, []);
  
  // 当资源ID变化时，设置当前资源
  useEffect(() => {
    if (resourceId && resourceId !== currentResourceId) {
      resourceVersionService.setCurrentResource(resourceId);
    }
  }, [resourceId, currentResourceId]);
  
  // 处理关闭版本面板
  const handleCloseVersionPanel = () => {
    dispatch(setShowResourceVersionPanel(false));
  };
  
  // 处理关闭比较面板
  const handleCloseComparePanel = () => {
    dispatch(setShowComparePanel(false));
  };
  
  // 处理创建版本
  const handleCreateVersion = (description: string, tags: string[]) => {
    resourceVersionService.createVersion(
      resourceId,
      '', // URL，实际应用中应从资源管理器获取
      'unknown', // 资源类型，实际应用中应从资源管理器获取
      '', // 哈希，实际应用中应从资源管理器获取
      0, // 大小，实际应用中应从资源管理器获取
      {}, // 元数据，实际应用中应从资源管理器获取
      description,
      tags
    );
  };
  
  // 处理回滚版本
  const handleRollbackVersion = (versionId: string) => {
    resourceVersionService.rollbackToVersion(resourceId, versionId);
  };
  
  // 处理删除版本
  const handleDeleteVersion = (versionId: string) => {
    resourceVersionService.deleteVersion(resourceId, versionId);
  };
  
  // 处理比较版本
  const handleCompareVersions = (versionId1: string, versionId2: string) => {
    resourceVersionService.compareVersions(resourceId, versionId1, versionId2);
  };
  
  // 处理添加标签
  const handleAddTag = (versionId: string, tag: string) => {
    resourceVersionService.addVersionTag(resourceId, versionId, tag);
  };
  
  // 处理移除标签
  const handleRemoveTag = (versionId: string, tag: string) => {
    resourceVersionService.removeVersionTag(resourceId, versionId, tag);
  };
  
  // 处理更新描述
  const handleUpdateDescription = (versionId: string, description: string) => {
    resourceVersionService.updateVersionDescription(resourceId, versionId, description);
  };
  
  return (
    <>
      {showVersionPanel && (
        <ResourceVersionHistoryPanel
          resourceName={resourceName}
          versions={versions}
          currentVersionId={currentVersionId}
          onClose={handleCloseVersionPanel}
          onCreateVersion={handleCreateVersion}
          onRollbackVersion={handleRollbackVersion}
          onDeleteVersion={handleDeleteVersion}
          onCompareVersions={handleCompareVersions}
          onAddTag={handleAddTag}
          onRemoveTag={handleRemoveTag}
          onUpdateDescription={handleUpdateDescription}
          isRollingBack={isRollingBack}
        />
      )}
      
      {showComparePanel && comparisonResult && (
        <ResourceVersionComparePanel
          resourceName={resourceName}
          comparisonResult={comparisonResult}
          onClose={handleCloseComparePanel}
          onRollbackVersion={handleRollbackVersion}
        />
      )}
    </>
  );
};

export default ResourceVersionContainer;
