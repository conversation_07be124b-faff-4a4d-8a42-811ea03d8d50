.scene-preview-card {
  .ant-card-head {
    padding: 8px 12px;
    min-height: 40px;

    .ant-card-head-title {
      font-size: 14px;
      font-weight: 500;
    }
  }

  .ant-card-body {
    padding: 12px;
  }

  .preview-container {
    position: relative;
  }

  .preview-placeholder {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fafafa;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    color: #999;
  }

  .preview-canvas {
    width: 100%;
    height: auto;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    display: block;
    background: #f5f5f5;
    cursor: grab;

    &:active {
      cursor: grabbing;
    }
  }

  .preview-controls {
    margin-top: 12px;
    background: #fafafa;

    .ant-card-body {
      padding: 8px 12px;
    }

    .ant-slider {
      margin: 4px 0 8px 0;
    }

    .ant-switch {
      margin-left: 8px;
    }

    .ant-space {
      width: 100%;

      &.ant-space-vertical {
        .ant-space-item {
          width: 100%;
        }
      }
    }
  }

  .preview-stats {
    margin-top: 8px;
    background: #f0f0f0;

    .ant-card-body {
      padding: 8px 12px;
      font-size: 12px;
    }

    .ant-space {
      .ant-space-item {
        margin-bottom: 2px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .ant-spin-nested-loading {
    .ant-spin-container {
      position: relative;
    }

    .ant-spin {
      max-height: none;
    }
  }

  // 控制按钮样式
  .ant-btn {
    &.ant-btn-sm {
      height: 24px;
      padding: 0 8px;
      font-size: 12px;
    }
  }

  // 滑块样式
  .ant-slider {
    .ant-slider-track {
      background: #1890ff;
    }

    .ant-slider-handle {
      border-color: #1890ff;

      &:hover,
      &:focus {
        border-color: #40a9ff;
        box-shadow: 0 0 0 5px rgba(24, 144, 255, 0.12);
      }
    }
  }

  // 开关样式
  .ant-switch {
    &.ant-switch-checked {
      background: #1890ff;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .preview-canvas {
      height: 200px;
    }

    .preview-controls {
      .ant-space {
        flex-direction: column;
        align-items: flex-start;

        .ant-space-item {
          width: 100%;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .ant-card-extra {
      .ant-space {
        flex-direction: column;
        gap: 4px;

        .ant-btn {
          width: 100%;
        }
      }
    }
  }

  // 暗色主题支持
  .dark-theme & {
    .preview-placeholder {
      background: #262626;
      border-color: #434343;
      color: #999;
    }

    .preview-canvas {
      background: #1f1f1f;
      border-color: #434343;
    }

    .preview-controls {
      background: #262626;

      .ant-card-body {
        background: transparent;
      }
    }

    .preview-stats {
      background: #303030;

      .ant-card-body {
        background: transparent;
        color: #d9d9d9;
      }
    }

    .ant-slider {
      .ant-slider-rail {
        background: #434343;
      }

      .ant-slider-track {
        background: #1890ff;
      }

      .ant-slider-handle {
        background: #1f1f1f;
        border-color: #1890ff;
      }
    }
  }

  // 加载状态样式
  .ant-spin-nested-loading {
    .ant-spin-blur {
      opacity: 0.5;
      pointer-events: none;
    }
  }

  // 全屏模式样式
  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    background: white;

    .preview-canvas {
      width: 100vw;
      height: calc(100vh - 120px);
    }

    .preview-controls {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      margin: 0;
      border-radius: 0;
      border-top: 1px solid #d9d9d9;
    }
  }

  // 动画效果
  .preview-canvas {
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .preview-controls {
    transition: all 0.3s ease;
  }

  .preview-stats {
    transition: all 0.3s ease;
  }

  // 工具提示样式
  .ant-tooltip {
    .ant-tooltip-inner {
      font-size: 12px;
    }
  }
}
