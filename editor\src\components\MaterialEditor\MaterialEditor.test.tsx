/**
 * 材质编辑器组件测试
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../i18n';
import MaterialEditor from './index';
import { MaterialType } from '../../store/materials/materialsSlice';

// Mock 材质服务
jest.mock('../../services/materialService', () => ({
  materialService: {
    getMaterial: jest.fn().mockResolvedValue(null),
    createMaterial: jest.fn().mockResolvedValue({ id: 'new-material' }),
    updateMaterial: jest.fn().mockResolvedValue({}),
    uploadTexture: jest.fn().mockResolvedValue('mock-url'),
  }
}));

// Mock dl-engine
jest.mock('../../libs/dl-engine.mjs', () => ({
  Engine: jest.fn().mockImplementation(() => ({
    createScene: jest.fn(),
    createCamera: jest.fn(),
    createLight: jest.fn(),
    createGeometry: jest.fn(),
    createMaterial: jest.fn(),
    createMesh: jest.fn(),
    start: jest.fn(),
    dispose: jest.fn(),
    getTextureLoader: jest.fn(() => ({
      load: jest.fn()
    }))
  }))
}));

// 创建测试store
const createTestStore = () => {
  return configureStore({
    reducer: {
      materials: (state = { materials: [] }) => state
    }
  });
};

// 测试组件包装器
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const store = createTestStore();
  
  return (
    <Provider store={store}>
      <I18nextProvider i18n={i18n}>
        {children}
      </I18nextProvider>
    </Provider>
  );
};

describe('MaterialEditor', () => {
  beforeEach(() => {
    // 清除所有mock
    jest.clearAllMocks();
    
    // Mock canvas context
    HTMLCanvasElement.prototype.getContext = jest.fn(() => ({
      fillRect: jest.fn(),
      clearRect: jest.fn(),
      getImageData: jest.fn(() => ({ data: new Array(4) })),
      putImageData: jest.fn(),
      createImageData: jest.fn(() => ({ data: new Array(4) })),
      setTransform: jest.fn(),
      drawImage: jest.fn(),
      save: jest.fn(),
      fillText: jest.fn(),
      restore: jest.fn(),
      beginPath: jest.fn(),
      moveTo: jest.fn(),
      lineTo: jest.fn(),
      closePath: jest.fn(),
      stroke: jest.fn(),
      translate: jest.fn(),
      scale: jest.fn(),
      rotate: jest.fn(),
      arc: jest.fn(),
      fill: jest.fn(),
      measureText: jest.fn(() => ({ width: 0 })),
      transform: jest.fn(),
      rect: jest.fn(),
      clip: jest.fn(),
    })) as any;
  });

  test('渲染材质编辑器组件', () => {
    render(
      <TestWrapper>
        <MaterialEditor />
      </TestWrapper>
    );

    expect(screen.getByText(/材质编辑器|Material Editor/i)).toBeInTheDocument();
  });

  test('显示预览画布', () => {
    render(
      <TestWrapper>
        <MaterialEditor />
      </TestWrapper>
    );

    const canvas = screen.getByRole('img', { hidden: true }) || 
                   document.querySelector('canvas');
    expect(canvas).toBeInTheDocument();
  });

  test('显示材质类型选择器', () => {
    render(
      <TestWrapper>
        <MaterialEditor />
      </TestWrapper>
    );

    const typeSelect = screen.getByLabelText(/材质类型|Material Type/i);
    expect(typeSelect).toBeInTheDocument();
  });

  test('显示基础属性表单', () => {
    render(
      <TestWrapper>
        <MaterialEditor />
      </TestWrapper>
    );

    expect(screen.getByLabelText(/名称|Name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/颜色|Color/i)).toBeInTheDocument();
  });

  test('切换预览几何体', async () => {
    render(
      <TestWrapper>
        <MaterialEditor />
      </TestWrapper>
    );

    // 查找几何体切换按钮
    const sphereButton = screen.getByText('🌐') || screen.getByTitle(/球体|Sphere/i);
    const cubeButton = screen.getByText('📦') || screen.getByTitle(/立方体|Cube/i);

    if (sphereButton) {
      fireEvent.click(sphereButton);
    }
    
    if (cubeButton) {
      fireEvent.click(cubeButton);
    }

    // 验证预览模式已更改
    await waitFor(() => {
      expect(cubeButton).toHaveClass('ant-btn-primary');
    });
  });

  test('处理表单值变化', async () => {
    render(
      <TestWrapper>
        <MaterialEditor />
      </TestWrapper>
    );

    const nameInput = screen.getByLabelText(/名称|Name/i);
    fireEvent.change(nameInput, { target: { value: '测试材质' } });

    await waitFor(() => {
      expect(nameInput).toHaveValue('测试材质');
    });
  });

  test('显示纹理管理界面', () => {
    render(
      <TestWrapper>
        <MaterialEditor />
      </TestWrapper>
    );

    // 切换到纹理标签页
    const textureTab = screen.getByText(/纹理|Textures/i);
    fireEvent.click(textureTab);

    expect(screen.getByText(/添加纹理|Add Texture/i)).toBeInTheDocument();
  });

  test('调用保存回调', async () => {
    const mockOnSave = jest.fn();
    
    render(
      <TestWrapper>
        <MaterialEditor onSave={mockOnSave} />
      </TestWrapper>
    );

    const saveButton = screen.getByText(/保存|Save/i);
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalled();
    });
  });

  test('调用取消回调', () => {
    const mockOnCancel = jest.fn();
    
    render(
      <TestWrapper>
        <MaterialEditor onCancel={mockOnCancel} />
      </TestWrapper>
    );

    const cancelButton = screen.getByText(/取消|Cancel/i);
    fireEvent.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();
  });

  test('支持只读模式', () => {
    render(
      <TestWrapper>
        <MaterialEditor readonly={true} />
      </TestWrapper>
    );

    const nameInput = screen.getByLabelText(/名称|Name/i);
    expect(nameInput).toBeDisabled();
  });

  test('加载现有材质', async () => {
    const mockMaterial = {
      id: 'test-material',
      name: '测试材质',
      type: MaterialType.STANDARD,
      color: '#ff0000',
      metalness: 0.5,
      roughness: 0.3
    };

    const { materialService } = require('../../services/materialService');
    (materialService.getMaterial as jest.Mock).mockResolvedValue(mockMaterial);

    render(
      <TestWrapper>
        <MaterialEditor materialId="test-material" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(materialService.getMaterial).toHaveBeenCalledWith('test-material');
    });
  });

  test('处理材质类型变化', async () => {
    render(
      <TestWrapper>
        <MaterialEditor />
      </TestWrapper>
    );

    const typeSelect = screen.getByLabelText(/材质类型|Material Type/i);
    
    // 模拟选择物理材质
    fireEvent.change(typeSelect, { target: { value: MaterialType.PHYSICAL } });

    await waitFor(() => {
      // 验证PBR相关属性显示
      expect(screen.getByLabelText(/金属度|Metalness/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/粗糙度|Roughness/i)).toBeInTheDocument();
    });
  });
});
