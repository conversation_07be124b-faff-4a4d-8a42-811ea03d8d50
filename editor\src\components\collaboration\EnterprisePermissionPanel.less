/**
 * 企业权限管理面板样式
 */

.enterprise-permission-panel {
  .ant-modal-body {
    padding: 16px;
    max-height: 70vh;
    overflow-y: auto;
  }

  // 通用区域样式
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;

    .ant-typography {
      margin-bottom: 0;
    }
  }

  // 用户管理
  .user-management {
    .ant-table {
      .ant-table-tbody {
        .ant-table-row {
          &:hover {
            background-color: #f5f5f5;
          }
        }
      }
    }
  }

  // 团队管理
  .team-management {
    .ant-card {
      height: 200px;
      transition: all 0.2s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
      }

      .ant-card-head {
        .ant-card-head-title {
          font-size: 14px;
        }
      }

      .ant-card-body {
        padding: 12px;
      }

      .team-info {
        .ant-statistic {
          .ant-statistic-title {
            font-size: 11px;
            color: #8c8c8c;
          }

          .ant-statistic-content {
            font-size: 14px;
            font-weight: 600;
          }
        }

        .ant-progress {
          margin-top: 4px;
        }
      }
    }
  }

  // 审计日志
  .audit-logs {
    .ant-table {
      .ant-table-tbody {
        .ant-table-row {
          &:hover {
            background-color: #f5f5f5;
          }
        }
      }
    }
  }

  // 权限统计
  .permission-stats {
    .ant-statistic {
      text-align: center;

      .ant-statistic-title {
        font-size: 12px;
        color: #8c8c8c;
      }

      .ant-statistic-content {
        font-size: 20px;
        font-weight: 600;
      }
    }

    .ant-card {
      .ant-card-head {
        .ant-card-head-title {
          font-size: 14px;
          font-weight: 600;
        }
      }

      .ant-card-body {
        padding: 16px;
      }
    }
  }
}

// 角色颜色
.enterprise-permission-panel {
  .role-super-admin {
    color: #ff4d4f;
    background: rgba(255, 77, 79, 0.1);
  }

  .role-admin {
    color: #fa8c16;
    background: rgba(250, 140, 22, 0.1);
  }

  .role-manager {
    color: #1890ff;
    background: rgba(24, 144, 255, 0.1);
  }

  .role-editor {
    color: #52c41a;
    background: rgba(82, 196, 26, 0.1);
  }

  .role-viewer {
    color: #8c8c8c;
    background: rgba(140, 140, 140, 0.1);
  }

  .role-guest {
    color: #d9d9d9;
    background: rgba(217, 217, 217, 0.1);
  }
}

// 风险级别颜色
.enterprise-permission-panel {
  .risk-critical {
    color: #ff4d4f;
    background: rgba(255, 77, 79, 0.1);
  }

  .risk-high {
    color: #fa8c16;
    background: rgba(250, 140, 22, 0.1);
  }

  .risk-medium {
    color: #faad14;
    background: rgba(250, 173, 20, 0.1);
  }

  .risk-low {
    color: #52c41a;
    background: rgba(82, 196, 26, 0.1);
  }
}

// 深色主题
.dark-theme {
  .enterprise-permission-panel {
    .ant-modal-content {
      background: #2d2d2d;
      color: #cccccc;
    }

    .section-header {
      border-bottom-color: #404040;

      .ant-typography {
        color: #ffffff;
      }
    }

    .user-management {
      .ant-table {
        background: #2d2d2d;

        .ant-table-thead {
          background: #2d2d2d;

          .ant-table-cell {
            background: #2d2d2d;
            border-bottom-color: #404040;
            color: #ffffff;
          }
        }

        .ant-table-tbody {
          .ant-table-cell {
            background: #2d2d2d;
            border-bottom-color: #404040;
            color: #cccccc;
          }

          .ant-table-row {
            &:hover {
              background-color: #404040;
            }
          }
        }
      }
    }

    .team-management {
      .ant-card {
        background: #2d2d2d;
        border-color: #404040;

        .ant-card-head {
          background: #2d2d2d;
          border-bottom-color: #404040;

          .ant-card-head-title {
            color: #ffffff;
          }
        }

        .ant-card-body {
          background: #2d2d2d;
          color: #cccccc;
        }

        .team-info {
          .ant-statistic {
            .ant-statistic-title {
              color: #8c8c8c;
            }

            .ant-statistic-content {
              color: #ffffff;
            }
          }
        }
      }
    }

    .audit-logs {
      .ant-table {
        background: #2d2d2d;

        .ant-table-thead {
          background: #2d2d2d;

          .ant-table-cell {
            background: #2d2d2d;
            border-bottom-color: #404040;
            color: #ffffff;
          }
        }

        .ant-table-tbody {
          .ant-table-cell {
            background: #2d2d2d;
            border-bottom-color: #404040;
            color: #cccccc;
          }

          .ant-table-row {
            &:hover {
              background-color: #404040;
            }
          }
        }
      }
    }

    .permission-stats {
      .ant-statistic {
        .ant-statistic-title {
          color: #8c8c8c;
        }

        .ant-statistic-content {
          color: #ffffff;
        }
      }

      .ant-card {
        background: #2d2d2d;
        border-color: #404040;

        .ant-card-head {
          background: #2d2d2d;
          border-bottom-color: #404040;

          .ant-card-head-title {
            color: #ffffff;
          }
        }

        .ant-card-body {
          background: #2d2d2d;
          color: #cccccc;
        }
      }
    }
  }
}

// 紧凑模式
.compact-theme {
  .enterprise-permission-panel {
    .ant-modal-body {
      padding: 12px;
    }

    .section-header {
      margin-bottom: 12px;
      padding-bottom: 6px;
    }

    .team-management {
      .ant-card {
        height: 160px;

        .ant-card-body {
          padding: 8px;
        }

        .team-info {
          .ant-statistic {
            .ant-statistic-content {
              font-size: 12px;
            }
          }
        }
      }
    }

    .permission-stats {
      .ant-statistic {
        .ant-statistic-content {
          font-size: 16px;
        }
      }

      .ant-card {
        .ant-card-body {
          padding: 12px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .enterprise-permission-panel {
    .ant-modal {
      width: 95% !important;
      max-width: none;
    }
  }
}

@media (max-width: 768px) {
  .enterprise-permission-panel {
    .section-header {
      flex-direction: column;
      align-items: stretch;
      gap: 8px;
    }

    .team-management {
      .ant-row {
        .ant-col {
          flex: 0 0 100%;
          max-width: 100%;
          margin-bottom: 16px;
        }
      }
    }

    .permission-stats {
      .ant-row {
        .ant-col {
          flex: 0 0 50%;
          max-width: 50%;
          margin-bottom: 16px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .enterprise-permission-panel {
    .permission-stats {
      .ant-row {
        .ant-col {
          flex: 0 0 100%;
          max-width: 100%;
          margin-bottom: 12px;
        }
      }
    }
  }
}

// 动画效果
.enterprise-permission-panel {
  .ant-table-row {
    transition: all 0.2s ease;
  }

  .ant-card {
    transition: all 0.2s ease;
  }

  .ant-btn {
    transition: all 0.2s ease;
  }

  .ant-statistic {
    transition: all 0.2s ease;
  }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
  .enterprise-permission-panel {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .enterprise-permission-panel {
    .ant-table {
      border: 2px solid #000;
    }

    .ant-card {
      border: 2px solid #000;
    }

    .section-header {
      border-bottom: 2px solid #000;
    }
  }
}

// 权限管理特定样式
.enterprise-permission-panel {
  .permission-indicator {
    &.granted {
      color: #52c41a;
    }

    &.denied {
      color: #ff4d4f;
    }

    &.inherited {
      color: #1890ff;
    }
  }

  .security-level {
    &.high {
      background: linear-gradient(45deg, #52c41a, #73d13d);
      color: #ffffff;
    }

    &.medium {
      background: linear-gradient(45deg, #faad14, #ffc53d);
      color: #ffffff;
    }

    &.low {
      background: linear-gradient(45deg, #ff4d4f, #ff7875);
      color: #ffffff;
    }
  }

  .audit-action {
    &.create {
      color: #52c41a;
    }

    &.update {
      color: #1890ff;
    }

    &.delete {
      color: #ff4d4f;
    }

    &.login {
      color: #722ed1;
    }

    &.logout {
      color: #8c8c8c;
    }
  }

  .team-role-indicator {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 2px;
      right: 2px;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: currentColor;
    }

    &.owner::after {
      background: #ff4d4f;
    }

    &.admin::after {
      background: #fa8c16;
    }

    &.member::after {
      background: #52c41a;
    }
  }
}

// 权限矩阵样式
.permission-matrix {
  .ant-table {
    .permission-cell {
      text-align: center;
      padding: 4px;

      &.granted {
        background: rgba(82, 196, 26, 0.1);
        color: #52c41a;
      }

      &.denied {
        background: rgba(255, 77, 79, 0.1);
        color: #ff4d4f;
      }

      &.inherited {
        background: rgba(24, 144, 255, 0.1);
        color: #1890ff;
      }
    }
  }
}
