/**
 * DockLayout 样式文件
 */

.dock-layout-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;

  .dock-toolbar {
    height: 40px;
    background: #ffffff;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);

    .dock-toolbar-left,
    .dock-toolbar-right {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .ant-btn {
      border: none;
      box-shadow: none;
      
      &:hover {
        background: #f0f0f0;
      }
    }
  }

  // rc-dock 样式覆盖
  .dock-layout {
    background: #f5f5f5;

    // 面板标题栏样式
    .dock-bar {
      background: #ffffff;
      border-bottom: 1px solid #e8e8e8;
      height: 32px;

      .dock-tab {
        background: transparent;
        border: none;
        border-radius: 0;
        color: #666666;
        font-size: 12px;
        padding: 0 12px;
        height: 32px;
        line-height: 32px;
        transition: all 0.2s;

        &:hover {
          background: #f0f0f0;
          color: #333333;
        }

        &.dock-tab-active {
          background: #1890ff;
          color: #ffffff;
          
          &:hover {
            background: #40a9ff;
          }
        }

        .dock-tab-close-btn {
          color: inherit;
          opacity: 0.6;
          
          &:hover {
            opacity: 1;
            background: rgba(255, 255, 255, 0.2);
          }
        }
      }

      // 面板标题图标
      .panel-title-container {
        display: flex;
        align-items: center;
        gap: 6px;

        .panel-title-text {
          font-size: 12px;
        }

        .anticon {
          font-size: 14px;
        }
      }
    }

    // 面板内容区域
    .dock-panel {
      background: #ffffff;
      border: 1px solid #e8e8e8;
      border-radius: 4px;
      overflow: hidden;

      .dock-panel-content {
        height: 100%;
        overflow: auto;
      }
    }

    // 分割线样式
    .dock-divider {
      background: #e8e8e8;
      
      &:hover {
        background: #1890ff;
      }
    }

    // 拖拽指示器
    .dock-drop-indicator {
      background: rgba(24, 144, 255, 0.3);
      border: 2px solid #1890ff;
      border-radius: 4px;
    }

    // 最大化面板样式
    .dock-panel-max {
      .dock-bar {
        background: #001529;
        
        .dock-tab {
          color: #ffffff;
          
          &:hover {
            background: rgba(255, 255, 255, 0.1);
          }
          
          &.dock-tab-active {
            background: #1890ff;
          }
        }
      }
    }
  }
}

// 深色主题
.dock-layout-container.dark-theme {
  background: #1f1f1f;

  .dock-toolbar {
    background: #2d2d2d;
    border-bottom-color: #404040;

    .ant-btn {
      color: #ffffff;
      
      &:hover {
        background: #404040;
      }
    }
  }

  .dock-layout {
    background: #1f1f1f;

    .dock-bar {
      background: #2d2d2d;
      border-bottom-color: #404040;

      .dock-tab {
        color: #cccccc;
        
        &:hover {
          background: #404040;
          color: #ffffff;
        }

        &.dock-tab-active {
          background: #1890ff;
          color: #ffffff;
        }
      }
    }

    .dock-panel {
      background: #2d2d2d;
      border-color: #404040;
    }

    .dock-divider {
      background: #404040;
    }
  }
}

// 紧凑模式
.dock-layout-container.compact-mode {
  .dock-toolbar {
    height: 32px;
    padding: 0 8px;

    .ant-btn {
      font-size: 12px;
      padding: 0 8px;
    }
  }

  .dock-layout {
    .dock-bar {
      height: 28px;

      .dock-tab {
        height: 28px;
        line-height: 28px;
        padding: 0 8px;
        font-size: 11px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dock-layout-container {
    .dock-toolbar {
      height: 36px;
      
      .dock-toolbar-left,
      .dock-toolbar-right {
        gap: 4px;
      }
    }

    .dock-layout {
      .dock-bar {
        height: 30px;

        .dock-tab {
          height: 30px;
          line-height: 30px;
          padding: 0 8px;
          font-size: 11px;
        }
      }
    }
  }
}

// 动画效果
.dock-layout-container {
  .dock-layout {
    .dock-tab {
      transition: all 0.2s ease-in-out;
    }

    .dock-divider {
      transition: background-color 0.2s ease-in-out;
    }

    .dock-drop-indicator {
      animation: dock-drop-pulse 1s infinite;
    }
  }
}

@keyframes dock-drop-pulse {
  0% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 0.3;
  }
}
