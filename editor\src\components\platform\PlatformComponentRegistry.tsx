/**
 * PlatformComponentRegistry.tsx
 * 
 * 跨平台组件注册器，统一管理不同平台的组件实现
 */

import React from 'react';
import { crossPlatformAdapter, PlatformType, CrossPlatformComponentConfig } from '../../core/CrossPlatformAdapter';
import { Button, Input, Card, List } from 'antd';
import {
  MobileNavBar,
  MobileCard,
  MobileListItem,
  MobileSearchBar
} from '../mobile/MobileComponents';
import {
  DesktopMenuBar
} from '../desktop/DesktopComponents';

/**
 * 跨平台按钮组件配置
 */
const BUTTON_CONFIG: CrossPlatformComponentConfig = {
  id: 'button',
  name: '按钮',
  supportedPlatforms: [PlatformType.WEB, PlatformType.MOBILE, PlatformType.DESKTOP, PlatformType.TABLET],
  implementations: {
    [PlatformType.WEB]: {
      component: Button,
      styles: {
        base: {
          borderRadius: '6px',
          padding: '8px 16px',
          fontSize: '14px'
        }
      }
    },
    [PlatformType.MOBILE]: {
      component: Button,
      styles: {
        base: {
          borderRadius: '8px',
          padding: '12px 20px',
          fontSize: '16px',
          minHeight: '44px'
        }
      }
    },
    [PlatformType.DESKTOP]: {
      component: Button,
      styles: {
        base: {
          borderRadius: '4px',
          padding: '6px 12px',
          fontSize: '13px'
        }
      }
    },
    [PlatformType.TABLET]: {
      component: Button,
      styles: {
        base: {
          borderRadius: '6px',
          padding: '10px 18px',
          fontSize: '15px',
          minHeight: '40px'
        }
      }
    }
  },
  defaultProps: {
    type: 'default',
    size: 'middle'
  },
  adaptationRules: {
    [PlatformType.MOBILE]: {
      propMappings: {
        size: 'size'
      },
      styleTransforms: [
        (styles) => ({
          ...styles,
          touchAction: 'manipulation',
          userSelect: 'none'
        })
      ]
    }
  }
};

/**
 * 跨平台输入框组件配置
 */
const INPUT_CONFIG: CrossPlatformComponentConfig = {
  id: 'input',
  name: '输入框',
  supportedPlatforms: [PlatformType.WEB, PlatformType.MOBILE, PlatformType.DESKTOP, PlatformType.TABLET],
  implementations: {
    [PlatformType.WEB]: {
      component: Input,
      styles: {
        base: {
          borderRadius: '6px',
          padding: '8px 12px',
          fontSize: '14px'
        }
      }
    },
    [PlatformType.MOBILE]: {
      component: Input,
      styles: {
        base: {
          borderRadius: '8px',
          padding: '12px 16px',
          fontSize: '16px',
          minHeight: '44px'
        }
      }
    },
    [PlatformType.DESKTOP]: {
      component: Input,
      styles: {
        base: {
          borderRadius: '4px',
          padding: '6px 10px',
          fontSize: '13px'
        }
      }
    },
    [PlatformType.TABLET]: {
      component: Input,
      styles: {
        base: {
          borderRadius: '6px',
          padding: '10px 14px',
          fontSize: '15px',
          minHeight: '40px'
        }
      }
    }
  },
  defaultProps: {
    placeholder: '请输入'
  }
};

/**
 * 跨平台卡片组件配置
 */
const CARD_CONFIG: CrossPlatformComponentConfig = {
  id: 'card',
  name: '卡片',
  supportedPlatforms: [PlatformType.WEB, PlatformType.MOBILE, PlatformType.DESKTOP, PlatformType.TABLET],
  implementations: {
    [PlatformType.WEB]: {
      component: Card,
      styles: {
        base: {
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }
      }
    },
    [PlatformType.MOBILE]: {
      component: MobileCard,
      styles: {
        base: {
          borderRadius: '12px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
          margin: '8px'
        }
      }
    },
    [PlatformType.DESKTOP]: {
      component: Card,
      styles: {
        base: {
          borderRadius: '6px',
          boxShadow: '0 1px 4px rgba(0,0,0,0.1)'
        }
      }
    },
    [PlatformType.TABLET]: {
      component: Card,
      styles: {
        base: {
          borderRadius: '10px',
          boxShadow: '0 3px 10px rgba(0,0,0,0.1)'
        }
      }
    }
  },
  defaultProps: {
    bordered: true
  }
};

/**
 * 跨平台导航栏组件配置
 */
const NAVBAR_CONFIG: CrossPlatformComponentConfig = {
  id: 'navbar',
  name: '导航栏',
  supportedPlatforms: [PlatformType.MOBILE, PlatformType.TABLET],
  implementations: {
    [PlatformType.MOBILE]: {
      component: MobileNavBar,
      styles: {
        base: {
          height: '44px',
          padding: '0 16px'
        }
      }
    },
    [PlatformType.TABLET]: {
      component: MobileNavBar,
      styles: {
        base: {
          height: '48px',
          padding: '0 20px'
        }
      }
    }
  },
  defaultProps: {
    title: '标题',
    fixed: true
  }
};

/**
 * 跨平台菜单栏组件配置
 */
const MENUBAR_CONFIG: CrossPlatformComponentConfig = {
  id: 'menubar',
  name: '菜单栏',
  supportedPlatforms: [PlatformType.WEB, PlatformType.DESKTOP],
  implementations: {
    [PlatformType.WEB]: {
      component: DesktopMenuBar,
      styles: {
        base: {
          height: '32px',
          backgroundColor: '#f5f5f5'
        }
      }
    },
    [PlatformType.DESKTOP]: {
      component: DesktopMenuBar,
      styles: {
        base: {
          height: '28px',
          backgroundColor: '#ffffff'
        }
      }
    }
  },
  defaultProps: {
    menus: []
  }
};

/**
 * 跨平台搜索栏组件配置
 */
const SEARCHBAR_CONFIG: CrossPlatformComponentConfig = {
  id: 'searchbar',
  name: '搜索栏',
  supportedPlatforms: [PlatformType.WEB, PlatformType.MOBILE, PlatformType.DESKTOP, PlatformType.TABLET],
  implementations: {
    [PlatformType.WEB]: {
      component: Input.Search,
      styles: {
        base: {
          borderRadius: '20px'
        }
      }
    },
    [PlatformType.MOBILE]: {
      component: MobileSearchBar,
      styles: {
        base: {
          borderRadius: '20px',
          padding: '8px 16px'
        }
      }
    },
    [PlatformType.DESKTOP]: {
      component: Input.Search,
      styles: {
        base: {
          borderRadius: '16px'
        }
      }
    },
    [PlatformType.TABLET]: {
      component: MobileSearchBar,
      styles: {
        base: {
          borderRadius: '18px',
          padding: '10px 18px'
        }
      }
    }
  },
  defaultProps: {
    placeholder: '搜索'
  }
};

/**
 * 跨平台列表组件配置
 */
const LIST_CONFIG: CrossPlatformComponentConfig = {
  id: 'list',
  name: '列表',
  supportedPlatforms: [PlatformType.WEB, PlatformType.MOBILE, PlatformType.DESKTOP, PlatformType.TABLET],
  implementations: {
    [PlatformType.WEB]: {
      component: List,
      styles: {
        base: {
          backgroundColor: '#ffffff'
        }
      }
    },
    [PlatformType.MOBILE]: {
      component: ({ dataSource, renderItem }: any) => (
        <div className="mobile-list">
          {dataSource?.map((item: any, index: number) => (
            <MobileListItem key={index} {...renderItem(item, index)} />
          ))}
        </div>
      ),
      styles: {
        base: {
          backgroundColor: '#ffffff'
        }
      }
    },
    [PlatformType.DESKTOP]: {
      component: List,
      styles: {
        base: {
          backgroundColor: '#ffffff'
        }
      }
    },
    [PlatformType.TABLET]: {
      component: List,
      styles: {
        base: {
          backgroundColor: '#ffffff'
        }
      }
    }
  },
  defaultProps: {
    dataSource: []
  }
};

/**
 * 平台组件注册器类
 */
export class PlatformComponentRegistry {
  private static instance: PlatformComponentRegistry;
  private initialized = false;

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): PlatformComponentRegistry {
    if (!PlatformComponentRegistry.instance) {
      PlatformComponentRegistry.instance = new PlatformComponentRegistry();
    }
    return PlatformComponentRegistry.instance;
  }

  /**
   * 初始化注册器
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    // 注册所有跨平台组件
    this.registerAllComponents();
    
    this.initialized = true;
  }

  /**
   * 注册所有组件
   */
  private registerAllComponents(): void {
    const components = [
      BUTTON_CONFIG,
      INPUT_CONFIG,
      CARD_CONFIG,
      NAVBAR_CONFIG,
      MENUBAR_CONFIG,
      SEARCHBAR_CONFIG,
      LIST_CONFIG
    ];

    components.forEach(config => {
      crossPlatformAdapter.registerComponent(config);
    });
  }

  /**
   * 获取平台适配的组件
   */
  public getComponent(componentId: string, props: Record<string, any> = {}) {
    return crossPlatformAdapter.adaptComponent(componentId, props);
  }

  /**
   * 获取当前平台支持的组件列表
   */
  public getSupportedComponents(): string[] {
    return crossPlatformAdapter.getSupportedComponents();
  }

  /**
   * 检查组件是否支持当前平台
   */
  public isComponentSupported(componentId: string): boolean {
    return crossPlatformAdapter.isPlatformSupported(componentId);
  }

  /**
   * 注册自定义组件
   */
  public registerCustomComponent(config: CrossPlatformComponentConfig): void {
    crossPlatformAdapter.registerComponent(config);
  }
}

/**
 * 跨平台组件包装器
 */
export interface PlatformComponentProps {
  componentId: string;
  [key: string]: any;
}

export const PlatformComponent: React.FC<PlatformComponentProps> = ({
  componentId,
  ...props
}) => {
  const registry = PlatformComponentRegistry.getInstance();
  const adaptedComponent = registry.getComponent(componentId, props);

  if (!adaptedComponent) {
    console.warn(`组件 ${componentId} 在当前平台不可用`);
    return <div>组件不可用</div>;
  }

  const { component: Component, props: adaptedProps, styles } = adaptedComponent;

  return (
    <Component
      {...adaptedProps}
      style={{ ...styles, ...props.style }}
    />
  );
};

// 导出单例实例
export const platformComponentRegistry = PlatformComponentRegistry.getInstance();

// 自动初始化
platformComponentRegistry.initialize();
