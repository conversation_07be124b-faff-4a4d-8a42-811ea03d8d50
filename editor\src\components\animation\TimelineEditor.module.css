/**
 * TimelineEditor.module.css
 * 
 * 动画时间轴编辑器样式
 */

.timeline-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--background-color, #ffffff);
  border: 1px solid var(--border-color, #d9d9d9);
  border-radius: 8px;
  overflow: hidden;
}

/* 工具栏 */
.timeline-toolbar {
  padding: 12px 16px;
  background: var(--surface-color, #fafafa);
  border-bottom: 1px solid var(--border-color, #d9d9d9);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.timeline-toolbar :global(.ant-btn-group) {
  display: flex;
}

.timeline-toolbar :global(.ant-btn) {
  display: flex;
  align-items: center;
  justify-content: center;
}

.timeline-toolbar :global(.ant-divider-vertical) {
  height: 24px;
  margin: 0 12px;
}

.timeline-toolbar :global(.ant-slider) {
  margin: 0;
}

.timeline-toolbar :global(.ant-input-number) {
  border-radius: 4px;
}

.timeline-toolbar :global(.ant-select) {
  border-radius: 4px;
}

/* 时间轴容器 */
.timeline-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 轨道列表 */
.track-list {
  width: 200px;
  background: var(--surface-color, #fafafa);
  border-right: 1px solid var(--border-color, #d9d9d9);
  overflow-y: auto;
  flex-shrink: 0;
}

.track-header {
  height: 40px;
  padding: 8px 12px;
  border-bottom: 1px solid var(--border-color-light, #f0f0f0);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--background-color, #ffffff);
}

.track-header:hover {
  background: var(--item-hover-background, #f5f5f5);
}

.track-name {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-color, #000000);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.track-controls {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.track-header:hover .track-controls {
  opacity: 1;
}

/* 时间轴内容 */
.timeline-content {
  flex: 1;
  position: relative;
  overflow-x: auto;
  overflow-y: hidden;
  background: var(--timeline-background, #f8f9fa);
  min-height: 300px;
}

/* 时间刻度 */
.time-ruler {
  height: 30px;
  position: relative;
  background: var(--ruler-background, #e6e6e6);
  border-bottom: 1px solid var(--border-color, #d9d9d9);
}

.time-mark {
  position: absolute;
  top: 0;
  height: 100%;
  border-left: 1px solid var(--ruler-mark-color, #999999);
  display: flex;
  align-items: center;
  padding-left: 4px;
}

.time-mark span {
  font-size: 10px;
  color: var(--text-color-secondary, #8c8c8c);
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

/* 播放头 */
.playhead {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--primary-color, #1890ff);
  z-index: 1000;
  pointer-events: none;
}

.playhead::before {
  content: '';
  position: absolute;
  top: 0;
  left: -6px;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 8px solid var(--primary-color, #1890ff);
}

.playhead::after {
  content: '';
  position: absolute;
  top: 0;
  left: -1px;
  right: -1px;
  bottom: 0;
  background: var(--primary-color, #1890ff);
  opacity: 0.1;
  pointer-events: none;
}

/* 轨道内容 */
.tracks-content {
  position: relative;
}

.track-content {
  height: 40px;
  position: relative;
  border-bottom: 1px solid var(--border-color-light, #f0f0f0);
  background: var(--background-color, #ffffff);
  cursor: crosshair;
}

.track-content:hover {
  background: var(--track-hover-background, #f9f9f9);
}

.track-content:nth-child(even) {
  background: var(--track-alt-background, #fafafa);
}

.track-content:nth-child(even):hover {
  background: var(--track-alt-hover-background, #f5f5f5);
}

/* 关键帧 */
.keyframe {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  cursor: pointer;
  z-index: 100;
  transition: all 0.2s ease;
}

.keyframe:hover {
  transform: translateY(-50%) scale(1.2);
  z-index: 101;
}

.keyframe.selected {
  transform: translateY(-50%) scale(1.3);
  z-index: 102;
}

.keyframe-diamond {
  width: 100%;
  height: 100%;
  background: inherit;
  border: 2px solid var(--background-color, #ffffff);
  border-radius: 2px;
  transform: rotate(45deg);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.keyframe.selected .keyframe-diamond {
  border-color: var(--primary-color, #1890ff);
  border-width: 3px;
  box-shadow: 0 0 0 2px var(--primary-color, #1890ff);
}

/* 选择框 */
.selection-box {
  position: absolute;
  border: 1px dashed var(--primary-color, #1890ff);
  background: rgba(24, 144, 255, 0.1);
  pointer-events: none;
  z-index: 999;
}

/* 拖拽状态 */
.timeline-content.dragging {
  cursor: grabbing;
}

.keyframe.dragging {
  opacity: 0.7;
  z-index: 1001;
}

/* 网格线 */
.timeline-content::before {
  content: '';
  position: absolute;
  top: 30px;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(to right, var(--grid-color, #e0e0e0) 1px, transparent 1px),
    linear-gradient(to bottom, var(--grid-color, #e0e0e0) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
  opacity: 0.3;
}

/* 暗色主题 */
[data-theme="dark"] .timeline-editor {
  --background-color: #141414;
  --surface-color: #1f1f1f;
  --border-color: #434343;
  --border-color-light: #303030;
  --text-color: #ffffff;
  --text-color-secondary: #a6a6a6;
  --timeline-background: #0f1419;
  --ruler-background: #262626;
  --ruler-mark-color: #666666;
  --track-hover-background: #1a1a1a;
  --track-alt-background: #1a1a1a;
  --track-alt-hover-background: #262626;
  --item-hover-background: #262626;
  --grid-color: #333333;
  --primary-color: #177ddc;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .track-list {
    width: 150px;
  }
  
  .timeline-toolbar {
    padding: 8px 12px;
  }
  
  .timeline-toolbar :global(.ant-space) {
    flex-wrap: wrap;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .timeline-container {
    flex-direction: column;
  }
  
  .track-list {
    width: 100%;
    height: 120px;
    border-right: none;
    border-bottom: 1px solid var(--border-color, #d9d9d9);
  }
  
  .track-header {
    height: 30px;
    padding: 4px 8px;
  }
  
  .track-name {
    font-size: 11px;
  }
  
  .timeline-toolbar {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .keyframe {
    width: 16px;
    height: 16px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .timeline-editor {
    border-width: 2px;
  }
  
  .keyframe.selected .keyframe-diamond {
    border-width: 4px;
  }
  
  .playhead {
    width: 3px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .keyframe,
  .track-controls {
    transition: none;
  }
  
  .keyframe:hover,
  .keyframe.selected {
    transform: translateY(-50%);
  }
}

/* 自定义滚动条 */
.track-list::-webkit-scrollbar,
.timeline-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.track-list::-webkit-scrollbar-track,
.timeline-content::-webkit-scrollbar-track {
  background: var(--scrollbar-track-color, #f1f1f1);
  border-radius: 4px;
}

.track-list::-webkit-scrollbar-thumb,
.timeline-content::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-color, #c1c1c1);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.track-list::-webkit-scrollbar-thumb:hover,
.timeline-content::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-color, #a8a8a8);
}

/* 工具提示 */
.timeline-editor :global(.ant-tooltip) {
  z-index: 10000;
}

.timeline-editor :global(.ant-tooltip-inner) {
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 4px;
}

/* 下拉菜单 */
.timeline-editor :global(.ant-dropdown) {
  z-index: 10001;
}

/* 性能优化 */
.timeline-editor {
  contain: layout style paint;
}

.timeline-content {
  contain: layout style paint;
  will-change: scroll-position;
}

.keyframe {
  contain: layout style paint;
  will-change: transform;
}

.playhead {
  contain: layout style paint;
  will-change: transform;
}

/* 打印样式 */
@media print {
  .timeline-toolbar {
    display: none;
  }
  
  .timeline-editor {
    border: none;
    height: auto;
  }
  
  .timeline-content {
    overflow: visible;
    height: auto;
  }
}

/* 焦点状态 */
.timeline-editor:focus-within {
  outline: 2px solid var(--primary-color, #1890ff);
  outline-offset: -2px;
}

.keyframe:focus {
  outline: 2px solid var(--primary-color, #1890ff);
  outline-offset: 2px;
}

/* 加载状态 */
.timeline-editor.loading {
  opacity: 0.5;
  pointer-events: none;
}

.timeline-editor.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32px;
  height: 32px;
  margin: -16px 0 0 -16px;
  border: 3px solid var(--border-color, #d9d9d9);
  border-top-color: var(--primary-color, #1890ff);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 10002;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
