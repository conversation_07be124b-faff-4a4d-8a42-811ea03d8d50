/**
 * 实时同步优化服务
 * 
 * 提供高性能的实时数据同步，包括：
 * - 增量同步算法
 * - 数据压缩和优化
 * - 网络延迟补偿
 * - 同步状态管理
 * - 带宽自适应调整
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import Redis from 'ioredis';
import { v4 as uuidv4 } from 'uuid';
import * as zlib from 'zlib';

/**
 * 同步策略枚举
 */
export enum SyncStrategy {
  FULL_SYNC = 'full_sync',
  INCREMENTAL_SYNC = 'incremental_sync',
  DELTA_SYNC = 'delta_sync',
  OPTIMISTIC_SYNC = 'optimistic_sync',
  PESSIMISTIC_SYNC = 'pessimistic_sync'
}

/**
 * 数据变更类型
 */
export enum ChangeType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  BATCH = 'batch'
}

/**
 * 同步状态枚举
 */
export enum SyncStatus {
  SYNCED = 'synced',
  SYNCING = 'syncing',
  CONFLICT = 'conflict',
  ERROR = 'error',
  OFFLINE = 'offline'
}

/**
 * 数据变更接口
 */
export interface DataChange {
  id: string;
  type: ChangeType;
  entityType: string;
  entityId: string;
  data: any;
  timestamp: number;
  userId: string;
  sessionId: string;
  checksum?: string;
  dependencies?: string[];
}

/**
 * 同步包接口
 */
export interface SyncPacket {
  id: string;
  sessionId: string;
  changes: DataChange[];
  timestamp: number;
  compressed: boolean;
  size: number;
  checksum: string;
  sequenceNumber: number;
}

/**
 * 客户端同步状态接口
 */
export interface ClientSyncState {
  sessionId: string;
  lastSyncTimestamp: number;
  lastSequenceNumber: number;
  status: SyncStatus;
  pendingChanges: DataChange[];
  networkLatency: number;
  bandwidth: number;
  compressionEnabled: boolean;
}

/**
 * 实时同步优化服务
 */
@Injectable()
export class RealtimeSyncService {
  private readonly logger = new Logger(RealtimeSyncService.name);
  private readonly redis: Redis;
  
  // 同步状态管理
  private clientStates = new Map<string, ClientSyncState>();
  private pendingPackets = new Map<string, SyncPacket[]>();
  private changeBuffer = new Map<string, DataChange[]>();
  
  // 性能监控
  private syncMetrics = {
    totalSyncs: 0,
    successfulSyncs: 0,
    failedSyncs: 0,
    averageSyncTime: 0,
    averagePacketSize: 0,
    compressionRatio: 0,
    networkUtilization: 0
  };
  
  // 配置参数
  private readonly maxBufferSize = 1000;
  private readonly syncInterval = 100; // 100ms
  private readonly compressionThreshold = 1024; // 1KB
  private readonly maxRetries = 3;
  private readonly networkTimeoutMs = 5000;

  constructor(
    private readonly eventEmitter: EventEmitter2,
    redisConfig: any
  ) {
    this.redis = new Redis(redisConfig);
    this.initializeService();
  }

  /**
   * 初始化服务
   */
  private async initializeService(): Promise<void> {
    try {
      // 启动同步调度器
      this.startSyncScheduler();
      
      // 启动性能监控
      this.startPerformanceMonitoring();
      
      // 启动网络质量检测
      this.startNetworkQualityMonitoring();
      
      this.logger.log('实时同步优化服务已启动');
      
    } catch (error) {
      this.logger.error('服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 注册客户端
   */
  public async registerClient(sessionId: string): Promise<void> {
    try {
      const clientState: ClientSyncState = {
        sessionId,
        lastSyncTimestamp: Date.now(),
        lastSequenceNumber: 0,
        status: SyncStatus.SYNCED,
        pendingChanges: [],
        networkLatency: 0,
        bandwidth: 0,
        compressionEnabled: true
      };
      
      this.clientStates.set(sessionId, clientState);
      this.changeBuffer.set(sessionId, []);
      this.pendingPackets.set(sessionId, []);
      
      // 保存到Redis
      await this.redis.setex(
        `sync:client:${sessionId}`,
        3600,
        JSON.stringify(clientState)
      );
      
      this.eventEmitter.emit('sync.client.registered', { sessionId });
      this.logger.log(`客户端已注册: ${sessionId}`);
      
    } catch (error) {
      this.logger.error('注册客户端失败:', error);
      throw error;
    }
  }

  /**
   * 提交数据变更
   */
  public async submitChange(
    sessionId: string,
    change: Omit<DataChange, 'id' | 'timestamp' | 'sessionId'>
  ): Promise<string> {
    try {
      const clientState = this.clientStates.get(sessionId);
      if (!clientState) {
        throw new Error('客户端未注册');
      }
      
      const fullChange: DataChange = {
        ...change,
        id: uuidv4(),
        timestamp: Date.now(),
        sessionId,
        checksum: this.calculateChecksum(change.data)
      };
      
      // 添加到变更缓冲区
      const buffer = this.changeBuffer.get(sessionId)!;
      buffer.push(fullChange);
      
      // 限制缓冲区大小
      if (buffer.length > this.maxBufferSize) {
        buffer.shift();
      }
      
      // 更新客户端状态
      clientState.pendingChanges.push(fullChange);
      clientState.status = SyncStatus.SYNCING;
      
      // 触发立即同步（如果是高优先级变更）
      if (this.isHighPriorityChange(fullChange)) {
        await this.performImmediateSync(sessionId);
      }
      
      this.eventEmitter.emit('sync.change.submitted', fullChange);
      
      return fullChange.id;
      
    } catch (error) {
      this.logger.error('提交数据变更失败:', error);
      throw error;
    }
  }

  /**
   * 执行同步
   */
  public async performSync(
    sessionId: string,
    strategy: SyncStrategy = SyncStrategy.INCREMENTAL_SYNC
  ): Promise<{
    success: boolean;
    syncPacket?: SyncPacket;
    appliedChanges?: number;
    conflicts?: any[];
  }> {
    try {
      const clientState = this.clientStates.get(sessionId);
      if (!clientState) {
        throw new Error('客户端未注册');
      }
      
      const startTime = Date.now();
      
      let syncResult: any;
      
      switch (strategy) {
        case SyncStrategy.FULL_SYNC:
          syncResult = await this.performFullSync(sessionId);
          break;
        case SyncStrategy.INCREMENTAL_SYNC:
          syncResult = await this.performIncrementalSync(sessionId);
          break;
        case SyncStrategy.DELTA_SYNC:
          syncResult = await this.performDeltaSync(sessionId);
          break;
        case SyncStrategy.OPTIMISTIC_SYNC:
          syncResult = await this.performOptimisticSync(sessionId);
          break;
        case SyncStrategy.PESSIMISTIC_SYNC:
          syncResult = await this.performPessimisticSync(sessionId);
          break;
      }
      
      // 更新性能指标
      const syncTime = Date.now() - startTime;
      this.updateSyncMetrics(syncTime, syncResult.success);
      
      if (syncResult.success) {
        clientState.status = SyncStatus.SYNCED;
        clientState.lastSyncTimestamp = Date.now();
        clientState.pendingChanges = [];
      }
      
      return syncResult;
      
    } catch (error) {
      this.logger.error('执行同步失败:', error);
      return { success: false };
    }
  }

  /**
   * 增量同步
   */
  private async performIncrementalSync(sessionId: string): Promise<any> {
    try {
      const clientState = this.clientStates.get(sessionId)!;
      const buffer = this.changeBuffer.get(sessionId)!;
      
      // 获取自上次同步以来的变更
      const newChanges = buffer.filter(change => 
        change.timestamp > clientState.lastSyncTimestamp
      );
      
      if (newChanges.length === 0) {
        return { success: true, appliedChanges: 0 };
      }
      
      // 创建同步包
      const syncPacket = await this.createSyncPacket(sessionId, newChanges);
      
      // 应用变更
      const appliedChanges = await this.applyChanges(newChanges);
      
      // 检测冲突
      const conflicts = await this.detectSyncConflicts(newChanges);
      
      return {
        success: true,
        syncPacket,
        appliedChanges: appliedChanges.length,
        conflicts
      };
      
    } catch (error) {
      this.logger.error('增量同步失败:', error);
      return { success: false };
    }
  }

  /**
   * 创建同步包
   */
  private async createSyncPacket(
    sessionId: string,
    changes: DataChange[]
  ): Promise<SyncPacket> {
    try {
      const clientState = this.clientStates.get(sessionId)!;
      
      let packetData = JSON.stringify(changes);
      let compressed = false;
      
      // 如果数据大小超过阈值，进行压缩
      if (packetData.length > this.compressionThreshold && clientState.compressionEnabled) {
        const compressedData = await this.compressData(packetData);
        if (compressedData.length < packetData.length * 0.8) { // 压缩率超过20%才使用
          packetData = compressedData.toString('base64');
          compressed = true;
        }
      }
      
      const syncPacket: SyncPacket = {
        id: uuidv4(),
        sessionId,
        changes,
        timestamp: Date.now(),
        compressed,
        size: packetData.length,
        checksum: this.calculateChecksum(packetData),
        sequenceNumber: ++clientState.lastSequenceNumber
      };
      
      // 保存到待发送队列
      const pendingPackets = this.pendingPackets.get(sessionId)!;
      pendingPackets.push(syncPacket);
      
      return syncPacket;
      
    } catch (error) {
      this.logger.error('创建同步包失败:', error);
      throw error;
    }
  }

  /**
   * 压缩数据
   */
  private async compressData(data: string): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      zlib.deflate(Buffer.from(data), (err, compressed) => {
        if (err) {
          reject(err);
        } else {
          resolve(compressed);
        }
      });
    });
  }

  /**
   * 解压数据
   */
  private async decompressData(compressedData: Buffer): Promise<string> {
    return new Promise((resolve, reject) => {
      zlib.inflate(compressedData, (err, decompressed) => {
        if (err) {
          reject(err);
        } else {
          resolve(decompressed.toString());
        }
      });
    });
  }

  /**
   * 计算校验和
   */
  private calculateChecksum(data: any): string {
    const crypto = require('crypto');
    const hash = crypto.createHash('md5');
    hash.update(JSON.stringify(data));
    return hash.digest('hex');
  }

  /**
   * 应用变更
   */
  private async applyChanges(changes: DataChange[]): Promise<DataChange[]> {
    const appliedChanges: DataChange[] = [];
    
    try {
      for (const change of changes) {
        // 验证变更的完整性
        if (this.validateChange(change)) {
          // 应用变更到数据存储
          await this.applyChangeToStorage(change);
          appliedChanges.push(change);
          
          // 广播变更到其他客户端
          this.eventEmitter.emit('sync.change.applied', change);
        }
      }
      
      return appliedChanges;
      
    } catch (error) {
      this.logger.error('应用变更失败:', error);
      return appliedChanges;
    }
  }

  /**
   * 验证变更
   */
  private validateChange(change: DataChange): boolean {
    // 检查必要字段
    if (!change.id || !change.entityType || !change.entityId) {
      return false;
    }
    
    // 验证校验和
    if (change.checksum) {
      const calculatedChecksum = this.calculateChecksum(change.data);
      if (calculatedChecksum !== change.checksum) {
        this.logger.warn(`变更校验和不匹配: ${change.id}`);
        return false;
      }
    }
    
    return true;
  }

  /**
   * 应用变更到存储
   */
  private async applyChangeToStorage(change: DataChange): Promise<void> {
    try {
      const key = `entity:${change.entityType}:${change.entityId}`;
      
      switch (change.type) {
        case ChangeType.CREATE:
        case ChangeType.UPDATE:
          await this.redis.setex(key, 3600 * 24, JSON.stringify(change.data));
          break;
        case ChangeType.DELETE:
          await this.redis.del(key);
          break;
      }
      
    } catch (error) {
      this.logger.error('应用变更到存储失败:', error);
      throw error;
    }
  }

  /**
   * 检测同步冲突
   */
  private async detectSyncConflicts(changes: DataChange[]): Promise<any[]> {
    const conflicts: any[] = [];
    
    try {
      for (const change of changes) {
        // 检查是否有其他客户端同时修改了相同的实体
        const conflictingChanges = await this.findConflictingChanges(change);
        
        if (conflictingChanges.length > 0) {
          conflicts.push({
            changeId: change.id,
            conflictingChanges,
            resolutionStrategy: this.suggestResolutionStrategy(change, conflictingChanges)
          });
        }
      }
      
      return conflicts;
      
    } catch (error) {
      this.logger.error('检测同步冲突失败:', error);
      return [];
    }
  }

  /**
   * 查找冲突变更
   */
  private async findConflictingChanges(change: DataChange): Promise<DataChange[]> {
    const conflictingChanges: DataChange[] = [];
    
    // 检查所有客户端的变更缓冲区
    for (const [sessionId, buffer] of this.changeBuffer) {
      if (sessionId === change.sessionId) continue;
      
      const conflicts = buffer.filter(otherChange =>
        otherChange.entityType === change.entityType &&
        otherChange.entityId === change.entityId &&
        Math.abs(otherChange.timestamp - change.timestamp) < 5000 // 5秒内的变更
      );
      
      conflictingChanges.push(...conflicts);
    }
    
    return conflictingChanges;
  }

  /**
   * 建议解决策略
   */
  private suggestResolutionStrategy(change: DataChange, conflictingChanges: DataChange[]): string {
    // 简化的冲突解决策略
    if (conflictingChanges.length === 1) {
      const conflictChange = conflictingChanges[0];
      
      // 时间戳较新的获胜
      if (change.timestamp > conflictChange.timestamp) {
        return 'last_write_wins';
      } else {
        return 'first_write_wins';
      }
    }
    
    return 'manual_resolution_required';
  }

  /**
   * 判断是否为高优先级变更
   */
  private isHighPriorityChange(change: DataChange): boolean {
    // 某些类型的变更需要立即同步
    const highPriorityTypes = ['user_cursor', 'selection', 'typing'];
    return highPriorityTypes.includes(change.entityType);
  }

  /**
   * 执行立即同步
   */
  private async performImmediateSync(sessionId: string): Promise<void> {
    try {
      await this.performSync(sessionId, SyncStrategy.INCREMENTAL_SYNC);
    } catch (error) {
      this.logger.error('立即同步失败:', error);
    }
  }

  /**
   * 全量同步
   */
  private async performFullSync(sessionId: string): Promise<any> {
    try {
      const clientState = this.clientStates.get(sessionId)!;

      // 获取所有实体数据
      const allEntities = await this.getAllEntities(sessionId);

      // 创建全量同步包
      const changes: DataChange[] = allEntities.map(entity => ({
        id: uuidv4(),
        type: ChangeType.UPDATE,
        entityType: entity.type,
        entityId: entity.id,
        data: entity.data,
        timestamp: Date.now(),
        userId: 'system',
        sessionId,
        checksum: this.calculateChecksum(entity.data)
      }));

      const syncPacket = await this.createSyncPacket(sessionId, changes);

      return {
        success: true,
        syncPacket,
        appliedChanges: changes.length,
        conflicts: []
      };

    } catch (error) {
      this.logger.error('全量同步失败:', error);
      return { success: false };
    }
  }

  /**
   * Delta同步
   */
  private async performDeltaSync(sessionId: string): Promise<any> {
    try {
      const clientState = this.clientStates.get(sessionId)!;
      const buffer = this.changeBuffer.get(sessionId)!;

      // 计算增量变更
      const deltaChanges = await this.calculateDeltaChanges(sessionId, buffer);

      if (deltaChanges.length === 0) {
        return { success: true, appliedChanges: 0 };
      }

      const syncPacket = await this.createSyncPacket(sessionId, deltaChanges);
      const appliedChanges = await this.applyChanges(deltaChanges);

      return {
        success: true,
        syncPacket,
        appliedChanges: appliedChanges.length,
        conflicts: []
      };

    } catch (error) {
      this.logger.error('Delta同步失败:', error);
      return { success: false };
    }
  }

  /**
   * 乐观同步
   */
  private async performOptimisticSync(sessionId: string): Promise<any> {
    try {
      // 乐观同步：先应用变更，后检测冲突
      const clientState = this.clientStates.get(sessionId)!;
      const pendingChanges = [...clientState.pendingChanges];

      // 立即应用所有变更
      const appliedChanges = await this.applyChanges(pendingChanges);

      // 异步检测冲突
      setImmediate(async () => {
        const conflicts = await this.detectSyncConflicts(pendingChanges);
        if (conflicts.length > 0) {
          this.eventEmitter.emit('sync.conflicts.detected', { sessionId, conflicts });
        }
      });

      const syncPacket = await this.createSyncPacket(sessionId, pendingChanges);

      return {
        success: true,
        syncPacket,
        appliedChanges: appliedChanges.length,
        conflicts: []
      };

    } catch (error) {
      this.logger.error('乐观同步失败:', error);
      return { success: false };
    }
  }

  /**
   * 悲观同步
   */
  private async performPessimisticSync(sessionId: string): Promise<any> {
    try {
      // 悲观同步：先检测冲突，后应用变更
      const clientState = this.clientStates.get(sessionId)!;
      const pendingChanges = [...clientState.pendingChanges];

      // 先检测冲突
      const conflicts = await this.detectSyncConflicts(pendingChanges);

      if (conflicts.length > 0) {
        return {
          success: false,
          conflicts
        };
      }

      // 没有冲突，应用变更
      const appliedChanges = await this.applyChanges(pendingChanges);
      const syncPacket = await this.createSyncPacket(sessionId, pendingChanges);

      return {
        success: true,
        syncPacket,
        appliedChanges: appliedChanges.length,
        conflicts: []
      };

    } catch (error) {
      this.logger.error('悲观同步失败:', error);
      return { success: false };
    }
  }

  /**
   * 获取所有实体
   */
  private async getAllEntities(sessionId: string): Promise<any[]> {
    try {
      const pattern = 'entity:*';
      const keys = await this.redis.keys(pattern);
      const entities: any[] = [];

      for (const key of keys) {
        const data = await this.redis.get(key);
        if (data) {
          const [, type, id] = key.split(':');
          entities.push({
            type,
            id,
            data: JSON.parse(data)
          });
        }
      }

      return entities;

    } catch (error) {
      this.logger.error('获取所有实体失败:', error);
      return [];
    }
  }

  /**
   * 计算增量变更
   */
  private async calculateDeltaChanges(sessionId: string, buffer: DataChange[]): Promise<DataChange[]> {
    try {
      const clientState = this.clientStates.get(sessionId)!;
      const deltaChanges: DataChange[] = [];

      // 获取客户端最后同步的状态
      const lastSyncState = await this.getLastSyncState(sessionId);

      for (const change of buffer) {
        if (change.timestamp > clientState.lastSyncTimestamp) {
          // 计算与上次同步状态的差异
          const delta = await this.calculateEntityDelta(change, lastSyncState);
          if (delta) {
            deltaChanges.push(delta);
          }
        }
      }

      return deltaChanges;

    } catch (error) {
      this.logger.error('计算增量变更失败:', error);
      return [];
    }
  }

  /**
   * 获取最后同步状态
   */
  private async getLastSyncState(sessionId: string): Promise<any> {
    try {
      const stateData = await this.redis.get(`sync:state:${sessionId}`);
      return stateData ? JSON.parse(stateData) : {};
    } catch (error) {
      this.logger.error('获取最后同步状态失败:', error);
      return {};
    }
  }

  /**
   * 计算实体增量
   */
  private async calculateEntityDelta(change: DataChange, lastState: any): Promise<DataChange | null> {
    try {
      const entityKey = `${change.entityType}:${change.entityId}`;
      const lastEntityState = lastState[entityKey];

      if (!lastEntityState) {
        // 新实体，返回完整变更
        return change;
      }

      // 计算数据差异
      const delta = this.calculateDataDelta(lastEntityState, change.data);

      if (Object.keys(delta).length === 0) {
        // 没有变化
        return null;
      }

      return {
        ...change,
        data: delta,
        type: ChangeType.UPDATE
      };

    } catch (error) {
      this.logger.error('计算实体增量失败:', error);
      return change;
    }
  }

  /**
   * 计算数据差异
   */
  private calculateDataDelta(oldData: any, newData: any): any {
    const delta: any = {};

    // 简化的差异计算
    for (const key in newData) {
      if (newData[key] !== oldData[key]) {
        delta[key] = newData[key];
      }
    }

    // 检查删除的属性
    for (const key in oldData) {
      if (!(key in newData)) {
        delta[key] = null; // 标记为删除
      }
    }

    return delta;
  }

  /**
   * 启动同步调度器
   */
  private startSyncScheduler(): void {
    setInterval(async () => {
      await this.performScheduledSync();
    }, this.syncInterval);

    this.logger.log('同步调度器已启动');
  }

  /**
   * 执行计划同步
   */
  private async performScheduledSync(): Promise<void> {
    try {
      for (const [sessionId, clientState] of this.clientStates) {
        if (clientState.status === SyncStatus.SYNCING && clientState.pendingChanges.length > 0) {
          // 根据网络质量选择同步策略
          const strategy = this.selectOptimalSyncStrategy(clientState);
          await this.performSync(sessionId, strategy);
        }
      }
    } catch (error) {
      this.logger.error('计划同步失败:', error);
    }
  }

  /**
   * 选择最优同步策略
   */
  private selectOptimalSyncStrategy(clientState: ClientSyncState): SyncStrategy {
    // 根据网络延迟和带宽选择策略
    if (clientState.networkLatency > 1000) {
      // 高延迟网络，使用增量同步
      return SyncStrategy.INCREMENTAL_SYNC;
    } else if (clientState.bandwidth < 1000000) {
      // 低带宽网络，使用Delta同步
      return SyncStrategy.DELTA_SYNC;
    } else {
      // 良好网络条件，使用乐观同步
      return SyncStrategy.OPTIMISTIC_SYNC;
    }
  }

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring(): void {
    setInterval(() => {
      this.updatePerformanceMetrics();
    }, 60000); // 每分钟更新一次

    this.logger.log('性能监控已启动');
  }

  /**
   * 启动网络质量监控
   */
  private startNetworkQualityMonitoring(): void {
    setInterval(async () => {
      await this.measureNetworkQuality();
    }, 30000); // 每30秒检测一次

    this.logger.log('网络质量监控已启动');
  }

  /**
   * 测量网络质量
   */
  private async measureNetworkQuality(): Promise<void> {
    try {
      for (const [sessionId, clientState] of this.clientStates) {
        // 发送ping包测量延迟
        const latency = await this.measureLatency(sessionId);
        clientState.networkLatency = latency;

        // 估算带宽
        const bandwidth = await this.estimateBandwidth(sessionId);
        clientState.bandwidth = bandwidth;

        // 根据网络质量调整压缩设置
        clientState.compressionEnabled = latency > 500 || bandwidth < 2000000;
      }
    } catch (error) {
      this.logger.error('测量网络质量失败:', error);
    }
  }

  /**
   * 测量延迟
   */
  private async measureLatency(sessionId: string): Promise<number> {
    try {
      const startTime = Date.now();

      // 发送ping消息
      this.eventEmitter.emit('sync.ping', { sessionId, timestamp: startTime });

      // 等待pong响应（简化实现）
      return new Promise((resolve) => {
        const timeout = setTimeout(() => resolve(5000), this.networkTimeoutMs);

        this.eventEmitter.once(`sync.pong.${sessionId}`, (data) => {
          clearTimeout(timeout);
          const latency = Date.now() - data.timestamp;
          resolve(latency);
        });
      });

    } catch (error) {
      this.logger.error('测量延迟失败:', error);
      return 5000; // 默认高延迟
    }
  }

  /**
   * 估算带宽
   */
  private async estimateBandwidth(sessionId: string): Promise<number> {
    try {
      // 简化的带宽估算：基于最近的同步包大小和传输时间
      const recentPackets = this.pendingPackets.get(sessionId) || [];

      if (recentPackets.length === 0) {
        return 1000000; // 默认1Mbps
      }

      const totalSize = recentPackets.reduce((sum, packet) => sum + packet.size, 0);
      const totalTime = recentPackets.length * this.syncInterval; // 简化计算

      return totalSize * 8 / (totalTime / 1000); // bits per second

    } catch (error) {
      this.logger.error('估算带宽失败:', error);
      return 1000000; // 默认1Mbps
    }
  }

  /**
   * 更新同步指标
   */
  private updateSyncMetrics(syncTime: number, success: boolean): void {
    this.syncMetrics.totalSyncs++;

    if (success) {
      this.syncMetrics.successfulSyncs++;
    } else {
      this.syncMetrics.failedSyncs++;
    }

    // 更新平均同步时间
    const alpha = 0.1; // 指数移动平均
    this.syncMetrics.averageSyncTime =
      this.syncMetrics.averageSyncTime * (1 - alpha) + syncTime * alpha;
  }

  /**
   * 更新性能指标
   */
  private async updatePerformanceMetrics(): Promise<void> {
    try {
      // 计算压缩比
      let totalOriginalSize = 0;
      let totalCompressedSize = 0;

      for (const packets of this.pendingPackets.values()) {
        for (const packet of packets) {
          if (packet.compressed) {
            totalCompressedSize += packet.size;
            // 估算原始大小（简化）
            totalOriginalSize += packet.size * 1.5;
          } else {
            totalOriginalSize += packet.size;
            totalCompressedSize += packet.size;
          }
        }
      }

      this.syncMetrics.compressionRatio =
        totalOriginalSize > 0 ? totalCompressedSize / totalOriginalSize : 1;

      // 保存指标到Redis
      await this.redis.setex(
        'sync:performance_metrics',
        300,
        JSON.stringify(this.syncMetrics)
      );

    } catch (error) {
      this.logger.error('更新性能指标失败:', error);
    }
  }

  /**
   * 获取同步统计信息
   */
  public getSyncStats(): any {
    return {
      activeClients: this.clientStates.size,
      totalSyncs: this.syncMetrics.totalSyncs,
      successRate: this.syncMetrics.totalSyncs > 0 ?
        this.syncMetrics.successfulSyncs / this.syncMetrics.totalSyncs : 0,
      averageSyncTime: this.syncMetrics.averageSyncTime,
      compressionRatio: this.syncMetrics.compressionRatio,
      pendingPackets: Array.from(this.pendingPackets.values()).reduce(
        (sum, packets) => sum + packets.length, 0
      )
    };
  }

  /**
   * 定期清理
   */
  @Cron(CronExpression.EVERY_30_MINUTES)
  public async performPeriodicCleanup(): Promise<void> {
    try {
      this.logger.log('开始执行定期清理');

      // 清理过期的同步包
      const cutoffTime = Date.now() - 3600000; // 1小时前

      for (const [sessionId, packets] of this.pendingPackets) {
        const activePackets = packets.filter(packet => packet.timestamp > cutoffTime);
        this.pendingPackets.set(sessionId, activePackets);
      }

      // 清理过期的变更缓冲区
      for (const [sessionId, buffer] of this.changeBuffer) {
        const activeChanges = buffer.filter(change => change.timestamp > cutoffTime);
        this.changeBuffer.set(sessionId, activeChanges);
      }

      this.logger.log('定期清理完成');

    } catch (error) {
      this.logger.error('定期清理失败:', error);
    }
  }

  /**
   * 关闭服务
   */
  public async shutdown(): Promise<void> {
    this.logger.log('正在关闭实时同步优化服务...');

    // 保存当前状态
    const currentState = {
      clientStates: Object.fromEntries(this.clientStates),
      syncMetrics: this.syncMetrics,
      pendingPacketsCount: Array.from(this.pendingPackets.values()).reduce(
        (sum, packets) => sum + packets.length, 0
      ),
      timestamp: Date.now()
    };

    await this.redis.setex(
      'sync:final_state',
      3600,
      JSON.stringify(currentState)
    );

    this.redis.disconnect();
    this.logger.log('实时同步优化服务已关闭');
  }
}
