/**
 * 路径验证器组件
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Alert,
  List,
  Tag,
  Progress,
  Space,
  Divider,
  Descriptions,
  Tooltip,
  Empty,
  Spin
} from 'antd';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  ReloadOutlined,
  BugOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

/**
 * 路径数据接口
 */
interface PathData {
  id: string;
  name: string;
  avatarId: string;
  points: PathPointData[];
  loopMode: 'none' | 'loop' | 'pingpong';
  interpolation: 'linear' | 'smooth' | 'bezier' | 'spline';
  enabled: boolean;
  totalDuration: number;
}

/**
 * 路径点数据接口
 */
interface PathPointData {
  id: string;
  position: { x: number; y: number; z: number };
  waitTime: number;
  speed: number;
  animation: string;
  triggers?: any[];
}

/**
 * 验证结果接口
 */
interface ValidationResult {
  valid: boolean;
  score: number;
  errors: ValidationIssue[];
  warnings: ValidationIssue[];
  suggestions: ValidationIssue[];
  performance: PerformanceMetrics;
}

/**
 * 验证问题接口
 */
interface ValidationIssue {
  type: 'error' | 'warning' | 'suggestion';
  code: string;
  message: string;
  pointIndex?: number;
  severity: 'low' | 'medium' | 'high';
  fixable?: boolean;
}

/**
 * 性能指标接口
 */
interface PerformanceMetrics {
  complexity: number;
  memoryUsage: number;
  renderCost: number;
  pathLength: number;
  estimatedFPS: number;
}

/**
 * 验证器属性
 */
interface PathValidatorProps {
  /** 路径数据 */
  path: PathData;
  /** 验证结果 */
  validationResult?: ValidationResult | null;
  /** 验证回调 */
  onValidate?: () => void;
}

/**
 * 路径验证器组件
 */
export const PathValidator: React.FC<PathValidatorProps> = ({
  path,
  validationResult,
  onValidate
}) => {
  const { t } = useTranslation();
  const [isValidating, setIsValidating] = useState(false);
  const [autoValidate, setAutoValidate] = useState(true);

  /**
   * 执行验证
   */
  const handleValidate = async () => {
    setIsValidating(true);
    try {
      await onValidate?.();
    } finally {
      setIsValidating(false);
    }
  };

  /**
   * 获取问题图标
   */
  const getIssueIcon = (type: string, severity: string) => {
    switch (type) {
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'warning':
        return <WarningOutlined style={{ color: '#faad14' }} />;
      case 'suggestion':
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
      default:
        return <InfoCircleOutlined />;
    }
  };

  /**
   * 获取严重程度颜色
   */
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'red';
      case 'medium':
        return 'orange';
      case 'low':
        return 'blue';
      default:
        return 'default';
    }
  };

  /**
   * 获取分数颜色
   */
  const getScoreColor = (score: number) => {
    if (score >= 80) return '#52c41a';
    if (score >= 60) return '#faad14';
    return '#ff4d4f';
  };

  /**
   * 渲染问题列表
   */
  const renderIssueList = (issues: ValidationIssue[], title: string, type: string) => {
    if (issues.length === 0) return null;

    return (
      <Card size="small" title={title} style={{ marginBottom: 16 }}>
        <List
          size="small"
          dataSource={issues}
          renderItem={(issue, index) => (
            <List.Item
              actions={[
                issue.fixable && (
                  <Button
                    type="link"
                    size="small"
                    onClick={() => {
                      // 这里可以实现自动修复功能
                      console.log('Fix issue:', issue);
                    }}
                  >
                    {t('pathValidator.fix')}
                  </Button>
                )
              ].filter(Boolean)}
            >
              <List.Item.Meta
                avatar={getIssueIcon(issue.type, issue.severity)}
                title={
                  <Space>
                    <span>{issue.message}</span>
                    <Tag color={getSeverityColor(issue.severity)}>
                      {t(`pathValidator.severity.${issue.severity}`)}
                    </Tag>
                    {issue.pointIndex !== undefined && (
                      <Tag color="blue">
                        {t('pathValidator.point')} {issue.pointIndex + 1}
                      </Tag>
                    )}
                  </Space>
                }
                description={
                  <span style={{ fontSize: '12px', color: '#666' }}>
                    {t(`pathValidator.codes.${issue.code}`)}
                  </span>
                }
              />
            </List.Item>
          )}
        />
      </Card>
    );
  };

  /**
   * 渲染性能指标
   */
  const renderPerformanceMetrics = (metrics: PerformanceMetrics) => {
    return (
      <Card title={t('pathValidator.performance')} size="small" style={{ marginBottom: 16 }}>
        <Descriptions size="small" column={2}>
          <Descriptions.Item label={t('pathValidator.complexity')}>
            <Space>
              <Progress
                type="circle"
                size={40}
                percent={metrics.complexity}
                strokeColor={getScoreColor(100 - metrics.complexity)}
                format={() => `${metrics.complexity}%`}
              />
            </Space>
          </Descriptions.Item>
          
          <Descriptions.Item label={t('pathValidator.memoryUsage')}>
            <Space>
              <span>{(metrics.memoryUsage / 1024).toFixed(2)} KB</span>
              <Tag color={metrics.memoryUsage > 1024 * 1024 ? 'red' : 'green'}>
                {metrics.memoryUsage > 1024 * 1024 ? t('pathValidator.high') : t('pathValidator.normal')}
              </Tag>
            </Space>
          </Descriptions.Item>

          <Descriptions.Item label={t('pathValidator.renderCost')}>
            <Progress
              percent={metrics.renderCost}
              strokeColor={getScoreColor(100 - metrics.renderCost)}
              size="small"
            />
          </Descriptions.Item>

          <Descriptions.Item label={t('pathValidator.pathLength')}>
            {metrics.pathLength.toFixed(2)} m
          </Descriptions.Item>

          <Descriptions.Item label={t('pathValidator.estimatedFPS')}>
            <Space>
              <span>{metrics.estimatedFPS}</span>
              <Tag color={metrics.estimatedFPS >= 60 ? 'green' : metrics.estimatedFPS >= 30 ? 'orange' : 'red'}>
                {metrics.estimatedFPS >= 60 ? t('pathValidator.excellent') : 
                 metrics.estimatedFPS >= 30 ? t('pathValidator.good') : t('pathValidator.poor')}
              </Tag>
            </Space>
          </Descriptions.Item>
        </Descriptions>
      </Card>
    );
  };

  // 自动验证
  useEffect(() => {
    if (autoValidate && path) {
      const timer = setTimeout(() => {
        handleValidate();
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [path, autoValidate]);

  if (isValidating) {
    return (
      <div style={{ textAlign: 'center', padding: '40px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          {t('pathValidator.validating')}
        </div>
      </div>
    );
  }

  if (!validationResult) {
    return (
      <div style={{ textAlign: 'center', padding: '40px' }}>
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={t('pathValidator.noResult')}
        >
          <Button type="primary" onClick={handleValidate}>
            {t('pathValidator.startValidation')}
          </Button>
        </Empty>
      </div>
    );
  }

  return (
    <div className="path-validator">
      {/* 验证概览 */}
      <Card
        title={t('pathValidator.overview')}
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleValidate}
              loading={isValidating}
            >
              {t('pathValidator.revalidate')}
            </Button>
          </Space>
        }
        style={{ marginBottom: 16 }}
      >
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Progress
            type="circle"
            percent={validationResult.score}
            strokeColor={getScoreColor(validationResult.score)}
            width={120}
            format={(percent) => (
              <div>
                <div style={{ fontSize: '24px', fontWeight: 'bold' }}>
                  {percent}
                </div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  {t('pathValidator.score')}
                </div>
              </div>
            )}
          />
        </div>

        <Alert
          type={validationResult.valid ? 'success' : 'error'}
          message={
            validationResult.valid 
              ? t('pathValidator.validPath')
              : t('pathValidator.invalidPath')
          }
          description={
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                {t('pathValidator.summary', {
                  errors: validationResult.errors.length,
                  warnings: validationResult.warnings.length,
                  suggestions: validationResult.suggestions.length
                })}
              </div>
              
              {!validationResult.valid && (
                <div style={{ color: '#ff4d4f' }}>
                  {t('pathValidator.mustFixErrors')}
                </div>
              )}
            </Space>
          }
          showIcon
        />
      </Card>

      {/* 错误列表 */}
      {renderIssueList(
        validationResult.errors,
        t('pathValidator.errors'),
        'error'
      )}

      {/* 警告列表 */}
      {renderIssueList(
        validationResult.warnings,
        t('pathValidator.warnings'),
        'warning'
      )}

      {/* 建议列表 */}
      {renderIssueList(
        validationResult.suggestions,
        t('pathValidator.suggestions'),
        'suggestion'
      )}

      {/* 性能指标 */}
      {validationResult.performance && renderPerformanceMetrics(validationResult.performance)}

      {/* 验证详情 */}
      <Card title={t('pathValidator.details')} size="small">
        <Descriptions size="small" column={1}>
          <Descriptions.Item label={t('pathValidator.pathId')}>
            {path.id}
          </Descriptions.Item>
          <Descriptions.Item label={t('pathValidator.pathName')}>
            {path.name}
          </Descriptions.Item>
          <Descriptions.Item label={t('pathValidator.pointCount')}>
            {path.points.length}
          </Descriptions.Item>
          <Descriptions.Item label={t('pathValidator.totalDuration')}>
            {path.totalDuration.toFixed(2)}s
          </Descriptions.Item>
          <Descriptions.Item label={t('pathValidator.loopMode')}>
            {t(`pathValidator.loopModes.${path.loopMode}`)}
          </Descriptions.Item>
          <Descriptions.Item label={t('pathValidator.interpolation')}>
            {t(`pathValidator.interpolations.${path.interpolation}`)}
          </Descriptions.Item>
          <Descriptions.Item label={t('pathValidator.enabled')}>
            <Tag color={path.enabled ? 'green' : 'red'}>
              {path.enabled ? t('pathValidator.yes') : t('pathValidator.no')}
            </Tag>
          </Descriptions.Item>
        </Descriptions>
      </Card>
    </div>
  );
};
