/**
 * 环境组件共享类型定义
 */

/**
 * 环境类型枚举
 */
export enum EnvironmentType {
  INDOOR = 'indoor',
  OUTDOOR = 'outdoor',
  UNDERWATER = 'underwater',
  SPACE = 'space',
  CAVE = 'cave',
  FOREST = 'forest',
  DESERT = 'desert',
  SNOW = 'snow',
  URBAN = 'urban',
  CUSTOM = 'custom'
}

/**
 * 天气类型枚举
 */
export enum WeatherType {
  CLEAR = 'clear',
  CLOUDY = 'cloudy',
  RAINY = 'rainy',
  STORMY = 'stormy',
  SNOWY = 'snowy',
  FOGGY = 'foggy',
  CUSTOM = 'custom'
}

/**
 * 地形类型枚举
 */
export enum TerrainType {
  FLAT = 'flat',
  HILLS = 'hills',
  MOUNTAINS = 'mountains',
  WATER = 'water',
  URBAN = 'urban',
  CUSTOM = 'custom'
}

/**
 * 响应类型枚举
 */
export enum ResponseType {
  ANIMATION = 'animation',
  EFFECT = 'effect',
  SOUND = 'sound',
  BEHAVIOR = 'behavior',
  CUSTOM = 'custom',
  PHYSICS = 'physics',
  POSE = 'pose',
  FACIAL = 'facial',
  DIALOGUE = 'dialogue',
  LOCOMOTION = 'locomotion',
  STATE_CHANGE = 'state_change',
  PROPERTY_CHANGE = 'property_change',
  MATERIAL_CHANGE = 'material_change',
  PARTICLE = 'particle',
  LIGHTING = 'lighting'
}

/**
 * 响应优先级枚举
 */
export enum ResponsePriority {
  LOW = 0,
  MEDIUM = 1,
  HIGH = 2,
  CRITICAL = 3
}

/**
 * 日志级别枚举
 */
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error'
}

/**
 * 环境感知数据接口（完整版本）
 */
export interface EnvironmentAwarenessData {
  // 基本环境信息
  environmentType: EnvironmentType;
  weatherType: WeatherType;
  terrainType: TerrainType;
  
  // 环境条件
  lightIntensity: number;
  temperature: number;
  humidity: number;
  windSpeed: number;
  windDirection?: { x: number; y: number; z: number };
  noiseLevel: number;
  airQuality: number;
  waterLevel: number;
  visibility: number;
  timeOfDay: number;
  
  // 扩展属性（可选）
  customParameters?: Map<string, any>;
  lastEnvironmentChangeTime?: number;
  awarenessRange?: number;
}

/**
 * 环境感知数据接口（基础版本）
 */
export interface BasicEnvironmentAwarenessData {
  environmentType: EnvironmentType;
  weatherType: WeatherType;
  terrainType: TerrainType;
  lightIntensity: number;
  temperature: number;
  humidity: number;
  windSpeed: number;
  noiseLevel: number;
  airQuality: number;
  waterLevel: number;
  visibility: number;
  timeOfDay: number;
}

/**
 * 环境条件接口
 */
export interface EnvironmentCondition {
  type: string;
  params: any;
  evaluate: (data: EnvironmentAwarenessData) => boolean;
}

/**
 * 环境动作接口
 */
export interface EnvironmentAction {
  type: string;
  params: any;
  execute: (entity: any) => void;
  stop?: (entity: any) => void;
}

/**
 * 环境响应规则接口
 */
export interface EnvironmentResponseRule {
  id: string;
  name: string;
  description?: string;
  responseType: ResponseType;
  priority: ResponsePriority;
  conditions: EnvironmentCondition[];
  actions: EnvironmentAction[];
  cooldown?: number;
  lastTriggeredTime?: number;
  enabled: boolean;
}

/**
 * 日志项接口
 */
export interface LogItem {
  id: string;
  timestamp: number;
  level: LogLevel;
  message: string;
  details?: any;
}

/**
 * 创建完整的环境感知数据
 * @param basic 基础数据
 * @returns 完整数据
 */
export function createFullEnvironmentData(basic: BasicEnvironmentAwarenessData): EnvironmentAwarenessData {
  return {
    ...basic,
    windDirection: { x: 1, y: 0, z: 0 },
    customParameters: new Map(),
    lastEnvironmentChangeTime: Date.now(),
    awarenessRange: 50
  };
}

/**
 * 创建基础的环境感知数据
 * @param full 完整数据
 * @returns 基础数据
 */
export function createBasicEnvironmentData(full: EnvironmentAwarenessData): BasicEnvironmentAwarenessData {
  return {
    environmentType: full.environmentType,
    weatherType: full.weatherType,
    terrainType: full.terrainType,
    lightIntensity: full.lightIntensity,
    temperature: full.temperature,
    humidity: full.humidity,
    windSpeed: full.windSpeed,
    noiseLevel: full.noiseLevel,
    airQuality: full.airQuality,
    waterLevel: full.waterLevel,
    visibility: full.visibility,
    timeOfDay: full.timeOfDay
  };
}
