/**
 * 反馈统计组件
 * 用于显示反馈数据的统计分析
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Tabs,
  Select,
  Button,
  Space,
  Spin,
  Empty,
  Alert,
  Typography,
  DatePicker,
  Radio
} from 'antd';
import {
  <PERSON><PERSON>hart,
  Pie,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid, Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  Cell,
  LineChart,
  Line
} from 'recharts';
import {
  UserOutlined,
  BugOutlined,
  RocketOutlined,
  StarOutlined,
  FilterOutlined,
  ReloadOutlined,
  PieChartOutlined,
  BarChartOutlined,
  LineChartOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { FeedbackService, FeedbackStats } from '../../services/FeedbackService';
import './FeedbackStatistics.less';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { Title } = Typography;

// 图表颜色
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#FF6B6B', '#6B66FF'];

export interface FeedbackStatisticsProps {
  /** 反馈类型 */
  type?: string;
  /** 反馈子类型 */
  subType?: string;
  /** 是否自动刷新 */
  autoRefresh?: boolean;
  /** 刷新间隔（毫秒） */
  refreshInterval?: number;
}

/**
 * 反馈统计组件
 */
const FeedbackStatistics: React.FC<FeedbackStatisticsProps> = ({
  type,
  subType,
  autoRefresh = false,
  refreshInterval = 60000
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<FeedbackStats | null>(null);
  const [filters, setFilters] = useState<any>({
    type: type || undefined,
    subType: subType || undefined,
    dateRange: undefined,
    feedbackType: undefined
  });
  const [activeTab, setActiveTab] = useState('overview');
  const [chartType, setChartType] = useState('pie');
  const [timeRange, setTimeRange] = useState('week');

  // 加载反馈数据
  const loadFeedbackData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 准备过滤条件
      const queryFilters: any = { ...filters };
      
      // 处理日期范围
      if (queryFilters.dateRange && queryFilters.dateRange.length === 2) {
        queryFilters.startDate = queryFilters.dateRange[0].toISOString();
        queryFilters.endDate = queryFilters.dateRange[1].toISOString();
        delete queryFilters.dateRange;
      }

      // 获取反馈统计
      const statistics = await FeedbackService.getFeedbackStats(queryFilters);
      setStats(statistics);
    } catch (error) {
      console.error('加载反馈数据失败:', error);
      setError(t('feedback.statistics.loadError'));
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadFeedbackData();

    // 设置自动刷新
    if (autoRefresh && refreshInterval > 0) {
      const intervalId = setInterval(loadFeedbackData, refreshInterval);
      return () => clearInterval(intervalId);
    }
  }, []);

  // 过滤条件变化时重新加载
  useEffect(() => {
    loadFeedbackData();
  }, [filters]);

  // 处理过滤器变化
  const handleFilterChange = (key: string, value: any) => {
    setFilters((prev: any) => ({
      ...prev,
      [key]: value
    }));
  };

  // 重置过滤器
  const resetFilters = () => {
    setFilters({
      type: type || undefined,
      subType: subType || undefined,
      dateRange: undefined,
      feedbackType: undefined
    });
  };

  // 准备饼图数据
  const preparePieData = (data: Record<string, number>) => {
    return Object.entries(data).map(([name, value]) => ({
      name: t(`feedback.type.${name}`) || name,
      value
    }));
  };

  // 准备柱状图数据
  const prepareBarData = (data: Record<string, number>) => {
    return Object.entries(data).map(([name, value]) => ({
      name: t(`feedback.type.${name}`) || name,
      value
    }));
  };

  // 准备趋势图数据
  const prepareTrendData = () => {
    if (!stats || !stats.trendData) return [];
    
    return stats.trendData.map(item => ({
      date: new Date(item.date).toLocaleDateString(),
      count: item.count,
      bugs: item.byType.bug || 0,
      features: item.byType.feature || 0,
      improvements: item.byType.improvement || 0
    }));
  };

  // 渲染过滤器
  const renderFilters = () => {
    return (
      <div className="feedback-statistics-filters">
        <Space wrap>
          <Select
            placeholder={t('feedback.statistics.filterType') || ''}
            style={{ width: 150 }}
            onChange={(value) => handleFilterChange('type', value)}
            value={filters.type}
            allowClear
          >
            <Option value="animation">{t('feedback.title.animation') || ''}</Option>
            <Option value="physics">{t('feedback.title.physics') || ''}</Option>
            <Option value="rendering">{t('feedback.title.rendering') || ''}</Option>
            <Option value="editor">{t('feedback.title.editor') || ''}</Option>
            <Option value="general">{t('feedback.title.general') || ''}</Option>
          </Select>

          <Select
            placeholder={t('feedback.statistics.filterFeedbackType') || ''}
            style={{ width: 150 }}
            onChange={(value) => handleFilterChange('feedbackType', value)}
            value={filters.feedbackType}
            allowClear
          >
            <Option value="bug">{t('feedback.type.bug') || ''}</Option>
            <Option value="feature">{t('feedback.type.feature') || ''}</Option>
            <Option value="improvement">{t('feedback.type.improvement') || ''}</Option>
            <Option value="performance">{t('feedback.type.performance') || ''}</Option>
            <Option value="usability">{t('feedback.type.usability') || ''}</Option>
            <Option value="other">{t('feedback.type.other') || ''}</Option>
          </Select>

          <RangePicker
            placeholder={[
              t('feedback.statistics.startDate') || '',
              t('feedback.statistics.endDate') || ''
            ]}
            onChange={(dates) => handleFilterChange('dateRange', dates)}
            value={filters.dateRange}
          />
          
          <Button
            icon={<ReloadOutlined />}
            onClick={loadFeedbackData}
            loading={loading}
          >
            {t('feedback.statistics.refresh') || ''}
          </Button>

          <Button
            icon={<FilterOutlined />}
            onClick={resetFilters}
          >
            {t('feedback.statistics.resetFilters') || ''}
          </Button>
        </Space>
      </div>
    );
  };

  // 渲染统计卡片
  const renderStatCards = () => {
    if (!stats) return null;

    return (
      <Row gutter={[16, 16]} className="stat-cards">
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={t('feedback.statistics.stats.total') || ''}
              value={stats.total}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={t('feedback.statistics.stats.bugs') || ''}
              value={stats.byFeedbackType.bug || 0}
              prefix={<BugOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={t('feedback.statistics.stats.features') || ''}
              value={stats.byFeedbackType.feature || 0}
              prefix={<RocketOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={t('feedback.statistics.stats.satisfaction') || ''}
              value={stats.averageSatisfaction.toFixed(1)}
              prefix={<StarOutlined />}
              suffix="/ 5"
              precision={1}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染图表
  const renderCharts = () => {
    if (!stats) return null;

    return (
      <div className="feedback-statistics-charts">
        <div className="chart-controls">
          <Space>
            <span>{t('feedback.statistics.chartType') || ''}:</span>
            <Radio.Group value={chartType} onChange={(e) => setChartType(e.target.value)}>
              <Radio.Button value="pie">
                <PieChartOutlined /> {t('feedback.statistics.pieChart') || ''}
              </Radio.Button>
              <Radio.Button value="bar">
                <BarChartOutlined /> {t('feedback.statistics.barChart') || ''}
              </Radio.Button>
            </Radio.Group>
          </Space>
        </div>

        <Row gutter={[16, 16]} className="charts">
          <Col xs={24} md={12}>
            <Card title={t('feedback.statistics.charts.byType') || ''}>
              {chartType === 'pie' ? (
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={preparePieData(stats.byFeedbackType)}
                      cx="50%"
                      cy="50%"
                      labelLine={true}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {preparePieData(stats.byFeedbackType).map((_, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <RechartsTooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              ) : (
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart
                    data={prepareBarData(stats.byFeedbackType)}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <RechartsTooltip />
                    <Bar dataKey="value" fill="#8884d8">
                      {prepareBarData(stats.byFeedbackType).map((_, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              )}
            </Card>
          </Col>
          
          <Col xs={24} md={12}>
            <Card title={t('feedback.statistics.charts.bySubType') || ''}>
              {chartType === 'pie' ? (
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={preparePieData(stats.bySubType)}
                      cx="50%"
                      cy="50%"
                      labelLine={true}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {preparePieData(stats.bySubType).map((_, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <RechartsTooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              ) : (
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart
                    data={prepareBarData(stats.bySubType)}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <RechartsTooltip />
                    <Bar dataKey="value" fill="#82ca9d">
                      {prepareBarData(stats.bySubType).map((_, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              )}
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染趋势图
  const renderTrendChart = () => {
    if (!stats || !stats.trendData) return null;
    
    return (
      <Card title={t('feedback.statistics.charts.trend') || ''}>
        <div className="chart-controls">
          <Space>
            <span>{t('feedback.statistics.timeRange') || ''}:</span>
            <Radio.Group value={timeRange} onChange={(e) => setTimeRange(e.target.value)}>
              <Radio.Button value="week">
                {t('feedback.statistics.week') || ''}
              </Radio.Button>
              <Radio.Button value="month">
                {t('feedback.statistics.month') || ''}
              </Radio.Button>
              <Radio.Button value="year">
                {t('feedback.statistics.year') || ''}
              </Radio.Button>
            </Radio.Group>
          </Space>
        </div>

        <ResponsiveContainer width="100%" height={300}>
          <LineChart
            data={prepareTrendData()}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <RechartsTooltip />
            <Legend />
            <Line type="monotone" dataKey="count" stroke="#8884d8" name={t('feedback.statistics.totalCount') || ''} />
            <Line type="monotone" dataKey="bugs" stroke="#ff0000" name={t('feedback.statistics.bugs') || ''} />
            <Line type="monotone" dataKey="features" stroke="#00ff00" name={t('feedback.statistics.features') || ''} />
            <Line type="monotone" dataKey="improvements" stroke="#0000ff" name={t('feedback.statistics.improvements') || ''} />
          </LineChart>
        </ResponsiveContainer>
      </Card>
    );
  };

  // 渲染内容
  const renderContent = () => {
    if (loading) {
      return (
        <div className="loading-container">
          <Spin size="large" />
          <p>{t('feedback.statistics.loading') || ''}</p>
        </div>
      );
    }

    if (error) {
      return (
        <Alert
          message={t('feedback.statistics.error') || ''}
          description={error}
          type="error"
          showIcon
        />
      );
    }

    if (!stats) {
      return (
        <Empty description={t('feedback.statistics.noData') || ''} />
      );
    }

    return (
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <StarOutlined />
              {t('feedback.statistics.tabs.overview') || ''}
            </span>
          }
          key="overview"
        >
          {renderStatCards()}
          {renderCharts()}
        </TabPane>
        <TabPane
          tab={
            <span>
              <LineChartOutlined />
              {t('feedback.statistics.tabs.trends') || ''}
            </span>
          }
          key="trends"
        >
          {renderTrendChart()}
        </TabPane>
      </Tabs>
    );
  };

  return (
    <div className="feedback-statistics">
      <div className="feedback-statistics-header">
        <Title level={4}>{t('feedback.statistics.title') || ''}</Title>
        {renderFilters()}
      </div>
      <div className="feedback-statistics-content">
        {renderContent()}
      </div>
    </div>
  );
};

export default FeedbackStatistics;
