/**
 * 行为树编辑器样式
 */

.behavior-tree-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.behavior-tree-toolbar {
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #d9d9d9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.editor-layout {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.left-panel {
  width: 280px;
  background: #fff;
  border-right: 1px solid #d9d9d9;
  overflow-y: auto;
}

.center-panel {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.right-panel {
  width: 320px;
  background: #fff;
  border-left: 1px solid #d9d9d9;
  overflow-y: auto;
}

/* 节点面板 */
.node-palette {
  margin: 16px;
}

.node-palette-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin: 8px 0;
  border: 2px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;
  cursor: grab;
  transition: all 0.2s ease;
}

.node-palette-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.node-palette-item.dragging {
  cursor: grabbing;
  transform: rotate(5deg);
}

.node-icon {
  font-size: 18px;
  font-weight: bold;
  margin-right: 12px;
  width: 24px;
  text-align: center;
}

.node-info {
  flex: 1;
}

.node-name {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 4px;
}

.node-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

/* 画布 */
.behavior-tree-canvas {
  width: 100%;
  height: 100%;
  position: relative;
  background: 
    radial-gradient(circle, #ddd 1px, transparent 1px);
  background-size: 20px 20px;
  overflow: auto;
}

/* 行为树节点 */
.behavior-node {
  position: absolute;
  width: 200px;
  min-height: 80px;
  background: #fff;
  border: 2px solid #d9d9d9;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.behavior-node:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.behavior-node.selected {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.behavior-node.executing {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.node-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  color: #fff;
  font-weight: 500;
  border-radius: 6px 6px 0 0;
  position: relative;
}

.node-header .node-icon {
  margin-right: 8px;
  font-size: 16px;
}

.node-title {
  flex: 1;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.node-delete-btn {
  color: rgba(255, 255, 255, 0.8) !important;
  border: none !important;
  background: transparent !important;
  padding: 0 !important;
  width: 20px !important;
  height: 20px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.node-delete-btn:hover {
  color: #fff !important;
  background: rgba(255, 255, 255, 0.2) !important;
}

.node-body {
  padding: 12px;
}

.node-type {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.node-status {
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

/* 连接线 */
.connection-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.behavior-node {
  z-index: 2;
}

/* 属性面板 */
.properties-panel {
  margin: 16px;
}

.no-selection {
  text-align: center;
  color: #999;
  padding: 40px 20px;
  font-style: italic;
}

/* 黑板调试 */
.blackboard-debug {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .left-panel {
    width: 240px;
  }
  
  .right-panel {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .editor-layout {
    flex-direction: column;
  }
  
  .left-panel,
  .right-panel {
    width: 100%;
    height: 200px;
  }
  
  .center-panel {
    height: 400px;
  }
}

/* 滚动条样式 */
.left-panel::-webkit-scrollbar,
.right-panel::-webkit-scrollbar,
.blackboard-debug::-webkit-scrollbar {
  width: 6px;
}

.left-panel::-webkit-scrollbar-track,
.right-panel::-webkit-scrollbar-track,
.blackboard-debug::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.left-panel::-webkit-scrollbar-thumb,
.right-panel::-webkit-scrollbar-thumb,
.blackboard-debug::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.left-panel::-webkit-scrollbar-thumb:hover,
.right-panel::-webkit-scrollbar-thumb:hover,
.blackboard-debug::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
