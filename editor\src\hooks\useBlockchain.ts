/**
 * 区块链Hook - 提供区块链相关的状态管理和操作
 */

import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
// 临时的 EngineService 实现
const EngineService = {
  getEngine: () => ({
    getSystem: (_name: string) => ({
      connect: async (_network: any) => { console.log('连接区块链网络'); },
      disconnect: async () => { console.log('断开区块链连接'); },
      getBalance: async (_address: string) => '0',
      sendTransaction: async (_tx: any) => 'mock-tx-hash',
      getBlockchainState: () => ({
        isConnected: false,
        currentNetwork: null,
        walletAddress: null,
        currentAccount: null,
        balance: '0'
      }),
      connectWallet: async (_walletType?: string) => true,
      disconnectWallet: async () => {},
      switchNetwork: async (_network: any) => true,
      deployContract: async (_contract: any) => 'mock-contract-address',
      callContract: async (_address: string, _method: string, _params: any[]) => 'mock-result',
      getTransactionHistory: async (_address: string) => [],
      subscribeToEvents: (_callback: Function) => () => {},
      getBlockchainManager: () => ({
        switchNetwork: async (_network: any) => ({
          success: true,
          error: { message: '切换失败' }
        }),
        getWalletManager: () => ({
          getBalance: async (_account: string) => '0',
          signMessage: async (_message: string) => ({
            success: true,
            data: 'mock-signature',
            error: { message: '签名失败' }
          })
        }),
        sendTransaction: async (_transaction: any) => ({
          success: true,
          transactionHash: 'mock-hash',
          error: { message: '发送失败' }
        })
      })
    })
  })
};

export interface BlockchainNetwork {
  chainId: number;
  name: string;
  rpcUrl: string;
  blockExplorer: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
  testnet?: boolean;
}

export interface BlockchainState {
  isConnected: boolean;
  currentAccount: string | null;
  currentNetwork: BlockchainNetwork | null;
  balance: string;
  isLoading: boolean;
  error: string | null;
}

export interface UseBlockchainReturn extends BlockchainState {
  connectWallet: (walletType?: string) => Promise<void>;
  disconnectWallet: () => Promise<void>;
  switchNetwork: (networkId: string) => Promise<void>;
  refreshBalance: () => Promise<void>;
  sendTransaction: (transaction: any) => Promise<string>;
  signMessage: (message: string) => Promise<string>;
}

// 支持的网络配置
const SUPPORTED_NETWORKS: Record<string, BlockchainNetwork> = {
  ethereum: {
    chainId: 1,
    name: 'Ethereum Mainnet',
    rpcUrl: 'https://mainnet.infura.io/v3/YOUR_PROJECT_ID',
    blockExplorer: 'https://etherscan.io',
    nativeCurrency: {
      name: 'Ether',
      symbol: 'ETH',
      decimals: 18
    }
  },
  polygon: {
    chainId: 137,
    name: 'Polygon Mainnet',
    rpcUrl: 'https://polygon-rpc.com',
    blockExplorer: 'https://polygonscan.com',
    nativeCurrency: {
      name: 'MATIC',
      symbol: 'MATIC',
      decimals: 18
    }
  },
  goerli: {
    chainId: 5,
    name: 'Goerli Testnet',
    rpcUrl: 'https://goerli.infura.io/v3/YOUR_PROJECT_ID',
    blockExplorer: 'https://goerli.etherscan.io',
    nativeCurrency: {
      name: 'Goerli Ether',
      symbol: 'GoerliETH',
      decimals: 18
    },
    testnet: true
  }
};

export const useBlockchain = (): UseBlockchainReturn => {
  const [state, setState] = useState<BlockchainState>({
    isConnected: false,
    currentAccount: null,
    currentNetwork: null,
    balance: '0',
    isLoading: false,
    error: null
  });

  // 获取区块链系统
  const getBlockchainSystem = useCallback(() => {
    const engine = EngineService.getEngine();
    return engine?.getSystem('BlockchainSystem');
  }, []);

  // 初始化区块链连接检查
  useEffect(() => {
    checkExistingConnection();
  }, []);

  // 检查现有连接
  const checkExistingConnection = useCallback(async () => {
    try {
      const blockchainSystem = getBlockchainSystem();
      if (!blockchainSystem) return;

      const blockchainState = blockchainSystem.getBlockchainState();
      if (blockchainState.isConnected) {
        setState(prev => ({
          ...prev,
          isConnected: true,
          currentAccount: blockchainState.currentAccount,
          currentNetwork: blockchainState.currentNetwork,
          balance: blockchainState.balance
        }));
      }
    } catch (error) {
      console.error('检查现有连接失败:', error);
    }
  }, [getBlockchainSystem]);

  // 连接钱包
  const connectWallet = useCallback(async (walletType?: string) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const blockchainSystem = getBlockchainSystem();
      if (!blockchainSystem) {
        throw new Error('区块链系统未初始化');
      }

      const success = await blockchainSystem.connectWallet(walletType);
      if (success) {
        const blockchainState = blockchainSystem.getBlockchainState();
        setState(prev => ({
          ...prev,
          isConnected: true,
          currentAccount: blockchainState.currentAccount,
          currentNetwork: blockchainState.currentNetwork,
          balance: blockchainState.balance,
          isLoading: false
        }));

        message.success('钱包连接成功！');
      } else {
        throw new Error('钱包连接失败');
      }
    } catch (error: any) {
      const errorMessage = error.message || '连接钱包失败';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));
      message.error(errorMessage);
      throw error;
    }
  }, [getBlockchainSystem]);

  // 断开钱包连接
  const disconnectWallet = useCallback(async () => {
    try {
      const blockchainSystem = getBlockchainSystem();
      if (blockchainSystem) {
        await blockchainSystem.disconnectWallet();
      }

      setState({
        isConnected: false,
        currentAccount: null,
        currentNetwork: null,
        balance: '0',
        isLoading: false,
        error: null
      });

      message.success('钱包已断开连接');
    } catch (error: any) {
      const errorMessage = error.message || '断开连接失败';
      setState(prev => ({ ...prev, error: errorMessage }));
      message.error(errorMessage);
      throw error;
    }
  }, [getBlockchainSystem]);

  // 切换网络
  const switchNetwork = useCallback(async (networkId: string) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const network = SUPPORTED_NETWORKS[networkId];
      if (!network) {
        throw new Error('不支持的网络');
      }

      const blockchainSystem = getBlockchainSystem();
      if (!blockchainSystem) {
        throw new Error('区块链系统未初始化');
      }

      const blockchainManager = blockchainSystem.getBlockchainManager();
      const result = await blockchainManager.switchNetwork(network);

      if (result.success) {
        setState(prev => ({
          ...prev,
          currentNetwork: network,
          isLoading: false
        }));

        // 刷新余额
        await refreshBalance();
        message.success(`已切换到 ${network.name}`);
      } else {
        throw new Error(result.error?.message || '切换网络失败');
      }
    } catch (error: any) {
      const errorMessage = error.message || '切换网络失败';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));
      message.error(errorMessage);
      throw error;
    }
  }, [getBlockchainSystem]);

  // 刷新余额
  const refreshBalance = useCallback(async () => {
    if (!state.isConnected || !state.currentAccount) return;

    try {
      const blockchainSystem = getBlockchainSystem();
      if (!blockchainSystem) return;

      const blockchainManager = blockchainSystem.getBlockchainManager();
      const walletManager = blockchainManager.getWalletManager();
      const balance = await walletManager.getBalance(state.currentAccount);

      setState(prev => ({ ...prev, balance }));
    } catch (error) {
      console.error('刷新余额失败:', error);
    }
  }, [state.isConnected, state.currentAccount, getBlockchainSystem]);

  // 发送交易
  const sendTransaction = useCallback(async (transaction: any): Promise<string> => {
    if (!state.isConnected) {
      throw new Error('钱包未连接');
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const blockchainSystem = getBlockchainSystem();
      if (!blockchainSystem) {
        throw new Error('区块链系统未初始化');
      }

      const blockchainManager = blockchainSystem.getBlockchainManager();
      const result = await blockchainManager.sendTransaction(transaction);

      if (result.success && result.transactionHash) {
        setState(prev => ({ ...prev, isLoading: false }));
        message.success('交易已发送');
        return result.transactionHash;
      } else {
        throw new Error(result.error?.message || '发送交易失败');
      }
    } catch (error: any) {
      const errorMessage = error.message || '发送交易失败';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));
      message.error(errorMessage);
      throw error;
    }
  }, [state.isConnected, getBlockchainSystem]);

  // 签名消息
  const signMessage = useCallback(async (messageToSign: string): Promise<string> => {
    if (!state.isConnected) {
      throw new Error('钱包未连接');
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const blockchainSystem = getBlockchainSystem();
      if (!blockchainSystem) {
        throw new Error('区块链系统未初始化');
      }

      const blockchainManager = blockchainSystem.getBlockchainManager();
      const walletManager = blockchainManager.getWalletManager();
      const result = await walletManager.signMessage(messageToSign);

      if (result.success && result.data) {
        setState(prev => ({ ...prev, isLoading: false }));
        message.success('消息签名成功');
        return result.data;
      } else {
        throw new Error(result.error?.message || '签名失败');
      }
    } catch (error: any) {
      const errorMessage = error.message || '签名失败';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));
      message.error(errorMessage);
      throw error;
    }
  }, [state.isConnected, getBlockchainSystem]);

  return {
    ...state,
    connectWallet,
    disconnectWallet,
    switchNetwork,
    refreshBalance,
    sendTransaction,
    signMessage
  };
};
