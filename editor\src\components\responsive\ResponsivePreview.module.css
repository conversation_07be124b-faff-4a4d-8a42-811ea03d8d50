/**
 * ResponsivePreview.module.css
 * 
 * 响应式设计预览组件样式
 */

.responsive-preview {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--background-color, #ffffff);
  border: 1px solid var(--border-color, #d9d9d9);
  border-radius: 8px;
  overflow: hidden;
}

/* 工具栏 */
.preview-toolbar {
  padding: 12px 16px;
  background: var(--surface-color, #fafafa);
  border-bottom: 1px solid var(--border-color, #d9d9d9);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  flex-wrap: wrap;
  gap: 8px;
}

.preview-toolbar :global(.ant-btn) {
  display: flex;
  align-items: center;
  border-radius: 6px;
}

.preview-toolbar :global(.ant-dropdown-trigger) {
  min-width: 140px;
  justify-content: flex-start;
}

.preview-toolbar :global(.ant-divider-vertical) {
  height: 24px;
  margin: 0 8px;
}

.preview-toolbar :global(.ant-slider) {
  margin: 0;
}

.preview-toolbar :global(.ant-switch) {
  margin: 0 4px;
}

.size-display {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  color: var(--text-color-secondary, #8c8c8c);
  padding: 4px 8px;
  background: var(--background-color, #ffffff);
  border: 1px solid var(--border-color, #d9d9d9);
  border-radius: 4px;
}

/* 预览区域 */
.preview-area {
  flex: 1;
  position: relative;
  overflow: auto;
  background: var(--preview-background, #f0f2f5);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

/* 标尺 */
.ruler {
  position: absolute;
  background: var(--ruler-background, #ffffff);
  border: 1px solid var(--border-color, #d9d9d9);
  z-index: 100;
}

.ruler-horizontal {
  top: 0;
  left: 40px;
  right: 0;
  height: 30px;
  border-bottom: 1px solid var(--border-color, #d9d9d9);
}

.ruler-vertical {
  top: 30px;
  left: 0;
  bottom: 0;
  width: 40px;
  border-right: 1px solid var(--border-color, #d9d9d9);
}

.ruler-mark {
  position: absolute;
  font-size: 10px;
  color: var(--text-color-secondary, #8c8c8c);
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.ruler-horizontal .ruler-mark {
  border-left: 1px solid var(--ruler-mark-color, #d9d9d9);
  height: 100%;
  padding-left: 4px;
  display: flex;
  align-items: center;
}

.ruler-vertical .ruler-mark {
  border-top: 1px solid var(--ruler-mark-color, #d9d9d9);
  width: 100%;
  padding-top: 4px;
  text-align: center;
  writing-mode: vertical-lr;
  text-orientation: mixed;
}

/* 设备框架 */
.device-frame {
  position: relative;
  background: var(--device-background, #ffffff);
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  transition: all 0.3s ease;
}

.device-frame.with-frame {
  padding: 20px;
  background: var(--device-frame-color, #2c2c2c);
}

.device-frame.with-frame.mobile {
  border-radius: 24px;
  padding: 40px 20px;
}

.device-frame.with-frame.mobile::before {
  content: '';
  position: absolute;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 6px;
  background: var(--device-notch-color, #000000);
  border-radius: 3px;
}

.device-frame.with-frame.mobile::after {
  content: '';
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 4px;
  background: var(--device-home-indicator, #ffffff);
  border-radius: 2px;
  opacity: 0.6;
}

.device-frame.with-frame.tablet {
  border-radius: 16px;
  padding: 30px 20px;
}

.device-frame.with-frame.laptop {
  border-radius: 12px 12px 0 0;
  padding: 20px 20px 0;
  background: linear-gradient(to bottom, var(--device-frame-color, #2c2c2c) 0%, var(--device-frame-color, #2c2c2c) 90%, #1a1a1a 100%);
}

.device-frame.with-frame.laptop::after {
  content: '';
  position: absolute;
  bottom: -20px;
  left: -10px;
  right: -10px;
  height: 20px;
  background: #1a1a1a;
  border-radius: 0 0 20px 20px;
}

.device-frame.with-frame.desktop {
  border-radius: 8px;
  padding: 30px 20px 40px;
  background: var(--device-frame-color, #2c2c2c);
}

.device-frame.with-frame.desktop::after {
  content: '';
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 20px;
  background: #1a1a1a;
  border-radius: 10px;
}

/* 设备内容 */
.device-content {
  width: 100%;
  height: 100%;
  background: var(--background-color, #ffffff);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.device-frame.with-frame .device-content {
  border-radius: 8px;
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* 设备信息 */
.device-info {
  position: absolute;
  top: -30px;
  left: 0;
  background: var(--info-background, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.device-frame:hover .device-info {
  opacity: 1;
}

/* 断点指示器 */
.breakpoint-indicators {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  pointer-events: none;
  z-index: 50;
}

.breakpoint-indicator {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--breakpoint-color, #ff4d4f);
  opacity: 0.3;
  transition: opacity 0.2s ease;
}

.breakpoint-indicator.active {
  opacity: 0.8;
}

.breakpoint-indicator::before {
  content: attr(data-name);
  position: absolute;
  top: 10px;
  left: 4px;
  background: var(--breakpoint-color, #ff4d4f);
  color: white;
  padding: 2px 4px;
  border-radius: 2px;
  font-size: 10px;
  font-weight: 500;
  white-space: nowrap;
}

/* 下拉菜单样式 */
.responsive-preview :global(.ant-dropdown-menu) {
  max-height: 400px;
  overflow-y: auto;
}

.responsive-preview :global(.ant-dropdown-menu-submenu-title) {
  padding: 8px 12px;
}

.responsive-preview :global(.ant-dropdown-menu-item) {
  padding: 8px 12px;
}

.responsive-preview :global(.ant-dropdown-menu-item.selected) {
  background: var(--primary-background, #e6f7ff);
  color: var(--primary-color, #1890ff);
}

/* 徽章样式 */
.responsive-preview :global(.ant-badge) {
  font-size: 10px;
}

.responsive-preview :global(.ant-badge-count) {
  background: var(--info-color, #1890ff);
  color: white;
  font-size: 10px;
  height: 16px;
  line-height: 16px;
  min-width: 16px;
  padding: 0 4px;
  border-radius: 8px;
}

/* 暗色主题 */
[data-theme="dark"] .responsive-preview {
  --background-color: #141414;
  --surface-color: #1f1f1f;
  --border-color: #434343;
  --text-color-secondary: #a6a6a6;
  --preview-background: #0f1419;
  --ruler-background: #262626;
  --ruler-mark-color: #434343;
  --device-background: #1f1f1f;
  --device-frame-color: #0a0a0a;
  --device-notch-color: #ffffff;
  --device-home-indicator: #434343;
  --info-background: rgba(255, 255, 255, 0.9);
  --breakpoint-color: #d32029;
  --primary-color: #177ddc;
  --primary-background: #111b26;
  --info-color: #177ddc;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .preview-toolbar {
    padding: 8px 12px;
  }
  
  .preview-area {
    padding: 20px;
  }
  
  .preview-toolbar :global(.ant-space) {
    flex-wrap: wrap;
    gap: 4px;
  }
}

@media (max-width: 768px) {
  .preview-toolbar {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .preview-toolbar :global(.ant-space) {
    justify-content: center;
  }
  
  .preview-area {
    padding: 10px;
  }
  
  .ruler-horizontal,
  .ruler-vertical {
    display: none;
  }
  
  .device-frame.with-frame {
    padding: 10px;
  }
}

/* 全屏模式 */
.responsive-preview:fullscreen {
  border: none;
  border-radius: 0;
}

.responsive-preview:fullscreen .preview-area {
  background: var(--fullscreen-background, #000000);
}

.responsive-preview:fullscreen .device-frame {
  box-shadow: 0 0 50px rgba(255, 255, 255, 0.1);
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .responsive-preview {
    border-width: 2px;
  }
  
  .device-frame {
    border: 2px solid var(--border-color, #d9d9d9);
  }
  
  .breakpoint-indicator {
    width: 3px;
    opacity: 1;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .device-frame,
  .device-info,
  .breakpoint-indicator {
    transition: none;
  }
}

/* 自定义滚动条 */
.preview-area::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.preview-area::-webkit-scrollbar-track {
  background: var(--scrollbar-track-color, #f1f1f1);
  border-radius: 4px;
}

.preview-area::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-color, #c1c1c1);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.preview-area::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-color, #a8a8a8);
}

/* 性能优化 */
.responsive-preview {
  contain: layout style paint;
}

.device-frame {
  contain: layout style paint;
  will-change: transform;
}

.device-content {
  contain: layout style paint;
  will-change: transform;
}

/* 打印样式 */
@media print {
  .preview-toolbar {
    display: none;
  }
  
  .responsive-preview {
    border: none;
    height: auto;
  }
  
  .preview-area {
    overflow: visible;
    height: auto;
    padding: 0;
  }
  
  .ruler-horizontal,
  .ruler-vertical,
  .breakpoint-indicators {
    display: none;
  }
}
