/**
 * 口型编辑器样式
 */

.viseme-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.viseme-editor-content {
  display: flex;
  flex-direction: column;
  padding: 16px;
  gap: 16px;
}

.viseme-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-group label {
  font-weight: 500;
  color: #262626;
  white-space: nowrap;
}

.viseme-preview {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.viseme-preview h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.preview-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 200px;
  height: 200px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.preview-placeholder {
  color: #8c8c8c;
  font-size: 14px;
}

.preview-description {
  font-size: 14px;
  color: #595959;
  text-align: center;
}

.sequence-editor-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.sequence-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.sequence-timeline {
  display: flex;
  flex-direction: column;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;
  min-height: 200px;
}

.timeline-header {
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.timeline-scale {
  display: flex;
  position: relative;
  height: 30px;
}

.timeline-marker {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
}

.marker-line {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 1px;
  background: #d9d9d9;
}

.marker-label {
  position: absolute;
  left: 4px;
  font-size: 12px;
  color: #8c8c8c;
}

.timeline-content {
  flex: 1;
  position: relative;
}

.audio-waveform {
  height: 50px;
  border-bottom: 1px solid #f0f0f0;
  background: #f9f9f9;
}

.audio-waveform canvas {
  width: 100%;
  height: 100%;
}

.viseme-sequence {
  position: relative;
  height: 60px;
  padding: 8px;
}

.sequence-item {
  position: absolute;
  top: 8px;
  bottom: 8px;
  background: #1890ff;
  border: 1px solid #40a9ff;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  min-width: 20px;
}

.sequence-item:hover {
  background: #40a9ff;
  border-color: #1890ff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sequence-item.selected {
  background: #722ed1;
  border-color: #9254de;
  box-shadow: 0 0 0 2px rgba(114, 46, 209, 0.2);
}

.item-label {
  color: #fff;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 4px;
}

.sequence-item-editor {
  padding: 16px;
  background: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  margin-top: 16px;
}

.sequence-item-editor h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.item-properties {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.property-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.property-group label {
  font-weight: 500;
  color: #262626;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .viseme-controls {
    flex-wrap: wrap;
    gap: 12px;
  }
  
  .sequence-controls {
    flex-wrap: wrap;
    gap: 12px;
  }
  
  .item-properties {
    flex-direction: column;
    align-items: stretch;
  }
  
  .property-group {
    justify-content: space-between;
  }
}

/* 暗色主题 */
.viseme-editor.dark {
  background: #141414;
  border-color: #434343;
  color: #fff;
}

.viseme-editor.dark .viseme-controls {
  background: #1f1f1f;
  border-color: #434343;
}

.viseme-editor.dark .sequence-controls {
  background: #1f1f1f;
  border-color: #434343;
}

.viseme-editor.dark .sequence-timeline {
  background: #262626;
  border-color: #434343;
}

.viseme-editor.dark .timeline-header {
  background: #1f1f1f;
  border-color: #434343;
}

.viseme-editor.dark .audio-waveform {
  background: #1f1f1f;
  border-color: #434343;
}

.viseme-editor.dark .sequence-item-editor {
  background: #262626;
  border-color: #434343;
}

.viseme-editor.dark .preview-image-container {
  background: #262626;
  border-color: #434343;
}

.viseme-editor.dark .marker-line {
  background: #434343;
}

.viseme-editor.dark .marker-label {
  color: #8c8c8c;
}
