/**
 * 资源依赖可视化面板
 * 用于在编辑器中显示资源依赖关系
 */
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, Row, Col, Select, Button, Space, Empty, Spin, Tabs, Radio, Switch, Input, Tag, Divider, message, Drawer, List, Badge, Checkbox, Slider } from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  FullscreenOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  SettingOutlined,
  WarningOutlined,
  ExportOutlined,
  BranchesOutlined,
  ApartmentOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import ReactFlow, {
  Background,
  Controls,
  MiniMap,
  Node,
  ReactFlowProvider,
  useNodesState,
  useEdgesState,
  Panel,
  useReactFlow
} from 'reactflow';
import 'reactflow/dist/style.css';
import { useTranslation } from 'react-i18next';
import type { DependencyType } from '../../libs/dl-engine-types';
import { ResourceDependencyAnalyzer, OptimizationSuggestion } from './ResourceDependencyAnalyzer';
import './ResourceDependencyVisualizerPanel.less';

const { TabPane } = Tabs;
const { Option } = Select;

// 本地枚举常量，对应 DependencyType
const DependencyTypeValues = {
  STRONG: 'strong' as const,
  WEAK: 'weak' as const
} as const;

// 自定义节点类型
const ResourceNode = ({ data }: { data: any }) => {
  const nodeStyle = {
    background: data.type === 'texture' ? '#91d5ff' : 
                data.type === 'model' ? '#b7eb8f' : 
                data.type === 'material' ? '#ffd666' : 
                data.type === 'audio' ? '#adc6ff' : 
                data.type === 'shader' ? '#ffadd2' : '#d9d9d9',
    border: data.selected ? '2px solid #1890ff' : '1px solid #d9d9d9',
    borderRadius: '4px',
    padding: '10px',
    width: '150px'};

  return (
    <div style={nodeStyle}>
      <div style={{ fontWeight: 'bold', marginBottom: '5px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
        {data.label}
      </div>
      <div style={{ fontSize: '12px', color: '#666' }}>
        {data.type}
      </div>
      {data.size && (
        <div style={{ fontSize: '12px', color: '#666' }}>
          {formatBytes(data.size)}
        </div>
      )}
      {data.warning && (
        <div style={{ marginTop: '5px' }}>
          <Tag color="warning" icon={<WarningOutlined />}>
            {data.warning}
          </Tag>
        </div>
      )}
    </div>
  );
};

// 格式化字节大小
const formatBytes = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

// ReactFlow控制组件（需要在ReactFlowProvider内部使用）
const ReactFlowControls: React.FC<{
  onRefresh: () => void;
  t: (key: string) => string;
}> = ({ onRefresh, t }) => {
  const reactFlowInstance = useReactFlow();

  return (
    <Panel position="top-right">
      <div className="graph-controls">
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={onRefresh}
            title={t('resources.dependency.refresh')}
          />
          <Button
            icon={<ZoomInOutlined />}
            onClick={() => reactFlowInstance.zoomIn()}
            title={t('resources.dependency.zoomIn')}
          />
          <Button
            icon={<ZoomOutOutlined />}
            onClick={() => reactFlowInstance.zoomOut()}
            title={t('resources.dependency.zoomOut')}
          />
          <Button
            icon={<FullscreenOutlined />}
            onClick={() => reactFlowInstance.fitView()}
            title={t('resources.dependency.fitView')}
          />
          <Button
            icon={<SettingOutlined />}
            onClick={() => {/* 设置功能将在父组件中处理 */}}
            title={t('resources.dependency.settings')}
          />
        </Space>
      </div>
    </Panel>
  );
};

// 组件属性接口
interface ResourceDependencyVisualizerPanelProps {
  resourceId?: string;
  resourceType?: string;
  showAllResources?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
  showDetails?: boolean;
  showWarnings?: boolean;
  showOptimizationSuggestions?: boolean;
  className?: string;
  onClose?: () => void;
}

/**
 * 资源依赖可视化面板组件
 */
const ResourceDependencyVisualizerPanel: React.FC<ResourceDependencyVisualizerPanelProps> = ({
  resourceId,
  resourceType,
  showAllResources = false,
  autoRefresh = false,
  refreshInterval = 5000,
  showDetails = true,
  showWarnings = true,
  showOptimizationSuggestions = true,
  className,
  onClose
}) => {
  const { t } = useTranslation();
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedResource, setSelectedResource] = useState<string | null>(resourceId || null);
  const [searchValue, setSearchValue] = useState<string>('');
  const [filterType, setFilterType] = useState<string | null>(null);
  const [showAllResourcesState, setShowAllResourcesState] = useState<boolean>(showAllResources);
  const [dependencyTypes, setDependencyTypes] = useState<DependencyType[]>([
    DependencyTypeValues.STRONG,
    DependencyTypeValues.WEAK
  ] as DependencyType[]);
  const [layoutType, setLayoutType] = useState<'dagre' | 'force' | 'radial'>('dagre');
  const [showMiniMap, setShowMiniMap] = useState<boolean>(true);
  const [showControls, setShowControls] = useState<boolean>(true);
  const [showGrid, setShowGrid] = useState<boolean>(true);
  const [analysisResults, setAnalysisResults] = useState<any>(null);
  const [optimizationSuggestions, setOptimizationSuggestions] = useState<OptimizationSuggestion[]>([]);
  const [activeTab, setActiveTab] = useState<string>('graph');
  const [refreshTimerId, setRefreshTimerId] = useState<number | null>(null);
  const [resourceOptions, setResourceOptions] = useState<{ label: string; value: string; type: string }[]>([]);
  const [detailDrawerVisible, setDetailDrawerVisible] = useState<boolean>(false);
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [selectedNodeData, setSelectedNodeData] = useState<any>(null);
  const [settingsDrawerVisible, setSettingsDrawerVisible] = useState<boolean>(false);
  const [maxDepth, setMaxDepth] = useState<number>(10);

  // 依赖分析器
  const analyzer = useMemo(() => new ResourceDependencyAnalyzer(), []);

  // 自定义节点类型
  const nodeTypes = useMemo(() => ({ resourceNode: ResourceNode }), []);

  // 使用传入的props但避免未使用警告
  console.debug('ResourceDependencyVisualizerPanel props:', {
    resourceType,
    showDetails,
    showWarnings,
    showOptimizationSuggestions
  });

  // 加载资源选项
  const loadResourceOptions = useCallback(async () => {
    try {
      // 模拟获取所有资源（实际实现中应该从服务获取）
      const mockResources = [
        { id: 'texture1', name: '纹理1', type: 'texture' },
        { id: 'model1', name: '模型1', type: 'model' },
        { id: 'material1', name: '材质1', type: 'material' }
      ];

      // 转换为选项格式
      const options = mockResources.map(resource => ({
        label: resource.name,
        value: resource.id,
        type: resource.type
      }));

      setResourceOptions(options);
    } catch (error) {
      console.error('加载资源选项失败:', error);
    }
  }, []);

  // 加载依赖数据
  const loadDependencyData = useCallback(async () => {
    if (!selectedResource && !showAllResourcesState) {
      setNodes([]);
      setEdges([]);
      setAnalysisResults(null);
      setOptimizationSuggestions([]);
      return;
    }

    setLoading(true);

    try {
      // 获取依赖数据
      const { nodes, edges, analysis, suggestions } = await analyzer.analyzeDependencies(
        selectedResource,
        {
          includeTypes: dependencyTypes,
          showAllResources: showAllResourcesState,
          filterType: filterType || undefined,
          searchValue: searchValue || undefined,
          layoutType,
          maxDepth
        }
      );

      setNodes(nodes);
      setEdges(edges);
      setAnalysisResults(analysis);
      setOptimizationSuggestions(suggestions);
    } catch (error) {
      console.error('加载依赖数据失败:', error);
      message.error('加载依赖数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  }, [
    selectedResource,
    showAllResourcesState,
    dependencyTypes,
    filterType,
    searchValue,
    layoutType,
    maxDepth,
    analyzer
  ]);

  // 初始加载
  useEffect(() => {
    loadResourceOptions();
  }, [loadResourceOptions]);

  // 当选择的资源变化时，加载依赖数据
  useEffect(() => {
    loadDependencyData();
  }, [loadDependencyData]);

  // 自动刷新
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const timerId = window.setInterval(() => {
        loadDependencyData();
      }, refreshInterval);

      setRefreshTimerId(timerId);

      return () => {
        if (timerId) {
          clearInterval(timerId);
        }
      };
    } else if (refreshTimerId) {
      clearInterval(refreshTimerId);
      setRefreshTimerId(null);
    }
  }, [autoRefresh, refreshInterval, loadDependencyData]);

  // 处理节点点击
  const handleNodeClick = useCallback((_event: React.MouseEvent, node: Node) => {
    setSelectedNodeId(node.id);
    setSelectedNodeData(node.data);
    setDetailDrawerVisible(true);
  }, []);

  // 处理导出数据
  const handleExportData = useCallback(() => {
    if (!analysisResults) {
      message.warning('没有可导出的数据');
      return;
    }

    try {
      // 创建导出数据
      const exportData = {
        timestamp: new Date().toISOString(),
        resource: selectedResource,
        analysis: analysisResults,
        suggestions: optimizationSuggestions
      };

      // 转换为JSON字符串
      const jsonString = JSON.stringify(exportData, null, 2);

      // 创建Blob
      const blob = new Blob([jsonString], { type: 'application/json' });

      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `resource-dependency-analysis-${selectedResource || 'all'}-${new Date().getTime()}.json`;
      document.body.appendChild(a);
      a.click();

      // 清理
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 0);

      message.success('导出成功');
    } catch (error) {
      console.error('导出数据失败:', error);
      message.error('导出数据失败');
    }
  }, [analysisResults, optimizationSuggestions, selectedResource]);

  // 渲染依赖图
  const renderDependencyGraph = () => {
    if (loading) {
      return (
        <div className="loading-container">
          <Spin size="large" tip="加载依赖数据中..." />
        </div>
      );
    }

    if (nodes.length === 0) {
      return (
        <Empty
          description={
            <span>
              {!selectedResource && !showAllResourcesState
                ? '请选择一个资源或启用显示所有资源'
                : '没有找到依赖数据'}
            </span>
          }
        />
      );
    }

    return (
      <div className="dependency-graph">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          nodeTypes={nodeTypes}
          onNodeClick={handleNodeClick}
          fitView
          attributionPosition="bottom-right"
        >
          {showGrid && <Background />}
          {showControls && <Controls />}
          {showMiniMap && <MiniMap />}
          <ReactFlowControls
            onRefresh={loadDependencyData}
            t={(key: string) => t(key) as string}
          />
          <Panel position="top-right">
            <div className="graph-controls">
              <Space>
                <Button
                  icon={<SettingOutlined />}
                  onClick={() => setSettingsDrawerVisible(true)}
                  title={t('resources.dependency.settings') as string}
                />
              </Space>
            </div>
          </Panel>
        </ReactFlow>
      </div>
    );
  };

  // 渲染分析结果
  const renderAnalysisResults = () => {
    if (loading) {
      return (
        <div className="loading-container">
          <Spin size="large" tip="分析依赖数据中..." />
        </div>
      );
    }

    if (!analysisResults) {
      return (
        <Empty description="没有分析结果" />
      );
    }

    return (
      <div className="analysis-results">
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Card title={t('resources.dependency.summary') as string} className="analysis-card">
              <div className="analysis-item">
                <div className="analysis-label">{t('resources.dependency.totalResources') as string}</div>
                <div className="analysis-value">{analysisResults.totalResources}</div>
              </div>
              <div className="analysis-item">
                <div className="analysis-label">{t('resources.dependency.totalDependencies') as string}</div>
                <div className="analysis-value">{analysisResults.totalDependencies}</div>
              </div>
              <div className="analysis-item">
                <div className="analysis-label">{t('resources.dependency.circularDependencies') as string}</div>
                <div className="analysis-value">{analysisResults.circularDependencies.length}</div>
              </div>
              <div className="analysis-item">
                <div className="analysis-label">{t('resources.dependency.unusedResources') as string}</div>
                <div className="analysis-value">{analysisResults.unusedResources.length}</div>
              </div>
              <div className="analysis-item">
                <div className="analysis-label">{t('resources.dependency.memoryUsage') as string}</div>
                <div className="analysis-value">{formatBytes(analysisResults.memoryUsage)}</div>
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card title={t('resources.dependency.resourceTypes') as string} className="analysis-card">
              {Object.entries(analysisResults.resourcesByType).map(([type, count]) => (
                <div className="analysis-item" key={type}>
                  <div className="analysis-label">{type}</div>
                  <div className="analysis-value">{count as number}</div>
                </div>
              ))}
            </Card>
          </Col>
          <Col span={8}>
            <Card title={t('resources.dependency.issues') as string} className="analysis-card">
              <div className="analysis-item">
                <div className="analysis-label">{t('resources.dependency.circularDependencies') as string}</div>
                <div className="analysis-value">
                  <Badge status={analysisResults.circularDependencies.length > 0 ? "error" : "success"} text={analysisResults.circularDependencies.length} />
                </div>
              </div>
              <div className="analysis-item">
                <div className="analysis-label">{t('resources.dependency.unusedResources') as string}</div>
                <div className="analysis-value">
                  <Badge status={analysisResults.unusedResources.length > 0 ? "warning" : "success"} text={analysisResults.unusedResources.length} />
                </div>
              </div>
              <div className="analysis-item">
                <div className="analysis-label">{t('resources.dependency.duplicateResources') as string}</div>
                <div className="analysis-value">
                  <Badge status={analysisResults.duplicateResources.length > 0 ? "warning" : "success"} text={analysisResults.duplicateResources.length} />
                </div>
              </div>
              <div className="analysis-item">
                <div className="analysis-label">{t('resources.dependency.largeResources') as string}</div>
                <div className="analysis-value">
                  <Badge status={analysisResults.largeResources.length > 0 ? "processing" : "success"} text={analysisResults.largeResources.length} />
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染优化建议
  const renderOptimizationSuggestions = () => {
    if (loading) {
      return (
        <div className="loading-container">
          <Spin size="large" tip="生成优化建议中..." />
        </div>
      );
    }

    if (!optimizationSuggestions || optimizationSuggestions.length === 0) {
      return (
        <Empty description="没有优化建议" />
      );
    }

    return (
      <div className="optimization-suggestions">
        <List
          itemLayout="vertical"
          dataSource={optimizationSuggestions}
          renderItem={suggestion => (
            <List.Item key={suggestion.id}>
              <Card className="suggestion-card">
                <div className="suggestion-header">
                  <Tag color={
                    suggestion.severity === 'high' ? 'error' :
                    suggestion.severity === 'medium' ? 'warning' :
                    'processing'
                  }>
                    {suggestion.severity === 'high' ? '高优先级' :
                     suggestion.severity === 'medium' ? '中优先级' :
                     '低优先级'}
                  </Tag>
                  <span className="suggestion-title">{suggestion.description}</span>
                </div>
                <div className="suggestion-content">
                  <div className="suggestion-section">
                    <div className="suggestion-label">影响：</div>
                    <div className="suggestion-value">{suggestion.impact}</div>
                  </div>
                  <div className="suggestion-section">
                    <div className="suggestion-label">解决方案：</div>
                    <div className="suggestion-value">{suggestion.solution}</div>
                  </div>
                  <div className="suggestion-section">
                    <div className="suggestion-label">相关资源：</div>
                    <div className="suggestion-value">
                      {suggestion.resources.map(resourceId => (
                        <Tag key={resourceId} onClick={() => setSelectedResource(resourceId)}>
                          {resourceId}
                        </Tag>
                      ))}
                    </div>
                  </div>
                </div>
              </Card>
            </List.Item>
          )}
        />
      </div>
    );
  };

  return (
    <div className={`resource-dependency-visualizer-panel ${className || ''}`}>
      <div className="visualizer-header">
        <Space>
          <Select
            placeholder={t('resources.dependency.selectResource') as string}
            value={selectedResource}
            onChange={setSelectedResource}
            style={{ width: 300 }}
            showSearch
            allowClear
            disabled={showAllResourcesState}
            options={resourceOptions}
            optionFilterProp="label"
          />

          <Switch
            checked={showAllResourcesState}
            onChange={(checked) => {
              setShowAllResourcesState(checked);
              if (checked) {
                setSelectedResource(null);
              }
            }}
            checkedChildren={t('resources.dependency.showAll') as string}
            unCheckedChildren={t('resources.dependency.showSelected') as string}
          />

          <Input
            placeholder={t('resources.dependency.search') as string}
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            prefix={<SearchOutlined />}
            style={{ width: 200 }}
            allowClear
          />

          <Select
            placeholder={t('resources.dependency.filterByType') as string}
            value={filterType}
            onChange={setFilterType}
            style={{ width: 150 }}
            allowClear
          >
            <Option value="texture">{t('resources.types.texture') as string}</Option>
            <Option value="model">{t('resources.types.model') as string}</Option>
            <Option value="material">{t('resources.types.material') as string}</Option>
            <Option value="audio">{t('resources.types.audio') as string}</Option>
            <Option value="shader">{t('resources.types.shader') as string}</Option>
            <Option value="other">{t('resources.types.other') as string}</Option>
          </Select>

          <Button
            icon={<ReloadOutlined />}
            onClick={loadDependencyData}
            loading={loading}
          >
            {t('resources.dependency.refresh') as string}
          </Button>

          <Button
            icon={<ExportOutlined />}
            onClick={handleExportData}
            disabled={!analysisResults}
          >
            {t('resources.dependency.export') as string}
          </Button>
        </Space>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={<span><ApartmentOutlined />{t('resources.dependency.graph') as string}</span>}
          key="graph"
        >
          <ReactFlowProvider>
            {renderDependencyGraph()}
          </ReactFlowProvider>
        </TabPane>
        <TabPane
          tab={<span><BranchesOutlined />{t('resources.dependency.analysis') as string}</span>}
          key="analysis"
        >
          {renderAnalysisResults()}
        </TabPane>
        <TabPane
          tab={<span><BarChartOutlined />{t('resources.dependency.optimization') as string}</span>}
          key="optimization"
        >
          {renderOptimizationSuggestions()}
        </TabPane>
      </Tabs>

      {/* 资源详情抽屉 */}
      <Drawer
        title="资源详情"
        placement="right"
        onClose={() => setDetailDrawerVisible(false)}
        open={detailDrawerVisible}
        width={400}
      >
        {selectedNodeData && (
          <div className="resource-details">
            <div className="detail-section">
              <div className="detail-label">名称：</div>
              <div className="detail-value">{selectedNodeData.label}</div>
            </div>
            <div className="detail-section">
              <div className="detail-label">类型：</div>
              <div className="detail-value">{selectedNodeData.type}</div>
            </div>
            <div className="detail-section">
              <div className="detail-label">大小：</div>
              <div className="detail-value">{formatBytes(selectedNodeData.size)}</div>
            </div>
            <div className="detail-section">
              <div className="detail-label">URL：</div>
              <div className="detail-value">{selectedNodeData.url}</div>
            </div>
            <div className="detail-section">
              <div className="detail-label">最后修改：</div>
              <div className="detail-value">{new Date(selectedNodeData.lastModified).toLocaleString()}</div>
            </div>
            <Divider />
            <Button type="primary" onClick={() => setSelectedResource(selectedNodeId)}>
              查看此资源的依赖
            </Button>
          </div>
        )}
      </Drawer>

      {/* 设置抽屉 */}
      <Drawer
        title="可视化设置"
        placement="right"
        onClose={() => setSettingsDrawerVisible(false)}
        open={settingsDrawerVisible}
        width={400}
      >
        <div className="visualizer-settings">
          <div className="setting-section">
            <div className="setting-label">依赖类型：</div>
            <div className="setting-value">
              <Checkbox.Group
                options={[
                  { label: '强依赖', value: DependencyTypeValues.STRONG as DependencyType },
                  { label: '弱依赖', value: DependencyTypeValues.WEAK as DependencyType }
                ]}
                value={dependencyTypes}
                onChange={(values) => setDependencyTypes(values as DependencyType[])}
              />
            </div>
          </div>
          <div className="setting-section">
            <div className="setting-label">布局类型：</div>
            <div className="setting-value">
              <Radio.Group
                options={[
                  { label: '层次布局', value: 'dagre' },
                  { label: '力导向布局', value: 'force' },
                  { label: '径向布局', value: 'radial' }
                ]}
                value={layoutType}
                onChange={(e) => setLayoutType(e.target.value)}
                optionType="button"
                buttonStyle="solid"
              />
            </div>
          </div>
          <div className="setting-section">
            <div className="setting-label">最大深度：</div>
            <div className="setting-value">
              <Slider
                min={1}
                max={20}
                value={maxDepth}
                onChange={setMaxDepth}
                marks={{ 1: '1', 5: '5', 10: '10', 15: '15', 20: '20' }}
              />
            </div>
          </div>
          <div className="setting-section">
            <div className="setting-label">显示选项：</div>
            <div className="setting-value">
              <Space direction="vertical">
                <Switch
                  checked={showMiniMap}
                  onChange={setShowMiniMap}
                  checkedChildren="显示小地图"
                  unCheckedChildren="隐藏小地图"
                />
                <Switch
                  checked={showControls}
                  onChange={setShowControls}
                  checkedChildren="显示控制器"
                  unCheckedChildren="隐藏控制器"
                />
                <Switch
                  checked={showGrid}
                  onChange={setShowGrid}
                  checkedChildren="显示网格"
                  unCheckedChildren="隐藏网格"
                />
                <Switch
                  checked={autoRefresh}
                  onChange={(_checked) => {
                    if (onClose) {
                      onClose();
                    }
                  }}
                  checkedChildren="自动刷新"
                  unCheckedChildren="手动刷新"
                />
              </Space>
            </div>
          </div>
        </div>
      </Drawer>
    </div>
  );
};

export default ResourceDependencyVisualizerPanel;
