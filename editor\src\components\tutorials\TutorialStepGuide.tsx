/**
 * 教程步骤引导组件
 */
import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Steps, 
  Button, 
  Typography, 
  List, 
  Checkbox, 
  Tooltip, 
  Space, 
  Divider,
  Progress,
  Badge
} from 'antd';
import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  CloseCircleOutlined,
  QuestionCircleOutlined,
  BulbOutlined,
  TrophyOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import {
  tutorialValidationService,
  Tutorial,
  TutorialTask
} from '../../services/TutorialValidationService';
import './TutorialStepGuide.css';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

/**
 * 教程步骤引导组件属性
 */
interface TutorialStepGuideProps {
  tutorial: Tutorial;
  onClose: () => void;
}

/**
 * 教程任务项组件
 */
const TaskItem: React.FC<{
  task: TutorialTask;
  tutorialId: string;
}> = ({ task, tutorialId: _tutorialId }) => {
  const [showHint, setShowHint] = useState(false);
  
  return (
    <div className={`task-item ${task.completed ? 'completed' : ''} ${task.optional ? 'optional' : ''}`}>
      <div className="task-header">
        <Checkbox 
          checked={task.completed} 
          disabled={true}
        />
        <div className="task-title">
          <Text strong>{task.title}</Text>
          {task.optional && (
            <Badge 
              count="可选" 
              style={{ backgroundColor: '#52c41a' }} 
            />
          )}
        </div>
      </div>
      
      <div className="task-content">
        <Paragraph className="task-description">
          {task.description}
        </Paragraph>
        
        {task.hint && (
          <div className="task-hint">
            <Button 
              type="link" 
              size="small"
              icon={<BulbOutlined />}
              onClick={() => setShowHint(!showHint)}
            >
              {showHint ? '隐藏提示' : '显示提示'}
            </Button>
            
            {showHint && (
              <div className="hint-content">
                <Paragraph>{task.hint}</Paragraph>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * 教程步骤引导组件
 */
export const TutorialStepGuide: React.FC<TutorialStepGuideProps> = ({ 
  tutorial, 
  onClose 
}) => {
  const { t } = useTranslation();
  const [currentTutorial, setCurrentTutorial] = useState<Tutorial>(tutorial);
  const [currentStepIndex, setCurrentStepIndex] = useState<number>(tutorial.currentStepIndex);
  
  // 监听教程变化
  useEffect(() => {
    const handleStepChanged = (data: { tutorialId: string; stepIndex: number }) => {
      if (data.tutorialId === tutorial.id) {
        setCurrentStepIndex(data.stepIndex);
      }
    };
    
    const handleTasksUpdated = () => {
      // 获取最新的教程状态
      const activeTutorial = tutorialValidationService.getActiveTutorial();
      if (activeTutorial && activeTutorial.id === tutorial.id) {
        setCurrentTutorial({ ...activeTutorial });
      }
    };
    
    const handleTutorialCompleted = (completedTutorial: Tutorial) => {
      if (completedTutorial.id === tutorial.id) {
        setCurrentTutorial({ ...completedTutorial });
      }
    };
    
    // 添加事件监听器
    tutorialValidationService.on('stepChanged', handleStepChanged);
    tutorialValidationService.on('tasksUpdated', handleTasksUpdated);
    tutorialValidationService.on('taskCompleted', handleTasksUpdated);
    tutorialValidationService.on('stepCompleted', handleTasksUpdated);
    tutorialValidationService.on('tutorialCompleted', handleTutorialCompleted);
    
    return () => {
      // 移除事件监听器
      tutorialValidationService.off('stepChanged', handleStepChanged);
      tutorialValidationService.off('tasksUpdated', handleTasksUpdated);
      tutorialValidationService.off('taskCompleted', handleTasksUpdated);
      tutorialValidationService.off('stepCompleted', handleTasksUpdated);
      tutorialValidationService.off('tutorialCompleted', handleTutorialCompleted);
    };
  }, [tutorial.id]);
  
  // 计算当前步骤
  const currentStep = currentTutorial.steps[currentStepIndex];
  
  // 计算总体进度
  const totalSteps = currentTutorial.steps.length;
  const completedSteps = currentTutorial.steps.filter(step => step.completed).length;
  const progressPercent = Math.floor((completedSteps / totalSteps) * 100);
  
  // 计算当前步骤的任务进度
  const totalTasks = currentStep?.tasks.length || 0;
  const completedTasks = currentStep?.tasks.filter(task => task.completed).length || 0;
  const taskProgressPercent = totalTasks > 0 ? Math.floor((completedTasks / totalTasks) * 100) : 0;
  
  // 处理下一步
  const handleNext = () => {
    tutorialValidationService.nextStep();
  };
  
  // 处理上一步
  const handlePrevious = () => {
    tutorialValidationService.previousStep();
  };
  
  // 处理关闭
  const handleClose = () => {
    tutorialValidationService.clearActiveTutorial();
    onClose();
  };
  

  
  // 如果教程已完成，显示完成页面
  if (currentTutorial.completed) {
    return (
      <Card className="tutorial-guide-card completed">
        <div className="tutorial-completed">
          <div className="tutorial-completed-header">
            <TrophyOutlined className="tutorial-completed-icon" />
            <Title level={3}>{t('tutorials.completed.title')}</Title>
          </div>
          
          <Paragraph>
            {t('tutorials.completed.message', { title: currentTutorial.title })}
          </Paragraph>
          
          {currentTutorial.reward && (
            <div className="tutorial-reward">
              <Title level={4}>{t('tutorials.completed.reward')}</Title>
              <Paragraph>
                {currentTutorial.reward.type === 'badge' 
                  ? t('tutorials.completed.badgeReward', { badge: currentTutorial.reward.value }) 
                  : t('tutorials.completed.itemReward', { item: currentTutorial.reward.value })}
              </Paragraph>
            </div>
          )}
          
          <div className="tutorial-completed-actions">
            <Button type="primary" size="large" onClick={onClose}>
              {t('tutorials.completed.close')}
            </Button>
          </div>
        </div>
      </Card>
    );
  }
  
  return (
    <Card className="tutorial-guide-card">
      <div className="tutorial-header">
        <div className="tutorial-title">
          <Title level={4}>{currentTutorial.title}</Title>
          <div className="tutorial-progress">
            <Text type="secondary">
              {t('tutorials.progress', { current: currentStepIndex + 1, total: totalSteps })}
            </Text>
            <Progress percent={progressPercent} size="small" showInfo={false} />
          </div>
        </div>
        <Button 
          type="text" 
          icon={<CloseCircleOutlined />} 
          onClick={handleClose}
          className="close-button"
        />
      </div>
      
      <Divider style={{ margin: '12px 0' }} />
      
      <Steps 
        current={currentStepIndex} 
        size="small" 
        className="tutorial-steps"
        onChange={(index) => {
          // 只允许导航到已完成的步骤
          if (index < currentStepIndex) {
            tutorialValidationService.previousStep();
          }
        }}
      >
        {currentTutorial.steps.map((step, index) => (
          <Step 
            key={step.id} 
            title={step.title} 
            disabled={index > currentStepIndex} 
            status={step.completed ? 'finish' : index === currentStepIndex ? 'process' : 'wait'}
          />
        ))}
      </Steps>
      
      <div className="tutorial-step-content">
        <div className="step-header">
          <Title level={4}>{currentStep.title}</Title>
          <div className="step-progress">
            <Text type="secondary">
              {t('tutorials.taskProgress', { completed: completedTasks, total: totalTasks })}
            </Text>
            <Progress percent={taskProgressPercent} size="small" showInfo={false} />
          </div>
        </div>
        
        <Paragraph className="step-description">
          {currentStep.description}
        </Paragraph>
        
        <List
          className="task-list"
          itemLayout="vertical"
          dataSource={currentStep.tasks}
          renderItem={task => (
            <List.Item key={task.id} className="task-list-item">
              <TaskItem task={task} tutorialId={currentTutorial.id} />
            </List.Item>
          )}
        />
      </div>
      
      <div className="tutorial-footer">
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={handlePrevious}
            disabled={currentStepIndex === 0}
          >
            {t('tutorials.previous')}
          </Button>
          
          <Button 
            type="primary" 
            icon={<ArrowRightOutlined />} 
            onClick={handleNext}
            disabled={!currentStep.completed}
          >
            {currentStepIndex === totalSteps - 1 
              ? t('tutorials.complete') 
              : t('tutorials.next')}
          </Button>
          
          <Tooltip title={t('tutorials.helpTooltip')}>
            <Button 
              icon={<QuestionCircleOutlined />} 
              type="link"
            />
          </Tooltip>
        </Space>
      </div>
    </Card>
  );
};

export default TutorialStepGuide;
