/**
 * DragPerformanceMonitor.tsx
 * 
 * 拖拽性能监控组件，用于监测和优化拖拽操作的性能
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, Progress, Statistic, Row, Col, Switch, Button } from 'antd';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

/**
 * 性能指标接口
 */
export interface PerformanceMetrics {
  /** 帧率 */
  fps: number;
  /** 拖拽延迟（毫秒） */
  dragLatency: number;
  /** 内存使用量（MB） */
  memoryUsage: number;
  /** CPU使用率（%） */
  cpuUsage: number;
  /** 渲染时间（毫秒） */
  renderTime: number;
  /** 事件处理时间（毫秒） */
  eventHandlingTime: number;
  /** 丢帧数 */
  droppedFrames: number;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 性能监控配置
 */
export interface MonitorConfig {
  /** 是否启用监控 */
  enabled: boolean;
  /** 采样间隔（毫秒） */
  sampleInterval: number;
  /** 历史数据保留数量 */
  historySize: number;
  /** 是否显示实时图表 */
  showChart: boolean;
  /** 是否显示详细统计 */
  showDetails: boolean;
  /** 性能警告阈值 */
  thresholds: {
    minFps: number;
    maxLatency: number;
    maxMemory: number;
    maxCpuUsage: number;
  };
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: MonitorConfig = {
  enabled: false,
  sampleInterval: 100,
  historySize: 100,
  showChart: true,
  showDetails: true,
  thresholds: {
    minFps: 30,
    maxLatency: 16,
    maxMemory: 100,
    maxCpuUsage: 80
  }
};

/**
 * 拖拽性能监控组件属性
 */
export interface DragPerformanceMonitorProps {
  /** 配置选项 */
  config?: Partial<MonitorConfig>;
  /** 是否显示监控面板 */
  visible?: boolean;
  /** 位置 */
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  /** 样式类名 */
  className?: string;
  /** 性能数据变化回调 */
  onMetricsChange?: (metrics: PerformanceMetrics) => void;
  /** 性能警告回调 */
  onPerformanceWarning?: (warning: string, metrics: PerformanceMetrics) => void;
}

/**
 * 拖拽性能监控组件
 */
export const DragPerformanceMonitor: React.FC<DragPerformanceMonitorProps> = ({
  config: userConfig,
  visible = false,
  position = 'top-right',
  className,
  onMetricsChange,
  onPerformanceWarning
}) => {
  const config = { ...DEFAULT_CONFIG, ...userConfig };
  
  const [metrics, setMetrics] = useState<PerformanceMetrics[]>([]);
  const [currentMetrics, setCurrentMetrics] = useState<PerformanceMetrics | null>(null);
  const [isMonitoring, setIsMonitoring] = useState(config.enabled);
  
  const frameCountRef = useRef(0);
  const lastFrameTimeRef = useRef(performance.now());
  const fpsHistoryRef = useRef<number[]>([]);
  const monitorIntervalRef = useRef<NodeJS.Timeout>();
  const performanceObserverRef = useRef<PerformanceObserver>();

  // 计算FPS
  const calculateFPS = useCallback(() => {
    const now = performance.now();
    const delta = now - lastFrameTimeRef.current;
    
    if (delta >= 1000) {
      const fps = Math.round((frameCountRef.current * 1000) / delta);
      fpsHistoryRef.current.push(fps);
      
      if (fpsHistoryRef.current.length > 10) {
        fpsHistoryRef.current.shift();
      }
      
      frameCountRef.current = 0;
      lastFrameTimeRef.current = now;
      
      return fps;
    }
    
    frameCountRef.current++;
    return fpsHistoryRef.current[fpsHistoryRef.current.length - 1] || 60;
  }, []);

  // 获取内存使用量
  const getMemoryUsage = useCallback((): number => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return Math.round(memory.usedJSHeapSize / 1024 / 1024);
    }
    return 0;
  }, []);

  // 获取CPU使用率（估算）
  const getCPUUsage = useCallback((): number => {
    // 这是一个简化的CPU使用率估算
    // 实际应用中可能需要更复杂的计算
    const entries = performance.getEntriesByType('measure');
    if (entries.length === 0) return 0;
    
    const totalTime = entries.reduce((sum, entry) => sum + entry.duration, 0);
    const avgTime = totalTime / entries.length;
    
    // 将平均执行时间转换为CPU使用率百分比
    return Math.min(100, Math.round((avgTime / 16) * 100));
  }, []);

  // 测量渲染时间
  const measureRenderTime = useCallback((): number => {
    const paintEntries = performance.getEntriesByType('paint');
    if (paintEntries.length === 0) return 0;
    
    const lastPaint = paintEntries[paintEntries.length - 1];
    return Math.round(lastPaint.duration || 0);
  }, []);

  // 测量事件处理时间
  const measureEventHandlingTime = useCallback((): number => {
    const eventEntries = performance.getEntriesByType('event');
    if (eventEntries.length === 0) return 0;
    
    const recentEvents = eventEntries.slice(-10);
    const avgDuration = recentEvents.reduce((sum, entry) => sum + (entry.duration || 0), 0) / recentEvents.length;
    
    return Math.round(avgDuration);
  }, []);

  // 收集性能指标
  const collectMetrics = useCallback((): PerformanceMetrics => {
    const fps = calculateFPS();
    const memoryUsage = getMemoryUsage();
    const cpuUsage = getCPUUsage();
    const renderTime = measureRenderTime();
    const eventHandlingTime = measureEventHandlingTime();
    
    // 计算拖拽延迟（基于FPS）
    const dragLatency = fps > 0 ? Math.round(1000 / fps) : 16;
    
    // 计算丢帧数
    const expectedFrames = Math.round(config.sampleInterval / 16.67); // 60fps
    const actualFrames = Math.round(fps * config.sampleInterval / 1000);
    const droppedFrames = Math.max(0, expectedFrames - actualFrames);
    
    return {
      fps,
      dragLatency,
      memoryUsage,
      cpuUsage,
      renderTime,
      eventHandlingTime,
      droppedFrames,
      timestamp: Date.now()
    };
  }, [calculateFPS, getMemoryUsage, getCPUUsage, measureRenderTime, measureEventHandlingTime, config.sampleInterval]);

  // 检查性能警告
  const checkPerformanceWarnings = useCallback((metrics: PerformanceMetrics) => {
    const { thresholds } = config;
    
    if (metrics.fps < thresholds.minFps) {
      onPerformanceWarning?.(`FPS过低: ${metrics.fps}`, metrics);
    }
    
    if (metrics.dragLatency > thresholds.maxLatency) {
      onPerformanceWarning?.(`拖拽延迟过高: ${metrics.dragLatency}ms`, metrics);
    }
    
    if (metrics.memoryUsage > thresholds.maxMemory) {
      onPerformanceWarning?.(`内存使用过高: ${metrics.memoryUsage}MB`, metrics);
    }
    
    if (metrics.cpuUsage > thresholds.maxCpuUsage) {
      onPerformanceWarning?.(`CPU使用率过高: ${metrics.cpuUsage}%`, metrics);
    }
  }, [config.thresholds, onPerformanceWarning]);

  // 开始监控
  const startMonitoring = useCallback(() => {
    if (monitorIntervalRef.current) {
      clearInterval(monitorIntervalRef.current);
    }
    
    monitorIntervalRef.current = setInterval(() => {
      const newMetrics = collectMetrics();
      
      setCurrentMetrics(newMetrics);
      setMetrics(prev => {
        const updated = [...prev, newMetrics];
        if (updated.length > config.historySize) {
          updated.shift();
        }
        return updated;
      });
      
      onMetricsChange?.(newMetrics);
      checkPerformanceWarnings(newMetrics);
    }, config.sampleInterval);
    
    // 设置Performance Observer
    if ('PerformanceObserver' in window) {
      performanceObserverRef.current = new PerformanceObserver((list) => {
        // 处理性能条目
        const entries = list.getEntries();
        entries.forEach(entry => {
          if (entry.entryType === 'measure' && entry.name.includes('drag')) {
            // 记录拖拽相关的性能数据
            performance.mark(`drag-${Date.now()}`);
          }
        });
      });
      
      performanceObserverRef.current.observe({ 
        entryTypes: ['measure', 'paint', 'event'] 
      });
    }
  }, [collectMetrics, config.sampleInterval, config.historySize, onMetricsChange, checkPerformanceWarnings]);

  // 停止监控
  const stopMonitoring = useCallback(() => {
    if (monitorIntervalRef.current) {
      clearInterval(monitorIntervalRef.current);
      monitorIntervalRef.current = undefined;
    }
    
    if (performanceObserverRef.current) {
      performanceObserverRef.current.disconnect();
      performanceObserverRef.current = undefined;
    }
  }, []);

  // 清除数据
  const clearData = useCallback(() => {
    setMetrics([]);
    setCurrentMetrics(null);
    fpsHistoryRef.current = [];
    frameCountRef.current = 0;
    lastFrameTimeRef.current = performance.now();
  }, []);

  // 监控状态变化
  useEffect(() => {
    if (isMonitoring) {
      startMonitoring();
    } else {
      stopMonitoring();
    }
    
    return stopMonitoring;
  }, [isMonitoring, startMonitoring, stopMonitoring]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      stopMonitoring();
    };
  }, [stopMonitoring]);

  if (!visible) {
    return null;
  }

  const positionStyles = {
    'top-left': { top: 16, left: 16 },
    'top-right': { top: 16, right: 16 },
    'bottom-left': { bottom: 16, left: 16 },
    'bottom-right': { bottom: 16, right: 16 }
  };

  return (
    <div
      className={`drag-performance-monitor ${className || ''}`}
      style={{
        position: 'fixed',
        ...positionStyles[position],
        zIndex: 10000,
        width: 400,
        maxHeight: '80vh',
        overflow: 'auto'
      }}
    >
      <Card
        title="拖拽性能监控"
        size="small"
        extra={
          <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
            <Switch
              size="small"
              checked={isMonitoring}
              onChange={setIsMonitoring}
              checkedChildren="开"
              unCheckedChildren="关"
            />
            <Button size="small" onClick={clearData}>
              清除
            </Button>
          </div>
        }
      >
        {currentMetrics && (
          <>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={12}>
                <Statistic
                  title="FPS"
                  value={currentMetrics.fps}
                  suffix="fps"
                  valueStyle={{
                    color: currentMetrics.fps >= config.thresholds.minFps ? '#3f8600' : '#cf1322'
                  }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="延迟"
                  value={currentMetrics.dragLatency}
                  suffix="ms"
                  valueStyle={{
                    color: currentMetrics.dragLatency <= config.thresholds.maxLatency ? '#3f8600' : '#cf1322'
                  }}
                />
              </Col>
            </Row>
            
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={12}>
                <div>
                  <div style={{ fontSize: 12, color: '#666' }}>内存使用</div>
                  <Progress
                    percent={Math.min(100, (currentMetrics.memoryUsage / config.thresholds.maxMemory) * 100)}
                    size="small"
                    status={currentMetrics.memoryUsage > config.thresholds.maxMemory ? 'exception' : 'normal'}
                    format={() => `${currentMetrics.memoryUsage}MB`}
                  />
                </div>
              </Col>
              <Col span={12}>
                <div>
                  <div style={{ fontSize: 12, color: '#666' }}>CPU使用率</div>
                  <Progress
                    percent={currentMetrics.cpuUsage}
                    size="small"
                    status={currentMetrics.cpuUsage > config.thresholds.maxCpuUsage ? 'exception' : 'normal'}
                    format={() => `${currentMetrics.cpuUsage}%`}
                  />
                </div>
              </Col>
            </Row>
            
            {config.showChart && metrics.length > 1 && (
              <div style={{ height: 200, marginTop: 16 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={metrics.slice(-20)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="timestamp" 
                      tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                    />
                    <YAxis />
                    <Tooltip 
                      labelFormatter={(value) => new Date(value).toLocaleTimeString()}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="fps" 
                      stroke="#8884d8" 
                      strokeWidth={2}
                      dot={false}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="dragLatency" 
                      stroke="#82ca9d" 
                      strokeWidth={2}
                      dot={false}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            )}
            
            {config.showDetails && (
              <Row gutter={16} style={{ marginTop: 16, fontSize: 12 }}>
                <Col span={8}>
                  <div>渲染时间: {currentMetrics.renderTime}ms</div>
                </Col>
                <Col span={8}>
                  <div>事件处理: {currentMetrics.eventHandlingTime}ms</div>
                </Col>
                <Col span={8}>
                  <div>丢帧数: {currentMetrics.droppedFrames}</div>
                </Col>
              </Row>
            )}
          </>
        )}
      </Card>
    </div>
  );
};

export default DragPerformanceMonitor;
