import { g as jt, c as Dt } from "./_commonjsHelpers-10dfc225.mjs";
function Ft(Nt, Ut) {
  for (var Bt = 0; Bt < Ut.length; Bt++) {
    const At = Ut[Bt];
    if (typeof At != "string" && !Array.isArray(At)) {
      for (const wt in At)
        if (wt !== "default" && !(wt in Nt)) {
          const Rt = Object.getOwnPropertyDescriptor(At, wt);
          Rt && Object.defineProperty(Nt, wt, Rt.get ? Rt : {
            enumerable: !0,
            get: () => At[wt]
          });
        }
    }
  }
  return Object.freeze(Object.defineProperty(Nt, Symbol.toStringTag, { value: "Module" }));
}
var kt = { exports: {} };
/*! For license information please see cbor.js.LICENSE.txt */
(function(Nt, Ut) {
  (function(Bt, At) {
    Nt.exports = At();
  })(Dt, () => (() => {
    var Bt = { 5568: (G) => {
      const { AbortController: N, AbortSignal: s } = typeof self < "u" ? self : typeof window < "u" ? window : void 0;
      G.exports = N, G.exports.AbortSignal = s, G.exports.default = N;
    }, 7526: (G, N) => {
      N.byteLength = function(i) {
        var d = w(i), b = d[0], A = d[1];
        return 3 * (b + A) / 4 - A;
      }, N.toByteArray = function(i) {
        var d, b, A = w(i), a = A[0], l = A[1], c = new m(function(y, v, z) {
          return 3 * (v + z) / 4 - z;
        }(0, a, l)), o = 0, h = l > 0 ? a - 4 : a;
        for (b = 0; b < h; b += 4)
          d = S[i.charCodeAt(b)] << 18 | S[i.charCodeAt(b + 1)] << 12 | S[i.charCodeAt(b + 2)] << 6 | S[i.charCodeAt(b + 3)], c[o++] = d >> 16 & 255, c[o++] = d >> 8 & 255, c[o++] = 255 & d;
        return l === 2 && (d = S[i.charCodeAt(b)] << 2 | S[i.charCodeAt(b + 1)] >> 4, c[o++] = 255 & d), l === 1 && (d = S[i.charCodeAt(b)] << 10 | S[i.charCodeAt(b + 1)] << 4 | S[i.charCodeAt(b + 2)] >> 2, c[o++] = d >> 8 & 255, c[o++] = 255 & d), c;
      }, N.fromByteArray = function(i) {
        for (var d, b = i.length, A = b % 3, a = [], l = 16383, c = 0, o = b - A; c < o; c += l)
          a.push(e(i, c, c + l > o ? o : c + l));
        return A === 1 ? (d = i[b - 1], a.push(s[d >> 2] + s[d << 4 & 63] + "==")) : A === 2 && (d = (i[b - 2] << 8) + i[b - 1], a.push(s[d >> 10] + s[d >> 4 & 63] + s[d << 2 & 63] + "=")), a.join("");
      };
      for (var s = [], S = [], m = typeof Uint8Array < "u" ? Uint8Array : Array, O = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", P = 0; P < 64; ++P)
        s[P] = O[P], S[O.charCodeAt(P)] = P;
      function w(i) {
        var d = i.length;
        if (d % 4 > 0)
          throw new Error("Invalid string. Length must be a multiple of 4");
        var b = i.indexOf("=");
        return b === -1 && (b = d), [b, b === d ? 0 : 4 - b % 4];
      }
      function e(i, d, b) {
        for (var A, a, l = [], c = d; c < b; c += 3)
          A = (i[c] << 16 & 16711680) + (i[c + 1] << 8 & 65280) + (255 & i[c + 2]), l.push(s[(a = A) >> 18 & 63] + s[a >> 12 & 63] + s[a >> 6 & 63] + s[63 & a]);
        return l.join("");
      }
      S["-".charCodeAt(0)] = 62, S["_".charCodeAt(0)] = 63;
    }, 8287: (G, N, s) => {
      const S = s(7526), m = s(251), O = typeof Symbol == "function" && typeof Symbol.for == "function" ? Symbol.for("nodejs.util.inspect.custom") : null;
      N.Buffer = e, N.SlowBuffer = function(t) {
        return +t != t && (t = 0), e.alloc(+t);
      }, N.INSPECT_MAX_BYTES = 50;
      const P = 2147483647;
      function w(t) {
        if (t > P)
          throw new RangeError('The value "' + t + '" is invalid for option "size"');
        const n = new Uint8Array(t);
        return Object.setPrototypeOf(n, e.prototype), n;
      }
      function e(t, n, r) {
        if (typeof t == "number") {
          if (typeof n == "string")
            throw new TypeError('The "string" argument must be of type string. Received type number');
          return b(t);
        }
        return i(t, n, r);
      }
      function i(t, n, r) {
        if (typeof t == "string")
          return function(x, q) {
            if (typeof q == "string" && q !== "" || (q = "utf8"), !e.isEncoding(q))
              throw new TypeError("Unknown encoding: " + q);
            const ut = 0 | c(x, q);
            let pt = w(ut);
            const bt = pt.write(x, q);
            return bt !== ut && (pt = pt.slice(0, bt)), pt;
          }(t, n);
        if (ArrayBuffer.isView(t))
          return function(x) {
            if (R(x, Uint8Array)) {
              const q = new Uint8Array(x);
              return a(q.buffer, q.byteOffset, q.byteLength);
            }
            return A(x);
          }(t);
        if (t == null)
          throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type " + typeof t);
        if (R(t, ArrayBuffer) || t && R(t.buffer, ArrayBuffer) || typeof SharedArrayBuffer < "u" && (R(t, SharedArrayBuffer) || t && R(t.buffer, SharedArrayBuffer)))
          return a(t, n, r);
        if (typeof t == "number")
          throw new TypeError('The "value" argument must not be of type number. Received type number');
        const _ = t.valueOf && t.valueOf();
        if (_ != null && _ !== t)
          return e.from(_, n, r);
        const E = function(x) {
          if (e.isBuffer(x)) {
            const q = 0 | l(x.length), ut = w(q);
            return ut.length === 0 || x.copy(ut, 0, 0, q), ut;
          }
          return x.length !== void 0 ? typeof x.length != "number" || V(x.length) ? w(0) : A(x) : x.type === "Buffer" && Array.isArray(x.data) ? A(x.data) : void 0;
        }(t);
        if (E)
          return E;
        if (typeof Symbol < "u" && Symbol.toPrimitive != null && typeof t[Symbol.toPrimitive] == "function")
          return e.from(t[Symbol.toPrimitive]("string"), n, r);
        throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type " + typeof t);
      }
      function d(t) {
        if (typeof t != "number")
          throw new TypeError('"size" argument must be of type number');
        if (t < 0)
          throw new RangeError('The value "' + t + '" is invalid for option "size"');
      }
      function b(t) {
        return d(t), w(t < 0 ? 0 : 0 | l(t));
      }
      function A(t) {
        const n = t.length < 0 ? 0 : 0 | l(t.length), r = w(n);
        for (let _ = 0; _ < n; _ += 1)
          r[_] = 255 & t[_];
        return r;
      }
      function a(t, n, r) {
        if (n < 0 || t.byteLength < n)
          throw new RangeError('"offset" is outside of buffer bounds');
        if (t.byteLength < n + (r || 0))
          throw new RangeError('"length" is outside of buffer bounds');
        let _;
        return _ = n === void 0 && r === void 0 ? new Uint8Array(t) : r === void 0 ? new Uint8Array(t, n) : new Uint8Array(t, n, r), Object.setPrototypeOf(_, e.prototype), _;
      }
      function l(t) {
        if (t >= P)
          throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x" + P.toString(16) + " bytes");
        return 0 | t;
      }
      function c(t, n) {
        if (e.isBuffer(t))
          return t.length;
        if (ArrayBuffer.isView(t) || R(t, ArrayBuffer))
          return t.byteLength;
        if (typeof t != "string")
          throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type ' + typeof t);
        const r = t.length, _ = arguments.length > 2 && arguments[2] === !0;
        if (!_ && r === 0)
          return 0;
        let E = !1;
        for (; ; )
          switch (n) {
            case "ascii":
            case "latin1":
            case "binary":
              return r;
            case "utf8":
            case "utf-8":
              return X(t).length;
            case "ucs2":
            case "ucs-2":
            case "utf16le":
            case "utf-16le":
              return 2 * r;
            case "hex":
              return r >>> 1;
            case "base64":
              return H(t).length;
            default:
              if (E)
                return _ ? -1 : X(t).length;
              n = ("" + n).toLowerCase(), E = !0;
          }
      }
      function o(t, n, r) {
        let _ = !1;
        if ((n === void 0 || n < 0) && (n = 0), n > this.length || ((r === void 0 || r > this.length) && (r = this.length), r <= 0) || (r >>>= 0) <= (n >>>= 0))
          return "";
        for (t || (t = "utf8"); ; )
          switch (t) {
            case "hex":
              return rt(this, n, r);
            case "utf8":
            case "utf-8":
              return f(this, n, r);
            case "ascii":
              return U(this, n, r);
            case "latin1":
            case "binary":
              return D(this, n, r);
            case "base64":
              return M(this, n, r);
            case "ucs2":
            case "ucs-2":
            case "utf16le":
            case "utf-16le":
              return u(this, n, r);
            default:
              if (_)
                throw new TypeError("Unknown encoding: " + t);
              t = (t + "").toLowerCase(), _ = !0;
          }
      }
      function h(t, n, r) {
        const _ = t[n];
        t[n] = t[r], t[r] = _;
      }
      function y(t, n, r, _, E) {
        if (t.length === 0)
          return -1;
        if (typeof r == "string" ? (_ = r, r = 0) : r > 2147483647 ? r = 2147483647 : r < -2147483648 && (r = -2147483648), V(r = +r) && (r = E ? 0 : t.length - 1), r < 0 && (r = t.length + r), r >= t.length) {
          if (E)
            return -1;
          r = t.length - 1;
        } else if (r < 0) {
          if (!E)
            return -1;
          r = 0;
        }
        if (typeof n == "string" && (n = e.from(n, _)), e.isBuffer(n))
          return n.length === 0 ? -1 : v(t, n, r, _, E);
        if (typeof n == "number")
          return n &= 255, typeof Uint8Array.prototype.indexOf == "function" ? E ? Uint8Array.prototype.indexOf.call(t, n, r) : Uint8Array.prototype.lastIndexOf.call(t, n, r) : v(t, [n], r, _, E);
        throw new TypeError("val must be string, number or Buffer");
      }
      function v(t, n, r, _, E) {
        let x, q = 1, ut = t.length, pt = n.length;
        if (_ !== void 0 && ((_ = String(_).toLowerCase()) === "ucs2" || _ === "ucs-2" || _ === "utf16le" || _ === "utf-16le")) {
          if (t.length < 2 || n.length < 2)
            return -1;
          q = 2, ut /= 2, pt /= 2, r /= 2;
        }
        function bt(dt, yt) {
          return q === 1 ? dt[yt] : dt.readUInt16BE(yt * q);
        }
        if (E) {
          let dt = -1;
          for (x = r; x < ut; x++)
            if (bt(t, x) === bt(n, dt === -1 ? 0 : x - dt)) {
              if (dt === -1 && (dt = x), x - dt + 1 === pt)
                return dt * q;
            } else
              dt !== -1 && (x -= x - dt), dt = -1;
        } else
          for (r + pt > ut && (r = ut - pt), x = r; x >= 0; x--) {
            let dt = !0;
            for (let yt = 0; yt < pt; yt++)
              if (bt(t, x + yt) !== bt(n, yt)) {
                dt = !1;
                break;
              }
            if (dt)
              return x;
          }
        return -1;
      }
      function z(t, n, r, _) {
        r = Number(r) || 0;
        const E = t.length - r;
        _ ? (_ = Number(_)) > E && (_ = E) : _ = E;
        const x = n.length;
        let q;
        for (_ > x / 2 && (_ = x / 2), q = 0; q < _; ++q) {
          const ut = parseInt(n.substr(2 * q, 2), 16);
          if (V(ut))
            return q;
          t[r + q] = ut;
        }
        return q;
      }
      function Y(t, n, r, _) {
        return at(X(n, t.length - r), t, r, _);
      }
      function k(t, n, r, _) {
        return at(function(E) {
          const x = [];
          for (let q = 0; q < E.length; ++q)
            x.push(255 & E.charCodeAt(q));
          return x;
        }(n), t, r, _);
      }
      function Q(t, n, r, _) {
        return at(H(n), t, r, _);
      }
      function L(t, n, r, _) {
        return at(function(E, x) {
          let q, ut, pt;
          const bt = [];
          for (let dt = 0; dt < E.length && !((x -= 2) < 0); ++dt)
            q = E.charCodeAt(dt), ut = q >> 8, pt = q % 256, bt.push(pt), bt.push(ut);
          return bt;
        }(n, t.length - r), t, r, _);
      }
      function M(t, n, r) {
        return n === 0 && r === t.length ? S.fromByteArray(t) : S.fromByteArray(t.slice(n, r));
      }
      function f(t, n, r) {
        r = Math.min(t.length, r);
        const _ = [];
        let E = n;
        for (; E < r; ) {
          const x = t[E];
          let q = null, ut = x > 239 ? 4 : x > 223 ? 3 : x > 191 ? 2 : 1;
          if (E + ut <= r) {
            let pt, bt, dt, yt;
            switch (ut) {
              case 1:
                x < 128 && (q = x);
                break;
              case 2:
                pt = t[E + 1], (192 & pt) == 128 && (yt = (31 & x) << 6 | 63 & pt, yt > 127 && (q = yt));
                break;
              case 3:
                pt = t[E + 1], bt = t[E + 2], (192 & pt) == 128 && (192 & bt) == 128 && (yt = (15 & x) << 12 | (63 & pt) << 6 | 63 & bt, yt > 2047 && (yt < 55296 || yt > 57343) && (q = yt));
                break;
              case 4:
                pt = t[E + 1], bt = t[E + 2], dt = t[E + 3], (192 & pt) == 128 && (192 & bt) == 128 && (192 & dt) == 128 && (yt = (15 & x) << 18 | (63 & pt) << 12 | (63 & bt) << 6 | 63 & dt, yt > 65535 && yt < 1114112 && (q = yt));
            }
          }
          q === null ? (q = 65533, ut = 1) : q > 65535 && (q -= 65536, _.push(q >>> 10 & 1023 | 55296), q = 56320 | 1023 & q), _.push(q), E += ut;
        }
        return function(x) {
          const q = x.length;
          if (q <= F)
            return String.fromCharCode.apply(String, x);
          let ut = "", pt = 0;
          for (; pt < q; )
            ut += String.fromCharCode.apply(String, x.slice(pt, pt += F));
          return ut;
        }(_);
      }
      N.kMaxLength = P, e.TYPED_ARRAY_SUPPORT = function() {
        try {
          const t = new Uint8Array(1), n = { foo: function() {
            return 42;
          } };
          return Object.setPrototypeOf(n, Uint8Array.prototype), Object.setPrototypeOf(t, n), t.foo() === 42;
        } catch {
          return !1;
        }
      }(), e.TYPED_ARRAY_SUPPORT || typeof console > "u" || typeof console.error != "function" || console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."), Object.defineProperty(e.prototype, "parent", { enumerable: !0, get: function() {
        if (e.isBuffer(this))
          return this.buffer;
      } }), Object.defineProperty(e.prototype, "offset", { enumerable: !0, get: function() {
        if (e.isBuffer(this))
          return this.byteOffset;
      } }), e.poolSize = 8192, e.from = function(t, n, r) {
        return i(t, n, r);
      }, Object.setPrototypeOf(e.prototype, Uint8Array.prototype), Object.setPrototypeOf(e, Uint8Array), e.alloc = function(t, n, r) {
        return function(_, E, x) {
          return d(_), _ <= 0 ? w(_) : E !== void 0 ? typeof x == "string" ? w(_).fill(E, x) : w(_).fill(E) : w(_);
        }(t, n, r);
      }, e.allocUnsafe = function(t) {
        return b(t);
      }, e.allocUnsafeSlow = function(t) {
        return b(t);
      }, e.isBuffer = function(t) {
        return t != null && t._isBuffer === !0 && t !== e.prototype;
      }, e.compare = function(t, n) {
        if (R(t, Uint8Array) && (t = e.from(t, t.offset, t.byteLength)), R(n, Uint8Array) && (n = e.from(n, n.offset, n.byteLength)), !e.isBuffer(t) || !e.isBuffer(n))
          throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');
        if (t === n)
          return 0;
        let r = t.length, _ = n.length;
        for (let E = 0, x = Math.min(r, _); E < x; ++E)
          if (t[E] !== n[E]) {
            r = t[E], _ = n[E];
            break;
          }
        return r < _ ? -1 : _ < r ? 1 : 0;
      }, e.isEncoding = function(t) {
        switch (String(t).toLowerCase()) {
          case "hex":
          case "utf8":
          case "utf-8":
          case "ascii":
          case "latin1":
          case "binary":
          case "base64":
          case "ucs2":
          case "ucs-2":
          case "utf16le":
          case "utf-16le":
            return !0;
          default:
            return !1;
        }
      }, e.concat = function(t, n) {
        if (!Array.isArray(t))
          throw new TypeError('"list" argument must be an Array of Buffers');
        if (t.length === 0)
          return e.alloc(0);
        let r;
        if (n === void 0)
          for (n = 0, r = 0; r < t.length; ++r)
            n += t[r].length;
        const _ = e.allocUnsafe(n);
        let E = 0;
        for (r = 0; r < t.length; ++r) {
          let x = t[r];
          if (R(x, Uint8Array))
            E + x.length > _.length ? (e.isBuffer(x) || (x = e.from(x)), x.copy(_, E)) : Uint8Array.prototype.set.call(_, x, E);
          else {
            if (!e.isBuffer(x))
              throw new TypeError('"list" argument must be an Array of Buffers');
            x.copy(_, E);
          }
          E += x.length;
        }
        return _;
      }, e.byteLength = c, e.prototype._isBuffer = !0, e.prototype.swap16 = function() {
        const t = this.length;
        if (t % 2 != 0)
          throw new RangeError("Buffer size must be a multiple of 16-bits");
        for (let n = 0; n < t; n += 2)
          h(this, n, n + 1);
        return this;
      }, e.prototype.swap32 = function() {
        const t = this.length;
        if (t % 4 != 0)
          throw new RangeError("Buffer size must be a multiple of 32-bits");
        for (let n = 0; n < t; n += 4)
          h(this, n, n + 3), h(this, n + 1, n + 2);
        return this;
      }, e.prototype.swap64 = function() {
        const t = this.length;
        if (t % 8 != 0)
          throw new RangeError("Buffer size must be a multiple of 64-bits");
        for (let n = 0; n < t; n += 8)
          h(this, n, n + 7), h(this, n + 1, n + 6), h(this, n + 2, n + 5), h(this, n + 3, n + 4);
        return this;
      }, e.prototype.toString = function() {
        const t = this.length;
        return t === 0 ? "" : arguments.length === 0 ? f(this, 0, t) : o.apply(this, arguments);
      }, e.prototype.toLocaleString = e.prototype.toString, e.prototype.equals = function(t) {
        if (!e.isBuffer(t))
          throw new TypeError("Argument must be a Buffer");
        return this === t || e.compare(this, t) === 0;
      }, e.prototype.inspect = function() {
        let t = "";
        const n = N.INSPECT_MAX_BYTES;
        return t = this.toString("hex", 0, n).replace(/(.{2})/g, "$1 ").trim(), this.length > n && (t += " ... "), "<Buffer " + t + ">";
      }, O && (e.prototype[O] = e.prototype.inspect), e.prototype.compare = function(t, n, r, _, E) {
        if (R(t, Uint8Array) && (t = e.from(t, t.offset, t.byteLength)), !e.isBuffer(t))
          throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type ' + typeof t);
        if (n === void 0 && (n = 0), r === void 0 && (r = t ? t.length : 0), _ === void 0 && (_ = 0), E === void 0 && (E = this.length), n < 0 || r > t.length || _ < 0 || E > this.length)
          throw new RangeError("out of range index");
        if (_ >= E && n >= r)
          return 0;
        if (_ >= E)
          return -1;
        if (n >= r)
          return 1;
        if (this === t)
          return 0;
        let x = (E >>>= 0) - (_ >>>= 0), q = (r >>>= 0) - (n >>>= 0);
        const ut = Math.min(x, q), pt = this.slice(_, E), bt = t.slice(n, r);
        for (let dt = 0; dt < ut; ++dt)
          if (pt[dt] !== bt[dt]) {
            x = pt[dt], q = bt[dt];
            break;
          }
        return x < q ? -1 : q < x ? 1 : 0;
      }, e.prototype.includes = function(t, n, r) {
        return this.indexOf(t, n, r) !== -1;
      }, e.prototype.indexOf = function(t, n, r) {
        return y(this, t, n, r, !0);
      }, e.prototype.lastIndexOf = function(t, n, r) {
        return y(this, t, n, r, !1);
      }, e.prototype.write = function(t, n, r, _) {
        if (n === void 0)
          _ = "utf8", r = this.length, n = 0;
        else if (r === void 0 && typeof n == "string")
          _ = n, r = this.length, n = 0;
        else {
          if (!isFinite(n))
            throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");
          n >>>= 0, isFinite(r) ? (r >>>= 0, _ === void 0 && (_ = "utf8")) : (_ = r, r = void 0);
        }
        const E = this.length - n;
        if ((r === void 0 || r > E) && (r = E), t.length > 0 && (r < 0 || n < 0) || n > this.length)
          throw new RangeError("Attempt to write outside buffer bounds");
        _ || (_ = "utf8");
        let x = !1;
        for (; ; )
          switch (_) {
            case "hex":
              return z(this, t, n, r);
            case "utf8":
            case "utf-8":
              return Y(this, t, n, r);
            case "ascii":
            case "latin1":
            case "binary":
              return k(this, t, n, r);
            case "base64":
              return Q(this, t, n, r);
            case "ucs2":
            case "ucs-2":
            case "utf16le":
            case "utf-16le":
              return L(this, t, n, r);
            default:
              if (x)
                throw new TypeError("Unknown encoding: " + _);
              _ = ("" + _).toLowerCase(), x = !0;
          }
      }, e.prototype.toJSON = function() {
        return { type: "Buffer", data: Array.prototype.slice.call(this._arr || this, 0) };
      };
      const F = 4096;
      function U(t, n, r) {
        let _ = "";
        r = Math.min(t.length, r);
        for (let E = n; E < r; ++E)
          _ += String.fromCharCode(127 & t[E]);
        return _;
      }
      function D(t, n, r) {
        let _ = "";
        r = Math.min(t.length, r);
        for (let E = n; E < r; ++E)
          _ += String.fromCharCode(t[E]);
        return _;
      }
      function rt(t, n, r) {
        const _ = t.length;
        (!n || n < 0) && (n = 0), (!r || r < 0 || r > _) && (r = _);
        let E = "";
        for (let x = n; x < r; ++x)
          E += j[t[x]];
        return E;
      }
      function u(t, n, r) {
        const _ = t.slice(n, r);
        let E = "";
        for (let x = 0; x < _.length - 1; x += 2)
          E += String.fromCharCode(_[x] + 256 * _[x + 1]);
        return E;
      }
      function B(t, n, r) {
        if (t % 1 != 0 || t < 0)
          throw new RangeError("offset is not uint");
        if (t + n > r)
          throw new RangeError("Trying to access beyond buffer length");
      }
      function K(t, n, r, _, E, x) {
        if (!e.isBuffer(t))
          throw new TypeError('"buffer" argument must be a Buffer instance');
        if (n > E || n < x)
          throw new RangeError('"value" argument is out of bounds');
        if (r + _ > t.length)
          throw new RangeError("Index out of range");
      }
      function T(t, n, r, _, E) {
        ht(n, _, E, t, r, 7);
        let x = Number(n & BigInt(4294967295));
        t[r++] = x, x >>= 8, t[r++] = x, x >>= 8, t[r++] = x, x >>= 8, t[r++] = x;
        let q = Number(n >> BigInt(32) & BigInt(4294967295));
        return t[r++] = q, q >>= 8, t[r++] = q, q >>= 8, t[r++] = q, q >>= 8, t[r++] = q, r;
      }
      function $(t, n, r, _, E) {
        ht(n, _, E, t, r, 7);
        let x = Number(n & BigInt(4294967295));
        t[r + 7] = x, x >>= 8, t[r + 6] = x, x >>= 8, t[r + 5] = x, x >>= 8, t[r + 4] = x;
        let q = Number(n >> BigInt(32) & BigInt(4294967295));
        return t[r + 3] = q, q >>= 8, t[r + 2] = q, q >>= 8, t[r + 1] = q, q >>= 8, t[r] = q, r + 8;
      }
      function tt(t, n, r, _, E, x) {
        if (r + _ > t.length)
          throw new RangeError("Index out of range");
        if (r < 0)
          throw new RangeError("Index out of range");
      }
      function W(t, n, r, _, E) {
        return n = +n, r >>>= 0, E || tt(t, 0, r, 4), m.write(t, n, r, _, 23, 4), r + 4;
      }
      function J(t, n, r, _, E) {
        return n = +n, r >>>= 0, E || tt(t, 0, r, 8), m.write(t, n, r, _, 52, 8), r + 8;
      }
      e.prototype.slice = function(t, n) {
        const r = this.length;
        (t = ~~t) < 0 ? (t += r) < 0 && (t = 0) : t > r && (t = r), (n = n === void 0 ? r : ~~n) < 0 ? (n += r) < 0 && (n = 0) : n > r && (n = r), n < t && (n = t);
        const _ = this.subarray(t, n);
        return Object.setPrototypeOf(_, e.prototype), _;
      }, e.prototype.readUintLE = e.prototype.readUIntLE = function(t, n, r) {
        t >>>= 0, n >>>= 0, r || B(t, n, this.length);
        let _ = this[t], E = 1, x = 0;
        for (; ++x < n && (E *= 256); )
          _ += this[t + x] * E;
        return _;
      }, e.prototype.readUintBE = e.prototype.readUIntBE = function(t, n, r) {
        t >>>= 0, n >>>= 0, r || B(t, n, this.length);
        let _ = this[t + --n], E = 1;
        for (; n > 0 && (E *= 256); )
          _ += this[t + --n] * E;
        return _;
      }, e.prototype.readUint8 = e.prototype.readUInt8 = function(t, n) {
        return t >>>= 0, n || B(t, 1, this.length), this[t];
      }, e.prototype.readUint16LE = e.prototype.readUInt16LE = function(t, n) {
        return t >>>= 0, n || B(t, 2, this.length), this[t] | this[t + 1] << 8;
      }, e.prototype.readUint16BE = e.prototype.readUInt16BE = function(t, n) {
        return t >>>= 0, n || B(t, 2, this.length), this[t] << 8 | this[t + 1];
      }, e.prototype.readUint32LE = e.prototype.readUInt32LE = function(t, n) {
        return t >>>= 0, n || B(t, 4, this.length), (this[t] | this[t + 1] << 8 | this[t + 2] << 16) + 16777216 * this[t + 3];
      }, e.prototype.readUint32BE = e.prototype.readUInt32BE = function(t, n) {
        return t >>>= 0, n || B(t, 4, this.length), 16777216 * this[t] + (this[t + 1] << 16 | this[t + 2] << 8 | this[t + 3]);
      }, e.prototype.readBigUInt64LE = et(function(t) {
        lt(t >>>= 0, "offset");
        const n = this[t], r = this[t + 7];
        n !== void 0 && r !== void 0 || ct(t, this.length - 8);
        const _ = n + 256 * this[++t] + 65536 * this[++t] + this[++t] * 2 ** 24, E = this[++t] + 256 * this[++t] + 65536 * this[++t] + r * 2 ** 24;
        return BigInt(_) + (BigInt(E) << BigInt(32));
      }), e.prototype.readBigUInt64BE = et(function(t) {
        lt(t >>>= 0, "offset");
        const n = this[t], r = this[t + 7];
        n !== void 0 && r !== void 0 || ct(t, this.length - 8);
        const _ = n * 2 ** 24 + 65536 * this[++t] + 256 * this[++t] + this[++t], E = this[++t] * 2 ** 24 + 65536 * this[++t] + 256 * this[++t] + r;
        return (BigInt(_) << BigInt(32)) + BigInt(E);
      }), e.prototype.readIntLE = function(t, n, r) {
        t >>>= 0, n >>>= 0, r || B(t, n, this.length);
        let _ = this[t], E = 1, x = 0;
        for (; ++x < n && (E *= 256); )
          _ += this[t + x] * E;
        return E *= 128, _ >= E && (_ -= Math.pow(2, 8 * n)), _;
      }, e.prototype.readIntBE = function(t, n, r) {
        t >>>= 0, n >>>= 0, r || B(t, n, this.length);
        let _ = n, E = 1, x = this[t + --_];
        for (; _ > 0 && (E *= 256); )
          x += this[t + --_] * E;
        return E *= 128, x >= E && (x -= Math.pow(2, 8 * n)), x;
      }, e.prototype.readInt8 = function(t, n) {
        return t >>>= 0, n || B(t, 1, this.length), 128 & this[t] ? -1 * (255 - this[t] + 1) : this[t];
      }, e.prototype.readInt16LE = function(t, n) {
        t >>>= 0, n || B(t, 2, this.length);
        const r = this[t] | this[t + 1] << 8;
        return 32768 & r ? 4294901760 | r : r;
      }, e.prototype.readInt16BE = function(t, n) {
        t >>>= 0, n || B(t, 2, this.length);
        const r = this[t + 1] | this[t] << 8;
        return 32768 & r ? 4294901760 | r : r;
      }, e.prototype.readInt32LE = function(t, n) {
        return t >>>= 0, n || B(t, 4, this.length), this[t] | this[t + 1] << 8 | this[t + 2] << 16 | this[t + 3] << 24;
      }, e.prototype.readInt32BE = function(t, n) {
        return t >>>= 0, n || B(t, 4, this.length), this[t] << 24 | this[t + 1] << 16 | this[t + 2] << 8 | this[t + 3];
      }, e.prototype.readBigInt64LE = et(function(t) {
        lt(t >>>= 0, "offset");
        const n = this[t], r = this[t + 7];
        n !== void 0 && r !== void 0 || ct(t, this.length - 8);
        const _ = this[t + 4] + 256 * this[t + 5] + 65536 * this[t + 6] + (r << 24);
        return (BigInt(_) << BigInt(32)) + BigInt(n + 256 * this[++t] + 65536 * this[++t] + this[++t] * 16777216);
      }), e.prototype.readBigInt64BE = et(function(t) {
        lt(t >>>= 0, "offset");
        const n = this[t], r = this[t + 7];
        n !== void 0 && r !== void 0 || ct(t, this.length - 8);
        const _ = (n << 24) + 65536 * this[++t] + 256 * this[++t] + this[++t];
        return (BigInt(_) << BigInt(32)) + BigInt(this[++t] * 16777216 + 65536 * this[++t] + 256 * this[++t] + r);
      }), e.prototype.readFloatLE = function(t, n) {
        return t >>>= 0, n || B(t, 4, this.length), m.read(this, t, !0, 23, 4);
      }, e.prototype.readFloatBE = function(t, n) {
        return t >>>= 0, n || B(t, 4, this.length), m.read(this, t, !1, 23, 4);
      }, e.prototype.readDoubleLE = function(t, n) {
        return t >>>= 0, n || B(t, 8, this.length), m.read(this, t, !0, 52, 8);
      }, e.prototype.readDoubleBE = function(t, n) {
        return t >>>= 0, n || B(t, 8, this.length), m.read(this, t, !1, 52, 8);
      }, e.prototype.writeUintLE = e.prototype.writeUIntLE = function(t, n, r, _) {
        t = +t, n >>>= 0, r >>>= 0, _ || K(this, t, n, r, Math.pow(2, 8 * r) - 1, 0);
        let E = 1, x = 0;
        for (this[n] = 255 & t; ++x < r && (E *= 256); )
          this[n + x] = t / E & 255;
        return n + r;
      }, e.prototype.writeUintBE = e.prototype.writeUIntBE = function(t, n, r, _) {
        t = +t, n >>>= 0, r >>>= 0, _ || K(this, t, n, r, Math.pow(2, 8 * r) - 1, 0);
        let E = r - 1, x = 1;
        for (this[n + E] = 255 & t; --E >= 0 && (x *= 256); )
          this[n + E] = t / x & 255;
        return n + r;
      }, e.prototype.writeUint8 = e.prototype.writeUInt8 = function(t, n, r) {
        return t = +t, n >>>= 0, r || K(this, t, n, 1, 255, 0), this[n] = 255 & t, n + 1;
      }, e.prototype.writeUint16LE = e.prototype.writeUInt16LE = function(t, n, r) {
        return t = +t, n >>>= 0, r || K(this, t, n, 2, 65535, 0), this[n] = 255 & t, this[n + 1] = t >>> 8, n + 2;
      }, e.prototype.writeUint16BE = e.prototype.writeUInt16BE = function(t, n, r) {
        return t = +t, n >>>= 0, r || K(this, t, n, 2, 65535, 0), this[n] = t >>> 8, this[n + 1] = 255 & t, n + 2;
      }, e.prototype.writeUint32LE = e.prototype.writeUInt32LE = function(t, n, r) {
        return t = +t, n >>>= 0, r || K(this, t, n, 4, 4294967295, 0), this[n + 3] = t >>> 24, this[n + 2] = t >>> 16, this[n + 1] = t >>> 8, this[n] = 255 & t, n + 4;
      }, e.prototype.writeUint32BE = e.prototype.writeUInt32BE = function(t, n, r) {
        return t = +t, n >>>= 0, r || K(this, t, n, 4, 4294967295, 0), this[n] = t >>> 24, this[n + 1] = t >>> 16, this[n + 2] = t >>> 8, this[n + 3] = 255 & t, n + 4;
      }, e.prototype.writeBigUInt64LE = et(function(t, n = 0) {
        return T(this, t, n, BigInt(0), BigInt("0xffffffffffffffff"));
      }), e.prototype.writeBigUInt64BE = et(function(t, n = 0) {
        return $(this, t, n, BigInt(0), BigInt("0xffffffffffffffff"));
      }), e.prototype.writeIntLE = function(t, n, r, _) {
        if (t = +t, n >>>= 0, !_) {
          const ut = Math.pow(2, 8 * r - 1);
          K(this, t, n, r, ut - 1, -ut);
        }
        let E = 0, x = 1, q = 0;
        for (this[n] = 255 & t; ++E < r && (x *= 256); )
          t < 0 && q === 0 && this[n + E - 1] !== 0 && (q = 1), this[n + E] = (t / x | 0) - q & 255;
        return n + r;
      }, e.prototype.writeIntBE = function(t, n, r, _) {
        if (t = +t, n >>>= 0, !_) {
          const ut = Math.pow(2, 8 * r - 1);
          K(this, t, n, r, ut - 1, -ut);
        }
        let E = r - 1, x = 1, q = 0;
        for (this[n + E] = 255 & t; --E >= 0 && (x *= 256); )
          t < 0 && q === 0 && this[n + E + 1] !== 0 && (q = 1), this[n + E] = (t / x | 0) - q & 255;
        return n + r;
      }, e.prototype.writeInt8 = function(t, n, r) {
        return t = +t, n >>>= 0, r || K(this, t, n, 1, 127, -128), t < 0 && (t = 255 + t + 1), this[n] = 255 & t, n + 1;
      }, e.prototype.writeInt16LE = function(t, n, r) {
        return t = +t, n >>>= 0, r || K(this, t, n, 2, 32767, -32768), this[n] = 255 & t, this[n + 1] = t >>> 8, n + 2;
      }, e.prototype.writeInt16BE = function(t, n, r) {
        return t = +t, n >>>= 0, r || K(this, t, n, 2, 32767, -32768), this[n] = t >>> 8, this[n + 1] = 255 & t, n + 2;
      }, e.prototype.writeInt32LE = function(t, n, r) {
        return t = +t, n >>>= 0, r || K(this, t, n, 4, 2147483647, -2147483648), this[n] = 255 & t, this[n + 1] = t >>> 8, this[n + 2] = t >>> 16, this[n + 3] = t >>> 24, n + 4;
      }, e.prototype.writeInt32BE = function(t, n, r) {
        return t = +t, n >>>= 0, r || K(this, t, n, 4, 2147483647, -2147483648), t < 0 && (t = 4294967295 + t + 1), this[n] = t >>> 24, this[n + 1] = t >>> 16, this[n + 2] = t >>> 8, this[n + 3] = 255 & t, n + 4;
      }, e.prototype.writeBigInt64LE = et(function(t, n = 0) {
        return T(this, t, n, -BigInt("0x8000000000000000"), BigInt("0x7fffffffffffffff"));
      }), e.prototype.writeBigInt64BE = et(function(t, n = 0) {
        return $(this, t, n, -BigInt("0x8000000000000000"), BigInt("0x7fffffffffffffff"));
      }), e.prototype.writeFloatLE = function(t, n, r) {
        return W(this, t, n, !0, r);
      }, e.prototype.writeFloatBE = function(t, n, r) {
        return W(this, t, n, !1, r);
      }, e.prototype.writeDoubleLE = function(t, n, r) {
        return J(this, t, n, !0, r);
      }, e.prototype.writeDoubleBE = function(t, n, r) {
        return J(this, t, n, !1, r);
      }, e.prototype.copy = function(t, n, r, _) {
        if (!e.isBuffer(t))
          throw new TypeError("argument should be a Buffer");
        if (r || (r = 0), _ || _ === 0 || (_ = this.length), n >= t.length && (n = t.length), n || (n = 0), _ > 0 && _ < r && (_ = r), _ === r || t.length === 0 || this.length === 0)
          return 0;
        if (n < 0)
          throw new RangeError("targetStart out of bounds");
        if (r < 0 || r >= this.length)
          throw new RangeError("Index out of range");
        if (_ < 0)
          throw new RangeError("sourceEnd out of bounds");
        _ > this.length && (_ = this.length), t.length - n < _ - r && (_ = t.length - n + r);
        const E = _ - r;
        return this === t && typeof Uint8Array.prototype.copyWithin == "function" ? this.copyWithin(n, r, _) : Uint8Array.prototype.set.call(t, this.subarray(r, _), n), E;
      }, e.prototype.fill = function(t, n, r, _) {
        if (typeof t == "string") {
          if (typeof n == "string" ? (_ = n, n = 0, r = this.length) : typeof r == "string" && (_ = r, r = this.length), _ !== void 0 && typeof _ != "string")
            throw new TypeError("encoding must be a string");
          if (typeof _ == "string" && !e.isEncoding(_))
            throw new TypeError("Unknown encoding: " + _);
          if (t.length === 1) {
            const x = t.charCodeAt(0);
            (_ === "utf8" && x < 128 || _ === "latin1") && (t = x);
          }
        } else
          typeof t == "number" ? t &= 255 : typeof t == "boolean" && (t = Number(t));
        if (n < 0 || this.length < n || this.length < r)
          throw new RangeError("Out of range index");
        if (r <= n)
          return this;
        let E;
        if (n >>>= 0, r = r === void 0 ? this.length : r >>> 0, t || (t = 0), typeof t == "number")
          for (E = n; E < r; ++E)
            this[E] = t;
        else {
          const x = e.isBuffer(t) ? t : e.from(t, _), q = x.length;
          if (q === 0)
            throw new TypeError('The value "' + t + '" is invalid for argument "value"');
          for (E = 0; E < r - n; ++E)
            this[E + n] = x[E % q];
        }
        return this;
      };
      const C = {};
      function nt(t, n, r) {
        C[t] = class extends r {
          constructor() {
            super(), Object.defineProperty(this, "message", { value: n.apply(this, arguments), writable: !0, configurable: !0 }), this.name = `${this.name} [${t}]`, this.stack, delete this.name;
          }
          get code() {
            return t;
          }
          set code(_) {
            Object.defineProperty(this, "code", { configurable: !0, enumerable: !0, value: _, writable: !0 });
          }
          toString() {
            return `${this.name} [${t}]: ${this.message}`;
          }
        };
      }
      function Z(t) {
        let n = "", r = t.length;
        const _ = t[0] === "-" ? 1 : 0;
        for (; r >= _ + 4; r -= 3)
          n = `_${t.slice(r - 3, r)}${n}`;
        return `${t.slice(0, r)}${n}`;
      }
      function ht(t, n, r, _, E, x) {
        if (t > r || t < n) {
          const q = typeof n == "bigint" ? "n" : "";
          let ut;
          throw ut = x > 3 ? n === 0 || n === BigInt(0) ? `>= 0${q} and < 2${q} ** ${8 * (x + 1)}${q}` : `>= -(2${q} ** ${8 * (x + 1) - 1}${q}) and < 2 ** ${8 * (x + 1) - 1}${q}` : `>= ${n}${q} and <= ${r}${q}`, new C.ERR_OUT_OF_RANGE("value", ut, t);
        }
        (function(q, ut, pt) {
          lt(ut, "offset"), q[ut] !== void 0 && q[ut + pt] !== void 0 || ct(ut, q.length - (pt + 1));
        })(_, E, x);
      }
      function lt(t, n) {
        if (typeof t != "number")
          throw new C.ERR_INVALID_ARG_TYPE(n, "number", t);
      }
      function ct(t, n, r) {
        throw Math.floor(t) !== t ? (lt(t, r), new C.ERR_OUT_OF_RANGE(r || "offset", "an integer", t)) : n < 0 ? new C.ERR_BUFFER_OUT_OF_BOUNDS() : new C.ERR_OUT_OF_RANGE(r || "offset", `>= ${r ? 1 : 0} and <= ${n}`, t);
      }
      nt("ERR_BUFFER_OUT_OF_BOUNDS", function(t) {
        return t ? `${t} is outside of buffer bounds` : "Attempt to access memory outside buffer bounds";
      }, RangeError), nt("ERR_INVALID_ARG_TYPE", function(t, n) {
        return `The "${t}" argument must be of type number. Received type ${typeof n}`;
      }, TypeError), nt("ERR_OUT_OF_RANGE", function(t, n, r) {
        let _ = `The value of "${t}" is out of range.`, E = r;
        return Number.isInteger(r) && Math.abs(r) > 4294967296 ? E = Z(String(r)) : typeof r == "bigint" && (E = String(r), (r > BigInt(2) ** BigInt(32) || r < -(BigInt(2) ** BigInt(32))) && (E = Z(E)), E += "n"), _ += ` It must be ${n}. Received ${E}`, _;
      }, RangeError);
      const I = /[^+/0-9A-Za-z-_]/g;
      function X(t, n) {
        let r;
        n = n || 1 / 0;
        const _ = t.length;
        let E = null;
        const x = [];
        for (let q = 0; q < _; ++q) {
          if (r = t.charCodeAt(q), r > 55295 && r < 57344) {
            if (!E) {
              if (r > 56319) {
                (n -= 3) > -1 && x.push(239, 191, 189);
                continue;
              }
              if (q + 1 === _) {
                (n -= 3) > -1 && x.push(239, 191, 189);
                continue;
              }
              E = r;
              continue;
            }
            if (r < 56320) {
              (n -= 3) > -1 && x.push(239, 191, 189), E = r;
              continue;
            }
            r = 65536 + (E - 55296 << 10 | r - 56320);
          } else
            E && (n -= 3) > -1 && x.push(239, 191, 189);
          if (E = null, r < 128) {
            if ((n -= 1) < 0)
              break;
            x.push(r);
          } else if (r < 2048) {
            if ((n -= 2) < 0)
              break;
            x.push(r >> 6 | 192, 63 & r | 128);
          } else if (r < 65536) {
            if ((n -= 3) < 0)
              break;
            x.push(r >> 12 | 224, r >> 6 & 63 | 128, 63 & r | 128);
          } else {
            if (!(r < 1114112))
              throw new Error("Invalid code point");
            if ((n -= 4) < 0)
              break;
            x.push(r >> 18 | 240, r >> 12 & 63 | 128, r >> 6 & 63 | 128, 63 & r | 128);
          }
        }
        return x;
      }
      function H(t) {
        return S.toByteArray(function(n) {
          if ((n = (n = n.split("=")[0]).trim().replace(I, "")).length < 2)
            return "";
          for (; n.length % 4 != 0; )
            n += "=";
          return n;
        }(t));
      }
      function at(t, n, r, _) {
        let E;
        for (E = 0; E < _ && !(E + r >= n.length || E >= t.length); ++E)
          n[E + r] = t[E];
        return E;
      }
      function R(t, n) {
        return t instanceof n || t != null && t.constructor != null && t.constructor.name != null && t.constructor.name === n.name;
      }
      function V(t) {
        return t != t;
      }
      const j = function() {
        const t = "0123456789abcdef", n = new Array(256);
        for (let r = 0; r < 16; ++r) {
          const _ = 16 * r;
          for (let E = 0; E < 16; ++E)
            n[_ + E] = t[r] + t[E];
        }
        return n;
      }();
      function et(t) {
        return typeof BigInt > "u" ? ot : t;
      }
      function ot() {
        throw new Error("BigInt not supported");
      }
    }, 9881: (G, N, s) => {
      const S = s(893), m = s(9777), O = s(3737), P = s(2893), w = s(5059), e = s(3557), i = s(2903), d = s(207);
      G.exports = { Commented: S, Diagnose: m, Decoder: O, Encoder: P, Simple: w, Tagged: e, Map: i, SharedValueEncoder: d, comment: S.comment, decodeAll: O.decodeAll, decodeFirst: O.decodeFirst, decodeAllSync: O.decodeAllSync, decodeFirstSync: O.decodeFirstSync, diagnose: m.diagnose, encode: P.encode, encodeCanonical: P.encodeCanonical, encodeOne: P.encodeOne, encodeAsync: P.encodeAsync, decode: O.decodeFirstSync, leveldb: { decode: O.decodeFirstSync, encode: P.encode, buffer: !0, name: "cbor" }, reset() {
        P.reset(), e.reset();
      } };
    }, 893: (G, N, s) => {
      const S = s(8310), m = s(7328), O = s(3737), P = s(5256), { MT: w, NUMBYTES: e, SYMS: i } = s(9452), { Buffer: d } = s(8287);
      function b(a) {
        return a > 1 ? "s" : "";
      }
      class A extends S.Transform {
        constructor(l = {}) {
          const { depth: c = 1, max_depth: o = 10, no_summary: h = !1, tags: y = {}, preferWeb: v, encoding: z, ...Y } = l;
          super({ ...Y, readableObjectMode: !1, writableObjectMode: !1 }), this.depth = c, this.max_depth = o, this.all = new P(), y[24] || (y[24] = this._tag_24.bind(this)), this.parser = new O({ tags: y, max_depth: o, preferWeb: v, encoding: z }), this.parser.on("value", this._on_value.bind(this)), this.parser.on("start", this._on_start.bind(this)), this.parser.on("start-string", this._on_start_string.bind(this)), this.parser.on("stop", this._on_stop.bind(this)), this.parser.on("more-bytes", this._on_more.bind(this)), this.parser.on("error", this._on_error.bind(this)), h || this.parser.on("data", this._on_data.bind(this)), this.parser.bs.on("read", this._on_read.bind(this));
        }
        _tag_24(l) {
          const c = new A({ depth: this.depth + 1, no_summary: !0 });
          c.on("data", (o) => this.push(o)), c.on("error", (o) => this.emit("error", o)), c.end(l);
        }
        _transform(l, c, o) {
          this.parser.write(l, c, o);
        }
        _flush(l) {
          return this.parser._flush(l);
        }
        static comment(l, c = {}, o = null) {
          if (l == null)
            throw new Error("input required");
          ({ options: c, cb: o } = function(k, Q) {
            switch (typeof k) {
              case "function":
                return { options: {}, cb: k };
              case "string":
                return { options: { encoding: k }, cb: Q };
              case "number":
                return { options: { max_depth: k }, cb: Q };
              case "object":
                return { options: k || {}, cb: Q };
              default:
                throw new TypeError("Unknown option type");
            }
          }(c, o));
          const h = new P(), { encoding: y = "hex", ...v } = c, z = new A(v);
          let Y = null;
          return typeof o == "function" ? (z.on("end", () => {
            o(null, h.toString("utf8"));
          }), z.on("error", o)) : Y = new Promise((k, Q) => {
            z.on("end", () => {
              k(h.toString("utf8"));
            }), z.on("error", Q);
          }), z.pipe(h), m.guessEncoding(l, y).pipe(z), Y;
        }
        _on_error(l) {
          this.push("ERROR: "), this.push(l.toString()), this.push(`
`);
        }
        _on_read(l) {
          this.all.write(l);
          const c = l.toString("hex");
          this.push(new Array(this.depth + 1).join("  ")), this.push(c);
          let o = 2 * (this.max_depth - this.depth) - c.length;
          o < 1 && (o = 1), this.push(new Array(o + 1).join(" ")), this.push("-- ");
        }
        _on_more(l, c, o, h) {
          let y = "";
          switch (this.depth++, l) {
            case w.POS_INT:
              y = "Positive number,";
              break;
            case w.NEG_INT:
              y = "Negative number,";
              break;
            case w.ARRAY:
              y = "Array, length";
              break;
            case w.MAP:
              y = "Map, count";
              break;
            case w.BYTE_STRING:
              y = "Bytes, length";
              break;
            case w.UTF8_STRING:
              y = "String, length";
              break;
            case w.SIMPLE_FLOAT:
              y = c === 1 ? "Simple value," : "Float,";
          }
          this.push(`${y} next ${c} byte${b(c)}
`);
        }
        _on_start_string(l, c, o, h) {
          let y = "";
          switch (this.depth++, l) {
            case w.BYTE_STRING:
              y = `Bytes, length: ${c}`;
              break;
            case w.UTF8_STRING:
              y = `String, length: ${c.toString()}`;
          }
          this.push(`${y}
`);
        }
        _on_start(l, c, o, h) {
          switch (this.depth++, o) {
            case w.ARRAY:
              this.push(`[${h}], `);
              break;
            case w.MAP:
              h % 2 ? this.push(`{Val:${Math.floor(h / 2)}}, `) : this.push(`{Key:${Math.floor(h / 2)}}, `);
          }
          switch (l) {
            case w.TAG:
              this.push(`Tag #${c}`), c === 24 && this.push(" Encoded CBOR data item");
              break;
            case w.ARRAY:
              c === i.STREAM ? this.push("Array (streaming)") : this.push(`Array, ${c} item${b(c)}`);
              break;
            case w.MAP:
              c === i.STREAM ? this.push("Map (streaming)") : this.push(`Map, ${c} pair${b(c)}`);
              break;
            case w.BYTE_STRING:
              this.push("Bytes (streaming)");
              break;
            case w.UTF8_STRING:
              this.push("String (streaming)");
          }
          this.push(`
`);
        }
        _on_stop(l) {
          this.depth--;
        }
        _on_value(l, c, o, h) {
          if (l !== i.BREAK)
            switch (c) {
              case w.ARRAY:
                this.push(`[${o}], `);
                break;
              case w.MAP:
                o % 2 ? this.push(`{Val:${Math.floor(o / 2)}}, `) : this.push(`{Key:${Math.floor(o / 2)}}, `);
            }
          const y = m.cborValueToString(l, -1 / 0);
          switch (typeof l == "string" || d.isBuffer(l) ? (l.length > 0 && (this.push(y), this.push(`
`)), this.depth--) : (this.push(y), this.push(`
`)), h) {
            case e.ONE:
            case e.TWO:
            case e.FOUR:
            case e.EIGHT:
              this.depth--;
          }
        }
        _on_data() {
          this.push("0x"), this.push(this.all.read().toString("hex")), this.push(`
`);
        }
      }
      G.exports = A;
    }, 9452: (G, N) => {
      N.MT = { POS_INT: 0, NEG_INT: 1, BYTE_STRING: 2, UTF8_STRING: 3, ARRAY: 4, MAP: 5, TAG: 6, SIMPLE_FLOAT: 7 }, N.TAG = { DATE_STRING: 0, DATE_EPOCH: 1, POS_BIGINT: 2, NEG_BIGINT: 3, DECIMAL_FRAC: 4, BIGFLOAT: 5, BASE64URL_EXPECTED: 21, BASE64_EXPECTED: 22, BASE16_EXPECTED: 23, CBOR: 24, URI: 32, BASE64URL: 33, BASE64: 34, REGEXP: 35, MIME: 36, SET: 258 }, N.NUMBYTES = { ZERO: 0, ONE: 24, TWO: 25, FOUR: 26, EIGHT: 27, INDEFINITE: 31 }, N.SIMPLE = { FALSE: 20, TRUE: 21, NULL: 22, UNDEFINED: 23 }, N.SYMS = { NULL: Symbol.for("github.com/hildjj/node-cbor/null"), UNDEFINED: Symbol.for("github.com/hildjj/node-cbor/undef"), PARENT: Symbol.for("github.com/hildjj/node-cbor/parent"), BREAK: Symbol.for("github.com/hildjj/node-cbor/break"), STREAM: Symbol.for("github.com/hildjj/node-cbor/stream") }, N.SHIFT32 = 4294967296, N.BI = { MINUS_ONE: BigInt(-1), NEG_MAX: BigInt(-1) - BigInt(Number.MAX_SAFE_INTEGER), MAXINT32: BigInt("0xffffffff"), MAXINT64: BigInt("0xffffffffffffffff"), SHIFT32: BigInt(N.SHIFT32) };
    }, 3737: (G, N, s) => {
      const S = s(4957), m = s(3557), O = s(5059), P = s(7328), w = s(5256), e = (s(8310), s(9452)), { MT: i, NUMBYTES: d, SYMS: b, BI: A } = e, { Buffer: a } = s(8287), l = Symbol("count"), c = Symbol("major type"), o = Symbol("error"), h = Symbol("not found");
      function y(Q, L, M) {
        const f = [];
        return f[l] = M, f[b.PARENT] = Q, f[c] = L, f;
      }
      function v(Q, L) {
        const M = new w();
        return M[l] = -1, M[b.PARENT] = Q, M[c] = L, M;
      }
      class z extends Error {
        constructor(L, M) {
          super(`Unexpected data: 0x${L.toString(16)}`), this.name = "UnexpectedDataError", this.byte = L, this.value = M;
        }
      }
      function Y(Q, L) {
        switch (typeof Q) {
          case "function":
            return { options: {}, cb: Q };
          case "string":
            return { options: { encoding: Q }, cb: L };
          case "object":
            return { options: Q || {}, cb: L };
          default:
            throw new TypeError("Unknown option type");
        }
      }
      class k extends S {
        constructor(L = {}) {
          const { tags: M = {}, max_depth: f = -1, preferMap: F = !1, preferWeb: U = !1, required: D = !1, encoding: rt = "hex", extendedResults: u = !1, preventDuplicateKeys: B = !1, ...K } = L;
          super({ defaultEncoding: rt, ...K }), this.running = !0, this.max_depth = f, this.tags = M, this.preferMap = F, this.preferWeb = U, this.extendedResults = u, this.required = D, this.preventDuplicateKeys = B, u && (this.bs.on("read", this._onRead.bind(this)), this.valueBytes = new w());
        }
        static nullcheck(L) {
          switch (L) {
            case b.NULL:
              return null;
            case b.UNDEFINED:
              return;
            case h:
              throw new Error("Value not found");
            default:
              return L;
          }
        }
        static decodeFirstSync(L, M = {}) {
          if (L == null)
            throw new TypeError("input required");
          ({ options: M } = Y(M));
          const { encoding: f = "hex", ...F } = M, U = new k(F), D = P.guessEncoding(L, f), rt = U._parse();
          let u = rt.next();
          for (; !u.done; ) {
            const K = D.read(u.value);
            if (K == null || K.length !== u.value)
              throw new Error("Insufficient data");
            U.extendedResults && U.valueBytes.write(K), u = rt.next(K);
          }
          let B = null;
          if (U.extendedResults)
            B = u.value, B.unused = D.read();
          else if (B = k.nullcheck(u.value), D.length > 0) {
            const K = D.read(1);
            throw D.unshift(K), new z(K[0], B);
          }
          return B;
        }
        static decodeAllSync(L, M = {}) {
          if (L == null)
            throw new TypeError("input required");
          ({ options: M } = Y(M));
          const { encoding: f = "hex", ...F } = M, U = new k(F), D = P.guessEncoding(L, f), rt = [];
          for (; D.length > 0; ) {
            const u = U._parse();
            let B = u.next();
            for (; !B.done; ) {
              const K = D.read(B.value);
              if (K == null || K.length !== B.value)
                throw new Error("Insufficient data");
              U.extendedResults && U.valueBytes.write(K), B = u.next(K);
            }
            rt.push(k.nullcheck(B.value));
          }
          return rt;
        }
        static decodeFirst(L, M = {}, f = null) {
          if (L == null)
            throw new TypeError("input required");
          ({ options: M, cb: f } = Y(M, f));
          const { encoding: F = "hex", required: U = !1, ...D } = M, rt = new k(D);
          let u = h;
          const B = P.guessEncoding(L, F), K = new Promise((T, $) => {
            rt.on("data", (tt) => {
              u = k.nullcheck(tt), rt.close();
            }), rt.once("error", (tt) => rt.extendedResults && tt instanceof z ? (u.unused = rt.bs.slice(), T(u)) : (u !== h && (tt.value = u), u = o, rt.close(), $(tt))), rt.once("end", () => {
              switch (u) {
                case h:
                  return U ? $(new Error("No CBOR found")) : T(u);
                case o:
                  return;
                default:
                  return T(u);
              }
            });
          });
          return typeof f == "function" && K.then((T) => f(null, T), f), B.pipe(rt), K;
        }
        static decodeAll(L, M = {}, f = null) {
          if (L == null)
            throw new TypeError("input required");
          ({ options: M, cb: f } = Y(M, f));
          const { encoding: F = "hex", ...U } = M, D = new k(U), rt = [];
          D.on("data", (B) => rt.push(k.nullcheck(B)));
          const u = new Promise((B, K) => {
            D.on("error", K), D.on("end", () => B(rt));
          });
          return typeof f == "function" && u.then((B) => f(void 0, B), (B) => f(B, void 0)), P.guessEncoding(L, F).pipe(D), u;
        }
        close() {
          this.running = !1, this.__fresh = !0;
        }
        _onRead(L) {
          this.valueBytes.write(L);
        }
        *_parse() {
          let L = null, M = 0, f = null;
          for (; ; ) {
            if (this.max_depth >= 0 && M > this.max_depth)
              throw new Error(`Maximum depth ${this.max_depth} exceeded`);
            const [F] = yield 1;
            if (!this.running)
              throw this.bs.unshift(a.from([F])), new z(F);
            const U = F >> 5, D = 31 & F, rt = L == null ? void 0 : L[c], u = L == null ? void 0 : L.length;
            switch (D) {
              case d.ONE:
                this.emit("more-bytes", U, 1, rt, u), [f] = yield 1;
                break;
              case d.TWO:
              case d.FOUR:
              case d.EIGHT: {
                const K = 1 << D - 24;
                this.emit("more-bytes", U, K, rt, u);
                const T = yield K;
                f = U === i.SIMPLE_FLOAT ? T : P.parseCBORint(D, T);
                break;
              }
              case 28:
              case 29:
              case 30:
                throw this.running = !1, new Error(`Additional info not implemented: ${D}`);
              case d.INDEFINITE:
                switch (U) {
                  case i.POS_INT:
                  case i.NEG_INT:
                  case i.TAG:
                    throw new Error(`Invalid indefinite encoding for MT ${U}`);
                }
                f = -1;
                break;
              default:
                f = D;
            }
            switch (U) {
              case i.POS_INT:
                break;
              case i.NEG_INT:
                f = f === Number.MAX_SAFE_INTEGER ? A.NEG_MAX : typeof f == "bigint" ? A.MINUS_ONE - f : -1 - f;
                break;
              case i.BYTE_STRING:
              case i.UTF8_STRING:
                switch (f) {
                  case 0:
                    this.emit("start-string", U, f, rt, u), f = U === i.UTF8_STRING ? "" : this.preferWeb ? new Uint8Array(0) : a.allocUnsafe(0);
                    break;
                  case -1:
                    this.emit("start", U, b.STREAM, rt, u), L = v(L, U), M++;
                    continue;
                  default:
                    this.emit("start-string", U, f, rt, u), f = yield f, U === i.UTF8_STRING ? f = P.utf8(f) : this.preferWeb && (f = new Uint8Array(f.buffer, f.byteOffset, f.length));
                }
                break;
              case i.ARRAY:
              case i.MAP:
                switch (f) {
                  case 0:
                    f = U === i.MAP ? this.preferMap ? /* @__PURE__ */ new Map() : {} : [];
                    break;
                  case -1:
                    this.emit("start", U, b.STREAM, rt, u), L = y(L, U, -1), M++;
                    continue;
                  default:
                    this.emit("start", U, f, rt, u), L = y(L, U, f * (U - 3)), M++;
                    continue;
                }
                break;
              case i.TAG:
                this.emit("start", U, f, rt, u), L = y(L, U, 1), L.push(f), M++;
                continue;
              case i.SIMPLE_FLOAT:
                if (typeof f == "number") {
                  if (D === d.ONE && f < 32)
                    throw new Error(`Invalid two-byte encoding of simple value ${f}`);
                  const K = L != null;
                  f = O.decode(f, K, K && L[l] < 0);
                } else
                  f = P.parseCBORfloat(f);
            }
            this.emit("value", f, rt, u, D);
            let B = !1;
            for (; L != null; ) {
              if (f === b.BREAK)
                L[l] = 1;
              else if (Array.isArray(L))
                L.push(f);
              else {
                const T = L[c];
                if (T != null && T !== U)
                  throw this.running = !1, new Error("Invalid major type in indefinite encoding");
                L.write(f);
              }
              if (--L[l] != 0) {
                B = !0;
                break;
              }
              if (--M, delete L[l], Array.isArray(L))
                switch (L[c]) {
                  case i.ARRAY:
                    f = L;
                    break;
                  case i.MAP: {
                    let T = !this.preferMap;
                    if (L.length % 2 != 0)
                      throw new Error(`Invalid map length: ${L.length}`);
                    for (let $ = 0, tt = L.length; T && $ < tt; $ += 2)
                      if (typeof L[$] != "string" || L[$] === "__proto__") {
                        T = !1;
                        break;
                      }
                    if (T) {
                      f = {};
                      for (let $ = 0, tt = L.length; $ < tt; $ += 2) {
                        if (this.preventDuplicateKeys && Object.prototype.hasOwnProperty.call(f, L[$]))
                          throw new Error("Duplicate keys in a map");
                        f[L[$]] = L[$ + 1];
                      }
                    } else {
                      f = /* @__PURE__ */ new Map();
                      for (let $ = 0, tt = L.length; $ < tt; $ += 2) {
                        if (this.preventDuplicateKeys && f.has(L[$]))
                          throw new Error("Duplicate keys in a map");
                        f.set(L[$], L[$ + 1]);
                      }
                    }
                    break;
                  }
                  case i.TAG:
                    f = new m(L[0], L[1]).convert(this.tags);
                }
              else if (L instanceof w)
                switch (L[c]) {
                  case i.BYTE_STRING:
                    f = L.slice(), this.preferWeb && (f = new Uint8Array(f.buffer, f.byteOffset, f.length));
                    break;
                  case i.UTF8_STRING:
                    f = L.toString("utf-8");
                }
              this.emit("stop", L[c]);
              const K = L;
              L = L[b.PARENT], delete K[b.PARENT], delete K[c];
            }
            if (!B) {
              if (this.extendedResults) {
                const K = this.valueBytes.slice(), T = { value: k.nullcheck(f), bytes: K, length: K.length };
                return this.valueBytes = new w(), T;
              }
              return f;
            }
          }
        }
      }
      k.NOT_FOUND = h, G.exports = k;
    }, 9777: (G, N, s) => {
      const S = s(8310), m = s(3737), O = s(7328), P = s(5256), { MT: w, SYMS: e } = s(9452);
      class i extends S.Transform {
        constructor(b = {}) {
          const { separator: A = `
`, stream_errors: a = !1, tags: l, max_depth: c, preferWeb: o, encoding: h, ...y } = b;
          super({ ...y, readableObjectMode: !1, writableObjectMode: !1 }), this.float_bytes = -1, this.separator = A, this.stream_errors = a, this.parser = new m({ tags: l, max_depth: c, preferWeb: o, encoding: h }), this.parser.on("more-bytes", this._on_more.bind(this)), this.parser.on("value", this._on_value.bind(this)), this.parser.on("start", this._on_start.bind(this)), this.parser.on("stop", this._on_stop.bind(this)), this.parser.on("data", this._on_data.bind(this)), this.parser.on("error", this._on_error.bind(this));
        }
        _transform(b, A, a) {
          this.parser.write(b, A, a);
        }
        _flush(b) {
          this.parser._flush((A) => this.stream_errors ? (A && this._on_error(A), b()) : b(A));
        }
        static diagnose(b, A = {}, a = null) {
          if (b == null)
            throw new TypeError("input required");
          ({ options: A, cb: a } = function(v, z) {
            switch (typeof v) {
              case "function":
                return { options: {}, cb: v };
              case "string":
                return { options: { encoding: v }, cb: z };
              case "object":
                return { options: v || {}, cb: z };
              default:
                throw new TypeError("Unknown option type");
            }
          }(A, a));
          const { encoding: l = "hex", ...c } = A, o = new P(), h = new i(c);
          let y = null;
          return typeof a == "function" ? (h.on("end", () => a(null, o.toString("utf8"))), h.on("error", a)) : y = new Promise((v, z) => {
            h.on("end", () => v(o.toString("utf8"))), h.on("error", z);
          }), h.pipe(o), O.guessEncoding(b, l).pipe(h), y;
        }
        _on_error(b) {
          this.stream_errors ? this.push(b.toString()) : this.emit("error", b);
        }
        _on_more(b, A, a, l) {
          b === w.SIMPLE_FLOAT && (this.float_bytes = { 2: 1, 4: 2, 8: 3 }[A]);
        }
        _fore(b, A) {
          switch (b) {
            case w.BYTE_STRING:
            case w.UTF8_STRING:
            case w.ARRAY:
              A > 0 && this.push(", ");
              break;
            case w.MAP:
              A > 0 && (A % 2 ? this.push(": ") : this.push(", "));
          }
        }
        _on_value(b, A, a) {
          if (b === e.BREAK)
            return;
          this._fore(A, a);
          const l = this.float_bytes;
          this.float_bytes = -1, this.push(O.cborValueToString(b, l));
        }
        _on_start(b, A, a, l) {
          switch (this._fore(a, l), b) {
            case w.TAG:
              this.push(`${A}(`);
              break;
            case w.ARRAY:
              this.push("[");
              break;
            case w.MAP:
              this.push("{");
              break;
            case w.BYTE_STRING:
            case w.UTF8_STRING:
              this.push("(");
          }
          A === e.STREAM && this.push("_ ");
        }
        _on_stop(b) {
          switch (b) {
            case w.TAG:
              this.push(")");
              break;
            case w.ARRAY:
              this.push("]");
              break;
            case w.MAP:
              this.push("}");
              break;
            case w.BYTE_STRING:
            case w.UTF8_STRING:
              this.push(")");
          }
        }
        _on_data() {
          this.push(this.separator);
        }
      }
      G.exports = i;
    }, 2893: (G, N, s) => {
      const S = s(8310), m = s(5256), O = s(7328), P = s(9452), { MT: w, NUMBYTES: e, SHIFT32: i, SIMPLE: d, SYMS: b, TAG: A, BI: a } = P, { Buffer: l } = s(8287), c = w.SIMPLE_FLOAT << 5 | e.TWO, o = w.SIMPLE_FLOAT << 5 | e.FOUR, h = w.SIMPLE_FLOAT << 5 | e.EIGHT, y = w.SIMPLE_FLOAT << 5 | d.TRUE, v = w.SIMPLE_FLOAT << 5 | d.FALSE, z = w.SIMPLE_FLOAT << 5 | d.UNDEFINED, Y = w.SIMPLE_FLOAT << 5 | d.NULL, k = l.from([255]), Q = l.from("f97e00", "hex"), L = l.from("f9fc00", "hex"), M = l.from("f97c00", "hex"), f = l.from("f98000", "hex"), F = {};
      let U = {};
      class D extends S.Transform {
        constructor(u = {}) {
          const { canonical: B = !1, encodeUndefined: K, disallowUndefinedKeys: T = !1, dateType: $ = "number", collapseBigIntegers: tt = !1, detectLoops: W = !1, omitUndefinedProperties: J = !1, genTypes: C = [], ...nt } = u;
          if (super({ ...nt, readableObjectMode: !1, writableObjectMode: !0 }), this.canonical = B, this.encodeUndefined = K, this.disallowUndefinedKeys = T, this.dateType = function(Z) {
            if (!Z)
              return "number";
            switch (Z.toLowerCase()) {
              case "number":
                return "number";
              case "float":
                return "float";
              case "int":
              case "integer":
                return "int";
              case "string":
                return "string";
            }
            throw new TypeError(`dateType invalid, got "${Z}"`);
          }($), this.collapseBigIntegers = !!this.canonical || tt, this.detectLoops = void 0, typeof W == "boolean")
            W && (this.detectLoops = /* @__PURE__ */ new WeakSet());
          else {
            if (!(W instanceof WeakSet))
              throw new TypeError("detectLoops must be boolean or WeakSet");
            this.detectLoops = W;
          }
          if (this.omitUndefinedProperties = J, this.semanticTypes = { ...D.SEMANTIC_TYPES }, Array.isArray(C))
            for (let Z = 0, ht = C.length; Z < ht; Z += 2)
              this.addSemanticType(C[Z], C[Z + 1]);
          else
            for (const [Z, ht] of Object.entries(C))
              this.addSemanticType(Z, ht);
        }
        _transform(u, B, K) {
          K(this.pushAny(u) === !1 ? new Error("Push Error") : void 0);
        }
        _flush(u) {
          u();
        }
        _pushUInt8(u) {
          const B = l.allocUnsafe(1);
          return B.writeUInt8(u, 0), this.push(B);
        }
        _pushUInt16BE(u) {
          const B = l.allocUnsafe(2);
          return B.writeUInt16BE(u, 0), this.push(B);
        }
        _pushUInt32BE(u) {
          const B = l.allocUnsafe(4);
          return B.writeUInt32BE(u, 0), this.push(B);
        }
        _pushFloatBE(u) {
          const B = l.allocUnsafe(4);
          return B.writeFloatBE(u, 0), this.push(B);
        }
        _pushDoubleBE(u) {
          const B = l.allocUnsafe(8);
          return B.writeDoubleBE(u, 0), this.push(B);
        }
        _pushNaN() {
          return this.push(Q);
        }
        _pushInfinity(u) {
          const B = u < 0 ? L : M;
          return this.push(B);
        }
        _pushFloat(u) {
          if (this.canonical) {
            const B = l.allocUnsafe(2);
            if (O.writeHalf(B, u))
              return this._pushUInt8(c) && this.push(B);
          }
          return Math.fround(u) === u ? this._pushUInt8(o) && this._pushFloatBE(u) : this._pushUInt8(h) && this._pushDoubleBE(u);
        }
        _pushInt(u, B, K) {
          const T = B << 5;
          if (u < 24)
            return this._pushUInt8(T | u);
          if (u <= 255)
            return this._pushUInt8(T | e.ONE) && this._pushUInt8(u);
          if (u <= 65535)
            return this._pushUInt8(T | e.TWO) && this._pushUInt16BE(u);
          if (u <= 4294967295)
            return this._pushUInt8(T | e.FOUR) && this._pushUInt32BE(u);
          let $ = Number.MAX_SAFE_INTEGER;
          return B === w.NEG_INT && $--, u <= $ ? this._pushUInt8(T | e.EIGHT) && this._pushUInt32BE(Math.floor(u / i)) && this._pushUInt32BE(u % i) : B === w.NEG_INT ? this._pushFloat(K) : this._pushFloat(u);
        }
        _pushIntNum(u) {
          return Object.is(u, -0) ? this.push(f) : u < 0 ? this._pushInt(-u - 1, w.NEG_INT, u) : this._pushInt(u, w.POS_INT);
        }
        _pushNumber(u) {
          return isNaN(u) ? this._pushNaN() : isFinite(u) ? Math.round(u) === u ? this._pushIntNum(u) : this._pushFloat(u) : this._pushInfinity(u);
        }
        _pushString(u) {
          const B = l.byteLength(u, "utf8");
          return this._pushInt(B, w.UTF8_STRING) && this.push(u, "utf8");
        }
        _pushBoolean(u) {
          return this._pushUInt8(u ? y : v);
        }
        _pushUndefined(u) {
          switch (typeof this.encodeUndefined) {
            case "undefined":
              return this._pushUInt8(z);
            case "function":
              return this.pushAny(this.encodeUndefined(u));
            case "object": {
              const B = O.bufferishToBuffer(this.encodeUndefined);
              if (B)
                return this.push(B);
            }
          }
          return this.pushAny(this.encodeUndefined);
        }
        _pushNull(u) {
          return this._pushUInt8(Y);
        }
        _pushTag(u) {
          return this._pushInt(u, w.TAG);
        }
        _pushJSBigint(u) {
          let B = w.POS_INT, K = A.POS_BIGINT;
          if (u < 0 && (u = -u + a.MINUS_ONE, B = w.NEG_INT, K = A.NEG_BIGINT), this.collapseBigIntegers && u <= a.MAXINT64)
            return u <= 4294967295 ? this._pushInt(Number(u), B) : this._pushUInt8(B << 5 | e.EIGHT) && this._pushUInt32BE(Number(u / a.SHIFT32)) && this._pushUInt32BE(Number(u % a.SHIFT32));
          let T = u.toString(16);
          T.length % 2 && (T = `0${T}`);
          const $ = l.from(T, "hex");
          return this._pushTag(K) && D._pushBuffer(this, $);
        }
        _pushObject(u, B) {
          if (!u)
            return this._pushNull(u);
          if (!(B = { indefinite: !1, skipTypes: !1, ...B }).indefinite && this.detectLoops) {
            if (this.detectLoops.has(u))
              throw new Error(`Loop detected while CBOR encoding.
Call removeLoopDetectors before resuming.`);
            this.detectLoops.add(u);
          }
          if (!B.skipTypes) {
            const tt = u.encodeCBOR;
            if (typeof tt == "function")
              return tt.call(u, this);
            const W = this.semanticTypes[u.constructor.name];
            if (W)
              return W.call(u, this, u);
          }
          const K = Object.keys(u).filter((tt) => {
            const W = typeof u[tt];
            return W !== "function" && (!this.omitUndefinedProperties || W !== "undefined");
          }), T = {};
          if (this.canonical && K.sort((tt, W) => {
            const J = T[tt] || (T[tt] = D.encode(tt)), C = T[W] || (T[W] = D.encode(W));
            return J.compare(C);
          }), B.indefinite) {
            if (!this._pushUInt8(w.MAP << 5 | e.INDEFINITE))
              return !1;
          } else if (!this._pushInt(K.length, w.MAP))
            return !1;
          let $ = null;
          for (let tt = 0, W = K.length; tt < W; tt++) {
            const J = K[tt];
            if (this.canonical && ($ = T[J])) {
              if (!this.push($))
                return !1;
            } else if (!this._pushString(J))
              return !1;
            if (!this.pushAny(u[J]))
              return !1;
          }
          if (B.indefinite) {
            if (!this.push(k))
              return !1;
          } else
            this.detectLoops && this.detectLoops.delete(u);
          return !0;
        }
        _encodeAll(u) {
          const B = new m({ highWaterMark: this.readableHighWaterMark });
          this.pipe(B);
          for (const K of u)
            this.pushAny(K);
          return this.end(), B.read();
        }
        addSemanticType(u, B) {
          const K = typeof u == "string" ? u : u.name, T = this.semanticTypes[K];
          if (B) {
            if (typeof B != "function")
              throw new TypeError("fun must be of type function");
            this.semanticTypes[K] = B;
          } else
            T && delete this.semanticTypes[K];
          return T;
        }
        pushAny(u) {
          switch (typeof u) {
            case "number":
              return this._pushNumber(u);
            case "bigint":
              return this._pushJSBigint(u);
            case "string":
              return this._pushString(u);
            case "boolean":
              return this._pushBoolean(u);
            case "undefined":
              return this._pushUndefined(u);
            case "object":
              return this._pushObject(u);
            case "symbol":
              switch (u) {
                case b.NULL:
                  return this._pushNull(null);
                case b.UNDEFINED:
                  return this._pushUndefined(void 0);
                default:
                  throw new TypeError(`Unknown symbol: ${u.toString()}`);
              }
            default:
              throw new TypeError(`Unknown type: ${typeof u}, ${typeof u.toString == "function" ? u.toString() : ""}`);
          }
        }
        static pushArray(u, B, K) {
          K = { indefinite: !1, ...K };
          const T = B.length;
          if (K.indefinite) {
            if (!u._pushUInt8(w.ARRAY << 5 | e.INDEFINITE))
              return !1;
          } else if (!u._pushInt(T, w.ARRAY))
            return !1;
          for (let $ = 0; $ < T; $++)
            if (!u.pushAny(B[$]))
              return !1;
          return !(K.indefinite && !u.push(k));
        }
        removeLoopDetectors() {
          return !!this.detectLoops && (this.detectLoops = /* @__PURE__ */ new WeakSet(), !0);
        }
        static _pushDate(u, B) {
          switch (u.dateType) {
            case "string":
              return u._pushTag(A.DATE_STRING) && u._pushString(B.toISOString());
            case "int":
              return u._pushTag(A.DATE_EPOCH) && u._pushIntNum(Math.round(B.getTime() / 1e3));
            case "float":
              return u._pushTag(A.DATE_EPOCH) && u._pushFloat(B.getTime() / 1e3);
            default:
              return u._pushTag(A.DATE_EPOCH) && u.pushAny(B.getTime() / 1e3);
          }
        }
        static _pushBuffer(u, B) {
          return u._pushInt(B.length, w.BYTE_STRING) && u.push(B);
        }
        static _pushNoFilter(u, B) {
          return D._pushBuffer(u, B.slice());
        }
        static _pushRegexp(u, B) {
          return u._pushTag(A.REGEXP) && u.pushAny(B.source);
        }
        static _pushSet(u, B) {
          if (!u._pushTag(A.SET) || !u._pushInt(B.size, w.ARRAY))
            return !1;
          for (const K of B)
            if (!u.pushAny(K))
              return !1;
          return !0;
        }
        static _pushURL(u, B) {
          return u._pushTag(A.URI) && u.pushAny(B.toString());
        }
        static _pushBoxed(u, B) {
          return u.pushAny(B.valueOf());
        }
        static _pushMap(u, B, K) {
          K = { indefinite: !1, ...K };
          let T = [...B.entries()];
          if (u.omitUndefinedProperties && (T = T.filter(([$, tt]) => tt !== void 0)), K.indefinite) {
            if (!u._pushUInt8(w.MAP << 5 | e.INDEFINITE))
              return !1;
          } else if (!u._pushInt(T.length, w.MAP))
            return !1;
          if (u.canonical) {
            const $ = new D({ genTypes: u.semanticTypes, canonical: u.canonical, detectLoops: !!u.detectLoops, dateType: u.dateType, disallowUndefinedKeys: u.disallowUndefinedKeys, collapseBigIntegers: u.collapseBigIntegers }), tt = new m({ highWaterMark: u.readableHighWaterMark });
            $.pipe(tt), T.sort(([W], [J]) => {
              $.pushAny(W);
              const C = tt.read();
              $.pushAny(J);
              const nt = tt.read();
              return C.compare(nt);
            });
            for (const [W, J] of T) {
              if (u.disallowUndefinedKeys && W === void 0)
                throw new Error("Invalid Map key: undefined");
              if (!u.pushAny(W) || !u.pushAny(J))
                return !1;
            }
          } else
            for (const [$, tt] of T) {
              if (u.disallowUndefinedKeys && $ === void 0)
                throw new Error("Invalid Map key: undefined");
              if (!u.pushAny($) || !u.pushAny(tt))
                return !1;
            }
          return !(K.indefinite && !u.push(k));
        }
        static _pushTypedArray(u, B) {
          let K = 64, T = B.BYTES_PER_ELEMENT;
          const { name: $ } = B.constructor;
          return $.startsWith("Float") ? (K |= 16, T /= 2) : $.includes("U") || (K |= 8), ($.includes("Clamped") || T !== 1 && !O.isBigEndian()) && (K |= 4), K |= { 1: 0, 2: 1, 4: 2, 8: 3 }[T], !!u._pushTag(K) && D._pushBuffer(u, l.from(B.buffer, B.byteOffset, B.byteLength));
        }
        static _pushArrayBuffer(u, B) {
          return D._pushBuffer(u, l.from(B));
        }
        static encodeIndefinite(u, B, K = {}) {
          if (B == null) {
            if (this == null)
              throw new Error("No object to encode");
            B = this;
          }
          const { chunkSize: T = 4096 } = K;
          let $ = !0;
          const tt = typeof B;
          let W = null;
          if (tt === "string") {
            $ = $ && u._pushUInt8(w.UTF8_STRING << 5 | e.INDEFINITE);
            let J = 0;
            for (; J < B.length; ) {
              const C = J + T;
              $ = $ && u._pushString(B.slice(J, C)), J = C;
            }
            $ = $ && u.push(k);
          } else if (W = O.bufferishToBuffer(B)) {
            $ = $ && u._pushUInt8(w.BYTE_STRING << 5 | e.INDEFINITE);
            let J = 0;
            for (; J < W.length; ) {
              const C = J + T;
              $ = $ && D._pushBuffer(u, W.slice(J, C)), J = C;
            }
            $ = $ && u.push(k);
          } else if (Array.isArray(B))
            $ = $ && D.pushArray(u, B, { indefinite: !0 });
          else if (B instanceof Map)
            $ = $ && D._pushMap(u, B, { indefinite: !0 });
          else {
            if (tt !== "object")
              throw new Error("Invalid indefinite encoding");
            $ = $ && u._pushObject(B, { indefinite: !0, skipTypes: !0 });
          }
          return $;
        }
        static encode(...u) {
          return new D()._encodeAll(u);
        }
        static encodeCanonical(...u) {
          return new D({ canonical: !0 })._encodeAll(u);
        }
        static encodeOne(u, B) {
          return new D(B)._encodeAll([u]);
        }
        static encodeAsync(u, B) {
          return new Promise((K, T) => {
            const $ = [], tt = new D(B);
            tt.on("data", (W) => $.push(W)), tt.on("error", T), tt.on("finish", () => K(l.concat($))), tt.pushAny(u), tt.end();
          });
        }
        static get SEMANTIC_TYPES() {
          return U;
        }
        static set SEMANTIC_TYPES(u) {
          U = u;
        }
        static reset() {
          D.SEMANTIC_TYPES = { ...F };
        }
      }
      Object.assign(F, { Array: D.pushArray, Date: D._pushDate, Buffer: D._pushBuffer, [l.name]: D._pushBuffer, Map: D._pushMap, NoFilter: D._pushNoFilter, [m.name]: D._pushNoFilter, RegExp: D._pushRegexp, Set: D._pushSet, ArrayBuffer: D._pushArrayBuffer, Uint8ClampedArray: D._pushTypedArray, Uint8Array: D._pushTypedArray, Uint16Array: D._pushTypedArray, Uint32Array: D._pushTypedArray, Int8Array: D._pushTypedArray, Int16Array: D._pushTypedArray, Int32Array: D._pushTypedArray, Float32Array: D._pushTypedArray, Float64Array: D._pushTypedArray, URL: D._pushURL, Boolean: D._pushBoxed, Number: D._pushBoxed, String: D._pushBoxed }), typeof BigUint64Array < "u" && (F[BigUint64Array.name] = D._pushTypedArray), typeof BigInt64Array < "u" && (F[BigInt64Array.name] = D._pushTypedArray), D.reset(), G.exports = D;
    }, 2903: (G, N, s) => {
      const { Buffer: S } = s(8287), m = s(2893), O = s(3737), { MT: P } = s(9452);
      class w extends Map {
        constructor(i) {
          super(i);
        }
        static _encode(i) {
          return m.encodeCanonical(i).toString("base64");
        }
        static _decode(i) {
          return O.decodeFirstSync(i, "base64");
        }
        get(i) {
          return super.get(w._encode(i));
        }
        set(i, d) {
          return super.set(w._encode(i), d);
        }
        delete(i) {
          return super.delete(w._encode(i));
        }
        has(i) {
          return super.has(w._encode(i));
        }
        *keys() {
          for (const i of super.keys())
            yield w._decode(i);
        }
        *entries() {
          for (const i of super.entries())
            yield [w._decode(i[0]), i[1]];
        }
        [Symbol.iterator]() {
          return this.entries();
        }
        forEach(i, d) {
          if (typeof i != "function")
            throw new TypeError("Must be function");
          for (const b of super.entries())
            i.call(this, b[1], w._decode(b[0]), this);
        }
        encodeCBOR(i) {
          if (!i._pushInt(this.size, P.MAP))
            return !1;
          if (i.canonical) {
            const d = Array.from(super.entries()).map((b) => [S.from(b[0], "base64"), b[1]]);
            d.sort((b, A) => b[0].compare(A[0]));
            for (const b of d)
              if (!i.push(b[0]) || !i.pushAny(b[1]))
                return !1;
          } else
            for (const d of super.entries())
              if (!i.push(S.from(d[0], "base64")) || !i.pushAny(d[1]))
                return !1;
          return !0;
        }
      }
      G.exports = w;
    }, 9744: (G) => {
      class N {
        constructor() {
          this.clear();
        }
        clear() {
          this.map = /* @__PURE__ */ new WeakMap(), this.count = 0, this.recording = !0;
        }
        stop() {
          this.recording = !1;
        }
        check(S) {
          const m = this.map.get(S);
          if (m)
            return m.length > 1 ? m[0] || this.recording ? m[1] : (m[0] = !0, N.FIRST) : this.recording ? (m.push(this.count++), m[1]) : N.NEVER;
          if (!this.recording)
            throw new Error("New object detected when not recording");
          return this.map.set(S, [!1]), N.NEVER;
        }
      }
      N.NEVER = -1, N.FIRST = -2, G.exports = N;
    }, 207: (G, N, s) => {
      const S = s(2893), m = s(9744), { Buffer: O } = s(8287);
      class P extends S {
        constructor(e) {
          super(e), this.valueSharing = new m();
        }
        _pushObject(e, i) {
          if (e !== null) {
            const d = this.valueSharing.check(e);
            switch (d) {
              case m.FIRST:
                this._pushTag(28);
                break;
              case m.NEVER:
                break;
              default:
                return this._pushTag(29) && this._pushIntNum(d);
            }
          }
          return super._pushObject(e, i);
        }
        stopRecording() {
          this.valueSharing.stop();
        }
        clearRecording() {
          this.valueSharing.clear();
        }
        static encode(...e) {
          const i = new P();
          i.on("data", () => {
          });
          for (const d of e)
            i.pushAny(d);
          return i.stopRecording(), i.removeAllListeners("data"), i._encodeAll(e);
        }
        static encodeCanonical(...e) {
          throw new Error("Cannot encode canonically in a SharedValueEncoder, which serializes objects multiple times.");
        }
        static encodeOne(e, i) {
          const d = new P(i);
          return d.on("data", () => {
          }), d.pushAny(e), d.stopRecording(), d.removeAllListeners("data"), d._encodeAll([e]);
        }
        static encodeAsync(e, i) {
          return new Promise((d, b) => {
            const A = [], a = new P(i);
            a.on("data", () => {
            }), a.on("error", b), a.on("finish", () => d(O.concat(A))), a.pushAny(e), a.stopRecording(), a.removeAllListeners("data"), a.on("data", (l) => A.push(l)), a.pushAny(e), a.end();
          });
        }
      }
      G.exports = P;
    }, 5059: (G, N, s) => {
      const { MT: S, SIMPLE: m, SYMS: O } = s(9452);
      class P {
        constructor(e) {
          if (typeof e != "number")
            throw new Error("Invalid Simple type: " + typeof e);
          if (e < 0 || e > 255 || (0 | e) !== e)
            throw new Error(`value must be a small positive integer: ${e}`);
          this.value = e;
        }
        toString() {
          return `simple(${this.value})`;
        }
        [Symbol.for("nodejs.util.inspect.custom")](e, i) {
          return `simple(${this.value})`;
        }
        encodeCBOR(e) {
          return e._pushInt(this.value, S.SIMPLE_FLOAT);
        }
        static isSimple(e) {
          return e instanceof P;
        }
        static decode(e, i = !0, d = !1) {
          switch (e) {
            case m.FALSE:
              return !1;
            case m.TRUE:
              return !0;
            case m.NULL:
              return i ? null : O.NULL;
            case m.UNDEFINED:
              return i ? void 0 : O.UNDEFINED;
            case -1:
              if (!i || !d)
                throw new Error("Invalid BREAK");
              return O.BREAK;
            default:
              return new P(e);
          }
        }
      }
      G.exports = P;
    }, 3557: (G, N, s) => {
      const S = s(9452), m = s(7328), O = Symbol("INTERNAL_JSON");
      function P(c, o) {
        if (m.isBufferish(c))
          c.toJSON = o;
        else if (Array.isArray(c))
          for (const h of c)
            P(h, o);
        else if (c && typeof c == "object" && (!(c instanceof l) || c.tag < 21 || c.tag > 23))
          for (const h of Object.values(c))
            P(h, o);
      }
      function w() {
        return m.base64(this);
      }
      function e() {
        return m.base64url(this);
      }
      function i() {
        return this.toString("hex");
      }
      const d = { 0: (c) => new Date(c), 1: (c) => new Date(1e3 * c), 2: (c) => m.bufferToBigInt(c), 3: (c) => S.BI.MINUS_ONE - m.bufferToBigInt(c), 21: (c, o) => (m.isBufferish(c) ? o[O] = e : P(c, e), o), 22: (c, o) => (m.isBufferish(c) ? o[O] = w : P(c, w), o), 23: (c, o) => (m.isBufferish(c) ? o[O] = i : P(c, i), o), 32: (c) => new URL(c), 33: (c, o) => {
        if (!c.match(/^[a-zA-Z0-9_-]+$/))
          throw new Error("Invalid base64url characters");
        const h = c.length % 4;
        if (h === 1)
          throw new Error("Invalid base64url length");
        if (h === 2) {
          if ("AQgw".indexOf(c[c.length - 1]) === -1)
            throw new Error("Invalid base64 padding");
        } else if (h === 3 && "AEIMQUYcgkosw048".indexOf(c[c.length - 1]) === -1)
          throw new Error("Invalid base64 padding");
        return o;
      }, 34: (c, o) => {
        const h = c.match(/^[a-zA-Z0-9+/]+(?<padding>={0,2})$/);
        if (!h)
          throw new Error("Invalid base64 characters");
        if (c.length % 4 != 0)
          throw new Error("Invalid base64 length");
        if (h.groups.padding === "=") {
          if ("AQgw".indexOf(c[c.length - 2]) === -1)
            throw new Error("Invalid base64 padding");
        } else if (h.groups.padding === "==" && "AEIMQUYcgkosw048".indexOf(c[c.length - 3]) === -1)
          throw new Error("Invalid base64 padding");
        return o;
      }, 35: (c) => new RegExp(c), 258: (c) => new Set(c) }, b = { 64: Uint8Array, 65: Uint16Array, 66: Uint32Array, 68: Uint8ClampedArray, 69: Uint16Array, 70: Uint32Array, 72: Int8Array, 73: Int16Array, 74: Int32Array, 77: Int16Array, 78: Int32Array, 81: Float32Array, 82: Float64Array, 85: Float32Array, 86: Float64Array };
      function A(c, o) {
        if (!m.isBufferish(c))
          throw new TypeError("val not a buffer");
        const { tag: h } = o, y = b[h];
        if (!y)
          throw new Error(`Invalid typed array tag: ${h}`);
        const v = 2 ** (((16 & h) >> 4) + (3 & h));
        return !(4 & h) !== m.isBigEndian() && v > 1 && function(z, Y, k, Q) {
          const L = new DataView(z), [M, f] = { 2: [L.getUint16, L.setUint16], 4: [L.getUint32, L.setUint32], 8: [L.getBigUint64, L.setBigUint64] }[Y], F = k + Q;
          for (let U = k; U < F; U += Y)
            f.call(L, U, M.call(L, U, !0));
        }(c.buffer, v, c.byteOffset, c.byteLength), new y(c.buffer.slice(c.byteOffset, c.byteOffset + c.byteLength));
      }
      typeof BigUint64Array < "u" && (b[67] = BigUint64Array, b[71] = BigUint64Array), typeof BigInt64Array < "u" && (b[75] = BigInt64Array, b[79] = BigInt64Array);
      for (const c of Object.keys(b))
        d[c] = A;
      let a = {};
      class l {
        constructor(o, h, y) {
          if (this.tag = o, this.value = h, this.err = y, typeof this.tag != "number")
            throw new Error(`Invalid tag type (${typeof this.tag})`);
          if (this.tag < 0 || (0 | this.tag) !== this.tag)
            throw new Error(`Tag must be a positive integer: ${this.tag}`);
        }
        toJSON() {
          if (this[O])
            return this[O].call(this.value);
          const o = { tag: this.tag, value: this.value };
          return this.err && (o.err = this.err), o;
        }
        toString() {
          return `${this.tag}(${JSON.stringify(this.value)})`;
        }
        encodeCBOR(o) {
          return o._pushTag(this.tag), o.pushAny(this.value);
        }
        convert(o) {
          let h = o == null ? void 0 : o[this.tag];
          if (h === null)
            return this;
          if (typeof h != "function" && (h = l.TAGS[this.tag], typeof h != "function"))
            return this;
          try {
            return h.call(this, this.value, this);
          } catch (y) {
            return y && y.message && y.message.length > 0 ? this.err = y.message : this.err = y, this;
          }
        }
        static get TAGS() {
          return a;
        }
        static set TAGS(o) {
          a = o;
        }
        static reset() {
          l.TAGS = { ...d };
        }
      }
      l.INTERNAL_JSON = O, l.reset(), G.exports = l;
    }, 7328: (G, N, s) => {
      const { Buffer: S } = s(8287), m = s(5256), O = s(8310), P = s(9452), { NUMBYTES: w, SHIFT32: e, BI: i, SYMS: d } = P, b = new TextDecoder("utf8", { fatal: !0, ignoreBOM: !0 });
      N.utf8 = (a) => b.decode(a), N.utf8.checksUTF8 = !0, N.isBufferish = function(a) {
        return a && typeof a == "object" && (S.isBuffer(a) || a instanceof Uint8Array || a instanceof Uint8ClampedArray || a instanceof ArrayBuffer || a instanceof DataView);
      }, N.bufferishToBuffer = function(a) {
        return S.isBuffer(a) ? a : ArrayBuffer.isView(a) ? S.from(a.buffer, a.byteOffset, a.byteLength) : a instanceof ArrayBuffer ? S.from(a) : null;
      }, N.parseCBORint = function(a, l) {
        switch (a) {
          case w.ONE:
            return l.readUInt8(0);
          case w.TWO:
            return l.readUInt16BE(0);
          case w.FOUR:
            return l.readUInt32BE(0);
          case w.EIGHT: {
            const c = l.readUInt32BE(0), o = l.readUInt32BE(4);
            return c > 2097151 ? BigInt(c) * i.SHIFT32 + BigInt(o) : c * e + o;
          }
          default:
            throw new Error(`Invalid additional info for int: ${a}`);
        }
      }, N.writeHalf = function(a, l) {
        const c = S.allocUnsafe(4);
        c.writeFloatBE(l, 0);
        const o = c.readUInt32BE(0);
        if (8191 & o)
          return !1;
        let h = o >> 16 & 32768;
        const y = o >> 23 & 255, v = 8388607 & o;
        if (y >= 113 && y <= 142)
          h += (y - 112 << 10) + (v >> 13);
        else {
          if (!(y >= 103 && y < 113) || v & (1 << 126 - y) - 1)
            return !1;
          h += v + 8388608 >> 126 - y;
        }
        return a.writeUInt16BE(h), !0;
      }, N.parseHalf = function(a) {
        const l = 128 & a[0] ? -1 : 1, c = (124 & a[0]) >> 2, o = (3 & a[0]) << 8 | a[1];
        return c ? c === 31 ? l * (o ? NaN : 1 / 0) : l * 2 ** (c - 25) * (1024 + o) : 5960464477539063e-23 * l * o;
      }, N.parseCBORfloat = function(a) {
        switch (a.length) {
          case 2:
            return N.parseHalf(a);
          case 4:
            return a.readFloatBE(0);
          case 8:
            return a.readDoubleBE(0);
          default:
            throw new Error(`Invalid float size: ${a.length}`);
        }
      }, N.hex = function(a) {
        return S.from(a.replace(/^0x/, ""), "hex");
      }, N.bin = function(a) {
        let l = 0, c = (a = a.replace(/\s/g, "")).length % 8 || 8;
        const o = [];
        for (; c <= a.length; )
          o.push(parseInt(a.slice(l, c), 2)), l = c, c += 8;
        return S.from(o);
      }, N.arrayEqual = function(a, l) {
        return a == null && l == null || a != null && l != null && a.length === l.length && a.every((c, o) => c === l[o]);
      }, N.bufferToBigInt = function(a) {
        return BigInt(`0x${a.toString("hex")}`);
      }, N.cborValueToString = function(a, l = -1) {
        switch (typeof a) {
          case "symbol": {
            switch (a) {
              case d.NULL:
                return "null";
              case d.UNDEFINED:
                return "undefined";
              case d.BREAK:
                return "BREAK";
            }
            if (a.description)
              return a.description;
            const c = a.toString().match(/^Symbol\((?<name>.*)\)/);
            return c && c.groups.name ? c.groups.name : "Symbol";
          }
          case "string":
            return JSON.stringify(a);
          case "bigint":
            return a.toString();
          case "number": {
            const c = Object.is(a, -0) ? "-0" : String(a);
            return l > 0 ? `${c}_${l}` : c;
          }
          case "object": {
            if (!a)
              return "null";
            const c = N.bufferishToBuffer(a);
            if (c) {
              const o = c.toString("hex");
              return l === -1 / 0 ? o : `h'${o}'`;
            }
            return a && typeof a[Symbol.for("nodejs.util.inspect.custom")] == "function" ? a[Symbol.for("nodejs.util.inspect.custom")]() : Array.isArray(a) ? "[]" : "{}";
          }
        }
        return String(a);
      }, N.guessEncoding = function(a, l) {
        if (typeof a == "string")
          return new m(a, l ?? "hex");
        const c = N.bufferishToBuffer(a);
        if (c)
          return new m(c);
        if ((o = a) instanceof O.Readable || ["read", "on", "pipe"].every((h) => typeof o[h] == "function"))
          return a;
        var o;
        throw new Error("Unknown input type");
      };
      const A = { "=": "", "+": "-", "/": "_" };
      N.base64url = function(a) {
        return N.bufferishToBuffer(a).toString("base64").replace(/[=+/]/g, (l) => A[l]);
      }, N.base64 = function(a) {
        return N.bufferishToBuffer(a).toString("base64");
      }, N.isBigEndian = function() {
        const a = new Uint8Array(4);
        return !((new Uint32Array(a.buffer)[0] = 1) & a[0]);
      };
    }, 4957: (G, N, s) => {
      const S = s(8310), m = s(5256);
      class O extends S.Transform {
        constructor(w) {
          super(w), this._writableState.objectMode = !1, this._readableState.objectMode = !0, this.bs = new m(), this.__restart();
        }
        _transform(w, e, i) {
          for (this.bs.write(w); this.bs.length >= this.__needed; ) {
            let d = null;
            const b = this.__needed === null ? void 0 : this.bs.read(this.__needed);
            try {
              d = this.__parser.next(b);
            } catch (A) {
              return i(A);
            }
            this.__needed && (this.__fresh = !1), d.done ? (this.push(d.value), this.__restart()) : this.__needed = d.value || 1 / 0;
          }
          return i();
        }
        *_parse() {
          throw new Error("Must be implemented in subclass");
        }
        __restart() {
          this.__needed = null, this.__parser = this._parse(), this.__fresh = !0;
        }
        _flush(w) {
          w(this.__fresh ? null : new Error("unexpected end of input"));
        }
      }
      G.exports = O;
    }, 7007: (G) => {
      var N, s = typeof Reflect == "object" ? Reflect : null, S = s && typeof s.apply == "function" ? s.apply : function(o, h, y) {
        return Function.prototype.apply.call(o, h, y);
      };
      N = s && typeof s.ownKeys == "function" ? s.ownKeys : Object.getOwnPropertySymbols ? function(o) {
        return Object.getOwnPropertyNames(o).concat(Object.getOwnPropertySymbols(o));
      } : function(o) {
        return Object.getOwnPropertyNames(o);
      };
      var m = Number.isNaN || function(o) {
        return o != o;
      };
      function O() {
        O.init.call(this);
      }
      G.exports = O, G.exports.once = function(o, h) {
        return new Promise(function(y, v) {
          function z(k) {
            o.removeListener(h, Y), v(k);
          }
          function Y() {
            typeof o.removeListener == "function" && o.removeListener("error", z), y([].slice.call(arguments));
          }
          c(o, h, Y, { once: !0 }), h !== "error" && function(k, Q) {
            typeof k.on == "function" && c(k, "error", Q, { once: !0 });
          }(o, z);
        });
      }, O.EventEmitter = O, O.prototype._events = void 0, O.prototype._eventsCount = 0, O.prototype._maxListeners = void 0;
      var P = 10;
      function w(o) {
        if (typeof o != "function")
          throw new TypeError('The "listener" argument must be of type Function. Received type ' + typeof o);
      }
      function e(o) {
        return o._maxListeners === void 0 ? O.defaultMaxListeners : o._maxListeners;
      }
      function i(o, h, y, v) {
        var z, Y, k, Q;
        if (w(y), (Y = o._events) === void 0 ? (Y = o._events = /* @__PURE__ */ Object.create(null), o._eventsCount = 0) : (Y.newListener !== void 0 && (o.emit("newListener", h, y.listener ? y.listener : y), Y = o._events), k = Y[h]), k === void 0)
          k = Y[h] = y, ++o._eventsCount;
        else if (typeof k == "function" ? k = Y[h] = v ? [y, k] : [k, y] : v ? k.unshift(y) : k.push(y), (z = e(o)) > 0 && k.length > z && !k.warned) {
          k.warned = !0;
          var L = new Error("Possible EventEmitter memory leak detected. " + k.length + " " + String(h) + " listeners added. Use emitter.setMaxListeners() to increase limit");
          L.name = "MaxListenersExceededWarning", L.emitter = o, L.type = h, L.count = k.length, Q = L, console && console.warn && console.warn(Q);
        }
        return o;
      }
      function d() {
        if (!this.fired)
          return this.target.removeListener(this.type, this.wrapFn), this.fired = !0, arguments.length === 0 ? this.listener.call(this.target) : this.listener.apply(this.target, arguments);
      }
      function b(o, h, y) {
        var v = { fired: !1, wrapFn: void 0, target: o, type: h, listener: y }, z = d.bind(v);
        return z.listener = y, v.wrapFn = z, z;
      }
      function A(o, h, y) {
        var v = o._events;
        if (v === void 0)
          return [];
        var z = v[h];
        return z === void 0 ? [] : typeof z == "function" ? y ? [z.listener || z] : [z] : y ? function(Y) {
          for (var k = new Array(Y.length), Q = 0; Q < k.length; ++Q)
            k[Q] = Y[Q].listener || Y[Q];
          return k;
        }(z) : l(z, z.length);
      }
      function a(o) {
        var h = this._events;
        if (h !== void 0) {
          var y = h[o];
          if (typeof y == "function")
            return 1;
          if (y !== void 0)
            return y.length;
        }
        return 0;
      }
      function l(o, h) {
        for (var y = new Array(h), v = 0; v < h; ++v)
          y[v] = o[v];
        return y;
      }
      function c(o, h, y, v) {
        if (typeof o.on == "function")
          v.once ? o.once(h, y) : o.on(h, y);
        else {
          if (typeof o.addEventListener != "function")
            throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type ' + typeof o);
          o.addEventListener(h, function z(Y) {
            v.once && o.removeEventListener(h, z), y(Y);
          });
        }
      }
      Object.defineProperty(O, "defaultMaxListeners", { enumerable: !0, get: function() {
        return P;
      }, set: function(o) {
        if (typeof o != "number" || o < 0 || m(o))
          throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received ' + o + ".");
        P = o;
      } }), O.init = function() {
        this._events !== void 0 && this._events !== Object.getPrototypeOf(this)._events || (this._events = /* @__PURE__ */ Object.create(null), this._eventsCount = 0), this._maxListeners = this._maxListeners || void 0;
      }, O.prototype.setMaxListeners = function(o) {
        if (typeof o != "number" || o < 0 || m(o))
          throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received ' + o + ".");
        return this._maxListeners = o, this;
      }, O.prototype.getMaxListeners = function() {
        return e(this);
      }, O.prototype.emit = function(o) {
        for (var h = [], y = 1; y < arguments.length; y++)
          h.push(arguments[y]);
        var v = o === "error", z = this._events;
        if (z !== void 0)
          v = v && z.error === void 0;
        else if (!v)
          return !1;
        if (v) {
          var Y;
          if (h.length > 0 && (Y = h[0]), Y instanceof Error)
            throw Y;
          var k = new Error("Unhandled error." + (Y ? " (" + Y.message + ")" : ""));
          throw k.context = Y, k;
        }
        var Q = z[o];
        if (Q === void 0)
          return !1;
        if (typeof Q == "function")
          S(Q, this, h);
        else {
          var L = Q.length, M = l(Q, L);
          for (y = 0; y < L; ++y)
            S(M[y], this, h);
        }
        return !0;
      }, O.prototype.addListener = function(o, h) {
        return i(this, o, h, !1);
      }, O.prototype.on = O.prototype.addListener, O.prototype.prependListener = function(o, h) {
        return i(this, o, h, !0);
      }, O.prototype.once = function(o, h) {
        return w(h), this.on(o, b(this, o, h)), this;
      }, O.prototype.prependOnceListener = function(o, h) {
        return w(h), this.prependListener(o, b(this, o, h)), this;
      }, O.prototype.removeListener = function(o, h) {
        var y, v, z, Y, k;
        if (w(h), (v = this._events) === void 0)
          return this;
        if ((y = v[o]) === void 0)
          return this;
        if (y === h || y.listener === h)
          --this._eventsCount == 0 ? this._events = /* @__PURE__ */ Object.create(null) : (delete v[o], v.removeListener && this.emit("removeListener", o, y.listener || h));
        else if (typeof y != "function") {
          for (z = -1, Y = y.length - 1; Y >= 0; Y--)
            if (y[Y] === h || y[Y].listener === h) {
              k = y[Y].listener, z = Y;
              break;
            }
          if (z < 0)
            return this;
          z === 0 ? y.shift() : function(Q, L) {
            for (; L + 1 < Q.length; L++)
              Q[L] = Q[L + 1];
            Q.pop();
          }(y, z), y.length === 1 && (v[o] = y[0]), v.removeListener !== void 0 && this.emit("removeListener", o, k || h);
        }
        return this;
      }, O.prototype.off = O.prototype.removeListener, O.prototype.removeAllListeners = function(o) {
        var h, y, v;
        if ((y = this._events) === void 0)
          return this;
        if (y.removeListener === void 0)
          return arguments.length === 0 ? (this._events = /* @__PURE__ */ Object.create(null), this._eventsCount = 0) : y[o] !== void 0 && (--this._eventsCount == 0 ? this._events = /* @__PURE__ */ Object.create(null) : delete y[o]), this;
        if (arguments.length === 0) {
          var z, Y = Object.keys(y);
          for (v = 0; v < Y.length; ++v)
            (z = Y[v]) !== "removeListener" && this.removeAllListeners(z);
          return this.removeAllListeners("removeListener"), this._events = /* @__PURE__ */ Object.create(null), this._eventsCount = 0, this;
        }
        if (typeof (h = y[o]) == "function")
          this.removeListener(o, h);
        else if (h !== void 0)
          for (v = h.length - 1; v >= 0; v--)
            this.removeListener(o, h[v]);
        return this;
      }, O.prototype.listeners = function(o) {
        return A(this, o, !0);
      }, O.prototype.rawListeners = function(o) {
        return A(this, o, !1);
      }, O.listenerCount = function(o, h) {
        return typeof o.listenerCount == "function" ? o.listenerCount(h) : a.call(o, h);
      }, O.prototype.listenerCount = a, O.prototype.eventNames = function() {
        return this._eventsCount > 0 ? N(this._events) : [];
      };
    }, 251: (G, N) => {
      N.read = function(s, S, m, O, P) {
        var w, e, i = 8 * P - O - 1, d = (1 << i) - 1, b = d >> 1, A = -7, a = m ? P - 1 : 0, l = m ? -1 : 1, c = s[S + a];
        for (a += l, w = c & (1 << -A) - 1, c >>= -A, A += i; A > 0; w = 256 * w + s[S + a], a += l, A -= 8)
          ;
        for (e = w & (1 << -A) - 1, w >>= -A, A += O; A > 0; e = 256 * e + s[S + a], a += l, A -= 8)
          ;
        if (w === 0)
          w = 1 - b;
        else {
          if (w === d)
            return e ? NaN : 1 / 0 * (c ? -1 : 1);
          e += Math.pow(2, O), w -= b;
        }
        return (c ? -1 : 1) * e * Math.pow(2, w - O);
      }, N.write = function(s, S, m, O, P, w) {
        var e, i, d, b = 8 * w - P - 1, A = (1 << b) - 1, a = A >> 1, l = P === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0, c = O ? 0 : w - 1, o = O ? 1 : -1, h = S < 0 || S === 0 && 1 / S < 0 ? 1 : 0;
        for (S = Math.abs(S), isNaN(S) || S === 1 / 0 ? (i = isNaN(S) ? 1 : 0, e = A) : (e = Math.floor(Math.log(S) / Math.LN2), S * (d = Math.pow(2, -e)) < 1 && (e--, d *= 2), (S += e + a >= 1 ? l / d : l * Math.pow(2, 1 - a)) * d >= 2 && (e++, d /= 2), e + a >= A ? (i = 0, e = A) : e + a >= 1 ? (i = (S * d - 1) * Math.pow(2, P), e += a) : (i = S * Math.pow(2, a - 1) * Math.pow(2, P), e = 0)); P >= 8; s[m + c] = 255 & i, c += o, i /= 256, P -= 8)
          ;
        for (e = e << P | i, b += P; b > 0; s[m + c] = 255 & e, c += o, e /= 256, b -= 8)
          ;
        s[m + c - o] |= 128 * h;
      };
    }, 6698: (G) => {
      typeof Object.create == "function" ? G.exports = function(N, s) {
        s && (N.super_ = s, N.prototype = Object.create(s.prototype, { constructor: { value: N, enumerable: !1, writable: !0, configurable: !0 } }));
      } : G.exports = function(N, s) {
        if (s) {
          N.super_ = s;
          var S = function() {
          };
          S.prototype = s.prototype, N.prototype = new S(), N.prototype.constructor = N;
        }
      };
    }, 5256: (G, N, s) => {
      const S = s(8310), { Buffer: m } = s(8287), O = new TextDecoder("utf8", { fatal: !0, ignoreBOM: !0 });
      class P extends S.Transform {
        constructor(e, i, d = {}) {
          let b = null, A = null;
          switch (typeof e) {
            case "object":
              m.isBuffer(e) ? b = e : e && (d = e);
              break;
            case "string":
              b = e;
              break;
            case "undefined":
              break;
            default:
              throw new TypeError("Invalid input");
          }
          switch (typeof i) {
            case "object":
              i && (d = i);
              break;
            case "string":
              A = i;
              break;
            case "undefined":
              break;
            default:
              throw new TypeError("Invalid inputEncoding");
          }
          if (!d || typeof d != "object")
            throw new TypeError("Invalid options");
          b == null && (b = d.input), A == null && (A = d.inputEncoding), delete d.input, delete d.inputEncoding;
          const a = d.watchPipe == null || d.watchPipe;
          delete d.watchPipe;
          const l = !!d.readError;
          delete d.readError, super(d), this.readError = l, a && this.on("pipe", (c) => {
            const o = c._readableState.objectMode;
            if (this.length > 0 && o !== this._readableState.objectMode)
              throw new Error("Do not switch objectMode in the middle of the stream");
            this._readableState.objectMode = o, this._writableState.objectMode = o;
          }), b != null && this.end(b, A);
        }
        static isNoFilter(e) {
          return e instanceof this;
        }
        static compare(e, i) {
          if (!(e instanceof this))
            throw new TypeError("Arguments must be NoFilters");
          return e === i ? 0 : e.compare(i);
        }
        static concat(e, i) {
          if (!Array.isArray(e))
            throw new TypeError("list argument must be an Array of NoFilters");
          if (e.length === 0 || i === 0)
            return m.alloc(0);
          i == null && (i = e.reduce((a, l) => {
            if (!(l instanceof P))
              throw new TypeError("list argument must be an Array of NoFilters");
            return a + l.length;
          }, 0));
          let d = !0, b = !0;
          const A = e.map((a) => {
            if (!(a instanceof P))
              throw new TypeError("list argument must be an Array of NoFilters");
            const l = a.slice();
            return m.isBuffer(l) ? b = !1 : d = !1, l;
          });
          if (d)
            return m.concat(A, i);
          if (b)
            return [].concat(...A).slice(0, i);
          throw new Error("Concatenating mixed object and byte streams not supported");
        }
        _transform(e, i, d) {
          this._readableState.objectMode || m.isBuffer(e) || (e = m.from(e, i)), this.push(e), d();
        }
        _bufArray() {
          let e = this._readableState.buffer;
          if (!Array.isArray(e)) {
            let i = e.head;
            for (e = []; i != null; )
              e.push(i.data), i = i.next;
          }
          return e;
        }
        read(e) {
          const i = super.read(e);
          if (i != null) {
            if (this.emit("read", i), this.readError && i.length < e)
              throw new Error(`Read ${i.length}, wanted ${e}`);
          } else if (this.readError)
            throw new Error(`No data available, wanted ${e}`);
          return i;
        }
        readFull(e) {
          let i = null, d = null, b = null;
          return new Promise((A, a) => {
            this.length >= e ? A(this.read(e)) : this.writableFinished ? a(new Error(`Stream finished before ${e} bytes were available`)) : (i = (l) => {
              this.length >= e && A(this.read(e));
            }, d = () => {
              a(new Error(`Stream finished before ${e} bytes were available`));
            }, b = a, this.on("readable", i), this.on("error", b), this.on("finish", d));
          }).finally(() => {
            i && (this.removeListener("readable", i), this.removeListener("error", b), this.removeListener("finish", d));
          });
        }
        promise(e) {
          let i = !1;
          return new Promise((d, b) => {
            this.on("finish", () => {
              const A = this.read();
              e == null || i || (i = !0, e(null, A)), d(A);
            }), this.on("error", (A) => {
              e == null || i || (i = !0, e(A)), b(A);
            });
          });
        }
        compare(e) {
          if (!(e instanceof P))
            throw new TypeError("Arguments must be NoFilters");
          if (this === e)
            return 0;
          const i = this.slice(), d = e.slice();
          if (m.isBuffer(i) && m.isBuffer(d))
            return i.compare(d);
          throw new Error("Cannot compare streams in object mode");
        }
        equals(e) {
          return this.compare(e) === 0;
        }
        slice(e, i) {
          if (this._readableState.objectMode)
            return this._bufArray().slice(e, i);
          const d = this._bufArray();
          switch (d.length) {
            case 0:
              return m.alloc(0);
            case 1:
              return d[0].slice(e, i);
            default:
              return m.concat(d).slice(e, i);
          }
        }
        get(e) {
          return this.slice()[e];
        }
        toJSON() {
          const e = this.slice();
          return m.isBuffer(e) ? e.toJSON() : e;
        }
        toString(e, i, d) {
          const b = this.slice(i, d);
          return m.isBuffer(b) ? e && e !== "utf8" ? b.toString(e) : O.decode(b) : JSON.stringify(b);
        }
        [Symbol.for("nodejs.util.inspect.custom")](e, i) {
          const d = this._bufArray().map((b) => m.isBuffer(b) ? i.stylize(b.toString("hex"), "string") : JSON.stringify(b)).join(", ");
          return `${this.constructor.name} [${d}]`;
        }
        get length() {
          return this._readableState.length;
        }
        writeBigInt(e) {
          let i = e.toString(16);
          if (e < 0) {
            const d = BigInt(Math.floor(i.length / 2));
            i = (e = (BigInt(1) << d * BigInt(8)) + e).toString(16);
          }
          return i.length % 2 && (i = `0${i}`), this.push(m.from(i, "hex"));
        }
        readUBigInt(e) {
          const i = this.read(e);
          return m.isBuffer(i) ? BigInt(`0x${i.toString("hex")}`) : null;
        }
        readBigInt(e) {
          const i = this.read(e);
          if (!m.isBuffer(i))
            return null;
          let d = BigInt(`0x${i.toString("hex")}`);
          return 128 & i[0] && (d -= BigInt(1) << BigInt(i.length) * BigInt(8)), d;
        }
        writeUInt8(e) {
          const i = m.from([e]);
          return this.push(i);
        }
        writeUInt16LE(e) {
          const i = m.alloc(2);
          return i.writeUInt16LE(e), this.push(i);
        }
        writeUInt16BE(e) {
          const i = m.alloc(2);
          return i.writeUInt16BE(e), this.push(i);
        }
        writeUInt32LE(e) {
          const i = m.alloc(4);
          return i.writeUInt32LE(e), this.push(i);
        }
        writeUInt32BE(e) {
          const i = m.alloc(4);
          return i.writeUInt32BE(e), this.push(i);
        }
        writeInt8(e) {
          const i = m.from([e]);
          return this.push(i);
        }
        writeInt16LE(e) {
          const i = m.alloc(2);
          return i.writeUInt16LE(e), this.push(i);
        }
        writeInt16BE(e) {
          const i = m.alloc(2);
          return i.writeUInt16BE(e), this.push(i);
        }
        writeInt32LE(e) {
          const i = m.alloc(4);
          return i.writeUInt32LE(e), this.push(i);
        }
        writeInt32BE(e) {
          const i = m.alloc(4);
          return i.writeUInt32BE(e), this.push(i);
        }
        writeFloatLE(e) {
          const i = m.alloc(4);
          return i.writeFloatLE(e), this.push(i);
        }
        writeFloatBE(e) {
          const i = m.alloc(4);
          return i.writeFloatBE(e), this.push(i);
        }
        writeDoubleLE(e) {
          const i = m.alloc(8);
          return i.writeDoubleLE(e), this.push(i);
        }
        writeDoubleBE(e) {
          const i = m.alloc(8);
          return i.writeDoubleBE(e), this.push(i);
        }
        writeBigInt64LE(e) {
          const i = m.alloc(8);
          return i.writeBigInt64LE(e), this.push(i);
        }
        writeBigInt64BE(e) {
          const i = m.alloc(8);
          return i.writeBigInt64BE(e), this.push(i);
        }
        writeBigUInt64LE(e) {
          const i = m.alloc(8);
          return i.writeBigUInt64LE(e), this.push(i);
        }
        writeBigUInt64BE(e) {
          const i = m.alloc(8);
          return i.writeBigUInt64BE(e), this.push(i);
        }
        readUInt8() {
          const e = this.read(1);
          return m.isBuffer(e) ? e.readUInt8() : null;
        }
        readUInt16LE() {
          const e = this.read(2);
          return m.isBuffer(e) ? e.readUInt16LE() : null;
        }
        readUInt16BE() {
          const e = this.read(2);
          return m.isBuffer(e) ? e.readUInt16BE() : null;
        }
        readUInt32LE() {
          const e = this.read(4);
          return m.isBuffer(e) ? e.readUInt32LE() : null;
        }
        readUInt32BE() {
          const e = this.read(4);
          return m.isBuffer(e) ? e.readUInt32BE() : null;
        }
        readInt8() {
          const e = this.read(1);
          return m.isBuffer(e) ? e.readInt8() : null;
        }
        readInt16LE() {
          const e = this.read(2);
          return m.isBuffer(e) ? e.readInt16LE() : null;
        }
        readInt16BE() {
          const e = this.read(2);
          return m.isBuffer(e) ? e.readInt16BE() : null;
        }
        readInt32LE() {
          const e = this.read(4);
          return m.isBuffer(e) ? e.readInt32LE() : null;
        }
        readInt32BE() {
          const e = this.read(4);
          return m.isBuffer(e) ? e.readInt32BE() : null;
        }
        readFloatLE() {
          const e = this.read(4);
          return m.isBuffer(e) ? e.readFloatLE() : null;
        }
        readFloatBE() {
          const e = this.read(4);
          return m.isBuffer(e) ? e.readFloatBE() : null;
        }
        readDoubleLE() {
          const e = this.read(8);
          return m.isBuffer(e) ? e.readDoubleLE() : null;
        }
        readDoubleBE() {
          const e = this.read(8);
          return m.isBuffer(e) ? e.readDoubleBE() : null;
        }
        readBigInt64LE() {
          const e = this.read(8);
          return m.isBuffer(e) ? e.readBigInt64LE() : null;
        }
        readBigInt64BE() {
          const e = this.read(8);
          return m.isBuffer(e) ? e.readBigInt64BE() : null;
        }
        readBigUInt64LE() {
          const e = this.read(8);
          return m.isBuffer(e) ? e.readBigUInt64LE() : null;
        }
        readBigUInt64BE() {
          const e = this.read(8);
          return m.isBuffer(e) ? e.readBigUInt64BE() : null;
        }
      }
      G.exports = P;
    }, 5606: (G) => {
      var N, s, S = G.exports = {};
      function m() {
        throw new Error("setTimeout has not been defined");
      }
      function O() {
        throw new Error("clearTimeout has not been defined");
      }
      function P(c) {
        if (N === setTimeout)
          return setTimeout(c, 0);
        if ((N === m || !N) && setTimeout)
          return N = setTimeout, setTimeout(c, 0);
        try {
          return N(c, 0);
        } catch {
          try {
            return N.call(null, c, 0);
          } catch {
            return N.call(this, c, 0);
          }
        }
      }
      (function() {
        try {
          N = typeof setTimeout == "function" ? setTimeout : m;
        } catch {
          N = m;
        }
        try {
          s = typeof clearTimeout == "function" ? clearTimeout : O;
        } catch {
          s = O;
        }
      })();
      var w, e = [], i = !1, d = -1;
      function b() {
        i && w && (i = !1, w.length ? e = w.concat(e) : d = -1, e.length && A());
      }
      function A() {
        if (!i) {
          var c = P(b);
          i = !0;
          for (var o = e.length; o; ) {
            for (w = e, e = []; ++d < o; )
              w && w[d].run();
            d = -1, o = e.length;
          }
          w = null, i = !1, function(h) {
            if (s === clearTimeout)
              return clearTimeout(h);
            if ((s === O || !s) && clearTimeout)
              return s = clearTimeout, clearTimeout(h);
            try {
              return s(h);
            } catch {
              try {
                return s.call(null, h);
              } catch {
                return s.call(this, h);
              }
            }
          }(c);
        }
      }
      function a(c, o) {
        this.fun = c, this.array = o;
      }
      function l() {
      }
      S.nextTick = function(c) {
        var o = new Array(arguments.length - 1);
        if (arguments.length > 1)
          for (var h = 1; h < arguments.length; h++)
            o[h - 1] = arguments[h];
        e.push(new a(c, o)), e.length !== 1 || i || P(A);
      }, a.prototype.run = function() {
        this.fun.apply(null, this.array);
      }, S.title = "browser", S.browser = !0, S.env = {}, S.argv = [], S.version = "", S.versions = {}, S.on = l, S.addListener = l, S.once = l, S.off = l, S.removeListener = l, S.removeAllListeners = l, S.emit = l, S.prependListener = l, S.prependOnceListener = l, S.listeners = function(c) {
        return [];
      }, S.binding = function(c) {
        throw new Error("process.binding is not supported");
      }, S.cwd = function() {
        return "/";
      }, S.chdir = function(c) {
        throw new Error("process.chdir is not supported");
      }, S.umask = function() {
        return 0;
      };
    }, 5382: (G, N, s) => {
      G.exports = s(5506).Duplex;
    }, 3600: (G, N, s) => {
      G.exports = s(5506).PassThrough;
    }, 5412: (G, N, s) => {
      G.exports = s(5506).Readable;
    }, 4610: (G, N, s) => {
      G.exports = s(5506).Transform;
    }, 6708: (G, N, s) => {
      G.exports = s(5506).Writable;
    }, 4147: (G, N, s) => {
      const { SymbolDispose: S } = s(4134), { AbortError: m, codes: O } = s(6371), { isNodeStream: P, isWebStream: w, kControllerErrorFunction: e } = s(6115), i = s(6238), { ERR_INVALID_ARG_TYPE: d } = O;
      let b;
      G.exports.addAbortSignal = function(A, a) {
        if (((l) => {
          if (typeof l != "object" || !("aborted" in l))
            throw new d("signal", "AbortSignal", l);
        })(A), !P(a) && !w(a))
          throw new d("stream", ["ReadableStream", "WritableStream", "Stream"], a);
        return G.exports.addAbortSignalNoValidate(A, a);
      }, G.exports.addAbortSignalNoValidate = function(A, a) {
        if (typeof A != "object" || !("aborted" in A))
          return a;
        const l = P(a) ? () => {
          a.destroy(new m(void 0, { cause: A.reason }));
        } : () => {
          a[e](new m(void 0, { cause: A.reason }));
        };
        if (A.aborted)
          l();
        else {
          b = b || s(7760).addAbortListener;
          const c = b(A, l);
          i(a, c[S]);
        }
        return a;
      };
    }, 345: (G, N, s) => {
      const { StringPrototypeSlice: S, SymbolIterator: m, TypedArrayPrototypeSet: O, Uint8Array: P } = s(4134), { Buffer: w } = s(8287), { inspect: e } = s(7760);
      G.exports = class {
        constructor() {
          this.head = null, this.tail = null, this.length = 0;
        }
        push(i) {
          const d = { data: i, next: null };
          this.length > 0 ? this.tail.next = d : this.head = d, this.tail = d, ++this.length;
        }
        unshift(i) {
          const d = { data: i, next: this.head };
          this.length === 0 && (this.tail = d), this.head = d, ++this.length;
        }
        shift() {
          if (this.length === 0)
            return;
          const i = this.head.data;
          return this.length === 1 ? this.head = this.tail = null : this.head = this.head.next, --this.length, i;
        }
        clear() {
          this.head = this.tail = null, this.length = 0;
        }
        join(i) {
          if (this.length === 0)
            return "";
          let d = this.head, b = "" + d.data;
          for (; (d = d.next) !== null; )
            b += i + d.data;
          return b;
        }
        concat(i) {
          if (this.length === 0)
            return w.alloc(0);
          const d = w.allocUnsafe(i >>> 0);
          let b = this.head, A = 0;
          for (; b; )
            O(d, b.data, A), A += b.data.length, b = b.next;
          return d;
        }
        consume(i, d) {
          const b = this.head.data;
          if (i < b.length) {
            const A = b.slice(0, i);
            return this.head.data = b.slice(i), A;
          }
          return i === b.length ? this.shift() : d ? this._getString(i) : this._getBuffer(i);
        }
        first() {
          return this.head.data;
        }
        *[m]() {
          for (let i = this.head; i; i = i.next)
            yield i.data;
        }
        _getString(i) {
          let d = "", b = this.head, A = 0;
          do {
            const a = b.data;
            if (!(i > a.length)) {
              i === a.length ? (d += a, ++A, b.next ? this.head = b.next : this.head = this.tail = null) : (d += S(a, 0, i), this.head = b, b.data = S(a, i));
              break;
            }
            d += a, i -= a.length, ++A;
          } while ((b = b.next) !== null);
          return this.length -= A, d;
        }
        _getBuffer(i) {
          const d = w.allocUnsafe(i), b = i;
          let A = this.head, a = 0;
          do {
            const l = A.data;
            if (!(i > l.length)) {
              i === l.length ? (O(d, l, b - i), ++a, A.next ? this.head = A.next : this.head = this.tail = null) : (O(d, new P(l.buffer, l.byteOffset, i), b - i), this.head = A, A.data = l.slice(i));
              break;
            }
            O(d, l, b - i), i -= l.length, ++a;
          } while ((A = A.next) !== null);
          return this.length -= a, d;
        }
        [Symbol.for("nodejs.util.inspect.custom")](i, d) {
          return e(this, { ...d, depth: 0, customInspect: !1 });
        }
      };
    }, 7830: (G, N, s) => {
      const { pipeline: S } = s(7758), m = s(3370), { destroyer: O } = s(5896), { isNodeStream: P, isReadable: w, isWritable: e, isWebStream: i, isTransformStream: d, isWritableStream: b, isReadableStream: A } = s(6115), { AbortError: a, codes: { ERR_INVALID_ARG_VALUE: l, ERR_MISSING_ARGS: c } } = s(6371), o = s(6238);
      G.exports = function(...h) {
        if (h.length === 0)
          throw new c("streams");
        if (h.length === 1)
          return m.from(h[0]);
        const y = [...h];
        if (typeof h[0] == "function" && (h[0] = m.from(h[0])), typeof h[h.length - 1] == "function") {
          const U = h.length - 1;
          h[U] = m.from(h[U]);
        }
        for (let U = 0; U < h.length; ++U)
          if (P(h[U]) || i(h[U])) {
            if (U < h.length - 1 && !(w(h[U]) || A(h[U]) || d(h[U])))
              throw new l(`streams[${U}]`, y[U], "must be readable");
            if (U > 0 && !(e(h[U]) || b(h[U]) || d(h[U])))
              throw new l(`streams[${U}]`, y[U], "must be writable");
          }
        let v, z, Y, k, Q;
        const L = h[0], M = S(h, function(U) {
          const D = k;
          k = null, D ? D(U) : U ? Q.destroy(U) : F || f || Q.destroy();
        }), f = !!(e(L) || b(L) || d(L)), F = !!(w(M) || A(M) || d(M));
        if (Q = new m({ writableObjectMode: !(L == null || !L.writableObjectMode), readableObjectMode: !(M == null || !M.readableObjectMode), writable: f, readable: F }), f) {
          if (P(L))
            Q._write = function(D, rt, u) {
              L.write(D, rt) ? u() : v = u;
            }, Q._final = function(D) {
              L.end(), z = D;
            }, L.on("drain", function() {
              if (v) {
                const D = v;
                v = null, D();
              }
            });
          else if (i(L)) {
            const D = (d(L) ? L.writable : L).getWriter();
            Q._write = async function(rt, u, B) {
              try {
                await D.ready, D.write(rt).catch(() => {
                }), B();
              } catch (K) {
                B(K);
              }
            }, Q._final = async function(rt) {
              try {
                await D.ready, D.close().catch(() => {
                }), z = rt;
              } catch (u) {
                rt(u);
              }
            };
          }
          const U = d(M) ? M.readable : M;
          o(U, () => {
            if (z) {
              const D = z;
              z = null, D();
            }
          });
        }
        if (F) {
          if (P(M))
            M.on("readable", function() {
              if (Y) {
                const U = Y;
                Y = null, U();
              }
            }), M.on("end", function() {
              Q.push(null);
            }), Q._read = function() {
              for (; ; ) {
                const U = M.read();
                if (U === null)
                  return void (Y = Q._read);
                if (!Q.push(U))
                  return;
              }
            };
          else if (i(M)) {
            const U = (d(M) ? M.readable : M).getReader();
            Q._read = async function() {
              for (; ; )
                try {
                  const { value: D, done: rt } = await U.read();
                  if (!Q.push(D))
                    return;
                  if (rt)
                    return void Q.push(null);
                } catch {
                  return;
                }
            };
          }
        }
        return Q._destroy = function(U, D) {
          U || k === null || (U = new a()), Y = null, v = null, z = null, k === null ? D(U) : (k = D, P(M) && O(M, U));
        }, Q;
      };
    }, 5896: (G, N, s) => {
      const S = s(5606), { aggregateTwoErrors: m, codes: { ERR_MULTIPLE_CALLBACK: O }, AbortError: P } = s(6371), { Symbol: w } = s(4134), { kIsDestroyed: e, isDestroyed: i, isFinished: d, isServerRequest: b } = s(6115), A = w("kDestroy"), a = w("kConstruct");
      function l(M, f, F) {
        M && (M.stack, f && !f.errored && (f.errored = M), F && !F.errored && (F.errored = M));
      }
      function c(M, f, F) {
        let U = !1;
        function D(rt) {
          if (U)
            return;
          U = !0;
          const u = M._readableState, B = M._writableState;
          l(rt, B, u), B && (B.closed = !0), u && (u.closed = !0), typeof F == "function" && F(rt), rt ? S.nextTick(o, M, rt) : S.nextTick(h, M);
        }
        try {
          M._destroy(f || null, D);
        } catch (rt) {
          D(rt);
        }
      }
      function o(M, f) {
        y(M, f), h(M);
      }
      function h(M) {
        const f = M._readableState, F = M._writableState;
        F && (F.closeEmitted = !0), f && (f.closeEmitted = !0), (F != null && F.emitClose || f != null && f.emitClose) && M.emit("close");
      }
      function y(M, f) {
        const F = M._readableState, U = M._writableState;
        U != null && U.errorEmitted || F != null && F.errorEmitted || (U && (U.errorEmitted = !0), F && (F.errorEmitted = !0), M.emit("error", f));
      }
      function v(M, f, F) {
        const U = M._readableState, D = M._writableState;
        if (D != null && D.destroyed || U != null && U.destroyed)
          return this;
        U != null && U.autoDestroy || D != null && D.autoDestroy ? M.destroy(f) : f && (f.stack, D && !D.errored && (D.errored = f), U && !U.errored && (U.errored = f), F ? S.nextTick(y, M, f) : y(M, f));
      }
      function z(M) {
        let f = !1;
        function F(U) {
          if (f)
            return void v(M, U ?? new O());
          f = !0;
          const D = M._readableState, rt = M._writableState, u = rt || D;
          D && (D.constructed = !0), rt && (rt.constructed = !0), u.destroyed ? M.emit(A, U) : U ? v(M, U, !0) : S.nextTick(Y, M);
        }
        try {
          M._construct((U) => {
            S.nextTick(F, U);
          });
        } catch (U) {
          S.nextTick(F, U);
        }
      }
      function Y(M) {
        M.emit(a);
      }
      function k(M) {
        return (M == null ? void 0 : M.setHeader) && typeof M.abort == "function";
      }
      function Q(M) {
        M.emit("close");
      }
      function L(M, f) {
        M.emit("error", f), S.nextTick(Q, M);
      }
      G.exports = { construct: function(M, f) {
        if (typeof M._construct != "function")
          return;
        const F = M._readableState, U = M._writableState;
        F && (F.constructed = !1), U && (U.constructed = !1), M.once(a, f), M.listenerCount(a) > 1 || S.nextTick(z, M);
      }, destroyer: function(M, f) {
        M && !i(M) && (f || d(M) || (f = new P()), b(M) ? (M.socket = null, M.destroy(f)) : k(M) ? M.abort() : k(M.req) ? M.req.abort() : typeof M.destroy == "function" ? M.destroy(f) : typeof M.close == "function" ? M.close() : f ? S.nextTick(L, M, f) : S.nextTick(Q, M), M.destroyed || (M[e] = !0));
      }, destroy: function(M, f) {
        const F = this._readableState, U = this._writableState, D = U || F;
        return U != null && U.destroyed || F != null && F.destroyed ? (typeof f == "function" && f(), this) : (l(M, U, F), U && (U.destroyed = !0), F && (F.destroyed = !0), D.constructed ? c(this, M, f) : this.once(A, function(rt) {
          c(this, m(rt, M), f);
        }), this);
      }, undestroy: function() {
        const M = this._readableState, f = this._writableState;
        M && (M.constructed = !0, M.closed = !1, M.closeEmitted = !1, M.destroyed = !1, M.errored = null, M.errorEmitted = !1, M.reading = !1, M.ended = M.readable === !1, M.endEmitted = M.readable === !1), f && (f.constructed = !0, f.destroyed = !1, f.closed = !1, f.closeEmitted = !1, f.errored = null, f.errorEmitted = !1, f.finalCalled = !1, f.prefinished = !1, f.ended = f.writable === !1, f.ending = f.writable === !1, f.finished = f.writable === !1);
      }, errorOrDestroy: v };
    }, 3370: (G, N, s) => {
      const { ObjectDefineProperties: S, ObjectGetOwnPropertyDescriptor: m, ObjectKeys: O, ObjectSetPrototypeOf: P } = s(4134);
      G.exports = i;
      const w = s(7576), e = s(8584);
      P(i.prototype, w.prototype), P(i, w);
      {
        const a = O(e.prototype);
        for (let l = 0; l < a.length; l++) {
          const c = a[l];
          i.prototype[c] || (i.prototype[c] = e.prototype[c]);
        }
      }
      function i(a) {
        if (!(this instanceof i))
          return new i(a);
        w.call(this, a), e.call(this, a), a ? (this.allowHalfOpen = a.allowHalfOpen !== !1, a.readable === !1 && (this._readableState.readable = !1, this._readableState.ended = !0, this._readableState.endEmitted = !0), a.writable === !1 && (this._writableState.writable = !1, this._writableState.ending = !0, this._writableState.ended = !0, this._writableState.finished = !0)) : this.allowHalfOpen = !0;
      }
      let d, b;
      function A() {
        return d === void 0 && (d = {}), d;
      }
      S(i.prototype, { writable: { __proto__: null, ...m(e.prototype, "writable") }, writableHighWaterMark: { __proto__: null, ...m(e.prototype, "writableHighWaterMark") }, writableObjectMode: { __proto__: null, ...m(e.prototype, "writableObjectMode") }, writableBuffer: { __proto__: null, ...m(e.prototype, "writableBuffer") }, writableLength: { __proto__: null, ...m(e.prototype, "writableLength") }, writableFinished: { __proto__: null, ...m(e.prototype, "writableFinished") }, writableCorked: { __proto__: null, ...m(e.prototype, "writableCorked") }, writableEnded: { __proto__: null, ...m(e.prototype, "writableEnded") }, writableNeedDrain: { __proto__: null, ...m(e.prototype, "writableNeedDrain") }, destroyed: { __proto__: null, get() {
        return this._readableState !== void 0 && this._writableState !== void 0 && this._readableState.destroyed && this._writableState.destroyed;
      }, set(a) {
        this._readableState && this._writableState && (this._readableState.destroyed = a, this._writableState.destroyed = a);
      } } }), i.fromWeb = function(a, l) {
        return A().newStreamDuplexFromReadableWritablePair(a, l);
      }, i.toWeb = function(a) {
        return A().newReadableWritablePairFromDuplex(a);
      }, i.from = function(a) {
        return b || (b = s(6706)), b(a, "body");
      };
    }, 6706: (G, N, s) => {
      const S = s(5606), m = s(8287), { isReadable: O, isWritable: P, isIterable: w, isNodeStream: e, isReadableNodeStream: i, isWritableNodeStream: d, isDuplexNodeStream: b, isReadableStream: A, isWritableStream: a } = s(6115), l = s(6238), { AbortError: c, codes: { ERR_INVALID_ARG_TYPE: o, ERR_INVALID_RETURN_VALUE: h } } = s(6371), { destroyer: y } = s(5896), v = s(3370), z = s(7576), Y = s(8584), { createDeferredPromise: k } = s(7760), Q = s(6532), L = globalThis.Blob || m.Blob, M = L !== void 0 ? function(rt) {
        return rt instanceof L;
      } : function(rt) {
        return !1;
      }, f = globalThis.AbortController || s(5568).AbortController, { FunctionPrototypeCall: F } = s(4134);
      class U extends v {
        constructor(u) {
          super(u), (u == null ? void 0 : u.readable) === !1 && (this._readableState.readable = !1, this._readableState.ended = !0, this._readableState.endEmitted = !0), (u == null ? void 0 : u.writable) === !1 && (this._writableState.writable = !1, this._writableState.ending = !0, this._writableState.ended = !0, this._writableState.finished = !0);
        }
      }
      function D(rt) {
        const u = rt.readable && typeof rt.readable.read != "function" ? z.wrap(rt.readable) : rt.readable, B = rt.writable;
        let K, T, $, tt, W, J = !!O(u), C = !!P(B);
        function nt(Z) {
          const ht = tt;
          tt = null, ht ? ht(Z) : Z && W.destroy(Z);
        }
        return W = new U({ readableObjectMode: !(u == null || !u.readableObjectMode), writableObjectMode: !(B == null || !B.writableObjectMode), readable: J, writable: C }), C && (l(B, (Z) => {
          C = !1, Z && y(u, Z), nt(Z);
        }), W._write = function(Z, ht, lt) {
          B.write(Z, ht) ? lt() : K = lt;
        }, W._final = function(Z) {
          B.end(), T = Z;
        }, B.on("drain", function() {
          if (K) {
            const Z = K;
            K = null, Z();
          }
        }), B.on("finish", function() {
          if (T) {
            const Z = T;
            T = null, Z();
          }
        })), J && (l(u, (Z) => {
          J = !1, Z && y(u, Z), nt(Z);
        }), u.on("readable", function() {
          if ($) {
            const Z = $;
            $ = null, Z();
          }
        }), u.on("end", function() {
          W.push(null);
        }), W._read = function() {
          for (; ; ) {
            const Z = u.read();
            if (Z === null)
              return void ($ = W._read);
            if (!W.push(Z))
              return;
          }
        }), W._destroy = function(Z, ht) {
          Z || tt === null || (Z = new c()), $ = null, K = null, T = null, tt === null ? ht(Z) : (tt = ht, y(B, Z), y(u, Z));
        }, W;
      }
      G.exports = function rt(u, B) {
        if (b(u))
          return u;
        if (i(u))
          return D({ readable: u });
        if (d(u))
          return D({ writable: u });
        if (e(u))
          return D({ writable: !1, readable: !1 });
        if (A(u))
          return D({ readable: z.fromWeb(u) });
        if (a(u))
          return D({ writable: Y.fromWeb(u) });
        if (typeof u == "function") {
          const { value: T, write: $, final: tt, destroy: W } = function(C) {
            let { promise: nt, resolve: Z } = k();
            const ht = new f(), lt = ht.signal;
            return { value: C(async function* () {
              for (; ; ) {
                const ct = nt;
                nt = null;
                const { chunk: I, done: X, cb: H } = await ct;
                if (S.nextTick(H), X)
                  return;
                if (lt.aborted)
                  throw new c(void 0, { cause: lt.reason });
                ({ promise: nt, resolve: Z } = k()), yield I;
              }
            }(), { signal: lt }), write(ct, I, X) {
              const H = Z;
              Z = null, H({ chunk: ct, done: !1, cb: X });
            }, final(ct) {
              const I = Z;
              Z = null, I({ done: !0, cb: ct });
            }, destroy(ct, I) {
              ht.abort(), I(ct);
            } };
          }(u);
          if (w(T))
            return Q(U, T, { objectMode: !0, write: $, final: tt, destroy: W });
          const J = T == null ? void 0 : T.then;
          if (typeof J == "function") {
            let C;
            const nt = F(J, T, (Z) => {
              if (Z != null)
                throw new h("nully", "body", Z);
            }, (Z) => {
              y(C, Z);
            });
            return C = new U({ objectMode: !0, readable: !1, write: $, final(Z) {
              tt(async () => {
                try {
                  await nt, S.nextTick(Z, null);
                } catch (ht) {
                  S.nextTick(Z, ht);
                }
              });
            }, destroy: W });
          }
          throw new h("Iterable, AsyncIterable or AsyncFunction", B, T);
        }
        if (M(u))
          return rt(u.arrayBuffer());
        if (w(u))
          return Q(U, u, { objectMode: !0, writable: !1 });
        if (A(u == null ? void 0 : u.readable) && a(u == null ? void 0 : u.writable))
          return U.fromWeb(u);
        if (typeof (u == null ? void 0 : u.writable) == "object" || typeof (u == null ? void 0 : u.readable) == "object")
          return D({ readable: u != null && u.readable ? i(u == null ? void 0 : u.readable) ? u == null ? void 0 : u.readable : rt(u.readable) : void 0, writable: u != null && u.writable ? d(u == null ? void 0 : u.writable) ? u == null ? void 0 : u.writable : rt(u.writable) : void 0 });
        const K = u == null ? void 0 : u.then;
        if (typeof K == "function") {
          let T;
          return F(K, u, ($) => {
            $ != null && T.push($), T.push(null);
          }, ($) => {
            y(T, $);
          }), T = new U({ objectMode: !0, writable: !1, read() {
          } });
        }
        throw new o(B, ["Blob", "ReadableStream", "WritableStream", "Stream", "Iterable", "AsyncIterable", "Function", "{ readable, writable } pair", "Promise"], u);
      };
    }, 6238: (G, N, s) => {
      const S = s(5606), { AbortError: m, codes: O } = s(6371), { ERR_INVALID_ARG_TYPE: P, ERR_STREAM_PREMATURE_CLOSE: w } = O, { kEmptyObject: e, once: i } = s(7760), { validateAbortSignal: d, validateFunction: b, validateObject: A, validateBoolean: a } = s(277), { Promise: l, PromisePrototypeThen: c, SymbolDispose: o } = s(4134), { isClosed: h, isReadable: y, isReadableNodeStream: v, isReadableStream: z, isReadableFinished: Y, isReadableErrored: k, isWritable: Q, isWritableNodeStream: L, isWritableStream: M, isWritableFinished: f, isWritableErrored: F, isNodeStream: U, willEmitClose: D, kIsClosedPromise: rt } = s(6115);
      let u;
      const B = () => {
      };
      function K(T, $, tt) {
        var W, J;
        if (arguments.length === 2 ? (tt = $, $ = e) : $ == null ? $ = e : A($, "options"), b(tt, "callback"), d($.signal, "options.signal"), tt = i(tt), z(T) || M(T))
          return function(n, r, _) {
            let E = !1, x = B;
            if (r.signal)
              if (x = () => {
                E = !0, _.call(n, new m(void 0, { cause: r.signal.reason }));
              }, r.signal.aborted)
                S.nextTick(x);
              else {
                u = u || s(7760).addAbortListener;
                const ut = u(r.signal, x), pt = _;
                _ = i((...bt) => {
                  ut[o](), pt.apply(n, bt);
                });
              }
            const q = (...ut) => {
              E || S.nextTick(() => _.apply(n, ut));
            };
            return c(n[rt].promise, q, q), B;
          }(T, $, tt);
        if (!U(T))
          throw new P("stream", ["ReadableStream", "WritableStream", "Stream"], T);
        const C = (W = $.readable) !== null && W !== void 0 ? W : v(T), nt = (J = $.writable) !== null && J !== void 0 ? J : L(T), Z = T._writableState, ht = T._readableState, lt = () => {
          T.writable || X();
        };
        let ct = D(T) && v(T) === C && L(T) === nt, I = f(T, !1);
        const X = () => {
          I = !0, T.destroyed && (ct = !1), (!ct || T.readable && !C) && (C && !H || tt.call(T));
        };
        let H = Y(T, !1);
        const at = () => {
          H = !0, T.destroyed && (ct = !1), (!ct || T.writable && !nt) && (nt && !I || tt.call(T));
        }, R = (n) => {
          tt.call(T, n);
        };
        let V = h(T);
        const j = () => {
          V = !0;
          const n = F(T) || k(T);
          return n && typeof n != "boolean" ? tt.call(T, n) : C && !H && v(T, !0) && !Y(T, !1) ? tt.call(T, new w()) : !nt || I || f(T, !1) ? void tt.call(T) : tt.call(T, new w());
        }, et = () => {
          V = !0;
          const n = F(T) || k(T);
          if (n && typeof n != "boolean")
            return tt.call(T, n);
          tt.call(T);
        }, ot = () => {
          T.req.on("finish", X);
        };
        (function(n) {
          return n.setHeader && typeof n.abort == "function";
        })(T) ? (T.on("complete", X), ct || T.on("abort", j), T.req ? ot() : T.on("request", ot)) : nt && !Z && (T.on("end", lt), T.on("close", lt)), ct || typeof T.aborted != "boolean" || T.on("aborted", j), T.on("end", at), T.on("finish", X), $.error !== !1 && T.on("error", R), T.on("close", j), V ? S.nextTick(j) : Z != null && Z.errorEmitted || ht != null && ht.errorEmitted ? ct || S.nextTick(et) : (C || ct && !y(T) || !I && Q(T) !== !1) && (nt || ct && !Q(T) || !H && y(T) !== !1) ? ht && T.req && T.aborted && S.nextTick(et) : S.nextTick(et);
        const t = () => {
          tt = B, T.removeListener("aborted", j), T.removeListener("complete", X), T.removeListener("abort", j), T.removeListener("request", ot), T.req && T.req.removeListener("finish", X), T.removeListener("end", lt), T.removeListener("close", lt), T.removeListener("finish", X), T.removeListener("end", at), T.removeListener("error", R), T.removeListener("close", j);
        };
        if ($.signal && !V) {
          const n = () => {
            const r = tt;
            t(), r.call(T, new m(void 0, { cause: $.signal.reason }));
          };
          if ($.signal.aborted)
            S.nextTick(n);
          else {
            u = u || s(7760).addAbortListener;
            const r = u($.signal, n), _ = tt;
            tt = i((...E) => {
              r[o](), _.apply(T, E);
            });
          }
        }
        return t;
      }
      G.exports = K, G.exports.finished = function(T, $) {
        var tt;
        let W = !1;
        return $ === null && ($ = e), (tt = $) !== null && tt !== void 0 && tt.cleanup && (a($.cleanup, "cleanup"), W = $.cleanup), new l((J, C) => {
          const nt = K(T, $, (Z) => {
            W && nt(), Z ? C(Z) : J();
          });
        });
      };
    }, 6532: (G, N, s) => {
      const S = s(5606), { PromisePrototypeThen: m, SymbolAsyncIterator: O, SymbolIterator: P } = s(4134), { Buffer: w } = s(8287), { ERR_INVALID_ARG_TYPE: e, ERR_STREAM_NULL_VALUES: i } = s(6371).codes;
      G.exports = function(d, b, A) {
        let a, l;
        if (typeof b == "string" || b instanceof w)
          return new d({ objectMode: !0, ...A, read() {
            this.push(b), this.push(null);
          } });
        if (b && b[O])
          l = !0, a = b[O]();
        else {
          if (!b || !b[P])
            throw new e("iterable", ["Iterable"], b);
          l = !1, a = b[P]();
        }
        const c = new d({ objectMode: !0, highWaterMark: 1, ...A });
        let o = !1;
        return c._read = function() {
          o || (o = !0, async function() {
            for (; ; ) {
              try {
                const { value: h, done: y } = l ? await a.next() : a.next();
                if (y)
                  c.push(null);
                else {
                  const v = h && typeof h.then == "function" ? await h : h;
                  if (v === null)
                    throw o = !1, new i();
                  if (c.push(v))
                    continue;
                  o = !1;
                }
              } catch (h) {
                c.destroy(h);
              }
              break;
            }
          }());
        }, c._destroy = function(h, y) {
          m(async function(v) {
            const z = v != null, Y = typeof a.throw == "function";
            if (z && Y) {
              const { value: k, done: Q } = await a.throw(v);
              if (await k, Q)
                return;
            }
            if (typeof a.return == "function") {
              const { value: k } = await a.return();
              await k;
            }
          }(h), () => S.nextTick(y, h), (v) => S.nextTick(y, v || h));
        }, c;
      };
    }, 4259: (G, N, s) => {
      const { ArrayIsArray: S, ObjectSetPrototypeOf: m } = s(4134), { EventEmitter: O } = s(7007);
      function P(e) {
        O.call(this, e);
      }
      function w(e, i, d) {
        if (typeof e.prependListener == "function")
          return e.prependListener(i, d);
        e._events && e._events[i] ? S(e._events[i]) ? e._events[i].unshift(d) : e._events[i] = [d, e._events[i]] : e.on(i, d);
      }
      m(P.prototype, O.prototype), m(P, O), P.prototype.pipe = function(e, i) {
        const d = this;
        function b(y) {
          e.writable && e.write(y) === !1 && d.pause && d.pause();
        }
        function A() {
          d.readable && d.resume && d.resume();
        }
        d.on("data", b), e.on("drain", A), e._isStdio || i && i.end === !1 || (d.on("end", l), d.on("close", c));
        let a = !1;
        function l() {
          a || (a = !0, e.end());
        }
        function c() {
          a || (a = !0, typeof e.destroy == "function" && e.destroy());
        }
        function o(y) {
          h(), O.listenerCount(this, "error") === 0 && this.emit("error", y);
        }
        function h() {
          d.removeListener("data", b), e.removeListener("drain", A), d.removeListener("end", l), d.removeListener("close", c), d.removeListener("error", o), e.removeListener("error", o), d.removeListener("end", h), d.removeListener("close", h), e.removeListener("close", h);
        }
        return w(d, "error", o), w(e, "error", o), d.on("end", h), d.on("close", h), e.on("close", h), e.emit("pipe", d), e;
      }, G.exports = { Stream: P, prependListener: w };
    }, 823: (G, N, s) => {
      const S = globalThis.AbortController || s(5568).AbortController, { codes: { ERR_INVALID_ARG_VALUE: m, ERR_INVALID_ARG_TYPE: O, ERR_MISSING_ARGS: P, ERR_OUT_OF_RANGE: w }, AbortError: e } = s(6371), { validateAbortSignal: i, validateInteger: d, validateObject: b } = s(277), A = s(4134).Symbol("kWeak"), a = s(4134).Symbol("kResistStopPropagation"), { finished: l } = s(6238), c = s(7830), { addAbortSignalNoValidate: o } = s(4147), { isWritable: h, isNodeStream: y } = s(6115), { deprecate: v } = s(7760), { ArrayPrototypePush: z, Boolean: Y, MathFloor: k, Number: Q, NumberIsNaN: L, Promise: M, PromiseReject: f, PromiseResolve: F, PromisePrototypeThen: U, Symbol: D } = s(4134), rt = D("kEmpty"), u = D("kEof");
      function B(W, J) {
        if (typeof W != "function")
          throw new O("fn", ["Function", "AsyncFunction"], W);
        J != null && b(J, "options"), (J == null ? void 0 : J.signal) != null && i(J.signal, "options.signal");
        let C = 1;
        (J == null ? void 0 : J.concurrency) != null && (C = k(J.concurrency));
        let nt = C - 1;
        return (J == null ? void 0 : J.highWaterMark) != null && (nt = k(J.highWaterMark)), d(C, "options.concurrency", 1), d(nt, "options.highWaterMark", 0), nt += C, (async function* () {
          const Z = s(7760).AbortSignalAny([J == null ? void 0 : J.signal].filter(Y)), ht = this, lt = [], ct = { signal: Z };
          let I, X, H = !1, at = 0;
          function R() {
            H = !0, V();
          }
          function V() {
            at -= 1, j();
          }
          function j() {
            X && !H && at < C && lt.length < nt && (X(), X = null);
          }
          (async function() {
            try {
              for await (let et of ht) {
                if (H)
                  return;
                if (Z.aborted)
                  throw new e();
                try {
                  if (et = W(et, ct), et === rt)
                    continue;
                  et = F(et);
                } catch (ot) {
                  et = f(ot);
                }
                at += 1, U(et, V, R), lt.push(et), I && (I(), I = null), !H && (lt.length >= nt || at >= C) && await new M((ot) => {
                  X = ot;
                });
              }
              lt.push(u);
            } catch (et) {
              const ot = f(et);
              U(ot, V, R), lt.push(ot);
            } finally {
              H = !0, I && (I(), I = null);
            }
          })();
          try {
            for (; ; ) {
              for (; lt.length > 0; ) {
                const et = await lt[0];
                if (et === u)
                  return;
                if (Z.aborted)
                  throw new e();
                et !== rt && (yield et), lt.shift(), j();
              }
              await new M((et) => {
                I = et;
              });
            }
          } finally {
            H = !0, X && (X(), X = null);
          }
        }).call(this);
      }
      async function K(W, J = void 0) {
        for await (const C of T.call(this, W, J))
          return !0;
        return !1;
      }
      function T(W, J) {
        if (typeof W != "function")
          throw new O("fn", ["Function", "AsyncFunction"], W);
        return B.call(this, async function(C, nt) {
          return await W(C, nt) ? C : rt;
        }, J);
      }
      class $ extends P {
        constructor() {
          super("reduce"), this.message = "Reduce of an empty stream requires an initial value";
        }
      }
      function tt(W) {
        if (W = Q(W), L(W))
          return 0;
        if (W < 0)
          throw new w("number", ">= 0", W);
        return W;
      }
      G.exports.streamReturningOperators = { asIndexedPairs: v(function(W = void 0) {
        return W != null && b(W, "options"), (W == null ? void 0 : W.signal) != null && i(W.signal, "options.signal"), (async function* () {
          let J = 0;
          for await (const nt of this) {
            var C;
            if (W != null && (C = W.signal) !== null && C !== void 0 && C.aborted)
              throw new e({ cause: W.signal.reason });
            yield [J++, nt];
          }
        }).call(this);
      }, "readable.asIndexedPairs will be removed in a future version."), drop: function(W, J = void 0) {
        return J != null && b(J, "options"), (J == null ? void 0 : J.signal) != null && i(J.signal, "options.signal"), W = tt(W), (async function* () {
          var C;
          if (J != null && (C = J.signal) !== null && C !== void 0 && C.aborted)
            throw new e();
          for await (const Z of this) {
            var nt;
            if (J != null && (nt = J.signal) !== null && nt !== void 0 && nt.aborted)
              throw new e();
            W-- <= 0 && (yield Z);
          }
        }).call(this);
      }, filter: T, flatMap: function(W, J) {
        const C = B.call(this, W, J);
        return (async function* () {
          for await (const nt of C)
            yield* nt;
        }).call(this);
      }, map: B, take: function(W, J = void 0) {
        return J != null && b(J, "options"), (J == null ? void 0 : J.signal) != null && i(J.signal, "options.signal"), W = tt(W), (async function* () {
          var C;
          if (J != null && (C = J.signal) !== null && C !== void 0 && C.aborted)
            throw new e();
          for await (const Z of this) {
            var nt;
            if (J != null && (nt = J.signal) !== null && nt !== void 0 && nt.aborted)
              throw new e();
            if (W-- > 0 && (yield Z), W <= 0)
              return;
          }
        }).call(this);
      }, compose: function(W, J) {
        if (J != null && b(J, "options"), (J == null ? void 0 : J.signal) != null && i(J.signal, "options.signal"), y(W) && !h(W))
          throw new m("stream", W, "must be writable");
        const C = c(this, W);
        return J != null && J.signal && o(J.signal, C), C;
      } }, G.exports.promiseReturningOperators = { every: async function(W, J = void 0) {
        if (typeof W != "function")
          throw new O("fn", ["Function", "AsyncFunction"], W);
        return !await K.call(this, async (...C) => !await W(...C), J);
      }, forEach: async function(W, J) {
        if (typeof W != "function")
          throw new O("fn", ["Function", "AsyncFunction"], W);
        for await (const C of B.call(this, async function(nt, Z) {
          return await W(nt, Z), rt;
        }, J))
          ;
      }, reduce: async function(W, J, C) {
        var nt;
        if (typeof W != "function")
          throw new O("reducer", ["Function", "AsyncFunction"], W);
        C != null && b(C, "options"), (C == null ? void 0 : C.signal) != null && i(C.signal, "options.signal");
        let Z = arguments.length > 1;
        if (C != null && (nt = C.signal) !== null && nt !== void 0 && nt.aborted) {
          const X = new e(void 0, { cause: C.signal.reason });
          throw this.once("error", () => {
          }), await l(this.destroy(X)), X;
        }
        const ht = new S(), lt = ht.signal;
        if (C != null && C.signal) {
          const X = { once: !0, [A]: this, [a]: !0 };
          C.signal.addEventListener("abort", () => ht.abort(), X);
        }
        let ct = !1;
        try {
          for await (const X of this) {
            var I;
            if (ct = !0, C != null && (I = C.signal) !== null && I !== void 0 && I.aborted)
              throw new e();
            Z ? J = await W(J, X, { signal: lt }) : (J = X, Z = !0);
          }
          if (!ct && !Z)
            throw new $();
        } finally {
          ht.abort();
        }
        return J;
      }, toArray: async function(W) {
        W != null && b(W, "options"), (W == null ? void 0 : W.signal) != null && i(W.signal, "options.signal");
        const J = [];
        for await (const nt of this) {
          var C;
          if (W != null && (C = W.signal) !== null && C !== void 0 && C.aborted)
            throw new e(void 0, { cause: W.signal.reason });
          z(J, nt);
        }
        return J;
      }, some: K, find: async function(W, J) {
        for await (const C of T.call(this, W, J))
          return C;
      } };
    }, 6524: (G, N, s) => {
      const { ObjectSetPrototypeOf: S } = s(4134);
      G.exports = O;
      const m = s(7382);
      function O(P) {
        if (!(this instanceof O))
          return new O(P);
        m.call(this, P);
      }
      S(O.prototype, m.prototype), S(O, m), O.prototype._transform = function(P, w, e) {
        e(null, P);
      };
    }, 7758: (G, N, s) => {
      const S = s(5606), { ArrayIsArray: m, Promise: O, SymbolAsyncIterator: P, SymbolDispose: w } = s(4134), e = s(6238), { once: i } = s(7760), d = s(5896), b = s(3370), { aggregateTwoErrors: A, codes: { ERR_INVALID_ARG_TYPE: a, ERR_INVALID_RETURN_VALUE: l, ERR_MISSING_ARGS: c, ERR_STREAM_DESTROYED: o, ERR_STREAM_PREMATURE_CLOSE: h }, AbortError: y } = s(6371), { validateFunction: v, validateAbortSignal: z } = s(277), { isIterable: Y, isReadable: k, isReadableNodeStream: Q, isNodeStream: L, isTransformStream: M, isWebStream: f, isReadableStream: F, isReadableFinished: U } = s(6115), D = globalThis.AbortController || s(5568).AbortController;
      let rt, u, B;
      function K(C, nt, Z) {
        let ht = !1;
        return C.on("close", () => {
          ht = !0;
        }), { destroy: (lt) => {
          ht || (ht = !0, d.destroyer(C, lt || new o("pipe")));
        }, cleanup: e(C, { readable: nt, writable: Z }, (lt) => {
          ht = !lt;
        }) };
      }
      function T(C) {
        if (Y(C))
          return C;
        if (Q(C))
          return async function* (nt) {
            u || (u = s(7576)), yield* u.prototype[P].call(nt);
          }(C);
        throw new a("val", ["Readable", "Iterable", "AsyncIterable"], C);
      }
      async function $(C, nt, Z, { end: ht }) {
        let lt, ct = null;
        const I = (at) => {
          if (at && (lt = at), ct) {
            const R = ct;
            ct = null, R();
          }
        }, X = () => new O((at, R) => {
          lt ? R(lt) : ct = () => {
            lt ? R(lt) : at();
          };
        });
        nt.on("drain", I);
        const H = e(nt, { readable: !1 }, I);
        try {
          nt.writableNeedDrain && await X();
          for await (const at of C)
            nt.write(at) || await X();
          ht && (nt.end(), await X()), Z();
        } catch (at) {
          Z(lt !== at ? A(lt, at) : at);
        } finally {
          H(), nt.off("drain", I);
        }
      }
      async function tt(C, nt, Z, { end: ht }) {
        M(nt) && (nt = nt.writable);
        const lt = nt.getWriter();
        try {
          for await (const ct of C)
            await lt.ready, lt.write(ct).catch(() => {
            });
          await lt.ready, ht && await lt.close(), Z();
        } catch (ct) {
          try {
            await lt.abort(ct), Z(ct);
          } catch (I) {
            Z(I);
          }
        }
      }
      function W(C, nt, Z) {
        if (C.length === 1 && m(C[0]) && (C = C[0]), C.length < 2)
          throw new c("streams");
        const ht = new D(), lt = ht.signal, ct = Z == null ? void 0 : Z.signal, I = [];
        function X() {
          t(new y());
        }
        let H, at, R;
        z(ct, "options.signal"), B = B || s(7760).addAbortListener, ct && (H = B(ct, X));
        const V = [];
        let j, et = 0;
        function ot(_) {
          t(_, --et == 0);
        }
        function t(_, E) {
          var x;
          if (!_ || at && at.code !== "ERR_STREAM_PREMATURE_CLOSE" || (at = _), at || E) {
            for (; V.length; )
              V.shift()(at);
            (x = H) === null || x === void 0 || x[w](), ht.abort(), E && (at || I.forEach((q) => q()), S.nextTick(nt, at, R));
          }
        }
        for (let _ = 0; _ < C.length; _++) {
          const E = C[_], x = _ < C.length - 1, q = _ > 0, ut = x || (Z == null ? void 0 : Z.end) !== !1, pt = _ === C.length - 1;
          if (L(E)) {
            let bt = function(dt) {
              dt && dt.name !== "AbortError" && dt.code !== "ERR_STREAM_PREMATURE_CLOSE" && ot(dt);
            };
            if (ut) {
              const { destroy: dt, cleanup: yt } = K(E, x, q);
              V.push(dt), k(E) && pt && I.push(yt);
            }
            E.on("error", bt), k(E) && pt && I.push(() => {
              E.removeListener("error", bt);
            });
          }
          if (_ === 0)
            if (typeof E == "function") {
              if (j = E({ signal: lt }), !Y(j))
                throw new l("Iterable, AsyncIterable or Stream", "source", j);
            } else
              j = Y(E) || Q(E) || M(E) ? E : b.from(E);
          else if (typeof E == "function") {
            var n;
            if (j = M(j) ? T((n = j) === null || n === void 0 ? void 0 : n.readable) : T(j), j = E(j, { signal: lt }), x) {
              if (!Y(j, !0))
                throw new l("AsyncIterable", `transform[${_ - 1}]`, j);
            } else {
              var r;
              rt || (rt = s(6524));
              const bt = new rt({ objectMode: !0 }), dt = (r = j) === null || r === void 0 ? void 0 : r.then;
              if (typeof dt == "function")
                et++, dt.call(j, (g) => {
                  R = g, g != null && bt.write(g), ut && bt.end(), S.nextTick(ot);
                }, (g) => {
                  bt.destroy(g), S.nextTick(ot, g);
                });
              else if (Y(j, !0))
                et++, $(j, bt, ot, { end: ut });
              else {
                if (!F(j) && !M(j))
                  throw new l("AsyncIterable or Promise", "destination", j);
                {
                  const g = j.readable || j;
                  et++, $(g, bt, ot, { end: ut });
                }
              }
              j = bt;
              const { destroy: yt, cleanup: p } = K(j, !1, !0);
              V.push(yt), pt && I.push(p);
            }
          } else if (L(E)) {
            if (Q(j)) {
              et += 2;
              const bt = J(j, E, ot, { end: ut });
              k(E) && pt && I.push(bt);
            } else if (M(j) || F(j)) {
              const bt = j.readable || j;
              et++, $(bt, E, ot, { end: ut });
            } else {
              if (!Y(j))
                throw new a("val", ["Readable", "Iterable", "AsyncIterable", "ReadableStream", "TransformStream"], j);
              et++, $(j, E, ot, { end: ut });
            }
            j = E;
          } else if (f(E)) {
            if (Q(j))
              et++, tt(T(j), E, ot, { end: ut });
            else if (F(j) || Y(j))
              et++, tt(j, E, ot, { end: ut });
            else {
              if (!M(j))
                throw new a("val", ["Readable", "Iterable", "AsyncIterable", "ReadableStream", "TransformStream"], j);
              et++, tt(j.readable, E, ot, { end: ut });
            }
            j = E;
          } else
            j = b.from(E);
        }
        return (lt != null && lt.aborted || ct != null && ct.aborted) && S.nextTick(X), j;
      }
      function J(C, nt, Z, { end: ht }) {
        let lt = !1;
        if (nt.on("close", () => {
          lt || Z(new h());
        }), C.pipe(nt, { end: !1 }), ht) {
          let ct = function() {
            lt = !0, nt.end();
          };
          U(C) ? S.nextTick(ct) : C.once("end", ct);
        } else
          Z();
        return e(C, { readable: !0, writable: !1 }, (ct) => {
          const I = C._readableState;
          ct && ct.code === "ERR_STREAM_PREMATURE_CLOSE" && I && I.ended && !I.errored && !I.errorEmitted ? C.once("end", Z).once("error", Z) : Z(ct);
        }), e(nt, { readable: !1, writable: !0 }, Z);
      }
      G.exports = { pipelineImpl: W, pipeline: function(...C) {
        return W(C, i(function(nt) {
          return v(nt[nt.length - 1], "streams[stream.length - 1]"), nt.pop();
        }(C)));
      } };
    }, 7576: (G, N, s) => {
      const S = s(5606), { ArrayPrototypeIndexOf: m, NumberIsInteger: O, NumberIsNaN: P, NumberParseInt: w, ObjectDefineProperties: e, ObjectKeys: i, ObjectSetPrototypeOf: d, Promise: b, SafeSet: A, SymbolAsyncDispose: a, SymbolAsyncIterator: l, Symbol: c } = s(4134);
      G.exports = H, H.ReadableState = X;
      const { EventEmitter: o } = s(7007), { Stream: h, prependListener: y } = s(4259), { Buffer: v } = s(8287), { addAbortSignal: z } = s(4147), Y = s(6238);
      let k = s(7760).debuglog("stream", (p) => {
        k = p;
      });
      const Q = s(345), L = s(5896), { getHighWaterMark: M, getDefaultHighWaterMark: f } = s(5291), { aggregateTwoErrors: F, codes: { ERR_INVALID_ARG_TYPE: U, ERR_METHOD_NOT_IMPLEMENTED: D, ERR_OUT_OF_RANGE: rt, ERR_STREAM_PUSH_AFTER_EOF: u, ERR_STREAM_UNSHIFT_AFTER_END_EVENT: B }, AbortError: K } = s(6371), { validateObject: T } = s(277), $ = c("kPaused"), { StringDecoder: tt } = s(3141), W = s(6532);
      d(H.prototype, h.prototype), d(H, h);
      const J = () => {
      }, { errorOrDestroy: C } = L, nt = 1, Z = 16, ht = 32, lt = 2048, ct = 4096;
      function I(p) {
        return { enumerable: !1, get() {
          return !!(this.state & p);
        }, set(g) {
          g ? this.state |= p : this.state &= ~p;
        } };
      }
      function X(p, g, st) {
        typeof st != "boolean" && (st = g instanceof s(3370)), this.state = lt | ct | Z | ht, p && p.objectMode && (this.state |= nt), st && p && p.readableObjectMode && (this.state |= nt), this.highWaterMark = p ? M(this, p, "readableHighWaterMark", st) : f(!1), this.buffer = new Q(), this.length = 0, this.pipes = [], this.flowing = null, this[$] = null, p && p.emitClose === !1 && (this.state &= ~lt), p && p.autoDestroy === !1 && (this.state &= ~ct), this.errored = null, this.defaultEncoding = p && p.defaultEncoding || "utf8", this.awaitDrainWriters = null, this.decoder = null, this.encoding = null, p && p.encoding && (this.decoder = new tt(p.encoding), this.encoding = p.encoding);
      }
      function H(p) {
        if (!(this instanceof H))
          return new H(p);
        const g = this instanceof s(3370);
        this._readableState = new X(p, this, g), p && (typeof p.read == "function" && (this._read = p.read), typeof p.destroy == "function" && (this._destroy = p.destroy), typeof p.construct == "function" && (this._construct = p.construct), p.signal && !g && z(p.signal, this)), h.call(this, p), L.construct(this, () => {
          this._readableState.needReadable && ot(this, this._readableState);
        });
      }
      function at(p, g, st, it) {
        k("readableAddChunk", g);
        const ft = p._readableState;
        let gt;
        if (ft.state & nt || (typeof g == "string" ? (st = st || ft.defaultEncoding, ft.encoding !== st && (it && ft.encoding ? g = v.from(g, st).toString(ft.encoding) : (g = v.from(g, st), st = ""))) : g instanceof v ? st = "" : h._isUint8Array(g) ? (g = h._uint8ArrayToBuffer(g), st = "") : g != null && (gt = new U("chunk", ["string", "Buffer", "Uint8Array"], g))), gt)
          C(p, gt);
        else if (g === null)
          ft.state &= -9, function(mt, _t) {
            if (k("onEofChunk"), !_t.ended) {
              if (_t.decoder) {
                const Tt = _t.decoder.end();
                Tt && Tt.length && (_t.buffer.push(Tt), _t.length += _t.objectMode ? 1 : Tt.length);
              }
              _t.ended = !0, _t.sync ? j(mt) : (_t.needReadable = !1, _t.emittedReadable = !0, et(mt));
            }
          }(p, ft);
        else if (ft.state & nt || g && g.length > 0)
          if (it)
            if (4 & ft.state)
              C(p, new B());
            else {
              if (ft.destroyed || ft.errored)
                return !1;
              R(p, ft, g, !0);
            }
          else if (ft.ended)
            C(p, new u());
          else {
            if (ft.destroyed || ft.errored)
              return !1;
            ft.state &= -9, ft.decoder && !st ? (g = ft.decoder.write(g), ft.objectMode || g.length !== 0 ? R(p, ft, g, !1) : ot(p, ft)) : R(p, ft, g, !1);
          }
        else
          it || (ft.state &= -9, ot(p, ft));
        return !ft.ended && (ft.length < ft.highWaterMark || ft.length === 0);
      }
      function R(p, g, st, it) {
        g.flowing && g.length === 0 && !g.sync && p.listenerCount("data") > 0 ? (65536 & g.state ? g.awaitDrainWriters.clear() : g.awaitDrainWriters = null, g.dataEmitted = !0, p.emit("data", st)) : (g.length += g.objectMode ? 1 : st.length, it ? g.buffer.unshift(st) : g.buffer.push(st), 64 & g.state && j(p)), ot(p, g);
      }
      function V(p, g) {
        return p <= 0 || g.length === 0 && g.ended ? 0 : g.state & nt ? 1 : P(p) ? g.flowing && g.length ? g.buffer.first().length : g.length : p <= g.length ? p : g.ended ? g.length : 0;
      }
      function j(p) {
        const g = p._readableState;
        k("emitReadable", g.needReadable, g.emittedReadable), g.needReadable = !1, g.emittedReadable || (k("emitReadable", g.flowing), g.emittedReadable = !0, S.nextTick(et, p));
      }
      function et(p) {
        const g = p._readableState;
        k("emitReadable_", g.destroyed, g.length, g.ended), g.destroyed || g.errored || !g.length && !g.ended || (p.emit("readable"), g.emittedReadable = !1), g.needReadable = !g.flowing && !g.ended && g.length <= g.highWaterMark, E(p);
      }
      function ot(p, g) {
        !g.readingMore && g.constructed && (g.readingMore = !0, S.nextTick(t, p, g));
      }
      function t(p, g) {
        for (; !g.reading && !g.ended && (g.length < g.highWaterMark || g.flowing && g.length === 0); ) {
          const st = g.length;
          if (k("maybeReadMore read 0"), p.read(0), st === g.length)
            break;
        }
        g.readingMore = !1;
      }
      function n(p) {
        const g = p._readableState;
        g.readableListening = p.listenerCount("readable") > 0, g.resumeScheduled && g[$] === !1 ? g.flowing = !0 : p.listenerCount("data") > 0 ? p.resume() : g.readableListening || (g.flowing = null);
      }
      function r(p) {
        k("readable nexttick read 0"), p.read(0);
      }
      function _(p, g) {
        k("resume", g.reading), g.reading || p.read(0), g.resumeScheduled = !1, p.emit("resume"), E(p), g.flowing && !g.reading && p.read(0);
      }
      function E(p) {
        const g = p._readableState;
        for (k("flow", g.flowing); g.flowing && p.read() !== null; )
          ;
      }
      function x(p, g) {
        typeof p.read != "function" && (p = H.wrap(p, { objectMode: !0 }));
        const st = async function* (it, ft) {
          let gt, mt = J;
          function _t(Et) {
            this === it ? (mt(), mt = J) : mt = Et;
          }
          it.on("readable", _t);
          const Tt = Y(it, { writable: !1 }, (Et) => {
            gt = Et ? F(gt, Et) : null, mt(), mt = J;
          });
          try {
            for (; ; ) {
              const Et = it.destroyed ? null : it.read();
              if (Et !== null)
                yield Et;
              else {
                if (gt)
                  throw gt;
                if (gt === null)
                  return;
                await new b(_t);
              }
            }
          } catch (Et) {
            throw gt = F(gt, Et), gt;
          } finally {
            !gt && (ft == null ? void 0 : ft.destroyOnReturn) === !1 || gt !== void 0 && !it._readableState.autoDestroy ? (it.off("readable", _t), Tt()) : L.destroyer(it, null);
          }
        }(p, g);
        return st.stream = p, st;
      }
      function q(p, g) {
        if (g.length === 0)
          return null;
        let st;
        return g.objectMode ? st = g.buffer.shift() : !p || p >= g.length ? (st = g.decoder ? g.buffer.join("") : g.buffer.length === 1 ? g.buffer.first() : g.buffer.concat(g.length), g.buffer.clear()) : st = g.buffer.consume(p, g.decoder), st;
      }
      function ut(p) {
        const g = p._readableState;
        k("endReadable", g.endEmitted), g.endEmitted || (g.ended = !0, S.nextTick(pt, g, p));
      }
      function pt(p, g) {
        if (k("endReadableNT", p.endEmitted, p.length), !p.errored && !p.closeEmitted && !p.endEmitted && p.length === 0) {
          if (p.endEmitted = !0, g.emit("end"), g.writable && g.allowHalfOpen === !1)
            S.nextTick(bt, g);
          else if (p.autoDestroy) {
            const st = g._writableState;
            (!st || st.autoDestroy && (st.finished || st.writable === !1)) && g.destroy();
          }
        }
      }
      function bt(p) {
        p.writable && !p.writableEnded && !p.destroyed && p.end();
      }
      let dt;
      function yt() {
        return dt === void 0 && (dt = {}), dt;
      }
      e(X.prototype, { objectMode: I(nt), ended: I(2), endEmitted: I(4), reading: I(8), constructed: I(Z), sync: I(ht), needReadable: I(64), emittedReadable: I(128), readableListening: I(256), resumeScheduled: I(512), errorEmitted: I(1024), emitClose: I(lt), autoDestroy: I(ct), destroyed: I(8192), closed: I(16384), closeEmitted: I(32768), multiAwaitDrain: I(65536), readingMore: I(131072), dataEmitted: I(262144) }), H.prototype.destroy = L.destroy, H.prototype._undestroy = L.undestroy, H.prototype._destroy = function(p, g) {
        g(p);
      }, H.prototype[o.captureRejectionSymbol] = function(p) {
        this.destroy(p);
      }, H.prototype[a] = function() {
        let p;
        return this.destroyed || (p = this.readableEnded ? null : new K(), this.destroy(p)), new b((g, st) => Y(this, (it) => it && it !== p ? st(it) : g(null)));
      }, H.prototype.push = function(p, g) {
        return at(this, p, g, !1);
      }, H.prototype.unshift = function(p, g) {
        return at(this, p, g, !0);
      }, H.prototype.isPaused = function() {
        const p = this._readableState;
        return p[$] === !0 || p.flowing === !1;
      }, H.prototype.setEncoding = function(p) {
        const g = new tt(p);
        this._readableState.decoder = g, this._readableState.encoding = this._readableState.decoder.encoding;
        const st = this._readableState.buffer;
        let it = "";
        for (const ft of st)
          it += g.write(ft);
        return st.clear(), it !== "" && st.push(it), this._readableState.length = it.length, this;
      }, H.prototype.read = function(p) {
        k("read", p), p === void 0 ? p = NaN : O(p) || (p = w(p, 10));
        const g = this._readableState, st = p;
        if (p > g.highWaterMark && (g.highWaterMark = function(gt) {
          if (gt > 1073741824)
            throw new rt("size", "<= 1GiB", gt);
          return gt--, gt |= gt >>> 1, gt |= gt >>> 2, gt |= gt >>> 4, gt |= gt >>> 8, gt |= gt >>> 16, ++gt;
        }(p)), p !== 0 && (g.state &= -129), p === 0 && g.needReadable && ((g.highWaterMark !== 0 ? g.length >= g.highWaterMark : g.length > 0) || g.ended))
          return k("read: emitReadable", g.length, g.ended), g.length === 0 && g.ended ? ut(this) : j(this), null;
        if ((p = V(p, g)) === 0 && g.ended)
          return g.length === 0 && ut(this), null;
        let it, ft = !!(64 & g.state);
        if (k("need readable", ft), (g.length === 0 || g.length - p < g.highWaterMark) && (ft = !0, k("length less than watermark", ft)), g.ended || g.reading || g.destroyed || g.errored || !g.constructed)
          ft = !1, k("reading, ended or constructing", ft);
        else if (ft) {
          k("do read"), g.state |= 8 | ht, g.length === 0 && (g.state |= 64);
          try {
            this._read(g.highWaterMark);
          } catch (gt) {
            C(this, gt);
          }
          g.state &= ~ht, g.reading || (p = V(st, g));
        }
        return it = p > 0 ? q(p, g) : null, it === null ? (g.needReadable = g.length <= g.highWaterMark, p = 0) : (g.length -= p, g.multiAwaitDrain ? g.awaitDrainWriters.clear() : g.awaitDrainWriters = null), g.length === 0 && (g.ended || (g.needReadable = !0), st !== p && g.ended && ut(this)), it === null || g.errorEmitted || g.closeEmitted || (g.dataEmitted = !0, this.emit("data", it)), it;
      }, H.prototype._read = function(p) {
        throw new D("_read()");
      }, H.prototype.pipe = function(p, g) {
        const st = this, it = this._readableState;
        it.pipes.length === 1 && (it.multiAwaitDrain || (it.multiAwaitDrain = !0, it.awaitDrainWriters = new A(it.awaitDrainWriters ? [it.awaitDrainWriters] : []))), it.pipes.push(p), k("pipe count=%d opts=%j", it.pipes.length, g);
        const ft = g && g.end === !1 || p === S.stdout || p === S.stderr ? Lt : gt;
        function gt() {
          k("onend"), p.end();
        }
        let mt;
        it.endEmitted ? S.nextTick(ft) : st.once("end", ft), p.on("unpipe", function St(It, vt) {
          k("onunpipe"), It === st && vt && vt.hasUnpiped === !1 && (vt.hasUnpiped = !0, k("cleanup"), p.removeListener("close", Mt), p.removeListener("finish", xt), mt && p.removeListener("drain", mt), p.removeListener("error", Ot), p.removeListener("unpipe", St), st.removeListener("end", gt), st.removeListener("end", Lt), st.removeListener("data", Et), _t = !0, mt && it.awaitDrainWriters && (!p._writableState || p._writableState.needDrain) && mt());
        });
        let _t = !1;
        function Tt() {
          _t || (it.pipes.length === 1 && it.pipes[0] === p ? (k("false write response, pause", 0), it.awaitDrainWriters = p, it.multiAwaitDrain = !1) : it.pipes.length > 1 && it.pipes.includes(p) && (k("false write response, pause", it.awaitDrainWriters.size), it.awaitDrainWriters.add(p)), st.pause()), mt || (mt = function(St, It) {
            return function() {
              const vt = St._readableState;
              vt.awaitDrainWriters === It ? (k("pipeOnDrain", 1), vt.awaitDrainWriters = null) : vt.multiAwaitDrain && (k("pipeOnDrain", vt.awaitDrainWriters.size), vt.awaitDrainWriters.delete(It)), vt.awaitDrainWriters && vt.awaitDrainWriters.size !== 0 || !St.listenerCount("data") || St.resume();
            };
          }(st, p), p.on("drain", mt));
        }
        function Et(St) {
          k("ondata");
          const It = p.write(St);
          k("dest.write", It), It === !1 && Tt();
        }
        function Ot(St) {
          if (k("onerror", St), Lt(), p.removeListener("error", Ot), p.listenerCount("error") === 0) {
            const It = p._writableState || p._readableState;
            It && !It.errorEmitted ? C(p, St) : p.emit("error", St);
          }
        }
        function Mt() {
          p.removeListener("finish", xt), Lt();
        }
        function xt() {
          k("onfinish"), p.removeListener("close", Mt), Lt();
        }
        function Lt() {
          k("unpipe"), st.unpipe(p);
        }
        return st.on("data", Et), y(p, "error", Ot), p.once("close", Mt), p.once("finish", xt), p.emit("pipe", st), p.writableNeedDrain === !0 ? Tt() : it.flowing || (k("pipe resume"), st.resume()), p;
      }, H.prototype.unpipe = function(p) {
        const g = this._readableState;
        if (g.pipes.length === 0)
          return this;
        if (!p) {
          const it = g.pipes;
          g.pipes = [], this.pause();
          for (let ft = 0; ft < it.length; ft++)
            it[ft].emit("unpipe", this, { hasUnpiped: !1 });
          return this;
        }
        const st = m(g.pipes, p);
        return st === -1 || (g.pipes.splice(st, 1), g.pipes.length === 0 && this.pause(), p.emit("unpipe", this, { hasUnpiped: !1 })), this;
      }, H.prototype.on = function(p, g) {
        const st = h.prototype.on.call(this, p, g), it = this._readableState;
        return p === "data" ? (it.readableListening = this.listenerCount("readable") > 0, it.flowing !== !1 && this.resume()) : p === "readable" && (it.endEmitted || it.readableListening || (it.readableListening = it.needReadable = !0, it.flowing = !1, it.emittedReadable = !1, k("on readable", it.length, it.reading), it.length ? j(this) : it.reading || S.nextTick(r, this))), st;
      }, H.prototype.addListener = H.prototype.on, H.prototype.removeListener = function(p, g) {
        const st = h.prototype.removeListener.call(this, p, g);
        return p === "readable" && S.nextTick(n, this), st;
      }, H.prototype.off = H.prototype.removeListener, H.prototype.removeAllListeners = function(p) {
        const g = h.prototype.removeAllListeners.apply(this, arguments);
        return p !== "readable" && p !== void 0 || S.nextTick(n, this), g;
      }, H.prototype.resume = function() {
        const p = this._readableState;
        return p.flowing || (k("resume"), p.flowing = !p.readableListening, function(g, st) {
          st.resumeScheduled || (st.resumeScheduled = !0, S.nextTick(_, g, st));
        }(this, p)), p[$] = !1, this;
      }, H.prototype.pause = function() {
        return k("call pause flowing=%j", this._readableState.flowing), this._readableState.flowing !== !1 && (k("pause"), this._readableState.flowing = !1, this.emit("pause")), this._readableState[$] = !0, this;
      }, H.prototype.wrap = function(p) {
        let g = !1;
        p.on("data", (it) => {
          !this.push(it) && p.pause && (g = !0, p.pause());
        }), p.on("end", () => {
          this.push(null);
        }), p.on("error", (it) => {
          C(this, it);
        }), p.on("close", () => {
          this.destroy();
        }), p.on("destroy", () => {
          this.destroy();
        }), this._read = () => {
          g && p.resume && (g = !1, p.resume());
        };
        const st = i(p);
        for (let it = 1; it < st.length; it++) {
          const ft = st[it];
          this[ft] === void 0 && typeof p[ft] == "function" && (this[ft] = p[ft].bind(p));
        }
        return this;
      }, H.prototype[l] = function() {
        return x(this);
      }, H.prototype.iterator = function(p) {
        return p !== void 0 && T(p, "options"), x(this, p);
      }, e(H.prototype, { readable: { __proto__: null, get() {
        const p = this._readableState;
        return !(!p || p.readable === !1 || p.destroyed || p.errorEmitted || p.endEmitted);
      }, set(p) {
        this._readableState && (this._readableState.readable = !!p);
      } }, readableDidRead: { __proto__: null, enumerable: !1, get: function() {
        return this._readableState.dataEmitted;
      } }, readableAborted: { __proto__: null, enumerable: !1, get: function() {
        return !(this._readableState.readable === !1 || !this._readableState.destroyed && !this._readableState.errored || this._readableState.endEmitted);
      } }, readableHighWaterMark: { __proto__: null, enumerable: !1, get: function() {
        return this._readableState.highWaterMark;
      } }, readableBuffer: { __proto__: null, enumerable: !1, get: function() {
        return this._readableState && this._readableState.buffer;
      } }, readableFlowing: { __proto__: null, enumerable: !1, get: function() {
        return this._readableState.flowing;
      }, set: function(p) {
        this._readableState && (this._readableState.flowing = p);
      } }, readableLength: { __proto__: null, enumerable: !1, get() {
        return this._readableState.length;
      } }, readableObjectMode: { __proto__: null, enumerable: !1, get() {
        return !!this._readableState && this._readableState.objectMode;
      } }, readableEncoding: { __proto__: null, enumerable: !1, get() {
        return this._readableState ? this._readableState.encoding : null;
      } }, errored: { __proto__: null, enumerable: !1, get() {
        return this._readableState ? this._readableState.errored : null;
      } }, closed: { __proto__: null, get() {
        return !!this._readableState && this._readableState.closed;
      } }, destroyed: { __proto__: null, enumerable: !1, get() {
        return !!this._readableState && this._readableState.destroyed;
      }, set(p) {
        this._readableState && (this._readableState.destroyed = p);
      } }, readableEnded: { __proto__: null, enumerable: !1, get() {
        return !!this._readableState && this._readableState.endEmitted;
      } } }), e(X.prototype, { pipesCount: { __proto__: null, get() {
        return this.pipes.length;
      } }, paused: { __proto__: null, get() {
        return this[$] !== !1;
      }, set(p) {
        this[$] = !!p;
      } } }), H._fromList = q, H.from = function(p, g) {
        return W(H, p, g);
      }, H.fromWeb = function(p, g) {
        return yt().newStreamReadableFromReadableStream(p, g);
      }, H.toWeb = function(p, g) {
        return yt().newReadableStreamFromStreamReadable(p, g);
      }, H.wrap = function(p, g) {
        var st, it;
        return new H({ objectMode: (st = (it = p.readableObjectMode) !== null && it !== void 0 ? it : p.objectMode) === null || st === void 0 || st, ...g, destroy(ft, gt) {
          L.destroyer(p, ft), gt(ft);
        } }).wrap(p);
      };
    }, 5291: (G, N, s) => {
      const { MathFloor: S, NumberIsInteger: m } = s(4134), { validateInteger: O } = s(277), { ERR_INVALID_ARG_VALUE: P } = s(6371).codes;
      let w = 16384, e = 16;
      function i(d) {
        return d ? e : w;
      }
      G.exports = { getHighWaterMark: function(d, b, A, a) {
        const l = function(c, o, h) {
          return c.highWaterMark != null ? c.highWaterMark : o ? c[h] : null;
        }(b, a, A);
        if (l != null) {
          if (!m(l) || l < 0)
            throw new P(a ? `options.${A}` : "options.highWaterMark", l);
          return S(l);
        }
        return i(d.objectMode);
      }, getDefaultHighWaterMark: i, setDefaultHighWaterMark: function(d, b) {
        O(b, "value", 0), d ? e = b : w = b;
      } };
    }, 7382: (G, N, s) => {
      const { ObjectSetPrototypeOf: S, Symbol: m } = s(4134);
      G.exports = i;
      const { ERR_METHOD_NOT_IMPLEMENTED: O } = s(6371).codes, P = s(3370), { getHighWaterMark: w } = s(5291);
      S(i.prototype, P.prototype), S(i, P);
      const e = m("kCallback");
      function i(A) {
        if (!(this instanceof i))
          return new i(A);
        const a = A ? w(this, A, "readableHighWaterMark", !0) : null;
        a === 0 && (A = { ...A, highWaterMark: null, readableHighWaterMark: a, writableHighWaterMark: A.writableHighWaterMark || 0 }), P.call(this, A), this._readableState.sync = !1, this[e] = null, A && (typeof A.transform == "function" && (this._transform = A.transform), typeof A.flush == "function" && (this._flush = A.flush)), this.on("prefinish", b);
      }
      function d(A) {
        typeof this._flush != "function" || this.destroyed ? (this.push(null), A && A()) : this._flush((a, l) => {
          a ? A ? A(a) : this.destroy(a) : (l != null && this.push(l), this.push(null), A && A());
        });
      }
      function b() {
        this._final !== d && d.call(this);
      }
      i.prototype._final = d, i.prototype._transform = function(A, a, l) {
        throw new O("_transform()");
      }, i.prototype._write = function(A, a, l) {
        const c = this._readableState, o = this._writableState, h = c.length;
        this._transform(A, a, (y, v) => {
          y ? l(y) : (v != null && this.push(v), o.ended || h === c.length || c.length < c.highWaterMark ? l() : this[e] = l);
        });
      }, i.prototype._read = function() {
        if (this[e]) {
          const A = this[e];
          this[e] = null, A();
        }
      };
    }, 6115: (G, N, s) => {
      const { SymbolAsyncIterator: S, SymbolIterator: m, SymbolFor: O } = s(4134), P = O("nodejs.stream.destroyed"), w = O("nodejs.stream.errored"), e = O("nodejs.stream.readable"), i = O("nodejs.stream.writable"), d = O("nodejs.stream.disturbed"), b = O("nodejs.webstream.isClosedPromise"), A = O("nodejs.webstream.controllerErrorFunction");
      function a(f, F = !1) {
        var U;
        return !(!f || typeof f.pipe != "function" || typeof f.on != "function" || F && (typeof f.pause != "function" || typeof f.resume != "function") || f._writableState && ((U = f._readableState) === null || U === void 0 ? void 0 : U.readable) === !1 || f._writableState && !f._readableState);
      }
      function l(f) {
        var F;
        return !(!f || typeof f.write != "function" || typeof f.on != "function" || f._readableState && ((F = f._writableState) === null || F === void 0 ? void 0 : F.writable) === !1);
      }
      function c(f) {
        return f && (f._readableState || f._writableState || typeof f.write == "function" && typeof f.on == "function" || typeof f.pipe == "function" && typeof f.on == "function");
      }
      function o(f) {
        return !(!f || c(f) || typeof f.pipeThrough != "function" || typeof f.getReader != "function" || typeof f.cancel != "function");
      }
      function h(f) {
        return !(!f || c(f) || typeof f.getWriter != "function" || typeof f.abort != "function");
      }
      function y(f) {
        return !(!f || c(f) || typeof f.readable != "object" || typeof f.writable != "object");
      }
      function v(f) {
        if (!c(f))
          return null;
        const F = f._writableState, U = f._readableState, D = F || U;
        return !!(f.destroyed || f[P] || D != null && D.destroyed);
      }
      function z(f) {
        if (!l(f))
          return null;
        if (f.writableEnded === !0)
          return !0;
        const F = f._writableState;
        return (F == null || !F.errored) && (typeof (F == null ? void 0 : F.ended) != "boolean" ? null : F.ended);
      }
      function Y(f, F) {
        if (!a(f))
          return null;
        const U = f._readableState;
        return (U == null || !U.errored) && (typeof (U == null ? void 0 : U.endEmitted) != "boolean" ? null : !!(U.endEmitted || F === !1 && U.ended === !0 && U.length === 0));
      }
      function k(f) {
        return f && f[e] != null ? f[e] : typeof (f == null ? void 0 : f.readable) != "boolean" ? null : !v(f) && a(f) && f.readable && !Y(f);
      }
      function Q(f) {
        return f && f[i] != null ? f[i] : typeof (f == null ? void 0 : f.writable) != "boolean" ? null : !v(f) && l(f) && f.writable && !z(f);
      }
      function L(f) {
        return typeof f._closed == "boolean" && typeof f._defaultKeepAlive == "boolean" && typeof f._removedConnection == "boolean" && typeof f._removedContLen == "boolean";
      }
      function M(f) {
        return typeof f._sent100 == "boolean" && L(f);
      }
      G.exports = { isDestroyed: v, kIsDestroyed: P, isDisturbed: function(f) {
        var F;
        return !(!f || !((F = f[d]) !== null && F !== void 0 ? F : f.readableDidRead || f.readableAborted));
      }, kIsDisturbed: d, isErrored: function(f) {
        var F, U, D, rt, u, B, K, T, $, tt;
        return !(!f || !((F = (U = (D = (rt = (u = (B = f[w]) !== null && B !== void 0 ? B : f.readableErrored) !== null && u !== void 0 ? u : f.writableErrored) !== null && rt !== void 0 ? rt : (K = f._readableState) === null || K === void 0 ? void 0 : K.errorEmitted) !== null && D !== void 0 ? D : (T = f._writableState) === null || T === void 0 ? void 0 : T.errorEmitted) !== null && U !== void 0 ? U : ($ = f._readableState) === null || $ === void 0 ? void 0 : $.errored) !== null && F !== void 0 ? F : !((tt = f._writableState) === null || tt === void 0) && tt.errored));
      }, kIsErrored: w, isReadable: k, kIsReadable: e, kIsClosedPromise: b, kControllerErrorFunction: A, kIsWritable: i, isClosed: function(f) {
        if (!c(f))
          return null;
        if (typeof f.closed == "boolean")
          return f.closed;
        const F = f._writableState, U = f._readableState;
        return typeof (F == null ? void 0 : F.closed) == "boolean" || typeof (U == null ? void 0 : U.closed) == "boolean" ? (F == null ? void 0 : F.closed) || (U == null ? void 0 : U.closed) : typeof f._closed == "boolean" && L(f) ? f._closed : null;
      }, isDuplexNodeStream: function(f) {
        return !(!f || typeof f.pipe != "function" || !f._readableState || typeof f.on != "function" || typeof f.write != "function");
      }, isFinished: function(f, F) {
        return c(f) ? !(!v(f) && ((F == null ? void 0 : F.readable) !== !1 && k(f) || (F == null ? void 0 : F.writable) !== !1 && Q(f))) : null;
      }, isIterable: function(f, F) {
        return f != null && (F === !0 ? typeof f[S] == "function" : F === !1 ? typeof f[m] == "function" : typeof f[S] == "function" || typeof f[m] == "function");
      }, isReadableNodeStream: a, isReadableStream: o, isReadableEnded: function(f) {
        if (!a(f))
          return null;
        if (f.readableEnded === !0)
          return !0;
        const F = f._readableState;
        return !(!F || F.errored) && (typeof (F == null ? void 0 : F.ended) != "boolean" ? null : F.ended);
      }, isReadableFinished: Y, isReadableErrored: function(f) {
        var F, U;
        return c(f) ? f.readableErrored ? f.readableErrored : (F = (U = f._readableState) === null || U === void 0 ? void 0 : U.errored) !== null && F !== void 0 ? F : null : null;
      }, isNodeStream: c, isWebStream: function(f) {
        return o(f) || h(f) || y(f);
      }, isWritable: Q, isWritableNodeStream: l, isWritableStream: h, isWritableEnded: z, isWritableFinished: function(f, F) {
        if (!l(f))
          return null;
        if (f.writableFinished === !0)
          return !0;
        const U = f._writableState;
        return (U == null || !U.errored) && (typeof (U == null ? void 0 : U.finished) != "boolean" ? null : !!(U.finished || F === !1 && U.ended === !0 && U.length === 0));
      }, isWritableErrored: function(f) {
        var F, U;
        return c(f) ? f.writableErrored ? f.writableErrored : (F = (U = f._writableState) === null || U === void 0 ? void 0 : U.errored) !== null && F !== void 0 ? F : null : null;
      }, isServerRequest: function(f) {
        var F;
        return typeof f._consuming == "boolean" && typeof f._dumped == "boolean" && ((F = f.req) === null || F === void 0 ? void 0 : F.upgradeOrConnect) === void 0;
      }, isServerResponse: M, willEmitClose: function(f) {
        if (!c(f))
          return null;
        const F = f._writableState, U = f._readableState, D = F || U;
        return !D && M(f) || !!(D && D.autoDestroy && D.emitClose && D.closed === !1);
      }, isTransformStream: y };
    }, 8584: (G, N, s) => {
      const S = s(5606), { ArrayPrototypeSlice: m, Error: O, FunctionPrototypeSymbolHasInstance: P, ObjectDefineProperty: w, ObjectDefineProperties: e, ObjectSetPrototypeOf: i, StringPrototypeToLowerCase: d, Symbol: b, SymbolHasInstance: A } = s(4134);
      G.exports = T, T.WritableState = B;
      const { EventEmitter: a } = s(7007), l = s(4259).Stream, { Buffer: c } = s(8287), o = s(5896), { addAbortSignal: h } = s(4147), { getHighWaterMark: y, getDefaultHighWaterMark: v } = s(5291), { ERR_INVALID_ARG_TYPE: z, ERR_METHOD_NOT_IMPLEMENTED: Y, ERR_MULTIPLE_CALLBACK: k, ERR_STREAM_CANNOT_PIPE: Q, ERR_STREAM_DESTROYED: L, ERR_STREAM_ALREADY_FINISHED: M, ERR_STREAM_NULL_VALUES: f, ERR_STREAM_WRITE_AFTER_END: F, ERR_UNKNOWN_ENCODING: U } = s(6371).codes, { errorOrDestroy: D } = o;
      function rt() {
      }
      i(T.prototype, l.prototype), i(T, l);
      const u = b("kOnFinished");
      function B(R, V, j) {
        typeof j != "boolean" && (j = V instanceof s(3370)), this.objectMode = !(!R || !R.objectMode), j && (this.objectMode = this.objectMode || !(!R || !R.writableObjectMode)), this.highWaterMark = R ? y(this, R, "writableHighWaterMark", j) : v(!1), this.finalCalled = !1, this.needDrain = !1, this.ending = !1, this.ended = !1, this.finished = !1, this.destroyed = !1;
        const et = !(!R || R.decodeStrings !== !1);
        this.decodeStrings = !et, this.defaultEncoding = R && R.defaultEncoding || "utf8", this.length = 0, this.writing = !1, this.corked = 0, this.sync = !0, this.bufferProcessing = !1, this.onwrite = J.bind(void 0, V), this.writecb = null, this.writelen = 0, this.afterWriteTickInfo = null, K(this), this.pendingcb = 0, this.constructed = !0, this.prefinished = !1, this.errorEmitted = !1, this.emitClose = !R || R.emitClose !== !1, this.autoDestroy = !R || R.autoDestroy !== !1, this.errored = null, this.closed = !1, this.closeEmitted = !1, this[u] = [];
      }
      function K(R) {
        R.buffered = [], R.bufferedIndex = 0, R.allBuffers = !0, R.allNoop = !0;
      }
      function T(R) {
        const V = this instanceof s(3370);
        if (!V && !P(T, this))
          return new T(R);
        this._writableState = new B(R, this, V), R && (typeof R.write == "function" && (this._write = R.write), typeof R.writev == "function" && (this._writev = R.writev), typeof R.destroy == "function" && (this._destroy = R.destroy), typeof R.final == "function" && (this._final = R.final), typeof R.construct == "function" && (this._construct = R.construct), R.signal && h(R.signal, this)), l.call(this, R), o.construct(this, () => {
          const j = this._writableState;
          j.writing || ht(this, j), ct(this, j);
        });
      }
      function $(R, V, j, et) {
        const ot = R._writableState;
        if (typeof j == "function")
          et = j, j = ot.defaultEncoding;
        else {
          if (j) {
            if (j !== "buffer" && !c.isEncoding(j))
              throw new U(j);
          } else
            j = ot.defaultEncoding;
          typeof et != "function" && (et = rt);
        }
        if (V === null)
          throw new f();
        if (!ot.objectMode)
          if (typeof V == "string")
            ot.decodeStrings !== !1 && (V = c.from(V, j), j = "buffer");
          else if (V instanceof c)
            j = "buffer";
          else {
            if (!l._isUint8Array(V))
              throw new z("chunk", ["string", "Buffer", "Uint8Array"], V);
            V = l._uint8ArrayToBuffer(V), j = "buffer";
          }
        let t;
        return ot.ending ? t = new F() : ot.destroyed && (t = new L("write")), t ? (S.nextTick(et, t), D(R, t, !0), t) : (ot.pendingcb++, function(n, r, _, E, x) {
          const q = r.objectMode ? 1 : _.length;
          r.length += q;
          const ut = r.length < r.highWaterMark;
          return ut || (r.needDrain = !0), r.writing || r.corked || r.errored || !r.constructed ? (r.buffered.push({ chunk: _, encoding: E, callback: x }), r.allBuffers && E !== "buffer" && (r.allBuffers = !1), r.allNoop && x !== rt && (r.allNoop = !1)) : (r.writelen = q, r.writecb = x, r.writing = !0, r.sync = !0, n._write(_, E, r.onwrite), r.sync = !1), ut && !r.errored && !r.destroyed;
        }(R, ot, V, j, et));
      }
      function tt(R, V, j, et, ot, t, n) {
        V.writelen = et, V.writecb = n, V.writing = !0, V.sync = !0, V.destroyed ? V.onwrite(new L("write")) : j ? R._writev(ot, V.onwrite) : R._write(ot, t, V.onwrite), V.sync = !1;
      }
      function W(R, V, j, et) {
        --V.pendingcb, et(j), Z(V), D(R, j);
      }
      function J(R, V) {
        const j = R._writableState, et = j.sync, ot = j.writecb;
        typeof ot == "function" ? (j.writing = !1, j.writecb = null, j.length -= j.writelen, j.writelen = 0, V ? (V.stack, j.errored || (j.errored = V), R._readableState && !R._readableState.errored && (R._readableState.errored = V), et ? S.nextTick(W, R, j, V, ot) : W(R, j, V, ot)) : (j.buffered.length > j.bufferedIndex && ht(R, j), et ? j.afterWriteTickInfo !== null && j.afterWriteTickInfo.cb === ot ? j.afterWriteTickInfo.count++ : (j.afterWriteTickInfo = { count: 1, cb: ot, stream: R, state: j }, S.nextTick(C, j.afterWriteTickInfo)) : nt(R, j, 1, ot))) : D(R, new k());
      }
      function C({ stream: R, state: V, count: j, cb: et }) {
        return V.afterWriteTickInfo = null, nt(R, V, j, et);
      }
      function nt(R, V, j, et) {
        for (!V.ending && !R.destroyed && V.length === 0 && V.needDrain && (V.needDrain = !1, R.emit("drain")); j-- > 0; )
          V.pendingcb--, et();
        V.destroyed && Z(V), ct(R, V);
      }
      function Z(R) {
        if (R.writing)
          return;
        for (let ot = R.bufferedIndex; ot < R.buffered.length; ++ot) {
          var V;
          const { chunk: t, callback: n } = R.buffered[ot], r = R.objectMode ? 1 : t.length;
          R.length -= r, n((V = R.errored) !== null && V !== void 0 ? V : new L("write"));
        }
        const j = R[u].splice(0);
        for (let ot = 0; ot < j.length; ot++) {
          var et;
          j[ot]((et = R.errored) !== null && et !== void 0 ? et : new L("end"));
        }
        K(R);
      }
      function ht(R, V) {
        if (V.corked || V.bufferProcessing || V.destroyed || !V.constructed)
          return;
        const { buffered: j, bufferedIndex: et, objectMode: ot } = V, t = j.length - et;
        if (!t)
          return;
        let n = et;
        if (V.bufferProcessing = !0, t > 1 && R._writev) {
          V.pendingcb -= t - 1;
          const r = V.allNoop ? rt : (E) => {
            for (let x = n; x < j.length; ++x)
              j[x].callback(E);
          }, _ = V.allNoop && n === 0 ? j : m(j, n);
          _.allBuffers = V.allBuffers, tt(R, V, !0, V.length, _, "", r), K(V);
        } else {
          do {
            const { chunk: r, encoding: _, callback: E } = j[n];
            j[n++] = null, tt(R, V, !1, ot ? 1 : r.length, r, _, E);
          } while (n < j.length && !V.writing);
          n === j.length ? K(V) : n > 256 ? (j.splice(0, n), V.bufferedIndex = 0) : V.bufferedIndex = n;
        }
        V.bufferProcessing = !1;
      }
      function lt(R) {
        return R.ending && !R.destroyed && R.constructed && R.length === 0 && !R.errored && R.buffered.length === 0 && !R.finished && !R.writing && !R.errorEmitted && !R.closeEmitted;
      }
      function ct(R, V, j) {
        lt(V) && (function(et, ot) {
          ot.prefinished || ot.finalCalled || (typeof et._final != "function" || ot.destroyed ? (ot.prefinished = !0, et.emit("prefinish")) : (ot.finalCalled = !0, function(t, n) {
            let r = !1;
            function _(E) {
              if (r)
                D(t, E ?? k());
              else if (r = !0, n.pendingcb--, E) {
                const x = n[u].splice(0);
                for (let q = 0; q < x.length; q++)
                  x[q](E);
                D(t, E, n.sync);
              } else
                lt(n) && (n.prefinished = !0, t.emit("prefinish"), n.pendingcb++, S.nextTick(I, t, n));
            }
            n.sync = !0, n.pendingcb++;
            try {
              t._final(_);
            } catch (E) {
              _(E);
            }
            n.sync = !1;
          }(et, ot)));
        }(R, V), V.pendingcb === 0 && (j ? (V.pendingcb++, S.nextTick((et, ot) => {
          lt(ot) ? I(et, ot) : ot.pendingcb--;
        }, R, V)) : lt(V) && (V.pendingcb++, I(R, V))));
      }
      function I(R, V) {
        V.pendingcb--, V.finished = !0;
        const j = V[u].splice(0);
        for (let et = 0; et < j.length; et++)
          j[et]();
        if (R.emit("finish"), V.autoDestroy) {
          const et = R._readableState;
          (!et || et.autoDestroy && (et.endEmitted || et.readable === !1)) && R.destroy();
        }
      }
      B.prototype.getBuffer = function() {
        return m(this.buffered, this.bufferedIndex);
      }, w(B.prototype, "bufferedRequestCount", { __proto__: null, get() {
        return this.buffered.length - this.bufferedIndex;
      } }), w(T, A, { __proto__: null, value: function(R) {
        return !!P(this, R) || this === T && R && R._writableState instanceof B;
      } }), T.prototype.pipe = function() {
        D(this, new Q());
      }, T.prototype.write = function(R, V, j) {
        return $(this, R, V, j) === !0;
      }, T.prototype.cork = function() {
        this._writableState.corked++;
      }, T.prototype.uncork = function() {
        const R = this._writableState;
        R.corked && (R.corked--, R.writing || ht(this, R));
      }, T.prototype.setDefaultEncoding = function(R) {
        if (typeof R == "string" && (R = d(R)), !c.isEncoding(R))
          throw new U(R);
        return this._writableState.defaultEncoding = R, this;
      }, T.prototype._write = function(R, V, j) {
        if (!this._writev)
          throw new Y("_write()");
        this._writev([{ chunk: R, encoding: V }], j);
      }, T.prototype._writev = null, T.prototype.end = function(R, V, j) {
        const et = this._writableState;
        let ot;
        if (typeof R == "function" ? (j = R, R = null, V = null) : typeof V == "function" && (j = V, V = null), R != null) {
          const t = $(this, R, V);
          t instanceof O && (ot = t);
        }
        return et.corked && (et.corked = 1, this.uncork()), ot || (et.errored || et.ending ? et.finished ? ot = new M("end") : et.destroyed && (ot = new L("end")) : (et.ending = !0, ct(this, et, !0), et.ended = !0)), typeof j == "function" && (ot || et.finished ? S.nextTick(j, ot) : et[u].push(j)), this;
      }, e(T.prototype, { closed: { __proto__: null, get() {
        return !!this._writableState && this._writableState.closed;
      } }, destroyed: { __proto__: null, get() {
        return !!this._writableState && this._writableState.destroyed;
      }, set(R) {
        this._writableState && (this._writableState.destroyed = R);
      } }, writable: { __proto__: null, get() {
        const R = this._writableState;
        return !(!R || R.writable === !1 || R.destroyed || R.errored || R.ending || R.ended);
      }, set(R) {
        this._writableState && (this._writableState.writable = !!R);
      } }, writableFinished: { __proto__: null, get() {
        return !!this._writableState && this._writableState.finished;
      } }, writableObjectMode: { __proto__: null, get() {
        return !!this._writableState && this._writableState.objectMode;
      } }, writableBuffer: { __proto__: null, get() {
        return this._writableState && this._writableState.getBuffer();
      } }, writableEnded: { __proto__: null, get() {
        return !!this._writableState && this._writableState.ending;
      } }, writableNeedDrain: { __proto__: null, get() {
        const R = this._writableState;
        return !!R && !R.destroyed && !R.ending && R.needDrain;
      } }, writableHighWaterMark: { __proto__: null, get() {
        return this._writableState && this._writableState.highWaterMark;
      } }, writableCorked: { __proto__: null, get() {
        return this._writableState ? this._writableState.corked : 0;
      } }, writableLength: { __proto__: null, get() {
        return this._writableState && this._writableState.length;
      } }, errored: { __proto__: null, enumerable: !1, get() {
        return this._writableState ? this._writableState.errored : null;
      } }, writableAborted: { __proto__: null, enumerable: !1, get: function() {
        return !(this._writableState.writable === !1 || !this._writableState.destroyed && !this._writableState.errored || this._writableState.finished);
      } } });
      const X = o.destroy;
      let H;
      function at() {
        return H === void 0 && (H = {}), H;
      }
      T.prototype.destroy = function(R, V) {
        const j = this._writableState;
        return !j.destroyed && (j.bufferedIndex < j.buffered.length || j[u].length) && S.nextTick(Z, j), X.call(this, R, V), this;
      }, T.prototype._undestroy = o.undestroy, T.prototype._destroy = function(R, V) {
        V(R);
      }, T.prototype[a.captureRejectionSymbol] = function(R) {
        this.destroy(R);
      }, T.fromWeb = function(R, V) {
        return at().newStreamWritableFromWritableStream(R, V);
      }, T.toWeb = function(R) {
        return at().newWritableStreamFromStreamWritable(R);
      };
    }, 277: (G, N, s) => {
      const { ArrayIsArray: S, ArrayPrototypeIncludes: m, ArrayPrototypeJoin: O, ArrayPrototypeMap: P, NumberIsInteger: w, NumberIsNaN: e, NumberMAX_SAFE_INTEGER: i, NumberMIN_SAFE_INTEGER: d, NumberParseInt: b, ObjectPrototypeHasOwnProperty: A, RegExpPrototypeExec: a, String: l, StringPrototypeToUpperCase: c, StringPrototypeTrim: o } = s(4134), { hideStackFrames: h, codes: { ERR_SOCKET_BAD_PORT: y, ERR_INVALID_ARG_TYPE: v, ERR_INVALID_ARG_VALUE: z, ERR_OUT_OF_RANGE: Y, ERR_UNKNOWN_SIGNAL: k } } = s(6371), { normalizeEncoding: Q } = s(7760), { isAsyncFunction: L, isArrayBufferView: M } = s(7760).types, f = {}, F = /^[0-7]+$/, U = h((I, X, H = d, at = i) => {
        if (typeof I != "number")
          throw new v(X, "number", I);
        if (!w(I))
          throw new Y(X, "an integer", I);
        if (I < H || I > at)
          throw new Y(X, `>= ${H} && <= ${at}`, I);
      }), D = h((I, X, H = -2147483648, at = 2147483647) => {
        if (typeof I != "number")
          throw new v(X, "number", I);
        if (!w(I))
          throw new Y(X, "an integer", I);
        if (I < H || I > at)
          throw new Y(X, `>= ${H} && <= ${at}`, I);
      }), rt = h((I, X, H = !1) => {
        if (typeof I != "number")
          throw new v(X, "number", I);
        if (!w(I))
          throw new Y(X, "an integer", I);
        const at = H ? 1 : 0, R = 4294967295;
        if (I < at || I > R)
          throw new Y(X, `>= ${at} && <= ${R}`, I);
      });
      function u(I, X) {
        if (typeof I != "string")
          throw new v(X, "string", I);
      }
      const B = h((I, X, H) => {
        if (!m(H, I)) {
          const at = O(P(H, (R) => typeof R == "string" ? `'${R}'` : l(R)), ", ");
          throw new z(X, I, "must be one of: " + at);
        }
      });
      function K(I, X) {
        if (typeof I != "boolean")
          throw new v(X, "boolean", I);
      }
      function T(I, X, H) {
        return I != null && A(I, X) ? I[X] : H;
      }
      const $ = h((I, X, H = null) => {
        const at = T(H, "allowArray", !1), R = T(H, "allowFunction", !1);
        if (!T(H, "nullable", !1) && I === null || !at && S(I) || typeof I != "object" && (!R || typeof I != "function"))
          throw new v(X, "Object", I);
      }), tt = h((I, X) => {
        if (I != null && typeof I != "object" && typeof I != "function")
          throw new v(X, "a dictionary", I);
      }), W = h((I, X, H = 0) => {
        if (!S(I))
          throw new v(X, "Array", I);
        if (I.length < H)
          throw new z(X, I, `must be longer than ${H}`);
      }), J = h((I, X = "buffer") => {
        if (!M(I))
          throw new v(X, ["Buffer", "TypedArray", "DataView"], I);
      }), C = h((I, X) => {
        if (I !== void 0 && (I === null || typeof I != "object" || !("aborted" in I)))
          throw new v(X, "AbortSignal", I);
      }), nt = h((I, X) => {
        if (typeof I != "function")
          throw new v(X, "Function", I);
      }), Z = h((I, X) => {
        if (typeof I != "function" || L(I))
          throw new v(X, "Function", I);
      }), ht = h((I, X) => {
        if (I !== void 0)
          throw new v(X, "undefined", I);
      }), lt = /^(?:<[^>]*>)(?:\s*;\s*[^;"\s]+(?:=(")?[^;"\s]*\1)?)*$/;
      function ct(I, X) {
        if (I === void 0 || !a(lt, I))
          throw new z(X, I, 'must be an array or string of format "</styles.css>; rel=preload; as=style"');
      }
      G.exports = { isInt32: function(I) {
        return I === (0 | I);
      }, isUint32: function(I) {
        return I === I >>> 0;
      }, parseFileMode: function(I, X, H) {
        if (I === void 0 && (I = H), typeof I == "string") {
          if (a(F, I) === null)
            throw new z(X, I, "must be a 32-bit unsigned integer or an octal string");
          I = b(I, 8);
        }
        return rt(I, X), I;
      }, validateArray: W, validateStringArray: function(I, X) {
        W(I, X);
        for (let H = 0; H < I.length; H++)
          u(I[H], `${X}[${H}]`);
      }, validateBooleanArray: function(I, X) {
        W(I, X);
        for (let H = 0; H < I.length; H++)
          K(I[H], `${X}[${H}]`);
      }, validateAbortSignalArray: function(I, X) {
        W(I, X);
        for (let H = 0; H < I.length; H++) {
          const at = I[H], R = `${X}[${H}]`;
          if (at == null)
            throw new v(R, "AbortSignal", at);
          C(at, R);
        }
      }, validateBoolean: K, validateBuffer: J, validateDictionary: tt, validateEncoding: function(I, X) {
        const H = Q(X), at = I.length;
        if (H === "hex" && at % 2 != 0)
          throw new z("encoding", X, `is invalid for data of length ${at}`);
      }, validateFunction: nt, validateInt32: D, validateInteger: U, validateNumber: function(I, X, H = void 0, at) {
        if (typeof I != "number")
          throw new v(X, "number", I);
        if (H != null && I < H || at != null && I > at || (H != null || at != null) && e(I))
          throw new Y(X, `${H != null ? `>= ${H}` : ""}${H != null && at != null ? " && " : ""}${at != null ? `<= ${at}` : ""}`, I);
      }, validateObject: $, validateOneOf: B, validatePlainFunction: Z, validatePort: function(I, X = "Port", H = !0) {
        if (typeof I != "number" && typeof I != "string" || typeof I == "string" && o(I).length === 0 || +I != +I >>> 0 || I > 65535 || I === 0 && !H)
          throw new y(X, I, H);
        return 0 | I;
      }, validateSignalName: function(I, X = "signal") {
        if (u(I, X), f[I] === void 0)
          throw f[c(I)] !== void 0 ? new k(I + " (signals must use all capital letters)") : new k(I);
      }, validateString: u, validateUint32: rt, validateUndefined: ht, validateUnion: function(I, X, H) {
        if (!m(H, I))
          throw new v(X, `('${O(H, "|")}')`, I);
      }, validateAbortSignal: C, validateLinkHeaderValue: function(I) {
        if (typeof I == "string")
          return ct(I, "hints"), I;
        if (S(I)) {
          const X = I.length;
          let H = "";
          if (X === 0)
            return H;
          for (let at = 0; at < X; at++) {
            const R = I[at];
            ct(R, "hints"), H += R, at !== X - 1 && (H += ", ");
          }
          return H;
        }
        throw new z("hints", I, 'must be an array or string of format "</styles.css>; rel=preload; as=style"');
      } };
    }, 6371: (G, N, s) => {
      const { format: S, inspect: m, AggregateError: O } = s(7760), P = globalThis.AggregateError || O, w = Symbol("kIsNodeError"), e = ["string", "function", "number", "object", "Function", "Object", "boolean", "bigint", "symbol"], i = /^([A-Z][a-z0-9]*)+$/, d = {};
      function b(o, h) {
        if (!o)
          throw new d.ERR_INTERNAL_ASSERTION(h);
      }
      function A(o) {
        let h = "", y = o.length;
        const v = o[0] === "-" ? 1 : 0;
        for (; y >= v + 4; y -= 3)
          h = `_${o.slice(y - 3, y)}${h}`;
        return `${o.slice(0, y)}${h}`;
      }
      function a(o, h, y) {
        y || (y = Error);
        class v extends y {
          constructor(...Y) {
            super(function(k, Q, L) {
              if (typeof Q == "function")
                return b(Q.length <= L.length, `Code: ${k}; The provided arguments length (${L.length}) does not match the required ones (${Q.length}).`), Q(...L);
              const M = (Q.match(/%[dfijoOs]/g) || []).length;
              return b(M === L.length, `Code: ${k}; The provided arguments length (${L.length}) does not match the required ones (${M}).`), L.length === 0 ? Q : S(Q, ...L);
            }(o, h, Y));
          }
          toString() {
            return `${this.name} [${o}]: ${this.message}`;
          }
        }
        Object.defineProperties(v.prototype, { name: { value: y.name, writable: !0, enumerable: !1, configurable: !0 }, toString: { value() {
          return `${this.name} [${o}]: ${this.message}`;
        }, writable: !0, enumerable: !1, configurable: !0 } }), v.prototype.code = o, v.prototype[w] = !0, d[o] = v;
      }
      function l(o) {
        const h = "__node_internal_" + o.name;
        return Object.defineProperty(o, "name", { value: h }), o;
      }
      class c extends Error {
        constructor(h = "The operation was aborted", y = void 0) {
          if (y !== void 0 && typeof y != "object")
            throw new d.ERR_INVALID_ARG_TYPE("options", "Object", y);
          super(h, y), this.code = "ABORT_ERR", this.name = "AbortError";
        }
      }
      a("ERR_ASSERTION", "%s", Error), a("ERR_INVALID_ARG_TYPE", (o, h, y) => {
        b(typeof o == "string", "'name' must be a string"), Array.isArray(h) || (h = [h]);
        let v = "The ";
        o.endsWith(" argument") ? v += `${o} ` : v += `"${o}" ${o.includes(".") ? "property" : "argument"} `, v += "must be ";
        const z = [], Y = [], k = [];
        for (const L of h)
          b(typeof L == "string", "All expected entries have to be of type string"), e.includes(L) ? z.push(L.toLowerCase()) : i.test(L) ? Y.push(L) : (b(L !== "object", 'The value "object" should be written as "Object"'), k.push(L));
        if (Y.length > 0) {
          const L = z.indexOf("object");
          L !== -1 && (z.splice(z, L, 1), Y.push("Object"));
        }
        if (z.length > 0) {
          switch (z.length) {
            case 1:
              v += `of type ${z[0]}`;
              break;
            case 2:
              v += `one of type ${z[0]} or ${z[1]}`;
              break;
            default: {
              const L = z.pop();
              v += `one of type ${z.join(", ")}, or ${L}`;
            }
          }
          (Y.length > 0 || k.length > 0) && (v += " or ");
        }
        if (Y.length > 0) {
          switch (Y.length) {
            case 1:
              v += `an instance of ${Y[0]}`;
              break;
            case 2:
              v += `an instance of ${Y[0]} or ${Y[1]}`;
              break;
            default: {
              const L = Y.pop();
              v += `an instance of ${Y.join(", ")}, or ${L}`;
            }
          }
          k.length > 0 && (v += " or ");
        }
        switch (k.length) {
          case 0:
            break;
          case 1:
            k[0].toLowerCase() !== k[0] && (v += "an "), v += `${k[0]}`;
            break;
          case 2:
            v += `one of ${k[0]} or ${k[1]}`;
            break;
          default: {
            const L = k.pop();
            v += `one of ${k.join(", ")}, or ${L}`;
          }
        }
        if (y == null)
          v += `. Received ${y}`;
        else if (typeof y == "function" && y.name)
          v += `. Received function ${y.name}`;
        else if (typeof y == "object") {
          var Q;
          (Q = y.constructor) !== null && Q !== void 0 && Q.name ? v += `. Received an instance of ${y.constructor.name}` : v += `. Received ${m(y, { depth: -1 })}`;
        } else {
          let L = m(y, { colors: !1 });
          L.length > 25 && (L = `${L.slice(0, 25)}...`), v += `. Received type ${typeof y} (${L})`;
        }
        return v;
      }, TypeError), a("ERR_INVALID_ARG_VALUE", (o, h, y = "is invalid") => {
        let v = m(h);
        return v.length > 128 && (v = v.slice(0, 128) + "..."), `The ${o.includes(".") ? "property" : "argument"} '${o}' ${y}. Received ${v}`;
      }, TypeError), a("ERR_INVALID_RETURN_VALUE", (o, h, y) => {
        var v;
        return `Expected ${o} to be returned from the "${h}" function but got ${y != null && (v = y.constructor) !== null && v !== void 0 && v.name ? `instance of ${y.constructor.name}` : "type " + typeof y}.`;
      }, TypeError), a("ERR_MISSING_ARGS", (...o) => {
        let h;
        b(o.length > 0, "At least one arg needs to be specified");
        const y = o.length;
        switch (o = (Array.isArray(o) ? o : [o]).map((v) => `"${v}"`).join(" or "), y) {
          case 1:
            h += `The ${o[0]} argument`;
            break;
          case 2:
            h += `The ${o[0]} and ${o[1]} arguments`;
            break;
          default: {
            const v = o.pop();
            h += `The ${o.join(", ")}, and ${v} arguments`;
          }
        }
        return `${h} must be specified`;
      }, TypeError), a("ERR_OUT_OF_RANGE", (o, h, y) => {
        let v;
        return b(h, 'Missing "range" argument'), Number.isInteger(y) && Math.abs(y) > 4294967296 ? v = A(String(y)) : typeof y == "bigint" ? (v = String(y), (y > 2n ** 32n || y < -(2n ** 32n)) && (v = A(v)), v += "n") : v = m(y), `The value of "${o}" is out of range. It must be ${h}. Received ${v}`;
      }, RangeError), a("ERR_MULTIPLE_CALLBACK", "Callback called multiple times", Error), a("ERR_METHOD_NOT_IMPLEMENTED", "The %s method is not implemented", Error), a("ERR_STREAM_ALREADY_FINISHED", "Cannot call %s after a stream was finished", Error), a("ERR_STREAM_CANNOT_PIPE", "Cannot pipe, not readable", Error), a("ERR_STREAM_DESTROYED", "Cannot call %s after a stream was destroyed", Error), a("ERR_STREAM_NULL_VALUES", "May not write null values to stream", TypeError), a("ERR_STREAM_PREMATURE_CLOSE", "Premature close", Error), a("ERR_STREAM_PUSH_AFTER_EOF", "stream.push() after EOF", Error), a("ERR_STREAM_UNSHIFT_AFTER_END_EVENT", "stream.unshift() after end event", Error), a("ERR_STREAM_WRITE_AFTER_END", "write after end", Error), a("ERR_UNKNOWN_ENCODING", "Unknown encoding: %s", TypeError), G.exports = { AbortError: c, aggregateTwoErrors: l(function(o, h) {
        if (o && h && o !== h) {
          if (Array.isArray(h.errors))
            return h.errors.push(o), h;
          const y = new P([h, o], h.message);
          return y.code = h.code, y;
        }
        return o || h;
      }), hideStackFrames: l, codes: d };
    }, 4134: (G) => {
      G.exports = { ArrayIsArray: (N) => Array.isArray(N), ArrayPrototypeIncludes: (N, s) => N.includes(s), ArrayPrototypeIndexOf: (N, s) => N.indexOf(s), ArrayPrototypeJoin: (N, s) => N.join(s), ArrayPrototypeMap: (N, s) => N.map(s), ArrayPrototypePop: (N, s) => N.pop(s), ArrayPrototypePush: (N, s) => N.push(s), ArrayPrototypeSlice: (N, s, S) => N.slice(s, S), Error, FunctionPrototypeCall: (N, s, ...S) => N.call(s, ...S), FunctionPrototypeSymbolHasInstance: (N, s) => Function.prototype[Symbol.hasInstance].call(N, s), MathFloor: Math.floor, Number, NumberIsInteger: Number.isInteger, NumberIsNaN: Number.isNaN, NumberMAX_SAFE_INTEGER: Number.MAX_SAFE_INTEGER, NumberMIN_SAFE_INTEGER: Number.MIN_SAFE_INTEGER, NumberParseInt: Number.parseInt, ObjectDefineProperties: (N, s) => Object.defineProperties(N, s), ObjectDefineProperty: (N, s, S) => Object.defineProperty(N, s, S), ObjectGetOwnPropertyDescriptor: (N, s) => Object.getOwnPropertyDescriptor(N, s), ObjectKeys: (N) => Object.keys(N), ObjectSetPrototypeOf: (N, s) => Object.setPrototypeOf(N, s), Promise, PromisePrototypeCatch: (N, s) => N.catch(s), PromisePrototypeThen: (N, s, S) => N.then(s, S), PromiseReject: (N) => Promise.reject(N), PromiseResolve: (N) => Promise.resolve(N), ReflectApply: Reflect.apply, RegExpPrototypeTest: (N, s) => N.test(s), SafeSet: Set, String, StringPrototypeSlice: (N, s, S) => N.slice(s, S), StringPrototypeToLowerCase: (N) => N.toLowerCase(), StringPrototypeToUpperCase: (N) => N.toUpperCase(), StringPrototypeTrim: (N) => N.trim(), Symbol, SymbolFor: Symbol.for, SymbolAsyncIterator: Symbol.asyncIterator, SymbolHasInstance: Symbol.hasInstance, SymbolIterator: Symbol.iterator, SymbolDispose: Symbol.dispose || Symbol("Symbol.dispose"), SymbolAsyncDispose: Symbol.asyncDispose || Symbol("Symbol.asyncDispose"), TypedArrayPrototypeSet: (N, s, S) => N.set(s, S), Boolean, Uint8Array };
    }, 7760: (G, N, s) => {
      const S = s(8287), { kResistStopPropagation: m, SymbolDispose: O } = s(4134), P = globalThis.AbortSignal || s(5568).AbortSignal, w = globalThis.AbortController || s(5568).AbortController, e = Object.getPrototypeOf(async function() {
      }).constructor, i = globalThis.Blob || S.Blob, d = i !== void 0 ? function(a) {
        return a instanceof i;
      } : function(a) {
        return !1;
      }, b = (a, l) => {
        if (a !== void 0 && (a === null || typeof a != "object" || !("aborted" in a)))
          throw new ERR_INVALID_ARG_TYPE(l, "AbortSignal", a);
      };
      class A extends Error {
        constructor(l) {
          if (!Array.isArray(l))
            throw new TypeError("Expected input to be an Array, got " + typeof l);
          let c = "";
          for (let o = 0; o < l.length; o++)
            c += `    ${l[o].stack}
`;
          super(c), this.name = "AggregateError", this.errors = l;
        }
      }
      G.exports = { AggregateError: A, kEmptyObject: Object.freeze({}), once(a) {
        let l = !1;
        return function(...c) {
          l || (l = !0, a.apply(this, c));
        };
      }, createDeferredPromise: function() {
        let a, l;
        return { promise: new Promise((c, o) => {
          a = c, l = o;
        }), resolve: a, reject: l };
      }, promisify: (a) => new Promise((l, c) => {
        a((o, ...h) => o ? c(o) : l(...h));
      }), debuglog: () => function() {
      }, format: (a, ...l) => a.replace(/%([sdifj])/g, function(...[c, o]) {
        const h = l.shift();
        return o === "f" ? h.toFixed(6) : o === "j" ? JSON.stringify(h) : o === "s" && typeof h == "object" ? `${h.constructor !== Object ? h.constructor.name : ""} {}`.trim() : h.toString();
      }), inspect(a) {
        switch (typeof a) {
          case "string":
            if (a.includes("'")) {
              if (!a.includes('"'))
                return `"${a}"`;
              if (!a.includes("`") && !a.includes("${"))
                return `\`${a}\``;
            }
            return `'${a}'`;
          case "number":
            return isNaN(a) ? "NaN" : Object.is(a, -0) ? String(a) : a;
          case "bigint":
            return `${String(a)}n`;
          case "boolean":
          case "undefined":
            return String(a);
          case "object":
            return "{}";
        }
      }, types: { isAsyncFunction: (a) => a instanceof e, isArrayBufferView: (a) => ArrayBuffer.isView(a) }, isBlob: d, deprecate: (a, l) => a, addAbortListener: s(7007).addAbortListener || function(a, l) {
        if (a === void 0)
          throw new ERR_INVALID_ARG_TYPE("signal", "AbortSignal", a);
        let c;
        return b(a, "signal"), ((o) => {
          if (typeof o != "function")
            throw new ERR_INVALID_ARG_TYPE("listener", "Function", o);
        })(l), a.aborted ? queueMicrotask(() => l()) : (a.addEventListener("abort", l, { __proto__: null, once: !0, [m]: !0 }), c = () => {
          a.removeEventListener("abort", l);
        }), { __proto__: null, [O]() {
          var o;
          (o = c) === null || o === void 0 || o();
        } };
      }, AbortSignalAny: P.any || function(a) {
        if (a.length === 1)
          return a[0];
        const l = new w(), c = () => l.abort();
        return a.forEach((o) => {
          b(o, "signals"), o.addEventListener("abort", c, { once: !0 });
        }), l.signal.addEventListener("abort", () => {
          a.forEach((o) => o.removeEventListener("abort", c));
        }, { once: !0 }), l.signal;
      } }, G.exports.promisify.custom = Symbol.for("nodejs.util.promisify.custom");
    }, 5506: (G, N, s) => {
      const { Buffer: S } = s(8287), { ObjectDefineProperty: m, ObjectKeys: O, ReflectApply: P } = s(4134), { promisify: { custom: w } } = s(7760), { streamReturningOperators: e, promiseReturningOperators: i } = s(823), { codes: { ERR_ILLEGAL_CONSTRUCTOR: d } } = s(6371), b = s(7830), { setDefaultHighWaterMark: A, getDefaultHighWaterMark: a } = s(5291), { pipeline: l } = s(7758), { destroyer: c } = s(5896), o = s(6238), h = s(3095), y = s(6115), v = G.exports = s(4259).Stream;
      v.isDestroyed = y.isDestroyed, v.isDisturbed = y.isDisturbed, v.isErrored = y.isErrored, v.isReadable = y.isReadable, v.isWritable = y.isWritable, v.Readable = s(7576);
      for (const Y of O(e)) {
        let Q = function(...L) {
          if (new.target)
            throw d();
          return v.Readable.from(P(k, this, L));
        };
        const k = e[Y];
        m(Q, "name", { __proto__: null, value: k.name }), m(Q, "length", { __proto__: null, value: k.length }), m(v.Readable.prototype, Y, { __proto__: null, value: Q, enumerable: !1, configurable: !0, writable: !0 });
      }
      for (const Y of O(i)) {
        let Q = function(...L) {
          if (new.target)
            throw d();
          return P(k, this, L);
        };
        const k = i[Y];
        m(Q, "name", { __proto__: null, value: k.name }), m(Q, "length", { __proto__: null, value: k.length }), m(v.Readable.prototype, Y, { __proto__: null, value: Q, enumerable: !1, configurable: !0, writable: !0 });
      }
      v.Writable = s(8584), v.Duplex = s(3370), v.Transform = s(7382), v.PassThrough = s(6524), v.pipeline = l;
      const { addAbortSignal: z } = s(4147);
      v.addAbortSignal = z, v.finished = o, v.destroy = c, v.compose = b, v.setDefaultHighWaterMark = A, v.getDefaultHighWaterMark = a, m(v, "promises", { __proto__: null, configurable: !0, enumerable: !0, get: () => h }), m(l, w, { __proto__: null, enumerable: !0, get: () => h.pipeline }), m(o, w, { __proto__: null, enumerable: !0, get: () => h.finished }), v.Stream = v, v._isUint8Array = function(Y) {
        return Y instanceof Uint8Array;
      }, v._uint8ArrayToBuffer = function(Y) {
        return S.from(Y.buffer, Y.byteOffset, Y.byteLength);
      };
    }, 3095: (G, N, s) => {
      const { ArrayPrototypePop: S, Promise: m } = s(4134), { isIterable: O, isNodeStream: P, isWebStream: w } = s(6115), { pipelineImpl: e } = s(7758), { finished: i } = s(6238);
      s(5506), G.exports = { finished: i, pipeline: function(...d) {
        return new m((b, A) => {
          let a, l;
          const c = d[d.length - 1];
          if (c && typeof c == "object" && !P(c) && !O(c) && !w(c)) {
            const o = S(d);
            a = o.signal, l = o.end;
          }
          e(d, (o, h) => {
            o ? A(o) : b(h);
          }, { signal: a, end: l });
        });
      } };
    }, 2861: (G, N, s) => {
      var S = s(8287), m = S.Buffer;
      function O(w, e) {
        for (var i in w)
          e[i] = w[i];
      }
      function P(w, e, i) {
        return m(w, e, i);
      }
      m.from && m.alloc && m.allocUnsafe && m.allocUnsafeSlow ? G.exports = S : (O(S, N), N.Buffer = P), P.prototype = Object.create(m.prototype), O(m, P), P.from = function(w, e, i) {
        if (typeof w == "number")
          throw new TypeError("Argument must not be a number");
        return m(w, e, i);
      }, P.alloc = function(w, e, i) {
        if (typeof w != "number")
          throw new TypeError("Argument must be a number");
        var d = m(w);
        return e !== void 0 ? typeof i == "string" ? d.fill(e, i) : d.fill(e) : d.fill(0), d;
      }, P.allocUnsafe = function(w) {
        if (typeof w != "number")
          throw new TypeError("Argument must be a number");
        return m(w);
      }, P.allocUnsafeSlow = function(w) {
        if (typeof w != "number")
          throw new TypeError("Argument must be a number");
        return S.SlowBuffer(w);
      };
    }, 8310: (G, N, s) => {
      G.exports = m;
      var S = s(7007).EventEmitter;
      function m() {
        S.call(this);
      }
      s(6698)(m, S), m.Readable = s(5412), m.Writable = s(6708), m.Duplex = s(5382), m.Transform = s(4610), m.PassThrough = s(3600), m.finished = s(6238), m.pipeline = s(7758), m.Stream = m, m.prototype.pipe = function(O, P) {
        var w = this;
        function e(c) {
          O.writable && O.write(c) === !1 && w.pause && w.pause();
        }
        function i() {
          w.readable && w.resume && w.resume();
        }
        w.on("data", e), O.on("drain", i), O._isStdio || P && P.end === !1 || (w.on("end", b), w.on("close", A));
        var d = !1;
        function b() {
          d || (d = !0, O.end());
        }
        function A() {
          d || (d = !0, typeof O.destroy == "function" && O.destroy());
        }
        function a(c) {
          if (l(), S.listenerCount(this, "error") === 0)
            throw c;
        }
        function l() {
          w.removeListener("data", e), O.removeListener("drain", i), w.removeListener("end", b), w.removeListener("close", A), w.removeListener("error", a), O.removeListener("error", a), w.removeListener("end", l), w.removeListener("close", l), O.removeListener("close", l);
        }
        return w.on("error", a), O.on("error", a), w.on("end", l), w.on("close", l), O.on("close", l), O.emit("pipe", w), O;
      };
    }, 3141: (G, N, s) => {
      var S = s(2861).Buffer, m = S.isEncoding || function(l) {
        switch ((l = "" + l) && l.toLowerCase()) {
          case "hex":
          case "utf8":
          case "utf-8":
          case "ascii":
          case "binary":
          case "base64":
          case "ucs2":
          case "ucs-2":
          case "utf16le":
          case "utf-16le":
          case "raw":
            return !0;
          default:
            return !1;
        }
      };
      function O(l) {
        var c;
        switch (this.encoding = function(o) {
          var h = function(y) {
            if (!y)
              return "utf8";
            for (var v; ; )
              switch (y) {
                case "utf8":
                case "utf-8":
                  return "utf8";
                case "ucs2":
                case "ucs-2":
                case "utf16le":
                case "utf-16le":
                  return "utf16le";
                case "latin1":
                case "binary":
                  return "latin1";
                case "base64":
                case "ascii":
                case "hex":
                  return y;
                default:
                  if (v)
                    return;
                  y = ("" + y).toLowerCase(), v = !0;
              }
          }(o);
          if (typeof h != "string" && (S.isEncoding === m || !m(o)))
            throw new Error("Unknown encoding: " + o);
          return h || o;
        }(l), this.encoding) {
          case "utf16le":
            this.text = e, this.end = i, c = 4;
            break;
          case "utf8":
            this.fillLast = w, c = 4;
            break;
          case "base64":
            this.text = d, this.end = b, c = 3;
            break;
          default:
            return this.write = A, void (this.end = a);
        }
        this.lastNeed = 0, this.lastTotal = 0, this.lastChar = S.allocUnsafe(c);
      }
      function P(l) {
        return l <= 127 ? 0 : l >> 5 == 6 ? 2 : l >> 4 == 14 ? 3 : l >> 3 == 30 ? 4 : l >> 6 == 2 ? -1 : -2;
      }
      function w(l) {
        var c = this.lastTotal - this.lastNeed, o = function(h, y) {
          if ((192 & y[0]) != 128)
            return h.lastNeed = 0, "�";
          if (h.lastNeed > 1 && y.length > 1) {
            if ((192 & y[1]) != 128)
              return h.lastNeed = 1, "�";
            if (h.lastNeed > 2 && y.length > 2 && (192 & y[2]) != 128)
              return h.lastNeed = 2, "�";
          }
        }(this, l);
        return o !== void 0 ? o : this.lastNeed <= l.length ? (l.copy(this.lastChar, c, 0, this.lastNeed), this.lastChar.toString(this.encoding, 0, this.lastTotal)) : (l.copy(this.lastChar, c, 0, l.length), void (this.lastNeed -= l.length));
      }
      function e(l, c) {
        if ((l.length - c) % 2 == 0) {
          var o = l.toString("utf16le", c);
          if (o) {
            var h = o.charCodeAt(o.length - 1);
            if (h >= 55296 && h <= 56319)
              return this.lastNeed = 2, this.lastTotal = 4, this.lastChar[0] = l[l.length - 2], this.lastChar[1] = l[l.length - 1], o.slice(0, -1);
          }
          return o;
        }
        return this.lastNeed = 1, this.lastTotal = 2, this.lastChar[0] = l[l.length - 1], l.toString("utf16le", c, l.length - 1);
      }
      function i(l) {
        var c = l && l.length ? this.write(l) : "";
        if (this.lastNeed) {
          var o = this.lastTotal - this.lastNeed;
          return c + this.lastChar.toString("utf16le", 0, o);
        }
        return c;
      }
      function d(l, c) {
        var o = (l.length - c) % 3;
        return o === 0 ? l.toString("base64", c) : (this.lastNeed = 3 - o, this.lastTotal = 3, o === 1 ? this.lastChar[0] = l[l.length - 1] : (this.lastChar[0] = l[l.length - 2], this.lastChar[1] = l[l.length - 1]), l.toString("base64", c, l.length - o));
      }
      function b(l) {
        var c = l && l.length ? this.write(l) : "";
        return this.lastNeed ? c + this.lastChar.toString("base64", 0, 3 - this.lastNeed) : c;
      }
      function A(l) {
        return l.toString(this.encoding);
      }
      function a(l) {
        return l && l.length ? this.write(l) : "";
      }
      N.StringDecoder = O, O.prototype.write = function(l) {
        if (l.length === 0)
          return "";
        var c, o;
        if (this.lastNeed) {
          if ((c = this.fillLast(l)) === void 0)
            return "";
          o = this.lastNeed, this.lastNeed = 0;
        } else
          o = 0;
        return o < l.length ? c ? c + this.text(l, o) : this.text(l, o) : c || "";
      }, O.prototype.end = function(l) {
        var c = l && l.length ? this.write(l) : "";
        return this.lastNeed ? c + "�" : c;
      }, O.prototype.text = function(l, c) {
        var o = function(y, v, z) {
          var Y = v.length - 1;
          if (Y < z)
            return 0;
          var k = P(v[Y]);
          return k >= 0 ? (k > 0 && (y.lastNeed = k - 1), k) : --Y < z || k === -2 ? 0 : (k = P(v[Y])) >= 0 ? (k > 0 && (y.lastNeed = k - 2), k) : --Y < z || k === -2 ? 0 : (k = P(v[Y])) >= 0 ? (k > 0 && (k === 2 ? k = 0 : y.lastNeed = k - 3), k) : 0;
        }(this, l, c);
        if (!this.lastNeed)
          return l.toString("utf8", c);
        this.lastTotal = o;
        var h = l.length - (o - this.lastNeed);
        return l.copy(this.lastChar, 0, h), l.toString("utf8", c, h);
      }, O.prototype.fillLast = function(l) {
        if (this.lastNeed <= l.length)
          return l.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, this.lastNeed), this.lastChar.toString(this.encoding, 0, this.lastTotal);
        l.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, l.length), this.lastNeed -= l.length;
      };
    } }, At = {};
    function wt(G) {
      var N = At[G];
      if (N !== void 0)
        return N.exports;
      var s = At[G] = { exports: {} };
      return Bt[G](s, s.exports, wt), s.exports;
    }
    wt.n = (G) => {
      var N = G && G.__esModule ? () => G.default : () => G;
      return wt.d(N, { a: N }), N;
    }, wt.d = (G, N) => {
      for (var s in N)
        wt.o(N, s) && !wt.o(G, s) && Object.defineProperty(G, s, { enumerable: !0, get: N[s] });
    }, wt.o = (G, N) => Object.prototype.hasOwnProperty.call(G, N), wt.r = (G) => {
      typeof Symbol < "u" && Symbol.toStringTag && Object.defineProperty(G, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(G, "__esModule", { value: !0 });
    };
    var Rt = {};
    return (() => {
      wt.r(Rt);
      var G = wt(9881), N = {};
      for (const s in G)
        s !== "default" && (N[s] = () => G[s]);
      wt.d(Rt, N);
    })(), Rt;
  })());
})(kt);
var Pt = kt.exports;
const Ct = /* @__PURE__ */ jt(Pt), Wt = /* @__PURE__ */ Ft({
  __proto__: null,
  default: Ct
}, [Pt]);
export {
  Wt as c
};
