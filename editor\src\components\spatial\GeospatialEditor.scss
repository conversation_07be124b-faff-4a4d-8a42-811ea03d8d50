/**
 * 地理数据编辑器组件样式
 */
.geospatial-editor {
  .toolbar {
    margin-bottom: 16px;
    padding: 12px;
    background: #fafafa;
    border-radius: 6px;
    border: 1px solid #f0f0f0;
  }

  .editor-content {
    display: flex;
    gap: 16px;
    height: 500px;

    .map-panel {
      flex: 2;
      min-width: 0;
    }

    .features-panel {
      flex: 1;
      min-width: 300px;
      
      .ant-list {
        height: calc(100% - 40px);
        overflow-y: auto;
        
        .ant-list-item {
          cursor: pointer;
          transition: all 0.2s ease;
          border-radius: 4px;
          margin-bottom: 4px;
          padding: 8px 12px;
          
          &:hover {
            background: #f5f5f5;
          }
          
          &.selected {
            background: #e6f7ff;
            border-color: #1890ff;
          }
          
          .feature-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            
            &.point {
              background: #fff2f0;
              color: #ff4d4f;
            }
            
            &.linestring {
              background: #f0f5ff;
              color: #1890ff;
            }
            
            &.polygon {
              background: #f6ffed;
              color: #52c41a;
            }
          }
          
          .ant-list-item-meta {
            .ant-list-item-meta-title {
              margin-bottom: 4px;
              font-size: 14px;
              font-weight: 500;
            }
            
            .ant-list-item-meta-description {
              font-size: 12px;
              color: #666;
              
              div {
                margin: 2px 0;
              }
            }
          }
          
          .ant-list-item-action {
            margin-left: 8px;
            
            .ant-btn {
              border: none;
              box-shadow: none;
              
              &:hover {
                background: rgba(0, 0, 0, 0.04);
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .geospatial-editor {
    .editor-content {
      flex-direction: column;
      height: auto;
      
      .map-panel {
        flex: none;
        margin-bottom: 16px;
      }
      
      .features-panel {
        flex: none;
        min-width: auto;
        
        .ant-list {
          height: 300px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .geospatial-editor {
    .toolbar {
      .ant-space {
        flex-wrap: wrap;
        
        .ant-btn-group {
          margin-bottom: 8px;
        }
      }
    }
    
    .editor-content {
      .features-panel {
        .ant-list {
          height: 250px;
          
          .ant-list-item {
            .ant-list-item-action {
              flex-direction: column;
              gap: 4px;
              
              .ant-btn {
                min-width: 32px;
                padding: 4px 8px;
              }
            }
          }
        }
      }
    }
  }
}

// 暗色主题支持
.dark-theme {
  .geospatial-editor {
    .toolbar {
      background: #262626;
      border-color: #434343;
    }
    
    .editor-content {
      .features-panel {
        .ant-list {
          .ant-list-item {
            &:hover {
              background: #262626;
            }
            
            &.selected {
              background: #111b26;
              border-color: #177ddc;
            }
            
            .feature-icon {
              &.point {
                background: #2a1215;
                color: #ff7875;
              }
              
              &.linestring {
                background: #111b26;
                color: #40a9ff;
              }
              
              &.polygon {
                background: #162312;
                color: #73d13d;
              }
            }
            
            .ant-list-item-meta-description {
              color: #999;
            }
          }
        }
      }
    }
  }
}

// 高对比度主题支持
.high-contrast-theme {
  .geospatial-editor {
    .toolbar {
      background: #fff;
      border: 2px solid #000;
    }
    
    .editor-content {
      .features-panel {
        .ant-list {
          .ant-list-item {
            border: 1px solid #000;
            
            &:hover {
              background: #f0f0f0;
            }
            
            &.selected {
              background: #000;
              color: #fff;
            }
            
            .feature-icon {
              border: 1px solid #000;
              
              &.point {
                background: #fff;
                color: #000;
              }
              
              &.linestring {
                background: #fff;
                color: #000;
              }
              
              &.polygon {
                background: #fff;
                color: #000;
              }
            }
          }
        }
      }
    }
  }
}

// 动画效果
.geospatial-editor {
  .toolbar {
    .ant-btn-group .ant-btn {
      transition: all 0.2s ease;
      
      &.ant-btn-primary {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
      }
    }
  }
  
  .editor-content {
    .features-panel {
      .ant-list {
        .ant-list-item {
          .feature-icon {
            transition: all 0.2s ease;
            
            &:hover {
              transform: scale(1.1);
            }
          }
        }
      }
    }
  }
}

// 加载状态
.geospatial-editor {
  &.loading {
    .editor-content {
      opacity: 0.6;
      pointer-events: none;
      
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 32px;
        height: 32px;
        margin: -16px 0 0 -16px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #1890ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 打印样式
@media print {
  .geospatial-editor {
    .toolbar {
      display: none;
    }

    .editor-content {
      .features-panel {
        display: none;
      }

      .map-panel {
        width: 100%;
        height: 80vh;
      }
    }
  }
}
