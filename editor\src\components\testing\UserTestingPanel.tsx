/**
 * 用户测试面板组件
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Switch,
  Space,
  Typography,
  Tabs,
  List,
  Tag,
  Progress,
  Empty,
  Badge,
  message,
  Modal,
  Drawer
} from 'antd';
import {
  ExperimentOutlined,
  CheckCircleOutlined,
  PlayCircleOutlined,
  FileTextOutlined,
  BulbOutlined,
  VideoCameraOutlined,
  StopOutlined,
  DownloadOutlined,
  ReloadOutlined,
  SettingOutlined,
  DeleteOutlined,
  EyeOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectTestingEnabled,
  selectCurrentSession,
  selectTasks,
  selectCurrentTaskId,
  selectSessionsHistory,
  selectReports,
  setTestingEnabled
} from '../../store/testing/userTestingSlice';
import { userTestingService, TestTask } from '../../services/UserTestingService';
import FeedbackForm from './FeedbackForm';

const { Text, Paragraph } = Typography;

/**
 * 用户测试面板属性
 */
interface UserTestingPanelProps {
  onClose?: () => void;
}

/**
 * 用户测试面板组件
 */
const UserTestingPanel: React.FC<UserTestingPanelProps> = ({ onClose }) => {
  const dispatch = useDispatch();

  // 从Redux获取状态
  const testingEnabled = useSelector(selectTestingEnabled);
  const currentSession = useSelector(selectCurrentSession);
  const tasks = useSelector(selectTasks);
  const currentTaskId = useSelector(selectCurrentTaskId);
  const sessionsHistory = useSelector(selectSessionsHistory);
  const reports = useSelector(selectReports);

  // 本地状态
  const [activeTab, setActiveTab] = useState('tasks');
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [recordingEnabled, setRecordingEnabled] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [selectedReport, setSelectedReport] = useState<string | null>(null);
  const [showSettingsDrawer, setShowSettingsDrawer] = useState(false);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const [showTaskDetails, setShowTaskDetails] = useState<string | null>(null);

  // 计算任务完成进度
  const completedTasks = tasks.filter(task => task.completed).length;
  const progress = tasks.length > 0 ? Math.round((completedTasks / tasks.length) * 100) : 0;

  // 组件初始化
  useEffect(() => {
    // 初始化用户测试服务
    const userId = 'test_user_' + Date.now();
    const userName = '测试用户';
    userTestingService.initialize(userId, userName);

    // 监听测试事件
    const handleSessionStarted = (session: any) => {
      console.log('测试会话已开始:', session);
    };

    const handleTaskCompleted = (task: any) => {
      console.log('任务已完成:', task);
    };

    const handleAllTasksCompleted = (session: any) => {
      console.log('所有任务已完成:', session);
      message.success('恭喜！您已完成所有测试任务');
      setActiveTab('feedback');
    };

    userTestingService.on('sessionStarted', handleSessionStarted);
    userTestingService.on('taskCompleted', handleTaskCompleted);
    userTestingService.on('allTasksCompleted', handleAllTasksCompleted);

    // 清理事件监听器
    return () => {
      userTestingService.off('sessionStarted', handleSessionStarted);
      userTestingService.off('taskCompleted', handleTaskCompleted);
      userTestingService.off('allTasksCompleted', handleAllTasksCompleted);
    };
  }, []);

  // 处理测试开关
  const handleToggleTesting = (checked: boolean) => {
    if (checked && !currentSession) {
      // 启动测试会话
      const defaultTasks: Omit<TestTask, 'completed' | 'startTime' | 'endTime' | 'timeSpent'>[] = [
        {
          id: 'task1',
          title: '创建一个新场景',
          description: '使用编辑器创建一个新的空场景'
        },
        {
          id: 'task2',
          title: '添加一个3D对象',
          description: '向场景中添加一个立方体或球体'
        },
        {
          id: 'task3',
          title: '邀请协作者',
          description: '邀请另一个用户加入协作编辑'
        },
        {
          id: 'task4',
          title: '解决编辑冲突',
          description: '尝试同时编辑同一个对象，并解决产生的冲突'
        }
      ];

      try {
        userTestingService.startSession(defaultTasks, {
          projectId: 'test_project',
          sceneId: 'test_scene'
        });

        // 设置第一个任务为当前任务
        if (defaultTasks.length > 0) {
          userTestingService.setCurrentTask(defaultTasks[0].id);
        }
      } catch (error) {
        message.error('启动测试会话失败');
        return;
      }
    } else if (!checked && currentSession) {
      // 结束测试会话
      userTestingService.endSession();
    }

    // 更新状态
    userTestingService.setEnabled(checked);
    dispatch(setTestingEnabled(checked));
  };

  // 处理录制开关
  const handleToggleRecording = (checked: boolean) => {
    setRecordingEnabled(checked);
    userTestingService.setRecordingEnabled(checked);

    if (checked) {
      message.info('开始录制用户操作');
    } else {
      message.info('停止录制用户操作');
    }
  };

  // 处理任务点击
  const handleTaskClick = (taskId: string) => {
    if (!currentSession) return;

    try {
      userTestingService.setCurrentTask(taskId);
    } catch (error) {
      message.error('设置当前任务失败');
    }
  };

  // 处理任务完成
  const handleCompleteTask = (taskId: string) => {
    if (!currentSession) return;

    try {
      userTestingService.completeTask(taskId);
      message.success('任务已完成');

      // 如果所有任务都完成了，提示用户
      const allCompleted = tasks.every(task => task.id === taskId ? true : task.completed);
      if (allCompleted) {
        message.success('所有任务已完成，请提交您的反馈');
        setActiveTab('feedback');
      } else {
        // 自动选择下一个未完成的任务
        const nextTask = tasks.find(task => !task.completed && task.id !== taskId);
        if (nextTask) {
          userTestingService.setCurrentTask(nextTask.id);
        }
      }
    } catch (error) {
      message.error('完成任务失败');
    }
  };

  // 处理提交反馈
  const handleSubmitFeedback = () => {
    setShowFeedbackForm(true);
  };

  // 处理关闭反馈表单
  const handleCloseFeedbackForm = () => {
    setShowFeedbackForm(false);
  };

  // 渲染任务列表
  const renderTasks = () => {
    if (!tasks || tasks.length === 0) {
      return (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="没有测试任务"
        />
      );
    }

    return (
      <List
        itemLayout="horizontal"
        dataSource={tasks}
        renderItem={task => (
          <List.Item
            actions={[
              <Button
                type="link"
                size="small"
                icon={<EyeOutlined />}
                onClick={() => setShowTaskDetails(task.id)}
              >
                详情
              </Button>,
              task.completed ? (
                <Tag color="success" icon={<CheckCircleOutlined />}>
                  已完成
                  {task.timeSpent && (
                    <span style={{ marginLeft: 4 }}>
                      ({Math.round(task.timeSpent / 1000)}s)
                    </span>
                  )}
                </Tag>
              ) : task.id === currentTaskId ? (
                <Button
                  type="primary"
                  size="small"
                  icon={<CheckCircleOutlined />}
                  onClick={() => handleCompleteTask(task.id)}
                >
                  完成
                </Button>
              ) : (
                <Button
                  type="default"
                  size="small"
                  onClick={() => handleTaskClick(task.id)}
                >
                  开始
                </Button>
              )
            ]}
          >
            <List.Item.Meta
              avatar={
                <Badge
                  status={task.completed ? 'success' : task.id === currentTaskId ? 'processing' : 'default'}
                  style={{ marginTop: 8 }}
                />
              }
              title={
                <Space>
                  <Text strong>{task.title}</Text>
                  {task.id === currentTaskId && (
                    <Tag color="blue">当前</Tag>
                  )}
                  {task.progress !== undefined && task.progress > 0 && task.progress < 100 && (
                    <Tag color="orange">{task.progress}%</Tag>
                  )}
                </Space>
              }
              description={
                <div>
                  <Text>{task.description}</Text>
                  {task.tags && task.tags.length > 0 && (
                    <div style={{ marginTop: 4 }}>
                      {task.tags.map(tag => (
                        <Tag key={tag} style={{ fontSize: '12px' }}>{tag}</Tag>
                      ))}
                    </div>
                  )}
                </div>
              }
            />
          </List.Item>
        )}
      />
    );
  };

  // 处理生成报告
  const handleGenerateReport = () => {
    if (!currentSession) return;

    try {
      const reportId = userTestingService.generateSessionReport(userTestingService.getCurrentSession()!);
      if (reportId) {
        setSelectedReport(reportId);
        setShowReportModal(true);
        message.success('测试报告已生成');
      }
    } catch (error) {
      message.error('生成测试报告失败');
    }
  };

  // 处理查看历史会话
  const handleViewHistorySession = (sessionId: string) => {
    // 这里可以添加加载历史会话的逻辑
    console.log('查看历史会话:', sessionId);
    message.info(`查看会话: ${sessionId}`);
  };

  // 处理截图
  const handleCaptureScreenshot = async () => {
    try {
      const screenshot = await userTestingService.captureScreenshot();
      if (screenshot) {
        message.success('截图已捕获');
        // 这里可以添加显示截图的逻辑
      }
    } catch (error) {
      message.error('捕获截图失败');
    }
  };

  // 渲染进度
  const renderProgress = () => {
    return (
      <Card size="small" title="测试进度">
        <Space direction="vertical" style={{ width: '100%' }}>
          <Progress percent={progress} status="active" />
          <Text>
            已完成 {completedTasks}/{tasks.length} 个任务
          </Text>
          <Space>
            <Button
              type="primary"
              size="small"
              icon={<FileTextOutlined />}
              onClick={handleGenerateReport}
              disabled={!currentSession || tasks.length === 0}
            >
              生成报告
            </Button>
            <Button
              size="small"
              icon={<SettingOutlined />}
              onClick={() => setShowSettingsDrawer(true)}
            >
              设置
            </Button>
          </Space>
        </Space>
      </Card>
    );
  };

  // 渲染历史会话
  const renderHistory = () => {
    if (!sessionsHistory || sessionsHistory.length === 0) {
      return (
        <div>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="没有历史测试会话"
          />
          <div style={{ textAlign: 'center', marginTop: 16 }}>
            <Button
              type="primary"
              icon={<ExperimentOutlined />}
              onClick={() => handleToggleTesting(true)}
            >
              开始新的测试会话
            </Button>
          </div>
        </div>
      );
    }

    return (
      <List
        itemLayout="horizontal"
        dataSource={sessionsHistory}
        renderItem={sessionId => {
          const timestamp = parseInt(sessionId.split('_')[1]);
          const sessionDate = new Date(timestamp);
          const isToday = sessionDate.toDateString() === new Date().toDateString();

          return (
            <List.Item
              actions={[
                <Button
                  type="link"
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={() => handleViewHistorySession(sessionId)}
                >
                  查看
                </Button>,
                <Button
                  type="link"
                  size="small"
                  icon={<DownloadOutlined />}
                  onClick={() => {
                    // 导出会话数据
                    const sessionData = { sessionId, timestamp };
                    const blob = new Blob([JSON.stringify(sessionData, null, 2)], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `会话_${sessionId}.json`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                  }}
                >
                  导出
                </Button>
              ]}
            >
              <List.Item.Meta
                avatar={<HistoryOutlined style={{ fontSize: 16, color: '#1890ff' }} />}
                title={
                  <Space>
                    <Text strong>会话 {sessionId.split('_')[2] || sessionId.split('_')[1]}</Text>
                    {isToday && <Tag color="blue">今天</Tag>}
                  </Space>
                }
                description={
                  <Space direction="vertical" size="small">
                    <Text type="secondary">
                      创建于 {sessionDate.toLocaleString()}
                    </Text>
                    <Text type="secondary">
                      会话ID: {sessionId}
                    </Text>
                  </Space>
                }
              />
            </List.Item>
          );
        }}
      />
    );
  };

  return (
    <Card
      title={
        <Space>
          <ExperimentOutlined />
          <span>用户测试</span>
        </Space>
      }
      extra={
        <Space>
          <Switch
            checked={testingEnabled}
            onChange={handleToggleTesting}
            checkedChildren="开启"
            unCheckedChildren="关闭"
          />
          {onClose && (
            <Button type="text" icon={<StopOutlined />} onClick={onClose} />
          )}
        </Space>
      }
      style={{ width: '100%', height: '100%' }}
    >
      {testingEnabled ? (
        <Space direction="vertical" style={{ width: '100%' }}>
          {renderProgress()}

          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={[
              {
                key: 'tasks',
                label: (
                  <span>
                    <FileTextOutlined />
                    任务
                  </span>
                ),
                children: renderTasks()
              },
              {
                key: 'feedback',
                label: (
                  <span>
                    <BulbOutlined />
                    反馈
                  </span>
                ),
                children: (
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Paragraph>
                      请提供您对协作编辑功能的反馈，包括使用体验、发现的问题或改进建议。
                    </Paragraph>

                    <Space>
                      <Button
                        type="primary"
                        icon={<BulbOutlined />}
                        onClick={handleSubmitFeedback}
                      >
                        提交反馈
                      </Button>

                      <Button
                        icon={<VideoCameraOutlined />}
                        onClick={handleCaptureScreenshot}
                      >
                        捕获截图
                      </Button>
                    </Space>
                  </Space>
                )
              },
              {
                key: 'recording',
                label: (
                  <span>
                    <VideoCameraOutlined />
                    录制
                  </span>
                ),
                children: (
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Paragraph>
                      启用操作录制功能，记录您的操作以帮助我们分析和改进用户体验。
                    </Paragraph>

                    <Space>
                      <Switch
                        checked={recordingEnabled}
                        onChange={handleToggleRecording}
                        checkedChildren={<VideoCameraOutlined />}
                        unCheckedChildren={<StopOutlined />}
                      />
                      <Text>{recordingEnabled ? '正在录制' : '未录制'}</Text>
                    </Space>
                  </Space>
                )
              },
              {
                key: 'history',
                label: (
                  <span>
                    <FileTextOutlined />
                    历史
                  </span>
                ),
                children: renderHistory()
              }
            ]}
          />
        </Space>
      ) : (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="启用用户测试以开始测试协作编辑功能"
        />
      )}

      {/* 反馈表单 */}
      <FeedbackForm
        visible={showFeedbackForm}
        onClose={handleCloseFeedbackForm}
      />

      {/* 报告模态框 */}
      {showReportModal && selectedReport && (
        <Modal
          title="测试报告"
          open={showReportModal}
          width={800}
          footer={[
            <Button key="close" onClick={() => setShowReportModal(false)}>
              关闭
            </Button>,
            <Button
              key="download"
              type="primary"
              icon={<DownloadOutlined />}
              onClick={() => {
                // 下载报告逻辑
                const reportData = reports[selectedReport];
                if (reportData) {
                  const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `测试报告_${selectedReport}.json`;
                  document.body.appendChild(a);
                  a.click();
                  document.body.removeChild(a);
                  URL.revokeObjectURL(url);
                }
              }}
            >
              下载报告
            </Button>
          ]}
          onCancel={() => setShowReportModal(false)}
        >
          {reports[selectedReport] ? (
            <div style={{ maxHeight: '500px', overflow: 'auto' }}>
              <pre>{JSON.stringify(reports[selectedReport], null, 2)}</pre>
            </div>
          ) : (
            <Empty description="无法加载报告数据" />
          )}
        </Modal>
      )}

      {/* 设置抽屉 */}
      <Drawer
        title="测试设置"
        placement="right"
        open={showSettingsDrawer}
        onClose={() => setShowSettingsDrawer(false)}
        width={400}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>自动保存</Text>
            <br />
            <Switch
              checked={autoSaveEnabled}
              onChange={setAutoSaveEnabled}
              checkedChildren="开启"
              unCheckedChildren="关闭"
            />
            <br />
            <Text type="secondary">自动保存测试进度和数据</Text>
          </div>

          <div>
            <Text strong>录制设置</Text>
            <br />
            <Switch
              checked={recordingEnabled}
              onChange={handleToggleRecording}
              checkedChildren="录制中"
              unCheckedChildren="已停止"
            />
            <br />
            <Text type="secondary">录制用户操作以供分析</Text>
          </div>

          <div>
            <Text strong>数据管理</Text>
            <br />
            <Space>
              <Button
                icon={<DeleteOutlined />}
                onClick={() => {
                  localStorage.removeItem('userTestingSessions');
                  message.success('历史数据已清除');
                }}
              >
                清除历史
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => {
                  window.location.reload();
                }}
              >
                重置界面
              </Button>
            </Space>
          </div>
        </Space>
      </Drawer>

      {/* 任务详情模态框 */}
      {showTaskDetails && (
        <Modal
          title="任务详情"
          open={!!showTaskDetails}
          onCancel={() => setShowTaskDetails(null)}
          width={600}
          footer={[
            <Button key="close" onClick={() => setShowTaskDetails(null)}>
              关闭
            </Button>,
            ...((() => {
              const task = tasks.find(t => t.id === showTaskDetails);
              if (!task) return [];

              if (task.completed) {
                return [
                  <Button key="restart" icon={<ReloadOutlined />} onClick={() => {
                    // 重新开始任务的逻辑
                    if (showTaskDetails) {
                      try {
                        // 这里应该调用服务来重置任务
                        // userTestingService.resetTask(showTaskDetails);
                        message.info('任务重置功能暂未实现');
                        setShowTaskDetails(null);
                      } catch (error) {
                        message.error('重置任务失败');
                      }
                    }
                  }}>
                    重新开始
                  </Button>
                ];
              } else if (task.id === currentTaskId) {
                return [
                  <Button key="complete" type="primary" icon={<CheckCircleOutlined />} onClick={() => {
                    handleCompleteTask(task.id);
                    setShowTaskDetails(null);
                  }}>
                    完成任务
                  </Button>
                ];
              } else {
                return [
                  <Button key="start" type="primary" icon={<PlayCircleOutlined />} onClick={() => {
                    handleTaskClick(task.id);
                    setShowTaskDetails(null);
                  }}>
                    开始任务
                  </Button>
                ];
              }
            })())
          ]}
        >
          {(() => {
            const task = tasks.find(t => t.id === showTaskDetails);
            if (!task) return <Empty description="任务不存在" />;

            return (
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text strong>任务标题：</Text>
                  <Text>{task.title}</Text>
                </div>

                <div>
                  <Text strong>任务描述：</Text>
                  <Paragraph>{task.description}</Paragraph>
                </div>

                <div>
                  <Text strong>任务状态：</Text>
                  <Tag color={task.completed ? 'success' : task.id === currentTaskId ? 'processing' : 'default'}>
                    {task.completed ? '已完成' : task.id === currentTaskId ? '进行中' : '未开始'}
                  </Tag>
                </div>

                {task.progress !== undefined && (
                  <div>
                    <Text strong>完成进度：</Text>
                    <Progress percent={task.progress} strokeWidth={6} />
                  </div>
                )}

                {task.startTime && (
                  <div>
                    <Text strong>开始时间：</Text>
                    <Text>{new Date(task.startTime).toLocaleString()}</Text>
                  </div>
                )}

                {task.endTime && (
                  <div>
                    <Text strong>完成时间：</Text>
                    <Text>{new Date(task.endTime).toLocaleString()}</Text>
                  </div>
                )}

                {task.timeSpent && (
                  <div>
                    <Text strong>耗时：</Text>
                    <Text>{Math.round(task.timeSpent / 1000)} 秒</Text>
                  </div>
                )}

                {task.tags && task.tags.length > 0 && (
                  <div>
                    <Text strong>标签：</Text>
                    <div style={{ marginTop: 4 }}>
                      {task.tags.map(tag => (
                        <Tag key={tag}>{tag}</Tag>
                      ))}
                    </div>
                  </div>
                )}

                {task.dependencies && task.dependencies.length > 0 && (
                  <div>
                    <Text strong>依赖任务：</Text>
                    <div style={{ marginTop: 4 }}>
                      {task.dependencies.map(depId => {
                        const depTask = tasks.find(t => t.id === depId);
                        return (
                          <Tag key={depId} color={depTask?.completed ? 'success' : 'default'}>
                            {depTask?.title || depId}
                          </Tag>
                        );
                      })}
                    </div>
                  </div>
                )}

                {task.subtasks && task.subtasks.length > 0 && (
                  <div>
                    <Text strong>子任务：</Text>
                    <List
                      size="small"
                      dataSource={task.subtasks}
                      renderItem={subtask => (
                        <List.Item>
                          <List.Item.Meta
                            avatar={
                              <Badge
                                status={subtask.completed ? 'success' : 'default'}
                                style={{ marginTop: 4 }}
                              />
                            }
                            title={subtask.title}
                            description={subtask.description}
                          />
                        </List.Item>
                      )}
                    />
                  </div>
                )}
              </Space>
            );
          })()}
        </Modal>
      )}
    </Card>
  );
};

export default UserTestingPanel;
