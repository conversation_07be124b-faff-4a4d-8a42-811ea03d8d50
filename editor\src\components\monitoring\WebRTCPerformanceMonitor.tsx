/**
 * WebRTC性能监控仪表板组件
 * 实时显示WebRTC连接的性能指标
 */
import React, { useState, useEffect, useRef } from 'react';
import { Line, Gauge } from '@ant-design/plots';
import { Card, Row, Col, Statistic, Alert, Badge, Progress, Tooltip } from 'antd';
import { WifiOutlined, ThunderboltOutlined, EyeOutlined } from '@ant-design/icons';
import { UltraLowLatencyWebRTC, LatencyMetrics } from '../../services/UltraLowLatencyWebRTC';

interface PerformanceData {
  timestamp: number;
  latency: number;
  packetLoss: number;
  jitter: number;
  bandwidth: number;
  fps: number;
}

interface ConnectionStatus {
  state: 'connecting' | 'connected' | 'disconnected' | 'failed';
  quality: 'excellent' | 'good' | 'fair' | 'poor';
  region: string;
  duration: number;
}

export interface WebRTCPerformanceMonitorProps {
  webrtcConnection?: UltraLowLatencyWebRTC;
  targetLatency?: number;
  autoOptimize?: boolean;
  showDetailedMetrics?: boolean;
}

export const WebRTCPerformanceMonitor: React.FC<WebRTCPerformanceMonitorProps> = ({
  webrtcConnection,
  targetLatency = 30,
  autoOptimize = true,
  showDetailedMetrics = false
}) => {
  const [performanceData, setPerformanceData] = useState<PerformanceData[]>([]);
  const [currentMetrics, setCurrentMetrics] = useState<LatencyMetrics | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    state: 'disconnected',
    quality: 'poor',
    region: 'unknown',
    duration: 0
  });
  const [alerts, setAlerts] = useState<string[]>([]);
  const intervalRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (webrtcConnection) {
      startMonitoring();
      setupEventListeners();
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [webrtcConnection]);

  const startMonitoring = () => {
    intervalRef.current = setInterval(async () => {
      if (webrtcConnection) {
        try {
          // 监听性能指标更新事件
          webrtcConnection.on('metricsUpdated', (metrics: LatencyMetrics) => {
            setCurrentMetrics(metrics);

            // 更新性能数据
            const newDataPoint: PerformanceData = {
              timestamp: Date.now(),
              latency: metrics.rtt,
              packetLoss: metrics.packetLoss,
              jitter: metrics.jitter,
              bandwidth: metrics.bandwidth,
              fps: metrics.fps
            };

            setPerformanceData(prev => {
              const updated = [...prev, newDataPoint];
              // 保持最近100个数据点
              return updated.slice(-100);
            });

            // 更新连接状态
            updateConnectionStatus(metrics);

            // 检查性能告警
            checkPerformanceAlerts(metrics);
          });

        } catch (error) {
          console.error('性能监控错误:', error);
        }
      }
    }, 1000); // 每秒更新一次
  };

  const setupEventListeners = () => {
    if (!webrtcConnection) return;

    webrtcConnection.on('latencyUpdate', (metrics: LatencyMetrics) => {
      setCurrentMetrics(metrics);
    });

    webrtcConnection.on('dataChannelOpen', () => {
      setConnectionStatus(prev => ({ ...prev, state: 'connected' }));
    });

    webrtcConnection.on('dataChannelError', () => {
      setConnectionStatus(prev => ({ ...prev, state: 'failed' }));
    });
  };

  const updateConnectionStatus = (metrics: LatencyMetrics) => {
    let quality: ConnectionStatus['quality'] = 'poor';
    
    if (metrics.rtt <= targetLatency) {
      quality = 'excellent';
    } else if (metrics.rtt <= targetLatency * 1.5) {
      quality = 'good';
    } else if (metrics.rtt <= targetLatency * 2) {
      quality = 'fair';
    }

    setConnectionStatus(prev => ({
      ...prev,
      quality,
      state: 'connected',
      duration: prev.duration + 1
    }));
  };

  const checkPerformanceAlerts = (metrics: LatencyMetrics) => {
    const newAlerts: string[] = [];

    if (metrics.rtt > targetLatency * 2) {
      newAlerts.push(`延迟过高: ${metrics.rtt.toFixed(1)}ms (目标: ${targetLatency}ms)`);
    }

    if (metrics.jitter > 10) {
      newAlerts.push(`抖动过高: ${metrics.jitter.toFixed(1)}ms`);
    }

    if (metrics.packetLoss > 5) {
      newAlerts.push(`丢包率过高: ${metrics.packetLoss.toFixed(1)}%`);
    }

    setAlerts(newAlerts);
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus.quality) {
      case 'excellent': return '#52c41a';
      case 'good': return '#1890ff';
      case 'fair': return '#faad14';
      case 'poor': return '#ff4d4f';
      default: return '#d9d9d9';
    }
  };

  const getConnectionStatusText = () => {
    switch (connectionStatus.quality) {
      case 'excellent': return '优秀';
      case 'good': return '良好';
      case 'fair': return '一般';
      case 'poor': return '较差';
      default: return '未知';
    }
  };

  // 延迟趋势图配置
  const latencyChartConfig = {
    data: performanceData,
    xField: 'timestamp',
    yField: 'latency',
    smooth: true,
    color: '#1890ff',
    point: {
      size: 2,
      shape: 'circle',
    },
    yAxis: {
      title: {
        text: '延迟 (ms)',
      },
      max: targetLatency * 3,
    },
    xAxis: {
      type: 'time',
      title: {
        text: '时间',
      },
    },
    annotations: [
      {
        type: 'line',
        start: ['min', targetLatency],
        end: ['max', targetLatency],
        style: {
          stroke: '#ff4d4f',
          lineDash: [4, 4],
        },
      },
    ],
  };

  // 延迟仪表盘配置
  const latencyGaugeConfig = {
    percent: currentMetrics ? Math.min(currentMetrics.rtt / (targetLatency * 2), 1) : 0,
    color: ['#30BF78', '#FAAD14', '#F4664A'],
    innerRadius: 0.75,
    radius: 0.95,
    statistic: {
      title: {
        style: {
          fontSize: '14px',
          lineHeight: '14px',
        },
        content: '当前延迟',
      },
      content: {
        style: {
          fontSize: '24px',
          lineHeight: '24px',
        },
        content: currentMetrics ? `${currentMetrics.rtt.toFixed(1)}ms` : '0ms',
      },
    },
  };

  return (
    <div className="webrtc-performance-monitor">
      {/* 告警信息 */}
      {alerts.length > 0 && (
        <Alert
          message="性能告警"
          description={
            <ul>
              {alerts.map((alert, index) => (
                <li key={index}>{alert}</li>
              ))}
            </ul>
          }
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 连接状态概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="连接状态"
              value={getConnectionStatusText()}
              prefix={<Badge color={getConnectionStatusColor()} />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="当前延迟"
              value={currentMetrics?.rtt.toFixed(1) || '0'}
              suffix="ms"
              prefix={<ThunderboltOutlined />}
              valueStyle={{ color: (currentMetrics?.rtt || 0) <= targetLatency ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="目标延迟"
              value={targetLatency}
              suffix="ms"
              prefix={<EyeOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="连接时长"
              value={Math.floor(connectionStatus.duration / 60)}
              suffix="分钟"
              prefix={<WifiOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 性能指标 */}
      <Row gutter={[16, 16]}>
        <Col span={16}>
          <Card title="延迟趋势" size="small">
            <Line {...latencyChartConfig} height={300} />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="延迟仪表盘" size="small">
            <Gauge {...latencyGaugeConfig} height={300} />
          </Card>
        </Col>
      </Row>

      {/* 详细指标 */}
      {showDetailedMetrics && currentMetrics && (
        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={24}>
            <Card title="详细性能指标" size="small">
              <Row gutter={[16, 16]}>
                <Col span={4}>
                  <Statistic
                    title="抖动"
                    value={currentMetrics.jitter.toFixed(1)}
                    suffix="ms"
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="丢包率"
                    value={currentMetrics.packetLoss.toFixed(1)}
                    suffix="%"
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="传输延迟"
                    value={currentMetrics.rtt.toFixed(1)}
                    suffix="ms"
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="带宽"
                    value={currentMetrics.bandwidth.toFixed(1)}
                    suffix="Mbps"
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="帧率"
                    value={currentMetrics.fps.toFixed(0)}
                    suffix="fps"
                  />
                </Col>
                <Col span={4}>
                  <Tooltip title="延迟达标率">
                    <Progress
                      type="circle"
                      size={80}
                      percent={currentMetrics.rtt <= targetLatency ? 100 : 0}
                      format={() => currentMetrics.rtt <= targetLatency ? '达标' : '超标'}
                    />
                  </Tooltip>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      )}

      {/* 优化建议 */}
      {autoOptimize && currentMetrics && currentMetrics.rtt > targetLatency && (
        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={24}>
            <Card title="优化建议" size="small">
              <Alert
                message="性能优化建议"
                description={
                  <ul>
                    {currentMetrics.rtt > targetLatency * 0.5 && (
                      <li>网络延迟较高，建议检查网络连接质量或切换到更近的服务器</li>
                    )}
                    {currentMetrics.jitter > 10 && (
                      <li>网络抖动较高，建议优化网络连接稳定性</li>
                    )}
                    {currentMetrics.packetLoss > 5 && (
                      <li>丢包率较高，建议检查网络质量或降低传输质量</li>
                    )}
                    {currentMetrics.fps < 30 && (
                      <li>帧率较低，建议优化编码设置或提升硬件性能</li>
                    )}
                    <li>建议启用自适应码率和缓冲区优化</li>
                  </ul>
                }
                type="info"
                showIcon
              />
            </Card>
          </Col>
        </Row>
      )}
    </div>
  );
};

export default WebRTCPerformanceMonitor;
