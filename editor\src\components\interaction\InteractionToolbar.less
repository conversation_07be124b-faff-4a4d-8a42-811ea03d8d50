/**
 * 交互工具栏样式
 */
.interaction-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  // 按钮样式
  .ant-btn {
    border-radius: 4px;
    font-size: 14px;
    height: 32px;
    padding: 4px 12px;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    }

    &.ant-btn-primary {
      background: linear-gradient(135deg, #1890ff, #36cfc9);
      border: none;

      &:hover {
        background: linear-gradient(135deg, #40a9ff, #5cdbd3);
      }

      &:disabled {
        background: #f5f5f5;
        color: #bfbfbf;
        transform: none;
        box-shadow: none;
      }
    }

    &.ant-btn-dangerous {
      &:hover {
        background: #ff7875;
        border-color: #ff7875;
      }
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    // 图标按钮
    &.ant-btn-icon-only {
      width: 32px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  // 分隔线
  .ant-divider-vertical {
    height: 20px;
    margin: 0 8px;
    border-color: #e8e8e8;
  }

  // 空间组件
  .ant-space {
    .ant-space-item {
      display: flex;
      align-items: center;
    }
  }

  // 下拉菜单
  .ant-dropdown {
    .ant-dropdown-menu {
      border-radius: 6px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

      .ant-dropdown-menu-item {
        padding: 8px 12px;
        border-radius: 4px;
        margin: 2px;

        &:hover {
          background: #f5f5f5;
        }

        .ant-space {
          .ant-space-item:first-child {
            font-size: 16px;
          }
        }
      }
    }
  }

  // 徽章
  .ant-badge {
    .ant-badge-count {
      background: #ff4d4f;
      border-radius: 10px;
      font-size: 10px;
      min-width: 16px;
      height: 16px;
      line-height: 16px;
      padding: 0 4px;
    }

    .ant-badge-status-dot {
      width: 8px;
      height: 8px;
    }

    .ant-badge-status-text {
      font-size: 12px;
      color: #666;
    }
  }

  // 工具提示
  .ant-tooltip {
    .ant-tooltip-inner {
      background: rgba(0, 0, 0, 0.85);
      border-radius: 4px;
      font-size: 12px;
      padding: 6px 8px;
    }

    .ant-tooltip-arrow {
      &::before {
        background: rgba(0, 0, 0, 0.85);
      }
    }
  }

  // 状态指示器
  .toolbar-status {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 12px;

    .ant-typography {
      margin: 0;
      font-size: 12px;
    }

    .ant-badge {
      font-size: 12px;
    }

    @media (max-width: 768px) {
      display: none;
    }
  }

  // 响应式设计
  @media (max-width: 1024px) {
    padding: 6px 12px;

    .ant-btn {
      height: 28px;
      font-size: 12px;
      padding: 2px 8px;

      &.ant-btn-icon-only {
        width: 28px;
      }
    }

    .ant-divider-vertical {
      height: 16px;
      margin: 0 6px;
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 8px;
    padding: 8px;

    .ant-space {
      flex-wrap: wrap;
      justify-content: center;
    }

    .toolbar-status {
      width: 100%;
      justify-content: center;
      border-top: 1px solid #f0f0f0;
      padding-top: 8px;
      margin-top: 8px;
    }
  }
}

// 设置弹出框样式
.interaction-settings {
  width: 280px;
  padding: 8px;

  .setting-item {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .ant-typography {
      margin-bottom: 8px;
      font-size: 13px;
      font-weight: 500;
    }

    .ant-slider {
      margin: 8px 0;

      .ant-slider-rail {
        background: #f5f5f5;
      }

      .ant-slider-track {
        background: linear-gradient(90deg, #1890ff, #36cfc9);
      }

      .ant-slider-handle {
        border: 2px solid #1890ff;
        background: #fff;

        &:hover,
        &:focus {
          border-color: #40a9ff;
          box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.2);
        }
      }
    }

    .ant-space {
      width: 100%;
      justify-content: space-between;
    }

    .ant-switch {
      &.ant-switch-checked {
        background: #52c41a;
      }
    }
  }

  .ant-divider {
    margin: 8px 0;
  }
}

// 弹出框样式
.ant-popover {
  .ant-popover-content {
    .ant-popover-inner {
      border-radius: 6px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .ant-popover-title {
      border-bottom: 1px solid #f0f0f0;
      font-weight: 600;
      font-size: 14px;
    }

    .ant-popover-inner-content {
      padding: 12px;
    }
  }

  .ant-popover-arrow {
    &::before {
      background: #fff;
      border: 1px solid #f0f0f0;
    }
  }
}

// 动画效果
@keyframes buttonHover {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-1px);
  }
}

@keyframes badgePulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// 活动状态动画
.ant-badge-count {
  animation: badgePulse 2s infinite;
}

// 加载状态
.interaction-toolbar.loading {
  .ant-btn {
    pointer-events: none;
    opacity: 0.6;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    z-index: 10;
  }
}

// 错误状态
.interaction-toolbar.error {
  border-bottom-color: #ff4d4f;
  background: #fff2f0;

  .ant-btn {
    &:not(.ant-btn-dangerous) {
      opacity: 0.6;
      pointer-events: none;
    }
  }
}

// 成功状态
.interaction-toolbar.success {
  border-bottom-color: #52c41a;

  .ant-btn-primary {
    background: linear-gradient(135deg, #52c41a, #73d13d);

    &:hover {
      background: linear-gradient(135deg, #73d13d, #95de64);
    }
  }
}

// 深色主题支持
@media (prefers-color-scheme: dark) {
  .interaction-toolbar {
    background: #1f1f1f;
    border-bottom-color: #434343;

    .ant-btn {
      background: #2f2f2f;
      border-color: #434343;
      color: #fff;

      &:hover {
        background: #3f3f3f;
        border-color: #595959;
      }

      &.ant-btn-primary {
        background: linear-gradient(135deg, #1890ff, #36cfc9);
        border: none;
        color: #fff;
      }
    }

    .ant-divider-vertical {
      border-color: #434343;
    }

    .toolbar-status {
      .ant-typography {
        color: #ccc;
      }
    }
  }

  .interaction-settings {
    background: #2f2f2f;
    color: #fff;

    .ant-typography {
      color: #fff;
    }

    .setting-item {
      .ant-slider {
        .ant-slider-rail {
          background: #434343;
        }
      }
    }
  }
}
