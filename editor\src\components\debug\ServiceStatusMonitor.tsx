/**
 * 服务状态监控组件
 * 显示微服务的健康状态和连接信息
 */
import React, { useState, useEffect } from 'react';
import { Card, Badge, Button, Table, Space, Tooltip, Modal, message } from 'antd';
import { 
  CheckCircleOutlined, 
  ExclamationCircleOutlined, 
  QuestionCircleOutlined,
  ReloadOutlined,
  SettingOutlined,
  BugOutlined
} from '@ant-design/icons';
// 创建模拟的微服务集成和测试工具
interface ServiceHealth {
  status: 'healthy' | 'unhealthy' | 'unknown';
  lastCheck: Date;
  responseTime: number;
  errorCount: number;
}

interface ServiceRegistry {
  [serviceName: string]: ServiceHealth;
}

interface IntegrationTestReport {
  timestamp: Date;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  skippedTests: number;
  duration: number;
  results: Array<{
    service: string;
    test: string;
    status: 'passed' | 'failed';
    message?: string;
  }>;
}

// 模拟微服务集成
const microserviceIntegration = {
  on(_event: string, _handler: Function) {
    // 模拟事件监听
    console.log(`监听事件: ${_event}`);
  },
  off(_event: string, _handler: Function) {
    // 模拟事件移除
    console.log(`移除事件监听: ${_event}`);
  },
  async initialize(): Promise<void> {
    // 模拟初始化
    console.log('初始化微服务集成');
  },
  getServiceHealth(): ServiceRegistry {
    // 模拟获取服务健康状态
    return {
      'user-service': {
        status: 'healthy',
        lastCheck: new Date(),
        responseTime: Math.random() * 100 + 50,
        errorCount: 0
      }
    };
  },
  startHealthCheck(): void {
    // 模拟开始健康检查
    console.log('开始健康检查');
  },
  async checkHealth(): Promise<ServiceRegistry> {
    // 模拟健康检查
    return {
      'user-service': {
        status: 'healthy',
        lastCheck: new Date(),
        responseTime: Math.random() * 100 + 50,
        errorCount: 0
      },
      'content-service': {
        status: 'healthy',
        lastCheck: new Date(),
        responseTime: Math.random() * 100 + 50,
        errorCount: 0
      },
      'ai-service': {
        status: Math.random() > 0.8 ? 'unhealthy' : 'healthy',
        lastCheck: new Date(),
        responseTime: Math.random() * 200 + 100,
        errorCount: Math.floor(Math.random() * 3)
      }
    };
  }
};

// 模拟集成测试器
const integrationTester = {
  async runTests(): Promise<IntegrationTestReport> {
    return this.runAllTests();
  },
  async runAllTests(): Promise<IntegrationTestReport> {
    // 模拟运行测试
    return new Promise(resolve => {
      setTimeout(() => {
        const totalTests = 10;
        const passedTests = Math.floor(Math.random() * 3) + 7;
        resolve({
          timestamp: new Date(),
          totalTests,
          passedTests,
          failedTests: totalTests - passedTests,
          skippedTests: 0,
          duration: Math.random() * 5000 + 1000,
          results: Array.from({ length: totalTests }, (_, i) => ({
            service: ['user-service', 'content-service', 'ai-service'][i % 3],
            test: `测试 ${i + 1}`,
            status: i < passedTests ? 'passed' : 'failed',
            message: i >= passedTests ? '连接超时' : undefined
          }))
        });
      }, 2000);
    });
  }
};

// 模拟配置
const config = {
  apiUrl: 'http://localhost:3000/api',
  collaborationServerUrl: 'ws://localhost:3001',
  enableDebug: true,
  services: {
    userService: { url: 'http://localhost:3001' },
    contentService: { url: 'http://localhost:3002' },
    aiService: { url: 'http://localhost:3003' }
  }
};
import './ServiceStatusMonitor.less';

interface ServiceStatusMonitorProps {
  visible?: boolean;
  onClose?: () => void;
}

/**
 * 服务状态监控组件
 */
const ServiceStatusMonitor: React.FC<ServiceStatusMonitorProps> = ({
  visible = true,
  onClose
}) => {
  const [serviceRegistry, setServiceRegistry] = useState<ServiceRegistry | null>(null);
  const [loading, setLoading] = useState(false);
  const [testReport, setTestReport] = useState<IntegrationTestReport | null>(null);
  const [testModalVisible, setTestModalVisible] = useState(false);

  useEffect(() => {
    // 初始化微服务集成
    initializeMicroservices();

    // 监听服务状态变化
    const handleHealthCheck = (registry: ServiceRegistry) => {
      setServiceRegistry(registry);
    };

    const handleServiceStatusChange = (event: any) => {
      message.info(`服务 ${event.service} 状态变更: ${event.previousStatus} → ${event.currentStatus}`);
    };

    microserviceIntegration.on('health:checked', handleHealthCheck);
    microserviceIntegration.on('service:status:changed', handleServiceStatusChange);

    return () => {
      microserviceIntegration.off('health:checked', handleHealthCheck);
      microserviceIntegration.off('service:status:changed', handleServiceStatusChange);
    };
  }, []);

  /**
   * 初始化微服务
   */
  const initializeMicroservices = async () => {
    try {
      setLoading(true);
      await microserviceIntegration.initialize();
      setServiceRegistry(microserviceIntegration.getServiceHealth());
    } catch (error) {
      console.error('微服务初始化失败:', error);
      message.error('微服务初始化失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 手动刷新服务状态
   */
  const handleRefresh = async () => {
    setLoading(true);
    try {
      microserviceIntegration.startHealthCheck();
      message.success('正在刷新服务状态...');
    } catch (error) {
      message.error('刷新失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 运行集成测试
   */
  const handleRunTests = async () => {
    setLoading(true);
    try {
      const report = await integrationTester.runAllTests();
      setTestReport(report);
      setTestModalVisible(true);
      
      if (report.failedTests === 0) {
        message.success('所有集成测试通过！');
      } else {
        message.warning(`${report.failedTests} 个测试失败`);
      }
    } catch (error) {
      message.error('集成测试运行失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取状态图标
   */
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'unhealthy':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <QuestionCircleOutlined style={{ color: '#faad14' }} />;
    }
  };

  /**
   * 获取状态徽章
   */
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'healthy':
        return <Badge status="success" text="健康" />;
      case 'unhealthy':
        return <Badge status="error" text="异常" />;
      default:
        return <Badge status="warning" text="未知" />;
    }
  };

  /**
   * 表格列定义
   */
  const columns = [
    {
      title: '服务名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string) => <strong>{name}</strong>
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Space>
          {getStatusIcon(status)}
          {getStatusBadge(status)}
        </Space>
      )
    },
    {
      title: '响应时间',
      dataIndex: 'responseTime',
      key: 'responseTime',
      render: (time?: number) => time ? `${time}ms` : '-'
    },
    {
      title: '最后检查',
      dataIndex: 'lastCheck',
      key: 'lastCheck',
      render: (date: Date) => new Date(date).toLocaleTimeString()
    },
    {
      title: '错误信息',
      dataIndex: 'error',
      key: 'error',
      render: (error?: string) => error ? (
        <Tooltip title={error}>
          <span style={{ color: '#ff4d4f', cursor: 'pointer' }}>
            查看错误
          </span>
        </Tooltip>
      ) : '-'
    }
  ];

  // 转换数据格式
  const tableData = serviceRegistry ? Object.values(serviceRegistry).map((service, index) => ({
    key: index,
    ...service
  })) : [];

  // 计算统计信息
  const healthyCount = tableData.filter(s => s.status === 'healthy').length;
  const unhealthyCount = tableData.filter(s => s.status === 'unhealthy').length;
  const unknownCount = tableData.filter(s => s.status === 'unknown').length;

  if (!visible) return null;

  return (
    <div className="service-status-monitor">
      <Card
        title={
          <Space>
            <SettingOutlined />
            服务状态监控
          </Space>
        }
        extra={
          <Space>
            <Button 
              icon={<BugOutlined />} 
              onClick={handleRunTests}
              loading={loading}
            >
              运行测试
            </Button>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={handleRefresh}
              loading={loading}
            >
              刷新
            </Button>
            {onClose && (
              <Button onClick={onClose}>
                关闭
              </Button>
            )}
          </Space>
        }
      >
        {/* 统计信息 */}
        <div className="status-summary">
          <Space size="large">
            <div className="status-item">
              <CheckCircleOutlined style={{ color: '#52c41a', fontSize: 16 }} />
              <span>健康: {healthyCount}</span>
            </div>
            <div className="status-item">
              <ExclamationCircleOutlined style={{ color: '#ff4d4f', fontSize: 16 }} />
              <span>异常: {unhealthyCount}</span>
            </div>
            <div className="status-item">
              <QuestionCircleOutlined style={{ color: '#faad14', fontSize: 16 }} />
              <span>未知: {unknownCount}</span>
            </div>
          </Space>
        </div>

        {/* 配置信息 */}
        <div className="config-info">
          <h4>当前配置</h4>
          <div className="config-item">
            <strong>API URL:</strong> {config.apiUrl}
          </div>
          <div className="config-item">
            <strong>协作服务器:</strong> {config.collaborationServerUrl}
          </div>
          <div className="config-item">
            <strong>环境:</strong> {config.enableDebug ? '开发' : '生产'}
          </div>
        </div>

        {/* 服务状态表格 */}
        <Table
          columns={columns}
          dataSource={tableData}
          loading={loading}
          pagination={false}
          size="small"
          style={{ marginTop: 16 }}
        />
      </Card>

      {/* 测试报告模态框 */}
      <Modal
        title="集成测试报告"
        open={testModalVisible}
        onCancel={() => setTestModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setTestModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {testReport && (
          <div className="test-report">
            <div className="report-summary">
              <Space size="large">
                <div>总测试数: {testReport.totalTests}</div>
                <div style={{ color: '#52c41a' }}>通过: {testReport.passedTests}</div>
                <div style={{ color: '#ff4d4f' }}>失败: {testReport.failedTests}</div>
                <div style={{ color: '#faad14' }}>跳过: {testReport.skippedTests}</div>
                <div>耗时: {testReport.duration}ms</div>
              </Space>
            </div>
            
            <Table
              columns={[
                { title: '测试名称', dataIndex: 'name', key: 'name' },
                { 
                  title: '状态', 
                  dataIndex: 'status', 
                  key: 'status',
                  render: (status: string) => (
                    <Badge 
                      status={status === 'pass' ? 'success' : status === 'fail' ? 'error' : 'warning'} 
                      text={status === 'pass' ? '通过' : status === 'fail' ? '失败' : '跳过'} 
                    />
                  )
                },
                { title: '消息', dataIndex: 'message', key: 'message' },
                { title: '耗时', dataIndex: 'duration', key: 'duration', render: (time: number) => `${time}ms` }
              ]}
              dataSource={testReport.results.map((result, index) => ({ ...result, key: index }))}
              pagination={false}
              size="small"
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ServiceStatusMonitor;
