/**
 * 高级光照面板样式
 */

.lighting-panel {
  .ant-modal-body {
    padding: 16px;
    height: 70vh;
    overflow: hidden;
  }

  // 光源列表
  .lights-list {
    height: 100%;
    display: flex;
    flex-direction: column;

    .lights-header {
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;

      .ant-btn {
        margin-right: 8px;
        margin-bottom: 8px;
      }
    }

    .ant-tree {
      flex: 1;
      overflow-y: auto;

      .ant-tree-node-content-wrapper {
        padding: 4px 8px;
        border-radius: 4px;

        &:hover {
          background-color: #f0f0f0;
        }

        &.ant-tree-node-selected {
          background-color: #e6f7ff;
        }
      }

      .light-node {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        span {
          flex: 1;
          font-size: 13px;
        }

        .ant-space {
          opacity: 0;
          transition: opacity 0.2s ease;
        }

        &:hover .ant-space {
          opacity: 1;
        }
      }

      .ant-tree-treenode {
        .ant-tree-switcher {
          width: 16px;
          height: 16px;
          line-height: 16px;
        }

        .ant-tree-iconEle {
          width: 16px;
          height: 16px;
          line-height: 16px;
          margin-right: 4px;
        }
      }
    }
  }

  // 光源编辑器
  .light-editor {
    height: 100%;
    overflow-y: auto;

    .editor-header {
      margin-bottom: 16px;

      .ant-typography {
        margin-bottom: 4px;
      }

      .ant-tag {
        margin-left: 8px;
      }
    }

    .no-light-selected {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #d9d9d9;
    }

    .ant-form {
      .ant-form-item {
        margin-bottom: 16px;

        .ant-form-item-label {
          font-weight: 500;
        }
      }

      .ant-slider {
        margin: 8px 0;
      }

      .ant-color-picker {
        width: 100%;
      }
    }

    .light-stats {
      margin-top: 16px;
      padding: 12px;
      background: #f9f9f9;
      border-radius: 4px;

      .ant-progress {
        margin-top: 4px;
      }
    }
  }

  // 全局光照设置
  .global-illumination {
    height: 100%;
    overflow-y: auto;

    .ant-card {
      .ant-card-head {
        .ant-card-head-title {
          font-size: 14px;
          font-weight: 600;
        }
      }

      .ant-card-body {
        padding: 16px;
      }

      .ant-form {
        .ant-form-item {
          margin-bottom: 12px;

          .ant-form-item-label {
            font-weight: 500;
          }
        }

        .ant-slider {
          margin: 8px 0;
        }
      }

      .ant-btn {
        margin-top: 8px;
      }
    }
  }
}

// 光源类型图标颜色
.lighting-panel {
  .light-type-directional {
    color: #faad14;
  }

  .light-type-point {
    color: #1890ff;
  }

  .light-type-spot {
    color: #ff4d4f;
  }

  .light-type-area {
    color: #52c41a;
  }

  .light-type-ambient {
    color: #722ed1;
  }

  .light-type-hemisphere {
    color: #eb2f96;
  }

  .light-type-ibl {
    color: #13c2c2;
  }

  .light-type-volumetric {
    color: #fa8c16;
  }
}

// IBL环境选择对话框
.ibl-modal {
  .ant-card {
    .ant-card-cover {
      height: 120px;
      overflow: hidden;
      background: linear-gradient(45deg, #1890ff, #52c41a);
      border-radius: 6px 6px 0 0;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .ant-card-meta {
      .ant-card-meta-title {
        font-size: 14px;
      }

      .ant-card-meta-description {
        font-size: 12px;

        .ant-tag {
          margin-bottom: 2px;
        }
      }
    }
  }
}

// 深色主题
.dark-theme {
  .lighting-panel {
    .ant-modal-content {
      background: #2d2d2d;
      color: #cccccc;
    }

    .lights-list {
      .lights-header {
        border-bottom-color: #404040;
      }

      .ant-tree {
        .ant-tree-node-content-wrapper {
          &:hover {
            background-color: #404040;
          }

          &.ant-tree-node-selected {
            background-color: #1f3a5f;
          }
        }

        .light-node {
          span {
            color: #ffffff;
          }
        }
      }
    }

    .light-editor {
      .editor-header {
        .ant-typography {
          color: #ffffff;
        }
      }

      .no-light-selected {
        color: #666666;
      }

      .light-stats {
        background: #404040;
        color: #cccccc;
      }
    }

    .global-illumination {
      .ant-card {
        background: #2d2d2d;
        border-color: #404040;

        .ant-card-head {
          background: #2d2d2d;
          border-bottom-color: #404040;

          .ant-card-head-title {
            color: #ffffff;
          }
        }

        .ant-card-body {
          background: #2d2d2d;
          color: #cccccc;
        }
      }
    }
  }
}

// 紧凑模式
.compact-theme {
  .lighting-panel {
    .ant-modal-body {
      padding: 12px;
    }

    .lights-list {
      .lights-header {
        margin-bottom: 12px;
        padding-bottom: 8px;
      }

      .ant-tree {
        .light-node {
          span {
            font-size: 12px;
          }
        }
      }
    }

    .light-editor {
      .editor-header {
        margin-bottom: 12px;
      }

      .ant-form {
        .ant-form-item {
          margin-bottom: 12px;
        }
      }

      .light-stats {
        margin-top: 12px;
        padding: 8px;
      }
    }

    .global-illumination {
      .ant-card {
        .ant-card-body {
          padding: 12px;
        }

        .ant-form {
          .ant-form-item {
            margin-bottom: 8px;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .lighting-panel {
    .ant-modal {
      width: 95% !important;
      max-width: none;
    }
  }
}

@media (max-width: 768px) {
  .lighting-panel {
    .ant-row {
      .ant-col {
        margin-bottom: 16px;
      }
    }

    .lights-list {
      .lights-header {
        .ant-space {
          flex-direction: column;
          align-items: stretch;
          width: 100%;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .lighting-panel {
    .light-editor {
      .ant-form {
        .ant-row {
          .ant-col {
            flex: 0 0 100%;
            max-width: 100%;
          }
        }
      }
    }

    .global-illumination {
      .ant-card {
        .ant-form {
          .ant-row {
            .ant-col {
              flex: 0 0 100%;
              max-width: 100%;
            }
          }
        }
      }
    }
  }
}

// 动画效果
.lighting-panel {
  .lights-list {
    .ant-tree {
      .ant-tree-node-content-wrapper {
        transition: all 0.2s ease;
      }

      .light-node {
        .ant-space {
          transition: opacity 0.2s ease;
        }
      }
    }
  }

  .ibl-modal {
    .ant-card {
      transition: all 0.3s ease;
    }
  }

  .ant-btn {
    transition: all 0.2s ease;
  }

  .ant-slider {
    .ant-slider-handle {
      transition: all 0.2s ease;
    }
  }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
  .lighting-panel {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }

    .ibl-modal {
      .ant-card {
        &:hover {
          transform: none;
        }
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .lighting-panel {
    .lights-list {
      .ant-tree {
        .ant-tree-node-content-wrapper {
          border: 1px solid #000;

          &.ant-tree-node-selected {
            border-color: #0066cc;
          }
        }
      }
    }

    .light-editor {
      .light-stats {
        border: 2px solid #000;
      }
    }

    .global-illumination {
      .ant-card {
        border: 2px solid #000;
      }
    }
  }
}

// 光照面板特定样式
.lighting-panel {
  .shadow-quality-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: 4px;

    &.none {
      background: #d9d9d9;
    }

    &.basic {
      background: #faad14;
    }

    &.pcf {
      background: #1890ff;
    }

    &.pcss {
      background: #52c41a;
    }

    &.raytraced {
      background: #722ed1;
    }
  }

  .performance-indicator {
    &.excellent {
      color: #52c41a;
    }

    &.good {
      color: #faad14;
    }

    &.poor {
      color: #ff4d4f;
    }
  }

  .light-enabled {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 2px;
      right: 2px;
      width: 6px;
      height: 6px;
      background: #52c41a;
      border-radius: 50%;
    }
  }

  .gi-type-indicator {
    &.lightmaps {
      color: #1890ff;
    }

    &.light-probes {
      color: #52c41a;
    }

    &.voxel-gi {
      color: #722ed1;
    }

    &.screen-space-gi {
      color: #faad14;
    }

    &.raytraced-gi {
      color: #ff4d4f;
    }
  }

  .baking-progress {
    position: relative;
    overflow: hidden;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, transparent, rgba(24, 144, 255, 0.3), transparent);
      animation: baking 3s infinite;
    }
  }

  .probe-visualization {
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 12px;
      height: 12px;
      background: radial-gradient(circle, #52c41a 30%, transparent 70%);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      animation: probe-pulse 2s infinite;
    }
  }
}

@keyframes baking {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes probe-pulse {
  0%, 100% {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
}
