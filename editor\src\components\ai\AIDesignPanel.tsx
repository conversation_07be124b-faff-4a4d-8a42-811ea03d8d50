/**
 * AI设计助手面板
 * 显示AI分析结果和设计建议
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  List,
  Button,
  Space,
  Tag,
  Progress,
  Tooltip,
  Badge,
  Tabs,
  Avatar,
  Divider,
  Alert,
  Spin,
  Empty,
  Typography,
  Rate,
  Switch
} from 'antd';
import {
  BulbOutlined,
  RobotOutlined,
  CheckCircleOutlined,
  ThunderboltOutlined,
  EyeOutlined,
  SettingOutlined,
  ReloadOutlined,
  StarOutlined,
  TrophyOutlined
} from '@ant-design/icons';
import {
  AIDesignAssistant,
  AISuggestion,
  AISuggestionType,
  AISuggestionPriority,
  DesignPattern
} from '../../services/AIDesignAssistant';
import { UIElementData } from '../ui/UIVisualEditor';
import './AIDesignPanel.less';

// const { Panel } = Collapse; // 暂未使用
const { TabPane } = Tabs;
const { Text, Title } = Typography;

// AI面板属性
interface AIDesignPanelProps {
  elements: UIElementData[];
  onApplySuggestion?: (suggestion: AISuggestion) => void;
  onApplyPattern?: (pattern: DesignPattern) => void;
  visible?: boolean;
  onClose?: () => void;
}

// 建议类型配置
const SUGGESTION_TYPE_CONFIG = {
  [AISuggestionType.LAYOUT_OPTIMIZATION]: {
    label: '布局优化',
    icon: <ThunderboltOutlined />,
    color: '#1890ff'
  },
  [AISuggestionType.COLOR_HARMONY]: {
    label: '色彩和谐',
    icon: <StarOutlined />,
    color: '#722ed1'
  },
  [AISuggestionType.SPACING_CONSISTENCY]: {
    label: '间距一致',
    icon: <CheckCircleOutlined />,
    color: '#52c41a'
  },
  [AISuggestionType.ACCESSIBILITY]: {
    label: '无障碍性',
    icon: <EyeOutlined />,
    color: '#fa8c16'
  },
  [AISuggestionType.RESPONSIVE_DESIGN]: {
    label: '响应式设计',
    icon: <ThunderboltOutlined />,
    color: '#13c2c2'
  },
  [AISuggestionType.COMPONENT_GROUPING]: {
    label: '组件分组',
    icon: <CheckCircleOutlined />,
    color: '#eb2f96'
  },
  [AISuggestionType.DESIGN_PATTERN]: {
    label: '设计模式',
    icon: <TrophyOutlined />,
    color: '#faad14'
  },
  [AISuggestionType.PERFORMANCE]: {
    label: '性能优化',
    icon: <ThunderboltOutlined />,
    color: '#f5222d'
  }
};

// 优先级配置
const PRIORITY_CONFIG = {
  [AISuggestionPriority.CRITICAL]: { label: '紧急', color: '#ff4d4f' },
  [AISuggestionPriority.HIGH]: { label: '高', color: '#ff7a45' },
  [AISuggestionPriority.MEDIUM]: { label: '中', color: '#faad14' },
  [AISuggestionPriority.LOW]: { label: '低', color: '#52c41a' }
};

export const AIDesignPanel: React.FC<AIDesignPanelProps> = ({
  elements,
  onApplySuggestion,
  onApplyPattern: _onApplyPattern,
  visible = true,
  onClose: _onClose
}) => {
  // const { t } = useTranslation(); // 暂未使用
  
  // 状态管理
  const [suggestions, setSuggestions] = useState<AISuggestion[]>([]);
  const [_patterns, _setPatterns] = useState<DesignPattern[]>([]);
  const [_loading, _setLoading] = useState(false);
  const [analyzing, setAnalyzing] = useState(false);
  const [aiEnabled, setAiEnabled] = useState(true);
  const [activeTab, setActiveTab] = useState('suggestions');
  const [appliedSuggestions, setAppliedSuggestions] = useState<Set<string>>(new Set());

  // AI助手实例
  const [aiAssistant] = useState(() => new AIDesignAssistant());

  // 分析设计
  const analyzeDesign = async () => {
    if (!aiEnabled || elements.length === 0) {
      setSuggestions([]);
      return;
    }

    setAnalyzing(true);
    try {
      const [newSuggestions, newPatterns] = await Promise.all([
        aiAssistant.analyzeDesign(elements),
        aiAssistant.recommendDesignPatterns(elements, 'ui_editor')
      ]);
      
      setSuggestions(newSuggestions);
      _setPatterns(newPatterns);
    } catch (error) {
      console.error('AI分析失败:', error);
    } finally {
      setAnalyzing(false);
    }
  };

  // 监听元素变化
  useEffect(() => {
    const timer = setTimeout(() => {
      analyzeDesign();
    }, 1000); // 防抖

    return () => clearTimeout(timer);
  }, [elements, aiEnabled]);

  // 应用建议
  const handleApplySuggestion = (suggestion: AISuggestion) => {
    setAppliedSuggestions(prev => new Set([...prev, suggestion.id]));
    onApplySuggestion?.(suggestion);
  };

  // 应用设计模式 (暂未使用)
  // const handleApplyPattern = (pattern: DesignPattern) => {
  //   onApplyPattern?.(pattern);
  // };

  // 渲染建议卡片
  const renderSuggestionCard = (suggestion: AISuggestion) => {
    const typeConfig = SUGGESTION_TYPE_CONFIG[suggestion.type];
    const priorityConfig = PRIORITY_CONFIG[suggestion.priority];
    const isApplied = appliedSuggestions.has(suggestion.id);

    return (
      <Card
        key={suggestion.id}
        size="small"
        className={`suggestion-card ${isApplied ? 'applied' : ''}`}
        title={
          <div className="suggestion-header">
            <Space>
              <Avatar
                size="small"
                icon={typeConfig.icon}
                style={{ backgroundColor: typeConfig.color }}
              />
              <span>{suggestion.title}</span>
              <Tag color={priorityConfig.color}>
                {priorityConfig.label}
              </Tag>
            </Space>
            <div className="suggestion-confidence">
              <Rate
                disabled
                count={5}
                value={suggestion.confidence * 5}
                style={{ fontSize: '12px' }}
              />
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {(suggestion.confidence * 100).toFixed(0)}%
              </Text>
            </div>
          </div>
        }
        extra={
          <Space>
            {suggestion.preview && (
              <Tooltip title="预览效果">
                <Button
                  type="text"
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={() => {
                    // 显示预览
                  }}
                />
              </Tooltip>
            )}
            <Button
              type="primary"
              size="small"
              disabled={isApplied}
              onClick={() => handleApplySuggestion(suggestion)}
            >
              {isApplied ? '已应用' : '应用'}
            </Button>
          </Space>
        }
      >
        <div className="suggestion-content">
          <Text>{suggestion.description}</Text>
          
          <Divider />
          
          <div className="suggestion-details">
            <div className="detail-item">
              <Text strong>分析原因:</Text>
              <Text type="secondary">{suggestion.reasoning}</Text>
            </div>
            
            <div className="detail-item">
              <Text strong>预期效果:</Text>
              <Text type="secondary">{suggestion.impact}</Text>
            </div>
            
            {suggestion.actions && suggestion.actions.length > 0 && (
              <div className="detail-item">
                <Text strong>具体操作:</Text>
                <List
                  size="small"
                  dataSource={suggestion.actions}
                  renderItem={(action) => (
                    <List.Item>
                      <Text>{action.description}</Text>
                    </List.Item>
                  )}
                />
              </div>
            )}
          </div>
        </div>
      </Card>
    );
  };

  // 渲染分析概览
  const renderAnalysisOverview = () => {
    const totalSuggestions = suggestions.length;
    const criticalSuggestions = suggestions.filter(s => s.priority === AISuggestionPriority.CRITICAL).length;
    const highSuggestions = suggestions.filter(s => s.priority === AISuggestionPriority.HIGH).length;
    const appliedCount = appliedSuggestions.size;

    return (
      <Card size="small" className="analysis-overview">
        <div className="overview-stats">
          <div className="stat-item">
            <div className="stat-value">{totalSuggestions}</div>
            <div className="stat-label">总建议数</div>
          </div>
          <div className="stat-item">
            <div className="stat-value critical">{criticalSuggestions}</div>
            <div className="stat-label">紧急</div>
          </div>
          <div className="stat-item">
            <div className="stat-value high">{highSuggestions}</div>
            <div className="stat-label">高优先级</div>
          </div>
          <div className="stat-item">
            <div className="stat-value applied">{appliedCount}</div>
            <div className="stat-label">已应用</div>
          </div>
        </div>
        
        {totalSuggestions > 0 && (
          <div className="overview-progress">
            <Text style={{ fontSize: '12px' }}>优化进度:</Text>
            <Progress
              percent={(appliedCount / totalSuggestions) * 100}
              size="small"
              status={appliedCount === totalSuggestions ? 'success' : 'active'}
            />
          </div>
        )}
      </Card>
    );
  };

  if (!visible) {
    return null;
  }

  return (
    <div className="ai-design-panel">
      <div className="panel-header">
        <div className="header-title">
          <Space>
            <RobotOutlined />
            <Title level={5}>AI设计助手</Title>
            <Badge count={suggestions.length} showZero />
          </Space>
        </div>
        
        <div className="header-actions">
          <Space>
            <Tooltip title="启用/禁用AI助手">
              <Switch
                checked={aiEnabled}
                onChange={setAiEnabled}
                size="small"
              />
            </Tooltip>
            <Tooltip title="重新分析">
              <Button
                type="text"
                size="small"
                icon={<ReloadOutlined />}
                loading={analyzing}
                onClick={analyzeDesign}
              />
            </Tooltip>
            <Tooltip title="设置">
              <Button
                type="text"
                size="small"
                icon={<SettingOutlined />}
              />
            </Tooltip>
          </Space>
        </div>
      </div>

      <div className="panel-content">
        {!aiEnabled ? (
          <Alert
            message="AI助手已禁用"
            description="启用AI助手以获取智能设计建议"
            type="info"
            showIcon
          />
        ) : analyzing ? (
          <div className="analyzing-state">
            <Spin size="large" />
            <Text>AI正在分析您的设计...</Text>
          </div>
        ) : elements.length === 0 ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="添加UI元素后，AI将为您提供设计建议"
          />
        ) : (
          <>
            {renderAnalysisOverview()}
            
            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              size="small"
              className="ai-tabs"
            >
              <TabPane
                tab={
                  <Space>
                    <BulbOutlined />
                    设计建议
                    <Badge count={suggestions.length} size="small" />
                  </Space>
                }
                key="suggestions"
              >
                {suggestions.length === 0 ? (
                  <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description="当前设计很棒，暂无优化建议"
                  />
                ) : (
                  <div className="suggestions-list">
                    {suggestions.map(renderSuggestionCard)}
                  </div>
                )}
              </TabPane>
            </Tabs>
          </>
        )}
      </div>
    </div>
  );
};

export default AIDesignPanel;
