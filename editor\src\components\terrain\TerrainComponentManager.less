/**
 * 地形组件管理器样式
 */
.terrain-component-manager {
  .ant-card {
    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      
      .ant-card-head-title {
        font-weight: 600;
        color: #262626;
      }
    }

    .ant-card-body {
      padding: 16px;
    }
  }

  .no-terrain-selected {
    text-align: center;
    padding: 40px 20px;
    color: #8c8c8c;

    p {
      margin-bottom: 16px;
      font-size: 14px;
    }
  }

  .ant-tabs {
    .ant-tabs-tab {
      font-weight: 500;
      
      &.ant-tabs-tab-active {
        .ant-tabs-tab-btn {
          color: #1890ff;
        }
      }
    }

    .ant-tabs-content-holder {
      padding-top: 16px;
    }
  }

  // 模态框样式
  .ant-modal {
    .ant-modal-header {
      border-bottom: 1px solid #f0f0f0;
      
      .ant-modal-title {
        font-weight: 600;
        color: #262626;
      }
    }

    .ant-modal-body {
      padding: 24px;
    }

    .ant-form {
      .ant-form-item {
        margin-bottom: 16px;

        .ant-form-item-label {
          > label {
            font-weight: 500;
            color: #262626;
          }
        }

        .ant-input-number {
          width: 100%;
        }

        .ant-select {
          width: 100%;
        }
      }

      .ant-divider {
        margin: 24px 0 16px 0;
      }
    }
  }

  // 文件上传样式
  input[type="file"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background-color: #fafafa;
    transition: all 0.3s;

    &:hover {
      border-color: #40a9ff;
    }

    &:focus {
      border-color: #1890ff;
      outline: 0;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .ant-card-head-extra {
      .ant-space {
        flex-wrap: wrap;
        gap: 8px !important;
      }

      .ant-btn {
        font-size: 12px;
        padding: 4px 8px;
        height: auto;

        .anticon {
          font-size: 12px;
        }
      }
    }

    .ant-tabs {
      .ant-tabs-tab {
        font-size: 12px;
        padding: 8px 12px;
      }
    }

    .no-terrain-selected {
      padding: 20px 10px;

      p {
        font-size: 12px;
      }

      .ant-btn {
        font-size: 12px;
        padding: 4px 8px;
        height: auto;
      }
    }
  }

  // 暗色主题支持
  .dark-theme & {
    .ant-card {
      background-color: #1f1f1f;
      border-color: #303030;

      .ant-card-head {
        background-color: #1f1f1f;
        border-bottom-color: #303030;

        .ant-card-head-title {
          color: #ffffff;
        }
      }

      .ant-card-body {
        background-color: #1f1f1f;
      }
    }

    .no-terrain-selected {
      color: #8c8c8c;
    }

    .ant-tabs {
      .ant-tabs-tab {
        color: #ffffff;

        &.ant-tabs-tab-active {
          .ant-tabs-tab-btn {
            color: #1890ff;
          }
        }
      }

      .ant-tabs-ink-bar {
        background-color: #1890ff;
      }
    }

    .ant-modal {
      .ant-modal-content {
        background-color: #1f1f1f;
      }

      .ant-modal-header {
        background-color: #1f1f1f;
        border-bottom-color: #303030;

        .ant-modal-title {
          color: #ffffff;
        }
      }

      .ant-modal-body {
        background-color: #1f1f1f;
      }

      .ant-form {
        .ant-form-item-label {
          > label {
            color: #ffffff;
          }
        }
      }
    }

    input[type="file"] {
      background-color: #262626;
      border-color: #434343;
      color: #ffffff;

      &:hover {
        border-color: #40a9ff;
      }

      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }
  }
}
