/**
 * 自动优化服务
 * 提供一键优化功能，自动应用优化建议
 */
import { EventEmitter } from '../utils/EventEmitter';
import SceneOptimizationService, { OptimizationSuggestion, OptimizationType } from './SceneOptimizationService';

// 优化任务接口
export interface OptimizationTask {
  id: string;
  type: OptimizationType;
  title: string;
  description: string;
  status: OptimizationTaskStatus;
  progress: number;
  startTime?: number;
  endTime?: number;
  error?: string;
  result?: OptimizationResult;
}

// 优化任务状态枚举
export enum OptimizationTaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// 优化结果接口
export interface OptimizationResult {
  success: boolean;
  improvements: {
    fps: number;
    memory: number;
    drawCalls: number;
    triangles: number;
  };
  appliedChanges: string[];
  warnings: string[];
}

// 批量优化配置接口
export interface BatchOptimizationConfig {
  enableGeometryOptimization: boolean;
  enableTextureOptimization: boolean;
  enableMaterialOptimization: boolean;
  enableLightingOptimization: boolean;
  enableBatchingOptimization: boolean;
  enableLODOptimization: boolean;
  aggressiveMode: boolean;
  preserveQuality: boolean;
  maxProcessingTime: number; // 最大处理时间(ms)
}

// 优化统计接口
export interface OptimizationStats {
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  totalImprovements: {
    fps: number;
    memory: number;
    drawCalls: number;
    triangles: number;
  };
  processingTime: number;
}

/**
 * 自动优化服务类
 */
export class AutoOptimizationService extends EventEmitter {
  private static instance: AutoOptimizationService;
  private optimizationTasks: Map<string, OptimizationTask> = new Map();
  private isOptimizing: boolean = false;
  private config: BatchOptimizationConfig;
  private sceneOptimizationService: SceneOptimizationService;

  private constructor() {
    super();
    this.config = this.getDefaultConfig();
    this.sceneOptimizationService = SceneOptimizationService.getInstance();
  }

  public static getInstance(): AutoOptimizationService {
    if (!AutoOptimizationService.instance) {
      AutoOptimizationService.instance = new AutoOptimizationService();
    }
    return AutoOptimizationService.instance;
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): BatchOptimizationConfig {
    return {
      enableGeometryOptimization: true,
      enableTextureOptimization: true,
      enableMaterialOptimization: true,
      enableLightingOptimization: false, // 需要手动确认
      enableBatchingOptimization: true,
      enableLODOptimization: false, // 需要手动确认
      aggressiveMode: false,
      preserveQuality: true,
      maxProcessingTime: 30000 // 30秒
    };
  }

  /**
   * 一键优化场景
   */
  public async optimizeScene(): Promise<OptimizationStats> {
    if (this.isOptimizing) {
      throw new Error('Optimization already in progress');
    }

    this.isOptimizing = true;
    this.emit('optimizationStarted');

    try {
      // 分析场景获取优化建议
      const analysisResult = this.sceneOptimizationService.analyzeScene();
      const suggestions = analysisResult.optimizationSuggestions.filter(s => s.autoFixAvailable);

      // 创建优化任务
      const tasks = this.createOptimizationTasks(suggestions);
      
      // 执行优化任务
      const stats = await this.executeTasks(tasks);

      this.emit('optimizationCompleted', stats);
      return stats;
    } catch (error) {
      this.emit('optimizationFailed', error);
      throw error;
    } finally {
      this.isOptimizing = false;
    }
  }

  /**
   * 创建优化任务
   */
  private createOptimizationTasks(suggestions: OptimizationSuggestion[]): OptimizationTask[] {
    const tasks: OptimizationTask[] = [];

    suggestions.forEach(suggestion => {
      if (this.shouldApplyOptimization(suggestion.type)) {
        const task: OptimizationTask = {
          id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: suggestion.type,
          title: suggestion.title,
          description: suggestion.description,
          status: OptimizationTaskStatus.PENDING,
          progress: 0
        };

        tasks.push(task);
        this.optimizationTasks.set(task.id, task);
      }
    });

    return tasks;
  }

  /**
   * 检查是否应该应用优化
   */
  private shouldApplyOptimization(type: OptimizationType): boolean {
    switch (type) {
      case OptimizationType.GEOMETRY_OPTIMIZATION:
        return this.config.enableGeometryOptimization;
      case OptimizationType.TEXTURE_OPTIMIZATION:
        return this.config.enableTextureOptimization;
      case OptimizationType.MATERIAL_OPTIMIZATION:
        return this.config.enableMaterialOptimization;
      case OptimizationType.LIGHTING_OPTIMIZATION:
        return this.config.enableLightingOptimization;
      case OptimizationType.BATCHING_OPTIMIZATION:
        return this.config.enableBatchingOptimization;
      case OptimizationType.LOD_OPTIMIZATION:
        return this.config.enableLODOptimization;
      default:
        return false;
    }
  }

  /**
   * 执行优化任务
   */
  private async executeTasks(tasks: OptimizationTask[]): Promise<OptimizationStats> {
    const startTime = Date.now();
    let completedTasks = 0;
    let failedTasks = 0;
    const totalImprovements = {
      fps: 0,
      memory: 0,
      drawCalls: 0,
      triangles: 0
    };

    for (const task of tasks) {
      try {
        await this.executeTask(task);
        
        if (task.status === OptimizationTaskStatus.COMPLETED && task.result) {
          completedTasks++;
          totalImprovements.fps += task.result.improvements.fps;
          totalImprovements.memory += task.result.improvements.memory;
          totalImprovements.drawCalls += task.result.improvements.drawCalls;
          totalImprovements.triangles += task.result.improvements.triangles;
        } else {
          failedTasks++;
        }
      } catch (error) {
        failedTasks++;
        task.status = OptimizationTaskStatus.FAILED;
        task.error = error instanceof Error ? error.message : 'Unknown error';
      }

      this.emit('taskCompleted', task);
    }

    const processingTime = Date.now() - startTime;

    return {
      totalTasks: tasks.length,
      completedTasks,
      failedTasks,
      totalImprovements,
      processingTime
    };
  }

  /**
   * 执行单个优化任务
   */
  private async executeTask(task: OptimizationTask): Promise<void> {
    task.status = OptimizationTaskStatus.RUNNING;
    task.startTime = Date.now();
    task.progress = 0;

    this.emit('taskStarted', task);

    try {
      const result = await this.applyOptimization(task);
      
      task.status = OptimizationTaskStatus.COMPLETED;
      task.progress = 100;
      task.endTime = Date.now();
      task.result = result;
    } catch (error) {
      task.status = OptimizationTaskStatus.FAILED;
      task.endTime = Date.now();
      task.error = error instanceof Error ? error.message : 'Unknown error';
      throw error;
    }
  }

  /**
   * 应用具体的优化
   */
  private async applyOptimization(task: OptimizationTask): Promise<OptimizationResult> {
    // 模拟优化过程
    await this.simulateOptimizationProgress(task);

    switch (task.type) {
      case OptimizationType.GEOMETRY_OPTIMIZATION:
        return this.optimizeGeometry();
      case OptimizationType.TEXTURE_OPTIMIZATION:
        return this.optimizeTextures();
      case OptimizationType.MATERIAL_OPTIMIZATION:
        return this.optimizeMaterials();
      case OptimizationType.LIGHTING_OPTIMIZATION:
        return this.optimizeLighting();
      case OptimizationType.BATCHING_OPTIMIZATION:
        return this.optimizeBatching();
      case OptimizationType.LOD_OPTIMIZATION:
        return this.optimizeLOD();
      default:
        throw new Error(`Unsupported optimization type: ${task.type}`);
    }
  }

  /**
   * 模拟优化进度
   */
  private async simulateOptimizationProgress(task: OptimizationTask): Promise<void> {
    const steps = 10;
    const stepDelay = 200; // 200ms per step

    for (let i = 1; i <= steps; i++) {
      await new Promise(resolve => setTimeout(resolve, stepDelay));
      task.progress = (i / steps) * 100;
      this.emit('taskProgress', task);
    }
  }

  /**
   * 几何优化
   */
  private async optimizeGeometry(): Promise<OptimizationResult> {
    // 模拟几何优化
    return {
      success: true,
      improvements: {
        fps: Math.floor(Math.random() * 15) + 5,
        memory: Math.floor(Math.random() * 50) * 1024 * 1024,
        drawCalls: 0,
        triangles: Math.floor(Math.random() * 10000) + 5000
      },
      appliedChanges: [
        'Simplified high-poly models',
        'Removed unnecessary vertices',
        'Optimized mesh topology'
      ],
      warnings: []
    };
  }

  /**
   * 纹理优化
   */
  private async optimizeTextures(): Promise<OptimizationResult> {
    return {
      success: true,
      improvements: {
        fps: Math.floor(Math.random() * 8) + 2,
        memory: Math.floor(Math.random() * 100) * 1024 * 1024,
        drawCalls: Math.floor(Math.random() * 20) + 5,
        triangles: 0
      },
      appliedChanges: [
        'Compressed textures using DXT format',
        'Reduced texture resolution',
        'Created texture atlases'
      ],
      warnings: [
        'Some texture quality may be reduced'
      ]
    };
  }

  /**
   * 材质优化
   */
  private async optimizeMaterials(): Promise<OptimizationResult> {
    return {
      success: true,
      improvements: {
        fps: Math.floor(Math.random() * 5) + 2,
        memory: Math.floor(Math.random() * 20) * 1024 * 1024,
        drawCalls: Math.floor(Math.random() * 15) + 5,
        triangles: 0
      },
      appliedChanges: [
        'Merged similar materials',
        'Removed unused materials',
        'Optimized shader complexity'
      ],
      warnings: []
    };
  }

  /**
   * 光照优化
   */
  private async optimizeLighting(): Promise<OptimizationResult> {
    return {
      success: true,
      improvements: {
        fps: Math.floor(Math.random() * 12) + 3,
        memory: Math.floor(Math.random() * 30) * 1024 * 1024,
        drawCalls: 0,
        triangles: 0
      },
      appliedChanges: [
        'Baked static lighting',
        'Reduced dynamic light count',
        'Optimized shadow settings'
      ],
      warnings: [
        'Dynamic lighting effects may be reduced'
      ]
    };
  }

  /**
   * 批处理优化
   */
  private async optimizeBatching(): Promise<OptimizationResult> {
    return {
      success: true,
      improvements: {
        fps: Math.floor(Math.random() * 20) + 10,
        memory: 0,
        drawCalls: Math.floor(Math.random() * 200) + 100,
        triangles: 0
      },
      appliedChanges: [
        'Batched similar objects',
        'Implemented instancing',
        'Merged static geometries'
      ],
      warnings: []
    };
  }

  /**
   * LOD优化
   */
  private async optimizeLOD(): Promise<OptimizationResult> {
    return {
      success: true,
      improvements: {
        fps: Math.floor(Math.random() * 18) + 7,
        memory: Math.floor(Math.random() * 40) * 1024 * 1024,
        drawCalls: 0,
        triangles: Math.floor(Math.random() * 15000) + 8000
      },
      appliedChanges: [
        'Generated LOD models',
        'Implemented distance-based culling',
        'Optimized rendering pipeline'
      ],
      warnings: [
        'Distant objects may appear less detailed'
      ]
    };
  }

  /**
   * 取消优化
   */
  public cancelOptimization(): void {
    if (!this.isOptimizing) return;

    this.isOptimizing = false;
    
    // 取消所有待处理的任务
    for (const task of this.optimizationTasks.values()) {
      if (task.status === OptimizationTaskStatus.PENDING || task.status === OptimizationTaskStatus.RUNNING) {
        task.status = OptimizationTaskStatus.CANCELLED;
        task.endTime = Date.now();
      }
    }

    this.emit('optimizationCancelled');
  }

  /**
   * 获取优化任务列表
   */
  public getOptimizationTasks(): OptimizationTask[] {
    return Array.from(this.optimizationTasks.values());
  }

  /**
   * 清除任务历史
   */
  public clearTaskHistory(): void {
    this.optimizationTasks.clear();
    this.emit('taskHistoryCleared');
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<BatchOptimizationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('configUpdated', this.config);
  }

  /**
   * 获取配置
   */
  public getConfig(): BatchOptimizationConfig {
    return { ...this.config };
  }

  /**
   * 检查是否正在优化
   */
  public isOptimizationInProgress(): boolean {
    return this.isOptimizing;
  }
}

export default AutoOptimizationService;
