/**
 * 编辑器主页面
 */
import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Tooltip, message, Modal } from 'antd';
import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  SaveOutlined,
  PlayCircleOutlined,
  SettingOutlined,
  ExportOutlined,
  ImportOutlined,
  UndoOutlined,
  RedoOutlined,
  PlusOutlined,
  DeleteOutlined,
  CopyOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  AppstoreOutlined,
  BookOutlined} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { toggleSidebar, togglePanel } from '../../store/ui/uiSlice';
import ScenePanel from '../../components/ScenePanel';
import PropertiesPanel from '../../components/PropertiesPanel';
import AssetsPanel from '../../components/AssetsPanel';
import Viewport from '../../components/Viewport';
import { ExampleBrowser } from '../../components/ExampleBrowser';
import './Editor.less';

const { Header, Sider, Content } = Layout;

const Editor: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { sidebarCollapsed } = useSelector((state: RootState) => state.ui);
  const [activePanel, setActivePanel] = useState<string>('scene');
  const [exampleBrowserVisible, setExampleBrowserVisible] = useState<boolean>(false);

  // 监听快捷键事件
  useEffect(() => {
    const handleShortcutAction = (event: CustomEvent) => {
      const { actionId } = event.detail;

      switch (actionId) {
        case 'nlp.openSceneGenerator':
          dispatch(togglePanel('nlpSceneGeneration'));
          message.info('打开自然语言场景生成面板');
          break;
        case 'nlp.quickGenerate':
          // 快速生成对话框
          Modal.confirm({
            title: '快速场景生成',
            content: '请输入场景描述',
            onOk: () => {
              // 这里可以打开快速生成对话框
              dispatch(togglePanel('nlpSceneGeneration'));
            }
          });
          break;
        default:
          break;
      }
    };

    // 添加事件监听器
    document.addEventListener('shortcut-action', handleShortcutAction as EventListener);

    // 清理函数
    return () => {
      document.removeEventListener('shortcut-action', handleShortcutAction as EventListener);
    };
  }, [dispatch]);

  // 菜单项
  const menuItems = [
    {
      key: 'file',
      label: t('editor.menu.file'),
      children: [
        {
          key: 'new',
          label: t('editor.menu.new'),
          icon: <PlusOutlined />},
        {
          key: 'open',
          label: t('editor.menu.open'),
          icon: <ImportOutlined />},
        {
          key: 'save',
          label: t('editor.menu.save'),
          icon: <SaveOutlined />},
        {
          key: 'export',
          label: t('editor.menu.export'),
          icon: <ExportOutlined />},
      ]},
    {
      key: 'edit',
      label: t('editor.menu.edit'),
      children: [
        {
          key: 'undo',
          label: t('editor.menu.undo'),
          icon: <UndoOutlined />},
        {
          key: 'redo',
          label: t('editor.menu.redo'),
          icon: <RedoOutlined />},
        {
          key: 'copy',
          label: t('editor.menu.copy'),
          icon: <CopyOutlined />},
        {
          key: 'delete',
          label: t('editor.menu.delete'),
          icon: <DeleteOutlined />},
      ]},
    {
      key: 'view',
      label: t('editor.menu.view'),
      children: [
        {
          key: 'show',
          label: t('editor.menu.show'),
          icon: <EyeOutlined />},
        {
          key: 'hide',
          label: t('editor.menu.hide'),
          icon: <EyeInvisibleOutlined />},
      ]},
    {
      key: 'examples',
      label: t('editor.menu.examples'),
      icon: <AppstoreOutlined />},
    {
      key: 'tutorials',
      label: t('editor.menu.tutorials'),
      icon: <BookOutlined />},
    {
      key: 'settings',
      label: t('editor.menu.settings'),
      icon: <SettingOutlined />},
  ];

  // 处理菜单点击
  const handleMenuClick = (e: { key: string }) => {
    switch (e.key) {
      case 'examples':
        setExampleBrowserVisible(true);
        break;
      case 'tutorials':
        // TODO: 打开教程浏览器
        message.info(t('editor.messages.tutorialsComingSoon'));
        break;
      default:
        message.info(`${t('editor.messages.clickedMenuItem')}: ${e.key}`);
        break;
    }
  };

  // 处理面板切换
  const handlePanelChange = (panel: string) => {
    setActivePanel(panel);
  };

  return (
    <Layout className="editor-layout">
      <Header className="editor-header">
        <div className="logo">DL（Digital Learning）引擎编辑器</div>
        <Menu
          theme="dark"
          mode="horizontal"
          items={menuItems}
          onClick={handleMenuClick}
        />
        <div className="header-actions">
          <Button type="primary" icon={<PlayCircleOutlined />}>
            {t('editor.preview')}
          </Button>
        </div>
      </Header>
      <Layout>
        <Sider
          width={250}
          collapsible
          collapsed={sidebarCollapsed}
          trigger={null}
          className="editor-sider"
        >
          <div className="sider-header">
            <Button
              type="text"
              icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => dispatch(toggleSidebar())}
            />
            <div className="panel-tabs">
              <Tooltip title={t('editor.panels.scene')}>
                <Button
                  type={activePanel === 'scene' ? 'primary' : 'text'}
                  onClick={() => handlePanelChange('scene')}
                >
                  {t('editor.panels.scene')}
                </Button>
              </Tooltip>
              <Tooltip title={t('editor.panels.assets')}>
                <Button
                  type={activePanel === 'assets' ? 'primary' : 'text'}
                  onClick={() => handlePanelChange('assets')}
                >
                  {t('editor.panels.assets')}
                </Button>
              </Tooltip>
            </div>
          </div>
          <div className="panel-content">
            {activePanel === 'scene' && <ScenePanel />}
            {activePanel === 'assets' && <AssetsPanel />}
          </div>
        </Sider>
        <Content className="editor-content">
          <Viewport />
        </Content>
        <Sider
          width={300}
          className="properties-sider"
          theme="light"
        >
          <PropertiesPanel />
        </Sider>
      </Layout>

      {/* 示例项目浏览器对话框 */}
      <Modal
        title={t('exampleBrowser.title')}
        open={exampleBrowserVisible}
        onCancel={() => setExampleBrowserVisible(false)}
        footer={null}
        width="90%"
        style={{ top: 20 }}
        styles={{ body: { padding: 0, height: 'calc(90vh - 55px)' } }}
      >
        <ExampleBrowser />
      </Modal>
    </Layout>
  );
};

export default Editor;
