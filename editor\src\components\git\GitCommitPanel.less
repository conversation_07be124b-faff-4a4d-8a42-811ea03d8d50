.git-commit-panel {
  .git-commit-card {
    .ant-card-body {
      padding: 16px;
    }
  }

  .git-commit-message {
    margin-bottom: 16px;

    .ant-input {
      resize: vertical;
    }
  }

  .git-file-list {
    margin-bottom: 16px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    max-height: 300px;
    overflow-y: auto;

    .git-file-list-header {
      padding: 8px 12px;
      background-color: #fafafa;
      border-bottom: 1px solid #d9d9d9;
      font-weight: 500;
    }

    .git-file-item {
      padding: 8px 12px;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .git-file-status {
        display: inline-block;
        width: 20px;
        text-align: center;
        font-weight: bold;
        margin-right: 8px;

        &.git-file-status-M {
          color: #faad14; // 修改
        }

        &.git-file-status-A {
          color: #52c41a; // 新增
        }

        &.git-file-status-D {
          color: #ff4d4f; // 删除
        }

        &.git-file-status-R {
          color: #1890ff; // 重命名
        }

        &.git-file-status-C {
          color: #722ed1; // 复制
        }

        &.git-file-status-U {
          color: #fa541c; // 未合并
        }
      }

      .git-file-path {
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 12px;
      }
    }
  }

  .git-no-changes {
    text-align: center;
    padding: 40px 20px;
    color: #999;
    font-style: italic;
  }

  .git-commit-actions {
    text-align: right;
  }
}
