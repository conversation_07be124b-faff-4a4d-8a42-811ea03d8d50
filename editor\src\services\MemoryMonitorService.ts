/**
 * 内存监控服务
 * 提供内存使用监控、泄漏检测和分析功能
 */
import { EventEmitter } from '../utils/EventEmitter';

// 内存快照接口
export interface MemorySnapshot {
  id: string;
  timestamp: number;
  jsHeapSizeLimit: number;
  totalJSHeapSize: number;
  usedJSHeapSize: number;
  objectCounts: ObjectCounts;
  references: ReferenceInfo[];
  gcInfo?: GCInfo;
}

// 对象计数接口
export interface ObjectCounts {
  total: number;
  byType: Record<string, number>;
  bySize: Record<string, number>;
  detached: number;
}

// 引用信息接口
export interface ReferenceInfo {
  id: string;
  type: string;
  size: number;
  retainedSize: number;
  references: string[];
  isDetached: boolean;
  path?: string;
}

// 垃圾回收信息接口
export interface GCInfo {
  type: string;
  duration: number;
  reclaimedBytes: number;
  timestamp: number;
}

// 内存泄漏检测结果接口
export interface MemoryLeakDetection {
  id: string;
  type: LeakType;
  severity: LeakSeverity;
  description: string;
  affectedObjects: string[];
  growthRate: number;
  suggestions: string[];
  timestamp: number;
}

// 泄漏类型枚举
export enum LeakType {
  DETACHED_DOM = 'detached_dom',
  EVENT_LISTENERS = 'event_listeners',
  CLOSURES = 'closures',
  TIMERS = 'timers',
  GLOBAL_VARIABLES = 'global_variables',
  CIRCULAR_REFERENCES = 'circular_references',
  LARGE_OBJECTS = 'large_objects'
}

// 泄漏严重程度枚举
export enum LeakSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// 内存分析配置接口
export interface MemoryAnalysisConfig {
  snapshotInterval: number; // 快照间隔(ms)
  maxSnapshots: number; // 最大快照数量
  leakDetectionThreshold: number; // 泄漏检测阈值(bytes)
  gcMonitoring: boolean; // 是否监控GC
  detailedAnalysis: boolean; // 是否进行详细分析
}

/**
 * 内存监控服务类
 */
export class MemoryMonitorService extends EventEmitter {
  private static instance: MemoryMonitorService;
  private isMonitoring: boolean = false;
  private snapshots: MemorySnapshot[] = [];
  private leakDetections: MemoryLeakDetection[] = [];
  private config: MemoryAnalysisConfig;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private gcObserver: PerformanceObserver | null = null;

  private constructor() {
    super();
    this.config = this.getDefaultConfig();
    this.setupGCObserver();
  }

  public static getInstance(): MemoryMonitorService {
    if (!MemoryMonitorService.instance) {
      MemoryMonitorService.instance = new MemoryMonitorService();
    }
    return MemoryMonitorService.instance;
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): MemoryAnalysisConfig {
    return {
      snapshotInterval: 5000, // 5秒
      maxSnapshots: 100,
      leakDetectionThreshold: 1024 * 1024, // 1MB
      gcMonitoring: true,
      detailedAnalysis: false
    };
  }

  /**
   * 设置GC观察器
   */
  private setupGCObserver(): void {
    if ('PerformanceObserver' in window && this.config.gcMonitoring) {
      try {
        this.gcObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach(entry => {
            if (entry.entryType === 'measure' && entry.name.includes('gc')) {
              this.handleGCEvent(entry);
            }
          });
        });
        
        this.gcObserver.observe({ entryTypes: ['measure'] });
      } catch (error) {
        console.warn('GC monitoring not supported:', error);
      }
    }
  }

  /**
   * 处理GC事件
   */
  private handleGCEvent(entry: PerformanceEntry): void {
    const gcInfo: GCInfo = {
      type: entry.name,
      duration: entry.duration,
      reclaimedBytes: 0, // 无法直接获取，需要估算
      timestamp: Date.now()
    };

    this.emit('gcEvent', gcInfo);
  }

  /**
   * 开始监控
   */
  public startMonitoring(): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;

    // 立即创建一个快照
    this.createSnapshot();

    // 启动定期快照
    this.monitoringInterval = setInterval(() => {
      this.createSnapshot();
      this.analyzeMemoryLeaks();
    }, this.config.snapshotInterval);

    this.emit('monitoringStarted');
  }

  /**
   * 停止监控
   */
  public stopMonitoring(): void {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    this.emit('monitoringStopped');
  }

  /**
   * 创建内存快照
   */
  public createSnapshot(): MemorySnapshot {
    const memory = (performance as any).memory;
    const snapshotId = `snapshot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const snapshot: MemorySnapshot = {
      id: snapshotId,
      timestamp: Date.now(),
      jsHeapSizeLimit: memory?.jsHeapSizeLimit || 0,
      totalJSHeapSize: memory?.totalJSHeapSize || 0,
      usedJSHeapSize: memory?.usedJSHeapSize || 0,
      objectCounts: this.analyzeObjectCounts(),
      references: this.config.detailedAnalysis ? this.analyzeReferences() : []
    };

    this.snapshots.push(snapshot);

    // 保持快照数量在限制内
    if (this.snapshots.length > this.config.maxSnapshots) {
      this.snapshots.shift();
    }

    this.emit('snapshotCreated', snapshot);
    return snapshot;
  }

  /**
   * 分析对象计数
   */
  private analyzeObjectCounts(): ObjectCounts {
    // 这里实现对象计数分析
    // 由于浏览器限制，我们只能提供估算值
    const counts: ObjectCounts = {
      total: 0,
      byType: {},
      bySize: {},
      detached: 0
    };

    // 分析全局对象
    this.analyzeGlobalObjects(counts);

    // 分析DOM节点
    this.analyzeDOMNodes(counts);

    return counts;
  }

  /**
   * 分析全局对象
   */
  private analyzeGlobalObjects(counts: ObjectCounts): void {
    try {
      const globalKeys = Object.keys(window);
      counts.total += globalKeys.length;

      globalKeys.forEach(key => {
        try {
          const obj = (window as any)[key];
          const type = typeof obj;
          counts.byType[type] = (counts.byType[type] || 0) + 1;

          if (obj && typeof obj === 'object') {
            const size = this.estimateObjectSize(obj);
            const sizeCategory = this.getSizeCategory(size);
            counts.bySize[sizeCategory] = (counts.bySize[sizeCategory] || 0) + 1;
          }
        } catch (error) {
          // 忽略访问错误
        }
      });
    } catch (error) {
      console.warn('Failed to analyze global objects:', error);
    }
  }

  /**
   * 分析DOM节点
   */
  private analyzeDOMNodes(counts: ObjectCounts): void {
    try {
      const allElements = document.querySelectorAll('*');
      counts.total += allElements.length;
      counts.byType['HTMLElement'] = allElements.length;

      // 检测分离的DOM节点
      let detachedCount = 0;
      allElements.forEach(element => {
        if (!document.contains(element)) {
          detachedCount++;
        }
      });
      counts.detached = detachedCount;
    } catch (error) {
      console.warn('Failed to analyze DOM nodes:', error);
    }
  }

  /**
   * 分析引用关系
   */
  private analyzeReferences(): ReferenceInfo[] {
    const references: ReferenceInfo[] = [];

    // 这里实现引用关系分析
    // 由于浏览器安全限制，只能分析有限的引用信息

    return references;
  }

  /**
   * 估算对象大小
   */
  private estimateObjectSize(obj: any): number {
    if (!obj || typeof obj !== 'object') return 0;

    try {
      // 简单的大小估算
      const jsonString = JSON.stringify(obj);
      return jsonString.length * 2; // 假设每个字符2字节
    } catch (error) {
      // 如果无法序列化，返回估算值
      return Object.keys(obj).length * 50; // 每个属性估算50字节
    }
  }

  /**
   * 获取大小分类
   */
  private getSizeCategory(size: number): string {
    if (size < 1024) return 'small';
    if (size < 1024 * 1024) return 'medium';
    if (size < 10 * 1024 * 1024) return 'large';
    return 'huge';
  }

  /**
   * 分析内存泄漏
   */
  private analyzeMemoryLeaks(): void {
    if (this.snapshots.length < 3) return;

    const recent = this.snapshots.slice(-3);
    const memoryGrowth = this.calculateMemoryGrowth(recent);

    // 检测持续增长的内存使用
    if (memoryGrowth > this.config.leakDetectionThreshold) {
      this.detectMemoryLeaks(recent, memoryGrowth);
    }
  }

  /**
   * 计算内存增长
   */
  private calculateMemoryGrowth(snapshots: MemorySnapshot[]): number {
    if (snapshots.length < 2) return 0;

    const first = snapshots[0];
    const last = snapshots[snapshots.length - 1];

    return last.usedJSHeapSize - first.usedJSHeapSize;
  }

  /**
   * 检测内存泄漏
   */
  private detectMemoryLeaks(snapshots: MemorySnapshot[], _growthRate: number): void {
    const leaks: MemoryLeakDetection[] = [];

    // 检测分离的DOM节点
    const detachedGrowth = this.analyzeDetachedDOMGrowth(snapshots);
    if (detachedGrowth > 10) {
      leaks.push({
        id: `leak_${Date.now()}_detached_dom`,
        type: LeakType.DETACHED_DOM,
        severity: LeakSeverity.HIGH,
        description: `Detached DOM nodes increasing: ${detachedGrowth} nodes`,
        affectedObjects: ['DOM nodes'],
        growthRate: detachedGrowth,
        suggestions: [
          'Remove event listeners before removing DOM elements',
          'Clear references to removed DOM elements',
          'Use WeakMap for DOM element associations'
        ],
        timestamp: Date.now()
      });
    }

    // 检测大对象增长
    const largeObjectGrowth = this.analyzeLargeObjectGrowth(snapshots);
    if (largeObjectGrowth > 5) {
      leaks.push({
        id: `leak_${Date.now()}_large_objects`,
        type: LeakType.LARGE_OBJECTS,
        severity: LeakSeverity.MEDIUM,
        description: `Large objects increasing: ${largeObjectGrowth} objects`,
        affectedObjects: ['Large objects'],
        growthRate: largeObjectGrowth,
        suggestions: [
          'Review large object creation patterns',
          'Implement object pooling',
          'Clear large objects when not needed'
        ],
        timestamp: Date.now()
      });
    }

    // 添加检测到的泄漏
    leaks.forEach(leak => {
      this.leakDetections.push(leak);
      this.emit('memoryLeakDetected', leak);
    });

    // 保持泄漏检测记录在合理范围内
    if (this.leakDetections.length > 50) {
      this.leakDetections = this.leakDetections.slice(-25);
    }
  }

  /**
   * 分析分离DOM节点增长
   */
  private analyzeDetachedDOMGrowth(snapshots: MemorySnapshot[]): number {
    if (snapshots.length < 2) return 0;

    const first = snapshots[0];
    const last = snapshots[snapshots.length - 1];

    return last.objectCounts.detached - first.objectCounts.detached;
  }

  /**
   * 分析大对象增长
   */
  private analyzeLargeObjectGrowth(snapshots: MemorySnapshot[]): number {
    if (snapshots.length < 2) return 0;

    const first = snapshots[0];
    const last = snapshots[snapshots.length - 1];

    const firstLarge = first.objectCounts.bySize['large'] || 0;
    const lastLarge = last.objectCounts.bySize['large'] || 0;

    return lastLarge - firstLarge;
  }

  /**
   * 强制垃圾回收
   */
  public forceGarbageCollection(): void {
    if ('gc' in window) {
      (window as any).gc();
      this.emit('gcForced');
    } else {
      console.warn('Garbage collection not available');
    }
  }

  /**
   * 获取内存快照列表
   */
  public getSnapshots(): MemorySnapshot[] {
    return [...this.snapshots];
  }

  /**
   * 获取最新快照
   */
  public getLatestSnapshot(): MemorySnapshot | null {
    return this.snapshots.length > 0 ? this.snapshots[this.snapshots.length - 1] : null;
  }

  /**
   * 获取内存泄漏检测结果
   */
  public getLeakDetections(): MemoryLeakDetection[] {
    return [...this.leakDetections];
  }

  /**
   * 清除快照历史
   */
  public clearSnapshots(): void {
    this.snapshots = [];
    this.emit('snapshotsCleared');
  }

  /**
   * 清除泄漏检测记录
   */
  public clearLeakDetections(): void {
    this.leakDetections = [];
    this.emit('leakDetectionsCleared');
  }

  /**
   * 获取内存使用趋势
   */
  public getMemoryTrend(minutes: number = 5): {
    timestamps: number[];
    usedMemory: number[];
    totalMemory: number[];
    trend: 'increasing' | 'decreasing' | 'stable';
  } {
    const cutoffTime = Date.now() - minutes * 60 * 1000;
    const recentSnapshots = this.snapshots.filter(s => s.timestamp > cutoffTime);

    if (recentSnapshots.length < 2) {
      return {
        timestamps: [],
        usedMemory: [],
        totalMemory: [],
        trend: 'stable'
      };
    }

    const timestamps = recentSnapshots.map(s => s.timestamp);
    const usedMemory = recentSnapshots.map(s => s.usedJSHeapSize);
    const totalMemory = recentSnapshots.map(s => s.totalJSHeapSize);

    // 计算趋势
    const firstUsed = usedMemory[0];
    const lastUsed = usedMemory[usedMemory.length - 1];
    const growth = lastUsed - firstUsed;
    const growthPercent = (growth / firstUsed) * 100;

    let trend: 'increasing' | 'decreasing' | 'stable';
    if (growthPercent > 5) {
      trend = 'increasing';
    } else if (growthPercent < -5) {
      trend = 'decreasing';
    } else {
      trend = 'stable';
    }

    return {
      timestamps,
      usedMemory,
      totalMemory,
      trend
    };
  }

  /**
   * 获取内存使用统计
   */
  public getMemoryStats(): {
    current: MemorySnapshot | null;
    peak: number;
    average: number;
    leakCount: number;
    gcCount: number;
  } {
    const current = this.getLatestSnapshot();
    const usedMemoryValues = this.snapshots.map(s => s.usedJSHeapSize);
    
    const peak = usedMemoryValues.length > 0 ? Math.max(...usedMemoryValues) : 0;
    const average = usedMemoryValues.length > 0 ? 
      usedMemoryValues.reduce((sum, val) => sum + val, 0) / usedMemoryValues.length : 0;

    return {
      current,
      peak,
      average,
      leakCount: this.leakDetections.length,
      gcCount: 0 // 需要从GC事件中统计
    };
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<MemoryAnalysisConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('configUpdated', this.config);
  }

  /**
   * 获取配置
   */
  public getConfig(): MemoryAnalysisConfig {
    return { ...this.config };
  }

  /**
   * 比较两个快照
   */
  public compareSnapshots(snapshot1Id: string, snapshot2Id: string): {
    memoryDiff: number;
    objectCountDiff: number;
    detachedDiff: number;
    suggestions: string[];
  } {
    const snap1 = this.snapshots.find(s => s.id === snapshot1Id);
    const snap2 = this.snapshots.find(s => s.id === snapshot2Id);

    if (!snap1 || !snap2) {
      throw new Error('Snapshot not found');
    }

    const memoryDiff = snap2.usedJSHeapSize - snap1.usedJSHeapSize;
    const objectCountDiff = snap2.objectCounts.total - snap1.objectCounts.total;
    const detachedDiff = snap2.objectCounts.detached - snap1.objectCounts.detached;

    const suggestions: string[] = [];

    if (memoryDiff > 1024 * 1024) {
      suggestions.push('Memory usage increased significantly');
    }

    if (objectCountDiff > 100) {
      suggestions.push('Object count increased significantly');
    }

    if (detachedDiff > 0) {
      suggestions.push('Detached DOM nodes detected');
    }

    return {
      memoryDiff,
      objectCountDiff,
      detachedDiff,
      suggestions
    };
  }
}

export default MemoryMonitorService;
