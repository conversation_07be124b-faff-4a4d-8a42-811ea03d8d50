/**
 * 后处理特效面板组件
 * 提供后处理特效编辑和管理功能
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Slider,
  Switch,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Select,
  Tag,
  Modal,
  Form,
  Divider,
  Badge,
  Progress,
  Dropdown,
  Input
} from 'antd';
import {
  EyeOutlined,
  PlusOutlined,
  DeleteOutlined,
  CopyOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  DownloadOutlined,
  UploadOutlined,
  StarOutlined,
  DragOutlined,
  MoreOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import PostProcessingService, {
  PostProcessingEffect,
  PostProcessingChain,
  PostProcessingEffectType,
  PostProcessingPreset
} from '../../services/PostProcessingService';
import './PostProcessingPanel.less';

const { Title, Text } = Typography;
const { Option } = Select;

interface PostProcessingPanelProps {
  visible: boolean;
  onClose: () => void;
}

const PostProcessingPanel: React.FC<PostProcessingPanelProps> = ({
  visible,
  onClose
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [chains, setChains] = useState<PostProcessingChain[]>([]);
  const [activeChain, setActiveChain] = useState<PostProcessingChain | null>(null);
  const [presets, setPresets] = useState<PostProcessingPreset[]>([]);
  const [selectedEffect, setSelectedEffect] = useState<PostProcessingEffect | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [presetModalVisible, setPresetModalVisible] = useState(false);
  const [newChainModalVisible, setNewChainModalVisible] = useState(false);

  const postProcessingService = PostProcessingService.getInstance();

  useEffect(() => {
    if (visible) {
      loadData();
      setupEventListeners();
    }

    return () => {
      cleanupEventListeners();
    };
  }, [visible]);

  const setupEventListeners = () => {
    postProcessingService.on('effectCreated', handleEffectCreated);
    postProcessingService.on('effectUpdated', handleEffectUpdated);
    postProcessingService.on('chainCreated', handleChainCreated);
    postProcessingService.on('activeChainChanged', handleActiveChainChanged);
    postProcessingService.on('processingStarted', () => setIsProcessing(true));
    postProcessingService.on('processingCompleted', () => setIsProcessing(false));
  };

  const cleanupEventListeners = () => {
    postProcessingService.off('effectCreated', handleEffectCreated);
    postProcessingService.off('effectUpdated', handleEffectUpdated);
    postProcessingService.off('chainCreated', handleChainCreated);
    postProcessingService.off('activeChainChanged', handleActiveChainChanged);
  };

  const loadData = () => {
    setChains(postProcessingService.getAllChains());
    setPresets(postProcessingService.getAllPresets());
    setActiveChain(postProcessingService.getActiveChain());
    setIsProcessing(postProcessingService.isProcessingEffects());
  };

  const handleEffectCreated = (_effect: PostProcessingEffect) => {
    // 重新加载数据以更新界面
    loadData();
  };

  const handleEffectUpdated = (effect: PostProcessingEffect) => {
    if (selectedEffect?.id === effect.id) {
      setSelectedEffect(effect);
    }
    // 重新加载数据以更新界面
    loadData();
  };

  const handleChainCreated = (chain: PostProcessingChain) => {
    setChains(prev => [...prev, chain]);
  };

  const handleActiveChainChanged = (chain: PostProcessingChain) => {
    setActiveChain(chain);
  };

  const handleCreateNewChain = async (values: any) => {
    const chain = postProcessingService.createChain(values.name, values.description);
    postProcessingService.setActiveChain(chain.id);
    setNewChainModalVisible(false);
    form.resetFields();
  };

  const handleAddEffectToChain = (effectType: PostProcessingEffectType) => {
    if (!activeChain) return;

    const effect = postProcessingService.createEffect({
      type: effectType,
      name: effectType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
      description: `${effectType} effect`,
      params: postProcessingService['getDefaultParamsForType'](effectType)
    });

    postProcessingService.addEffectToChain(activeChain.id, effect.id);
    loadData();
  };

  const handleRemoveEffectFromChain = (effectIndex: number) => {
    if (!activeChain) return;

    postProcessingService.removeEffectFromChain(activeChain.id, effectIndex);
    loadData();
  };

  const handleToggleEffect = (effectIndex: number) => {
    if (!activeChain) return;

    const effect = activeChain.effects[effectIndex];
    if (effect) {
      postProcessingService.updateEffect(effect.id, { enabled: !effect.enabled });
      loadData();
    }
  };

  const handleEffectParamChange = (param: string, value: any) => {
    if (!selectedEffect) return;

    postProcessingService.updateEffect(selectedEffect.id, { [param]: value });
  };



  const handleApplyPreset = (presetId: string) => {
    if (!activeChain) return;

    postProcessingService.applyPresetToChain(activeChain.id, presetId);
    setPresetModalVisible(false);
    loadData();
  };

  // 渲染效果列表
  const renderEffectsList = () => (
    <div className="effects-list">
      <div className="effects-header">
        <Space>
          <Button type="primary" onClick={() => setNewChainModalVisible(true)}>
            <PlusOutlined />
            {t('postProcessing.newChain')}
          </Button>
          <Button onClick={() => setPresetModalVisible(true)}>
            <StarOutlined />
            {t('postProcessing.presets')}
          </Button>
          <Select
            value={activeChain?.id}
            onChange={(chainId) => postProcessingService.setActiveChain(chainId)}
            style={{ width: 200 }}
            placeholder={t('postProcessing.selectChain')}
          >
            {chains.map(chain => (
              <Option key={chain.id} value={chain.id}>{chain.name}</Option>
            ))}
          </Select>
        </Space>
      </div>

      {activeChain && (
        <div className="chain-effects">
          <div className="add-effect-section">
            <Text strong>{t('postProcessing.addEffect')}</Text>
            <Row gutter={[8, 8]} style={{ marginTop: 8 }}>
              {Object.values(PostProcessingEffectType).map(type => (
                <Col key={type}>
                  <Button
                    size="small"
                    onClick={() => handleAddEffectToChain(type)}
                  >
                    {type.replace('_', ' ')}
                  </Button>
                </Col>
              ))}
            </Row>
          </div>

          <Divider />

          <div className="effects-container">
            {activeChain.effects.map((effect, index) => (
              <div
                key={effect.id}
                className={`effect-item ${effect.enabled ? 'enabled' : 'disabled'} ${
                  selectedEffect?.id === effect.id ? 'selected' : ''
                }`}
                onClick={() => setSelectedEffect(effect)}
              >
                <div className="effect-header">
                  <div className="drag-handle">
                    <DragOutlined />
                  </div>
                  <div className="effect-info">
                    <Text strong>{effect.name}</Text>
                    <Tag color={effect.enabled ? 'green' : 'default'}>
                      {effect.type}
                    </Tag>
                  </div>
                  <div className="effect-controls">
                    <Switch
                      size="small"
                      checked={effect.enabled}
                      onChange={() => handleToggleEffect(index)}
                    />
                    <Dropdown
                      menu={{
                        items: [
                          {
                            key: 'copy',
                            icon: <CopyOutlined />,
                            label: t('postProcessing.duplicate')
                          },
                          {
                            key: 'delete',
                            icon: <DeleteOutlined />,
                            label: t('postProcessing.remove'),
                            danger: true,
                            onClick: () => handleRemoveEffectFromChain(index)
                          }
                        ]
                      }}
                    >
                      <Button type="text" size="small" icon={<MoreOutlined />} />
                    </Dropdown>
                  </div>
                </div>
                <div className="effect-preview">
                  <Progress
                    percent={effect.metadata.performance}
                    size="small"
                    strokeColor="#52c41a"
                    showInfo={false}
                  />
                  <Text type="secondary" style={{ fontSize: '11px' }}>
                    Performance: {effect.metadata.performance}%
                  </Text>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );

  // 渲染效果参数编辑器
  const renderEffectEditor = () => {
    if (!selectedEffect) {
      return (
        <div className="no-effect-selected">
          <Text type="secondary">{t('postProcessing.selectEffectToEdit')}</Text>
        </div>
      );
    }

    return (
      <div className="effect-editor">
        <div className="editor-header">
          <Title level={4}>{selectedEffect.name}</Title>
          <Text type="secondary">{selectedEffect.description}</Text>
        </div>

        <Divider />

        <Form layout="vertical">
          <Form.Item label={t('postProcessing.enabled')}>
            <Switch
              checked={selectedEffect.enabled}
              onChange={(enabled) => handleEffectParamChange('enabled', enabled)}
            />
          </Form.Item>

          <Form.Item label={t('postProcessing.intensity')}>
            <Slider
              min={0}
              max={2}
              step={0.01}
              value={selectedEffect.params.intensity}
              onChange={(value) => handleEffectParamChange('intensity', value)}
            />
          </Form.Item>

          {/* 根据效果类型渲染特定参数 */}
          {selectedEffect.type === PostProcessingEffectType.BLOOM && (
            <>
              <Form.Item label={t('postProcessing.bloomThreshold')}>
                <Slider
                  min={0}
                  max={3}
                  step={0.01}
                  value={selectedEffect.params.bloomThreshold || 1}
                  onChange={(value) => handleEffectParamChange('bloomThreshold', value)}
                />
              </Form.Item>
              <Form.Item label={t('postProcessing.bloomRadius')}>
                <Slider
                  min={0}
                  max={5}
                  step={0.1}
                  value={selectedEffect.params.bloomRadius || 1}
                  onChange={(value) => handleEffectParamChange('bloomRadius', value)}
                />
              </Form.Item>
            </>
          )}

          {selectedEffect.type === PostProcessingEffectType.COLOR_GRADING && (
            <>
              <Form.Item label={t('postProcessing.exposure')}>
                <Slider
                  min={-3}
                  max={3}
                  step={0.01}
                  value={selectedEffect.params.exposure || 0}
                  onChange={(value) => handleEffectParamChange('exposure', value)}
                />
              </Form.Item>
              <Form.Item label={t('postProcessing.brightness')}>
                <Slider
                  min={-1}
                  max={1}
                  step={0.01}
                  value={selectedEffect.params.brightness || 0}
                  onChange={(value) => handleEffectParamChange('brightness', value)}
                />
              </Form.Item>
              <Form.Item label={t('postProcessing.contrast')}>
                <Slider
                  min={0}
                  max={3}
                  step={0.01}
                  value={selectedEffect.params.contrast || 1}
                  onChange={(value) => handleEffectParamChange('contrast', value)}
                />
              </Form.Item>
              <Form.Item label={t('postProcessing.saturation')}>
                <Slider
                  min={0}
                  max={3}
                  step={0.01}
                  value={selectedEffect.params.saturation || 1}
                  onChange={(value) => handleEffectParamChange('saturation', value)}
                />
              </Form.Item>
            </>
          )}

          {selectedEffect.type === PostProcessingEffectType.DEPTH_OF_FIELD && (
            <>
              <Form.Item label={t('postProcessing.focusDistance')}>
                <Slider
                  min={0.1}
                  max={100}
                  step={0.1}
                  value={selectedEffect.params.focusDistance || 10}
                  onChange={(value) => handleEffectParamChange('focusDistance', value)}
                />
              </Form.Item>
              <Form.Item label={t('postProcessing.aperture')}>
                <Slider
                  min={0.1}
                  max={22}
                  step={0.1}
                  value={selectedEffect.params.aperture || 2.8}
                  onChange={(value) => handleEffectParamChange('aperture', value)}
                />
              </Form.Item>
            </>
          )}
        </Form>

        <Divider />

        <div className="effect-stats">
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Text type="secondary">Performance Impact:</Text>
              <Progress
                percent={selectedEffect.metadata.performance}
                size="small"
                strokeColor="#52c41a"
              />
            </Col>
            <Col span={12}>
              <Text type="secondary">Quality:</Text>
              <Progress
                percent={selectedEffect.metadata.quality}
                size="small"
                strokeColor="#1890ff"
              />
            </Col>
          </Row>
        </div>
      </div>
    );
  };

  // 渲染预览区域
  const renderPreview = () => (
    <div className="preview-area">
      <div className="preview-controls">
        <Space>
          <Button
            type={isProcessing ? 'default' : 'primary'}
            icon={isProcessing ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
            onClick={() => {
              if (isProcessing) {
                // 停止处理
              } else {
                // 开始处理
                postProcessingService.processEffects(null, null);
              }
            }}
          >
            {isProcessing ? t('postProcessing.pause') : t('postProcessing.process')}
          </Button>
          <Button icon={<ReloadOutlined />}>
            {t('postProcessing.reset')}
          </Button>
        </Space>
      </div>

      <div className="preview-viewport">
        {/* 3D预览区域 */}
        <div style={{
          width: '100%',
          height: '300px',
          background: 'linear-gradient(45deg, #f0f0f0 25%, transparent 25%), linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #f0f0f0 75%), linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)',
          backgroundSize: '20px 20px',
          backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: '1px solid #d9d9d9',
          borderRadius: '4px'
        }}>
          <Text type="secondary">Post-Processing Preview</Text>
        </div>
      </div>

      {activeChain && (
        <div className="chain-stats">
          <Row gutter={[8, 8]}>
            <Col span={6}>
              <Text type="secondary">Effects:</Text>
              <br />
              <Text strong>{activeChain.effects.length}</Text>
            </Col>
            <Col span={6}>
              <Text type="secondary">Active:</Text>
              <br />
              <Text strong>{activeChain.effects.filter(e => e.enabled).length}</Text>
            </Col>
            <Col span={6}>
              <Text type="secondary">Performance:</Text>
              <br />
              <Text strong>
                {Math.round(activeChain.effects.reduce((sum, e) => sum + e.metadata.performance, 0) / activeChain.effects.length || 100)}%
              </Text>
            </Col>
            <Col span={6}>
              <Text type="secondary">Quality:</Text>
              <br />
              <Text strong>
                {Math.round(activeChain.effects.reduce((sum, e) => sum + e.metadata.quality, 0) / activeChain.effects.length || 0)}%
              </Text>
            </Col>
          </Row>
        </div>
      )}
    </div>
  );

  return (
    <Modal
      title={
        <Space>
          <EyeOutlined />
          {t('postProcessing.postProcessingEffects')}
          {activeChain && (
            <Badge count={activeChain.effects.filter(e => e.enabled).length} showZero>
              <Tag color="blue">{activeChain.name}</Tag>
            </Badge>
          )}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1400}
      footer={[
        <Button key="export" icon={<DownloadOutlined />}>
          {t('postProcessing.export')}
        </Button>,
        <Button key="import" icon={<UploadOutlined />}>
          {t('postProcessing.import')}
        </Button>,
        <Button key="close" onClick={onClose}>
          {t('common.close')}
        </Button>
      ]}
      className="post-processing-panel"
    >
      <Row gutter={[16, 0]} style={{ height: '70vh' }}>
        <Col span={8}>
          {renderEffectsList()}
        </Col>
        <Col span={8}>
          {renderEffectEditor()}
        </Col>
        <Col span={8}>
          {renderPreview()}
        </Col>
      </Row>

      {/* 新建链对话框 */}
      <Modal
        title={t('postProcessing.createNewChain')}
        open={newChainModalVisible}
        onCancel={() => setNewChainModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} layout="vertical" onFinish={handleCreateNewChain}>
          <Form.Item
            name="name"
            label={t('postProcessing.chainName')}
            rules={[{ required: true }]}
          >
            <Input />
          </Form.Item>
          <Form.Item name="description" label={t('postProcessing.description')}>
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 预设选择对话框 */}
      <Modal
        title={t('postProcessing.selectPreset')}
        open={presetModalVisible}
        onCancel={() => setPresetModalVisible(false)}
        footer={null}
        width={800}
      >
        <Row gutter={[16, 16]}>
          {presets.map(preset => (
            <Col span={8} key={preset.id}>
              <Card
                hoverable
                cover={<img src={preset.thumbnail} alt={preset.name} style={{ height: '120px', objectFit: 'cover' }} />}
                onClick={() => handleApplyPreset(preset.id)}
              >
                <Card.Meta
                  title={preset.name}
                  description={
                    <div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>{preset.description}</Text>
                      <br />
                      <Space wrap style={{ marginTop: 4 }}>
                        {preset.tags.map(tag => (
                          <Tag key={tag}>{tag}</Tag>
                        ))}
                      </Space>
                    </div>
                  }
                />
              </Card>
            </Col>
          ))}
        </Row>
      </Modal>
    </Modal>
  );
};

export default PostProcessingPanel;
