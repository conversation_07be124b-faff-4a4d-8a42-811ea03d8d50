// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/utils/Address.sol";

/**
 * @title SecurityAudit
 * @dev 安全审计合约，用于检测和防范常见的安全漏洞
 */
contract SecurityAudit is ReentrancyGuard, Pausable, AccessControl {
    using Address for address;

    bytes32 public constant AUDITOR_ROLE = keccak256("AUDITOR_ROLE");
    bytes32 public constant SECURITY_ADMIN_ROLE = keccak256("SECURITY_ADMIN_ROLE");

    // 安全事件
    event SecurityAlert(
        address indexed contract_,
        string alertType,
        string description,
        uint256 severity, // 1-5, 5为最高
        uint256 timestamp
    );

    event SecurityCheck(
        address indexed contract_,
        string checkType,
        bool passed,
        string details
    );

    // 安全配置
    struct SecurityConfig {
        uint256 maxGasLimit;
        uint256 maxTransactionValue;
        uint256 dailyTransactionLimit;
        bool requireWhitelist;
        bool enableRateLimiting;
    }

    // 合约安全状态
    struct ContractSecurityStatus {
        bool isAudited;
        uint256 auditScore; // 0-100
        uint256 lastAuditTime;
        string[] vulnerabilities;
        bool isBlacklisted;
    }

    // 用户安全状态
    struct UserSecurityStatus {
        uint256 dailyTransactionCount;
        uint256 lastTransactionTime;
        uint256 suspiciousActivityScore;
        bool isWhitelisted;
        bool isBlacklisted;
    }

    mapping(address => ContractSecurityStatus) public contractStatus;
    mapping(address => UserSecurityStatus) public userStatus;
    mapping(address => SecurityConfig) public securityConfigs;
    
    // 速率限制
    mapping(address => mapping(uint256 => uint256)) private dailyTransactionCounts;
    
    // 已知恶意地址
    mapping(address => bool) public blacklistedAddresses;
    
    // 白名单地址
    mapping(address => bool) public whitelistedAddresses;

    constructor() {
        _grantRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _grantRole(AUDITOR_ROLE, msg.sender);
        _grantRole(SECURITY_ADMIN_ROLE, msg.sender);
    }

    /**
     * @dev 审计合约安全性
     */
    function auditContract(address contractAddress) 
        external 
        onlyRole(AUDITOR_ROLE) 
        returns (uint256 score) 
    {
        require(contractAddress.isContract(), "Address is not a contract");
        
        string[] memory vulnerabilities = new string[](0);
        uint256 auditScore = 100;
        
        // 检查重入攻击防护
        if (!checkReentrancyProtection(contractAddress)) {
            auditScore -= 20;
            vulnerabilities = addVulnerability(vulnerabilities, "Missing reentrancy protection");
        }
        
        // 检查访问控制
        if (!checkAccessControl(contractAddress)) {
            auditScore -= 15;
            vulnerabilities = addVulnerability(vulnerabilities, "Insufficient access control");
        }
        
        // 检查整数溢出防护
        if (!checkIntegerOverflowProtection(contractAddress)) {
            auditScore -= 10;
            vulnerabilities = addVulnerability(vulnerabilities, "Integer overflow vulnerability");
        }
        
        // 检查暂停机制
        if (!checkPauseability(contractAddress)) {
            auditScore -= 10;
            vulnerabilities = addVulnerability(vulnerabilities, "Missing pause mechanism");
        }
        
        // 检查输入验证
        if (!checkInputValidation(contractAddress)) {
            auditScore -= 15;
            vulnerabilities = addVulnerability(vulnerabilities, "Insufficient input validation");
        }
        
        // 更新合约状态
        contractStatus[contractAddress] = ContractSecurityStatus({
            isAudited: true,
            auditScore: auditScore,
            lastAuditTime: block.timestamp,
            vulnerabilities: vulnerabilities,
            isBlacklisted: auditScore < 50
        });
        
        emit SecurityCheck(contractAddress, "Full Audit", auditScore >= 70, "Audit completed");
        
        if (auditScore < 50) {
            emit SecurityAlert(
                contractAddress,
                "Critical Vulnerability",
                "Contract failed security audit",
                5,
                block.timestamp
            );
        }
        
        return auditScore;
    }

    /**
     * @dev 检查交易安全性
     */
    function checkTransactionSecurity(
        address from,
        address to,
        uint256 value,
        bytes calldata data
    ) external view returns (bool safe, string memory reason) {
        // 检查黑名单
        if (blacklistedAddresses[from] || blacklistedAddresses[to]) {
            return (false, "Address is blacklisted");
        }
        
        // 检查合约安全状态
        if (to.isContract() && contractStatus[to].isBlacklisted) {
            return (false, "Target contract is blacklisted");
        }
        
        // 检查交易金额限制
        SecurityConfig memory config = securityConfigs[to];
        if (config.maxTransactionValue > 0 && value > config.maxTransactionValue) {
            return (false, "Transaction value exceeds limit");
        }
        
        // 检查Gas限制
        if (config.maxGasLimit > 0 && gasleft() > config.maxGasLimit) {
            return (false, "Gas limit too high");
        }
        
        // 检查速率限制
        if (config.enableRateLimiting && !checkRateLimit(from)) {
            return (false, "Rate limit exceeded");
        }
        
        // 检查白名单要求
        if (config.requireWhitelist && !whitelistedAddresses[from]) {
            return (false, "Address not whitelisted");
        }
        
        return (true, "Transaction is safe");
    }

    /**
     * @dev 报告可疑活动
     */
    function reportSuspiciousActivity(
        address user,
        string calldata activityType,
        uint256 severity
    ) external onlyRole(AUDITOR_ROLE) {
        userStatus[user].suspiciousActivityScore += severity;
        
        emit SecurityAlert(
            user,
            activityType,
            "Suspicious activity detected",
            severity,
            block.timestamp
        );
        
        // 如果可疑活动分数过高，自动加入黑名单
        if (userStatus[user].suspiciousActivityScore > 100) {
            blacklistedAddresses[user] = true;
            emit SecurityAlert(
                user,
                "Auto Blacklist",
                "User automatically blacklisted due to high suspicious activity score",
                5,
                block.timestamp
            );
        }
    }

    /**
     * @dev 设置安全配置
     */
    function setSecurityConfig(
        address contractAddress,
        SecurityConfig calldata config
    ) external onlyRole(SECURITY_ADMIN_ROLE) {
        securityConfigs[contractAddress] = config;
    }

    /**
     * @dev 添加到黑名单
     */
    function addToBlacklist(address addr) external onlyRole(SECURITY_ADMIN_ROLE) {
        blacklistedAddresses[addr] = true;
        emit SecurityAlert(addr, "Blacklisted", "Address added to blacklist", 4, block.timestamp);
    }

    /**
     * @dev 从黑名单移除
     */
    function removeFromBlacklist(address addr) external onlyRole(SECURITY_ADMIN_ROLE) {
        blacklistedAddresses[addr] = false;
    }

    /**
     * @dev 添加到白名单
     */
    function addToWhitelist(address addr) external onlyRole(SECURITY_ADMIN_ROLE) {
        whitelistedAddresses[addr] = true;
    }

    /**
     * @dev 从白名单移除
     */
    function removeFromWhitelist(address addr) external onlyRole(SECURITY_ADMIN_ROLE) {
        whitelistedAddresses[addr] = false;
    }

    /**
     * @dev 检查重入攻击防护
     */
    function checkReentrancyProtection(address contractAddress) 
        internal 
        view 
        returns (bool) 
    {
        // 这里应该检查合约是否实现了ReentrancyGuard
        // 由于Solidity限制，这里只是示例实现
        try this.hasReentrancyGuard(contractAddress) returns (bool result) {
            return result;
        } catch {
            return false;
        }
    }

    /**
     * @dev 检查访问控制
     */
    function checkAccessControl(address contractAddress) 
        internal 
        view 
        returns (bool) 
    {
        // 检查是否实现了适当的访问控制
        try this.hasAccessControl(contractAddress) returns (bool result) {
            return result;
        } catch {
            return false;
        }
    }

    /**
     * @dev 检查整数溢出防护
     */
    function checkIntegerOverflowProtection(address contractAddress) 
        internal 
        view 
        returns (bool) 
    {
        // 检查是否使用了SafeMath或Solidity 0.8+
        // 这里简化为检查编译器版本
        return true; // Solidity 0.8+ 默认有溢出检查
    }

    /**
     * @dev 检查暂停机制
     */
    function checkPauseability(address contractAddress) 
        internal 
        view 
        returns (bool) 
    {
        try this.hasPauseability(contractAddress) returns (bool result) {
            return result;
        } catch {
            return false;
        }
    }

    /**
     * @dev 检查输入验证
     */
    function checkInputValidation(address contractAddress) 
        internal 
        view 
        returns (bool) 
    {
        // 这里应该检查合约是否有适当的输入验证
        // 由于静态分析的限制，这里返回默认值
        return true;
    }

    /**
     * @dev 检查速率限制
     */
    function checkRateLimit(address user) internal view returns (bool) {
        uint256 today = block.timestamp / 1 days;
        SecurityConfig memory config = securityConfigs[msg.sender];
        
        if (config.dailyTransactionLimit == 0) {
            return true;
        }
        
        return dailyTransactionCounts[user][today] < config.dailyTransactionLimit;
    }

    /**
     * @dev 添加漏洞到列表
     */
    function addVulnerability(
        string[] memory vulnerabilities,
        string memory newVulnerability
    ) internal pure returns (string[] memory) {
        string[] memory newArray = new string[](vulnerabilities.length + 1);
        for (uint i = 0; i < vulnerabilities.length; i++) {
            newArray[i] = vulnerabilities[i];
        }
        newArray[vulnerabilities.length] = newVulnerability;
        return newArray;
    }

    // 外部调用函数（用于检查合约特性）
    function hasReentrancyGuard(address contractAddress) external view returns (bool) {
        // 实际实现中应该检查合约字节码或接口
        return contractAddress.isContract();
    }

    function hasAccessControl(address contractAddress) external view returns (bool) {
        // 实际实现中应该检查是否实现了AccessControl接口
        return contractAddress.isContract();
    }

    function hasPauseability(address contractAddress) external view returns (bool) {
        // 实际实现中应该检查是否实现了Pausable接口
        return contractAddress.isContract();
    }

    /**
     * @dev 获取合约安全评分
     */
    function getSecurityScore(address contractAddress) 
        external 
        view 
        returns (uint256) 
    {
        return contractStatus[contractAddress].auditScore;
    }

    /**
     * @dev 获取用户可疑活动分数
     */
    function getSuspiciousActivityScore(address user) 
        external 
        view 
        returns (uint256) 
    {
        return userStatus[user].suspiciousActivityScore;
    }

    /**
     * @dev 紧急暂停
     */
    function emergencyPause() external onlyRole(SECURITY_ADMIN_ROLE) {
        _pause();
        emit SecurityAlert(
            address(this),
            "Emergency Pause",
            "Security audit contract paused",
            5,
            block.timestamp
        );
    }

    /**
     * @dev 恢复运行
     */
    function unpause() external onlyRole(SECURITY_ADMIN_ROLE) {
        _unpause();
    }
}
