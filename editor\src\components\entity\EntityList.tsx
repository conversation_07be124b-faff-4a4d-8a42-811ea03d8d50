/**
 * 实体列表组件
 * 用于显示场景中的所有实体
 */
import React, { useState } from 'react';
import { 
  List, 
  Input, 
  Button, 
  Space, 
  Tooltip, 
  Typography, 
  Empty,
  Tag
} from 'antd';
import {
  EyeOutlined,
  EyeInvisibleOutlined,
  LockOutlined,
  UnlockOutlined,
  DeleteOutlined,
  PlusOutlined,
  SearchOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { selectEntity, updateEntity, removeEntity } from '../../store/scene/sceneSlice';

const { Search } = Input;
const { Text } = Typography;

/**
 * 实体列表组件
 */
export const EntityList: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  // 获取状态
  const entities = useSelector((state: RootState) => state.scene?.entities || []);
  const selectedEntityId = useSelector((state: RootState) => state.scene?.selectedEntityId);
  
  // 本地状态
  const [searchValue, setSearchValue] = useState('');
  
  // 过滤实体
  const filteredEntities = entities.filter(entity =>
    entity.name.toLowerCase().includes(searchValue.toLowerCase()) ||
    entity.type.toLowerCase().includes(searchValue.toLowerCase())
  );
  
  // 处理实体选择
  const handleEntitySelect = (entityId: string) => {
    dispatch(selectEntity(entityId));
  };
  
  // 处理可见性切换
  const handleVisibilityToggle = (entityId: string, visible: boolean) => {
    dispatch(updateEntity({
      id: entityId,
      changes: { visible: !visible }
    }));
  };
  
  // 处理锁定切换
  const handleLockToggle = (entityId: string, locked: boolean) => {
    dispatch(updateEntity({
      id: entityId,
      changes: { locked: !locked }
    }));
  };
  
  // 处理实体删除
  const handleEntityDelete = (entityId: string) => {
    dispatch(removeEntity(entityId));
  };
  
  // 获取实体类型颜色
  const getEntityTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
      'mesh': 'blue',
      'light': 'gold',
      'camera': 'green',
      'empty': 'default',
      'terrain': 'orange'
    };
    return colorMap[type] || 'default';
  };
  
  // 渲染实体项
  const renderEntityItem = (entity: any) => (
    <List.Item
      key={entity.id}
      className={`entity-item ${selectedEntityId === entity.id ? 'selected' : ''}`}
      style={{
        padding: '8px 12px',
        cursor: 'pointer',
        backgroundColor: selectedEntityId === entity.id ? '#e6f7ff' : 'transparent',
        borderLeft: selectedEntityId === entity.id ? '3px solid #1890ff' : '3px solid transparent'
      }}
      onClick={() => handleEntitySelect(entity.id)}
    >
      <List.Item.Meta
        title={
          <Space>
            <Text strong={selectedEntityId === entity.id}>
              {entity.name}
            </Text>
            <Tag color={getEntityTypeColor(entity.type) as any}>
              {entity.type}
            </Tag>
          </Space>
        }
        description={
          <Text type="secondary" style={{ fontSize: '12px' }}>
            ID: {entity.id}
          </Text>
        }
      />
      <Space>
        <Tooltip title={entity.visible ? t('editor.common.hide') : t('editor.common.show')}>
          <Button
            type="text"
            size="small"
            icon={entity.visible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              handleVisibilityToggle(entity.id, entity.visible);
            }}
          />
        </Tooltip>
        
        <Tooltip title={entity.locked ? t('editor.common.unlock') : t('editor.common.lock')}>
          <Button
            type="text"
            size="small"
            icon={entity.locked ? <LockOutlined /> : <UnlockOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              handleLockToggle(entity.id, entity.locked);
            }}
          />
        </Tooltip>
        
        <Tooltip title={t('editor.common.delete')}>
          <Button
            type="text"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              handleEntityDelete(entity.id);
            }}
          />
        </Tooltip>
      </Space>
    </List.Item>
  );
  
  return (
    <div className="entity-list">
      {/* 搜索栏 */}
      <div style={{ padding: '8px 0', borderBottom: '1px solid #f0f0f0' }}>
        <Search
          placeholder={t('editor.common.searchEntities') || '搜索实体...'}
          allowClear
          size="small"
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          prefix={<SearchOutlined />}
        />
      </div>
      
      {/* 工具栏 */}
      <div style={{ 
        padding: '8px 0', 
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          {filteredEntities.length} {t('editor.common.entities')}
        </Text>
        <Tooltip title={t('editor.common.addEntity')}>
          <Button
            type="text"
            size="small"
            icon={<PlusOutlined />}
          />
        </Tooltip>
      </div>
      
      {/* 实体列表 */}
      {filteredEntities.length > 0 ? (
        <List
          size="small"
          dataSource={filteredEntities}
          renderItem={renderEntityItem}
          style={{ maxHeight: '400px', overflowY: 'auto' }}
        />
      ) : (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            searchValue 
              ? t('editor.common.noSearchResults') 
              : t('editor.common.noEntities')
          }
          style={{ padding: '20px' }}
        />
      )}
    </div>
  );
};

export default EntityList;
