/**
 * 自然语言场景生成功能使用示例
 */
import React, { useState } from 'react';
import { Card, Button, Space, Typography, Divider, Alert } from 'antd';
import { RobotOutlined, PlayCircleOutlined, EyeOutlined } from '@ant-design/icons';
import NLPSceneGenerationPanel from '../components/panels/NLPSceneGenerationPanel';
import ScenePreview from '../components/nlp/ScenePreview';
import GenerationHistory from '../components/nlp/GenerationHistory';

const { Title, Paragraph, Text } = Typography;

const NLPSceneGenerationExample: React.FC = () => {
  const [showPanel, setShowPanel] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [generatedScene, setGeneratedScene] = useState<any>(null);

  // 示例场景数据
  const exampleScene = {
    name: '示例现代办公室',
    entities: [
      { id: '1', name: '办公桌', type: 'furniture', position: { x: 0, y: 0, z: 0 } },
      { id: '2', name: '办公椅', type: 'furniture', position: { x: 1, y: 0, z: 0 } },
      { id: '3', name: '电脑', type: 'electronics', position: { x: 0, y: 1, z: 0 } },
      { id: '4', name: '绿植', type: 'decoration', position: { x: 2, y: 0, z: 0 } },
    ],
    lighting: {
      ambient: { intensity: 0.3, color: '#ffffff' },
      directional: { intensity: 0.7, color: '#ffffff' }
    }
  };

  // 示例历史记录
  const exampleHistory = [
    {
      id: '1',
      text: '创建一个现代化的办公室，包含玻璃桌子、舒适的椅子和绿色植物',
      style: 'realistic',
      quality: 80,
      maxObjects: 50,
      timestamp: new Date('2024-01-15T10:30:00'),
      scene: exampleScene,
      metadata: {
        generationTime: 5200,
        objectCount: 4,
        polygonCount: 12000
      }
    },
    {
      id: '2',
      text: '设计一个温馨的客厅，有沙发、茶几、电视和装饰画',
      style: 'realistic',
      quality: 70,
      maxObjects: 40,
      timestamp: new Date('2024-01-15T09:15:00'),
      metadata: {
        generationTime: 4800,
        objectCount: 6,
        polygonCount: 15000
      }
    },
    {
      id: '3',
      text: '建造一个安静的图书馆，有书架、阅读桌和柔和的灯光',
      style: 'minimalist',
      quality: 85,
      maxObjects: 60,
      timestamp: new Date('2024-01-14T16:45:00'),
      metadata: {
        generationTime: 6100,
        objectCount: 8,
        polygonCount: 18000
      }
    }
  ];

  const handlePreview = (item: any) => {
    setGeneratedScene(item.scene || exampleScene);
    setShowPreview(true);
  };

  const handleRegenerate = (item: any) => {
    console.log('重新生成场景:', item.text);
    // 这里可以触发重新生成逻辑
  };

  const handleDelete = (id: string) => {
    console.log('删除历史记录:', id);
    // 这里可以实现删除逻辑
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={2}>
        <RobotOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
        自然语言场景生成功能演示
      </Title>

      <Alert
        message="功能说明"
        description="这是自然语言场景生成功能的演示页面。您可以通过自然语言描述快速生成3D场景，支持多种风格和质量设置。"
        type="info"
        showIcon
        style={{ marginBottom: '24px' }}
      />

      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 功能介绍 */}
        <Card title="功能特性" size="small">
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            <Text>• <strong>自然语言理解</strong>：智能解析场景描述，识别对象、位置和属性</Text>
            <Text>• <strong>多种风格支持</strong>：写实、卡通、简约、科幻、奇幻等风格</Text>
            <Text>• <strong>质量控制</strong>：可调节生成质量和场景复杂度</Text>
            <Text>• <strong>实时预览</strong>：生成后立即预览场景效果</Text>
            <Text>• <strong>历史管理</strong>：保存生成历史，支持重新生成和删除</Text>
            <Text>• <strong>快捷操作</strong>：快捷键 Ctrl+Shift+G 打开面板</Text>
          </Space>
        </Card>

        {/* 操作演示 */}
        <Card title="功能演示" size="small">
          <Space wrap>
            <Button
              type="primary"
              icon={<RobotOutlined />}
              onClick={() => setShowPanel(!showPanel)}
            >
              {showPanel ? '隐藏' : '显示'}场景生成面板
            </Button>
            
            <Button
              icon={<EyeOutlined />}
              onClick={() => {
                setGeneratedScene(exampleScene);
                setShowPreview(!showPreview);
              }}
            >
              {showPreview ? '隐藏' : '显示'}场景预览
            </Button>
            
            <Button
              icon={<PlayCircleOutlined />}
              onClick={() => setShowHistory(!showHistory)}
            >
              {showHistory ? '隐藏' : '显示'}生成历史
            </Button>
          </Space>
        </Card>

        {/* 场景生成面板 */}
        {showPanel && (
          <Card title="自然语言场景生成面板" size="small">
            <NLPSceneGenerationPanel />
          </Card>
        )}

        {/* 场景预览 */}
        {showPreview && (
          <Card title="场景预览组件" size="small">
            <ScenePreview 
              scene={generatedScene}
              width={600}
              height={400}
            />
          </Card>
        )}

        {/* 生成历史 */}
        {showHistory && (
          <Card title="生成历史组件" size="small">
            <GenerationHistory
              history={exampleHistory}
              onPreview={handlePreview}
              onRegenerate={handleRegenerate}
              onDelete={handleDelete}
              maxItems={5}
            />
          </Card>
        )}

        <Divider />

        {/* 使用说明 */}
        <Card title="使用说明" size="small">
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            <div>
              <Title level={4}>基本使用流程</Title>
              <ol>
                <li>打开自然语言场景生成面板</li>
                <li>在文本框中输入场景描述，例如："创建一个现代化的办公室"</li>
                <li>选择生成风格（写实、卡通等）和质量等级</li>
                <li>点击"生成场景"按钮开始生成</li>
                <li>在预览窗口中查看生成结果</li>
                <li>满意后点击"保存"将场景添加到项目中</li>
              </ol>
            </div>

            <div>
              <Title level={4}>快速模板</Title>
              <Paragraph>
                系统提供了多个预设模板，包括现代办公室、温馨客厅、图书馆、咖啡厅、科幻实验室等。
                点击模板按钮可以快速填充对应的场景描述。
              </Paragraph>
            </div>

            <div>
              <Title level={4}>高级设置</Title>
              <ul>
                <li><strong>风格选择</strong>：支持写实、卡通、简约、科幻、奇幻五种风格</li>
                <li><strong>质量等级</strong>：20-100级别，影响生成精度和处理时间</li>
                <li><strong>最大对象数</strong>：10-100个，控制场景复杂度</li>
                <li><strong>性能约束</strong>：可设置多边形数量限制和目标帧率</li>
              </ul>
            </div>

            <div>
              <Title level={4}>快捷键</Title>
              <ul>
                <li><strong>Ctrl+Shift+G</strong>：打开/关闭自然语言场景生成面板</li>
                <li><strong>Ctrl+Alt+G</strong>：快速打开场景生成对话框</li>
              </ul>
            </div>
          </Space>
        </Card>

        {/* 技术说明 */}
        <Card title="技术实现" size="small">
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            <Text>• <strong>前端框架</strong>：React + TypeScript + Ant Design</Text>
            <Text>• <strong>状态管理</strong>：Redux Toolkit</Text>
            <Text>• <strong>自然语言处理</strong>：实体识别、意图分类、情感分析</Text>
            <Text>• <strong>3D渲染</strong>：基于DL引擎的场景生成和渲染</Text>
            <Text>• <strong>性能优化</strong>：缓存机制、异步处理、LOD管理</Text>
          </Space>
        </Card>
      </Space>
    </div>
  );
};

export default NLPSceneGenerationExample;
