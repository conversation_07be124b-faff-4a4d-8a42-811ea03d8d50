/* 移动端区块链面板样式 */

/* 抽屉样式 */
.mobile-blockchain-drawer {
  z-index: 1050;
}

.mobile-blockchain-drawer .ant-drawer-content {
  border-radius: 16px 16px 0 0;
  overflow: hidden;
}

.mobile-blockchain-drawer .ant-drawer-body {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 卡片样式 */
.mobile-blockchain-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 头部样式 */
.mobile-blockchain-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.mobile-blockchain-title {
  flex: 1;
}

.mobile-blockchain-title .anticon {
  font-size: 18px;
  margin-right: 8px;
}

.mobile-blockchain-status {
  flex: 1;
  text-align: center;
}

.mobile-status-text {
  font-size: 12px;
  color: white !important;
}

.mobile-balance-text {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8) !important;
}

.mobile-blockchain-actions {
  flex: 0 0 auto;
}

.mobile-action-button {
  color: white !important;
  border: none !important;
}

.mobile-action-button:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

/* 内容区域 */
.mobile-blockchain-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 快速操作 */
.mobile-quick-actions {
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.mobile-quick-actions .ant-btn {
  border-radius: 20px;
  font-size: 12px;
  height: 32px;
  padding: 0 12px;
}

/* 标签页样式 */
.mobile-blockchain-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.mobile-blockchain-tabs .ant-tabs-content-holder {
  flex: 1;
  overflow: hidden;
}

.mobile-blockchain-tabs .ant-tabs-tabpane {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
}

/* 底部标签栏 */
.mobile-blockchain-tabs.ant-tabs-bottom .ant-tabs-nav {
  background: white;
  border-top: 1px solid #e9ecef;
  margin: 0;
  padding: 8px 0;
}

.mobile-blockchain-tabs.ant-tabs-bottom .ant-tabs-tab {
  margin: 0 4px;
  padding: 8px 12px;
  border-radius: 20px;
  transition: all 0.2s ease;
}

.mobile-blockchain-tabs.ant-tabs-bottom .ant-tabs-tab-active {
  background: #1890ff;
  color: white;
}

.mobile-blockchain-tabs.ant-tabs-bottom .ant-tabs-tab-active .mobile-tab-label {
  color: white;
}

.mobile-tab-label {
  font-size: 12px;
  margin-left: 4px;
}

/* 浮动按钮 */
.mobile-blockchain-float-button {
  position: fixed;
  bottom: 80px;
  right: 20px;
  z-index: 1000;
}

.mobile-blockchain-float-button .ant-float-btn {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 状态栏 */
.mobile-blockchain-status-bar {
  background: linear-gradient(90deg, #1890ff, #722ed1);
  color: white;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mobile-status-account,
.mobile-status-balance {
  color: white !important;
  font-size: 12px;
}

/* 快捷操作 */
.mobile-blockchain-shortcuts {
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 16px;
}

.mobile-shortcut-button {
  border-radius: 16px;
  font-size: 11px;
  height: 28px;
  padding: 0 8px;
  border: 1px solid #d9d9d9;
  background: white;
}

.mobile-shortcut-button:hover:not(:disabled) {
  border-color: #1890ff;
  color: #1890ff;
}

.mobile-shortcut-button:disabled {
  opacity: 0.5;
}

/* 响应式设计 */
@media (max-width: 576px) {
  .mobile-blockchain-header {
    padding: 12px 16px;
  }
  
  .mobile-blockchain-title .anticon {
    font-size: 16px;
  }
  
  .mobile-tab-label {
    display: none;
  }
  
  .mobile-blockchain-tabs.ant-tabs-bottom .ant-tabs-tab {
    padding: 8px;
    min-width: 48px;
    text-align: center;
  }
  
  .mobile-quick-actions {
    padding: 12px;
  }
  
  .mobile-blockchain-shortcuts {
    margin: 12px;
    padding: 8px 12px;
  }
  
  .mobile-shortcut-button {
    font-size: 10px;
    height: 24px;
    padding: 0 6px;
  }
}

@media (min-width: 577px) and (max-width: 768px) {
  .mobile-blockchain-drawer .ant-drawer-content {
    border-radius: 12px 12px 0 0;
  }
  
  .mobile-blockchain-tabs .ant-tabs-tabpane {
    padding: 20px;
  }
}

@media (min-width: 769px) {
  .mobile-blockchain-card {
    max-width: 480px;
    margin: 0 auto;
  }
  
  .mobile-blockchain-tabs {
    min-height: 600px;
  }
}

/* 暗色主题支持 */
[data-theme='dark'] .mobile-blockchain-header {
  background: linear-gradient(135deg, #434343 0%, #000000 100%);
}

[data-theme='dark'] .mobile-quick-actions {
  background: #1f1f1f;
  border-color: #434343;
}

[data-theme='dark'] .mobile-blockchain-tabs.ant-tabs-bottom .ant-tabs-nav {
  background: #1f1f1f;
  border-color: #434343;
}

[data-theme='dark'] .mobile-blockchain-tabs.ant-tabs-bottom .ant-tabs-tab-active {
  background: #1890ff;
}

[data-theme='dark'] .mobile-blockchain-status-bar {
  background: linear-gradient(90deg, #1890ff, #722ed1);
}

[data-theme='dark'] .mobile-blockchain-shortcuts {
  background: #1f1f1f;
}

[data-theme='dark'] .mobile-shortcut-button {
  background: #262626;
  border-color: #434343;
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .mobile-shortcut-button:hover:not(:disabled) {
  border-color: #1890ff;
  color: #1890ff;
}

/* 动画效果 */
.mobile-blockchain-drawer .ant-drawer-content {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.mobile-blockchain-tabs.ant-tabs-bottom .ant-tabs-tab {
  transition: all 0.2s ease;
}

.mobile-blockchain-tabs.ant-tabs-bottom .ant-tabs-tab:active {
  transform: scale(0.95);
}

.mobile-shortcut-button {
  transition: all 0.2s ease;
}

.mobile-shortcut-button:active:not(:disabled) {
  transform: scale(0.95);
}

/* 触摸优化 */
.mobile-blockchain-tabs.ant-tabs-bottom .ant-tabs-tab {
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobile-action-button {
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobile-shortcut-button {
  min-height: 32px;
  touch-action: manipulation;
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .mobile-blockchain-drawer .ant-drawer-body {
    padding-bottom: max(16px, env(safe-area-inset-bottom));
  }
  
  .mobile-blockchain-float-button {
    bottom: max(80px, calc(80px + env(safe-area-inset-bottom)));
  }
}

/* 横屏适配 */
@media (orientation: landscape) and (max-height: 500px) {
  .mobile-blockchain-drawer {
    height: 100vh !important;
  }
  
  .mobile-blockchain-header {
    padding: 8px 16px;
  }
  
  .mobile-quick-actions {
    padding: 8px 16px;
  }
  
  .mobile-blockchain-tabs .ant-tabs-tabpane {
    padding: 12px 16px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .mobile-blockchain-header {
    background: #000;
    border: 2px solid #fff;
  }
  
  .mobile-blockchain-tabs.ant-tabs-bottom .ant-tabs-tab-active {
    background: #000;
    color: #fff;
    border: 2px solid #fff;
  }
  
  .mobile-shortcut-button {
    border-width: 2px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .mobile-blockchain-drawer .ant-drawer-content {
    animation: none;
  }
  
  .mobile-blockchain-tabs.ant-tabs-bottom .ant-tabs-tab,
  .mobile-shortcut-button {
    transition: none;
  }
}
