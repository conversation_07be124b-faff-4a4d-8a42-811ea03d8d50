/**
 * DL（Digital Learning）引擎入口文件
 * 导出所有公共API
 */
import { Engine } from './core/Engine';
import { World } from './core/World';
import { Entity } from './core/Entity';
import { Component, ComponentState, ComponentConfig, ComponentDependency } from './core/Component';
import { System } from './core/System';
export { Engine, World, Entity, Component, System };
export type { ComponentState, ComponentConfig, ComponentDependency };
import { Renderer as CoreRenderer } from './rendering/Renderer';
import { Camera } from './rendering/Camera';
import { RenderSystem } from './rendering/RenderSystem';
import { Light } from './rendering/Light';
export { CoreRenderer, Camera, RenderSystem, Light };
export * from './rendering/materials/index';
export * from './scene/Scene';
export * from './scene/SceneManager';
export * from './scene/Transform';
export * from './scene/Skybox';
export * from './physics/PhysicsSystem';
export * from './physics/PhysicsRaycastResult';
export * from './physics/character/CharacterController';
export * from './physics/ccd/ContinuousCollisionDetection';
export * from './physics/debug/PhysicsDebugger';
export * from './physics/debug/EnhancedPhysicsDebugger';
export * from './physics/softbody/SoftBodySystem';
export * from './physics/softbody/SoftBodyComponent';
export * from './physics/constraints/SliderConstraint';
export * from './physics/constraints/FixedConstraint';
export * from './physics/constraints/WheelConstraint';
import type { PhysicsBody } from './physics/PhysicsBody';
import type { BodyType, PhysicsBodyOptions } from './physics/PhysicsBody';
export { PhysicsBody };
export type { BodyType, PhysicsBodyOptions };
import { PhysicsCollider } from './physics/PhysicsCollider';
import type { ColliderType as PhysicsColliderType, ColliderOptions as PhysicsColliderOptions } from './physics/PhysicsCollider';
export { PhysicsCollider };
export type { PhysicsColliderType, PhysicsColliderOptions };
export * from './physics/components/CharacterControllerComponent';
export * from './physics/components/PhysicsConstraintComponent';
export * from './physics/components/PhysicsWorldComponent';
import { PhysicsBodyComponent } from './physics/components/PhysicsBodyComponent';
import { PhysicsColliderComponent } from './physics/components/PhysicsColliderComponent';
export { PhysicsBodyComponent, PhysicsColliderComponent };
export * from './particles/ParticleSystem';
export * from './particles/ParticleEmitter';
export * from './particles/Particle';
export * from './assets/AssetManager';
export * from './assets/AssetLoader';
import { ResourceManager } from './assets/ResourceManager';
import type { AssetType as ResourceAssetType, ResourceState, ResourceInfo, ResourceManagerOptions } from './assets/ResourceManager';
export { ResourceManager };
export type { ResourceAssetType, ResourceState, ResourceInfo, ResourceManagerOptions };
export * from './gltf';
import type { AnimationClip } from './animation/AnimationClip';
import { Animator } from './animation/Animator';
import { AnimationSystem } from './animation/AnimationSystem';
import { BlendSpace1D } from './animation/BlendSpace1D';
import { BlendSpace2D } from './animation/BlendSpace2D';
import { AnimationStateMachine } from './animation/AnimationStateMachine';
export { AnimationClip, Animator, AnimationSystem, BlendSpace1D, BlendSpace2D, AnimationStateMachine };
import type { AnimationState, AnimationEventType } from './animation/Animator';
export type { AnimationState as AnimatorState, AnimationEventType as AnimatorEventType };
export * from './ui/UIModule';
import { InputSystem } from './input/InputSystem';
import { InputManager } from './input/InputManager';
import { InputEventType as SystemInputEventType, MouseButton as SystemMouseButton } from './input/InputSystem';
import type { InputManagerOptions } from './input/InputManager';
export { InputSystem, InputManager };
export type { InputManagerOptions, SystemInputEventType, SystemMouseButton };
export * from './input/InputDevice';
export * from './input/InputAction';
export * from './input/InputBinding';
export * from './input/InputMapping';
export * from './input/InputRecorder';
export * from './input/InputVisualizer';
export * from './input/components/InputComponent';
export * from './input/devices/KeyboardDevice';
export * from './input/devices/MouseDevice';
export * from './input/devices/GamepadDevice';
export * from './input/devices/TouchDevice';
export * from './input/devices/XRDevice';
import { InteractionSystem } from './interaction/InteractionSystem';
import type { InteractionSystemConfig } from './interaction/InteractionSystem';
import { InteractableComponent, InteractionType as InteractableInteractionType } from './interaction/components/InteractableComponent';
import type { InteractableComponentConfig, InteractionCallback } from './interaction/components/InteractableComponent';
import { InteractionEventComponent, InteractionEventType, InteractionEvent } from './interaction/components/InteractionEventComponent';
import type { InteractionEventComponentConfig, InteractionEventData, InteractionEventListener } from './interaction/components/InteractionEventComponent';
import { InteractionPromptComponent, PromptPositionType } from './interaction/components/InteractionPromptComponent';
import type { InteractionPromptComponentConfig } from './interaction/components/InteractionPromptComponent';
import { InteractionHighlightComponent, HighlightType } from './interaction/components/InteractionHighlightComponent';
import type { InteractionHighlightComponentConfig } from './interaction/components/InteractionHighlightComponent';
import { GrabSystem } from './interaction/systems/GrabSystem';
import type { GrabSystemConfig } from './interaction/systems/GrabSystem';
import { GrabbableComponent, GrabType, Hand } from './interaction/components/GrabbableComponent';
import type { GrabbableComponentConfig } from './interaction/components/GrabbableComponent';
import { GrabberComponent } from './interaction/components/GrabberComponent';
import type { GrabberComponentConfig } from './interaction/components/GrabberComponent';
import { GrabbedComponent } from './interaction/components/GrabbedComponent';
import type { GrabbedComponentConfig } from './interaction/components/GrabbedComponent';
import { PhysicsGrabComponent } from './interaction/components/PhysicsGrabComponent';
import type { PhysicsGrabComponentConfig } from './interaction/components/PhysicsGrabComponent';
import { GrabState, GrabEventType } from './interaction/state/GrabState';
import type { GrabEventData } from './interaction/state/GrabState';
export { InteractionSystem, InteractableComponent, InteractableInteractionType, InteractionEventComponent, InteractionEventType, InteractionEvent, InteractionPromptComponent, PromptPositionType, InteractionHighlightComponent, HighlightType, GrabSystem, GrabbableComponent, GrabType, Hand, GrabberComponent, GrabbedComponent, PhysicsGrabComponent, GrabState, GrabEventType };
export type { InteractionSystemConfig, InteractableComponentConfig, InteractionCallback, InteractionEventComponentConfig, InteractionEventData, InteractionEventListener, InteractionPromptComponentConfig, InteractionHighlightComponentConfig, GrabSystemConfig, GrabbableComponentConfig, GrabberComponentConfig, GrabbedComponentConfig, PhysicsGrabComponentConfig, GrabEventData };
export * from './audio/AudioSystem';
export * from './audio/AudioSource';
export * from './audio/AudioListener';
export * from './network/NetworkSystem';
export * from './network/NetworkManager';
export * from './network/NetworkConnection';
export * from './network/WebSocketConnection';
export * from './network/WebRTCConnection';
export * from './network/NetworkMessage';
export * from './network/NetworkEvent';
export * from './network/NetworkEntity';
export * from './network/NetworkUser';
export * from './network/MessageType';
export * from './network/MessageSerializer';
export * from './network/components/NetworkEntityComponent';
export * from './network/components/NetworkTransformComponent';
export * from './network/components/NetworkUserComponent';
export * from './utils/EventEmitter';
export * from './utils/Time';
export * from './utils/UUID';
import { Debug } from './utils/Debug';
import type { LogLevel as DebugLogLevel } from './utils/Debug';
import { Logger, LogLevel as LoggerLogLevel, defaultLogger, createLogger } from './utils/Logger';
import type { LoggerOptions } from './utils/Logger';
export { Debug, Logger, defaultLogger, createLogger };
export type { DebugLogLevel, LoggerLogLevel, LoggerOptions };
export * from './avatar/controllers';
export * from './i18n/I18n';
import { VisualScriptSystem } from './visualscript/VisualScriptSystem';
export { VisualScriptSystem };
export * from './visualscript/nodes/Node';
export * from './visualscript/execution/ExecutionContext';
export * from './visualscript/execution/Fiber';
import { NodeRegistry, ValidationResult as NodeValidationResult, NodeConstructor, NodeFactory, NodeValidator, NodeTypeInfo, NodeStatistics } from './visualscript/nodes/NodeRegistry';
export { NodeRegistry };
export type { NodeValidationResult, NodeConstructor, NodeFactory, NodeValidator, NodeTypeInfo, NodeStatistics };
import { BlockchainManager } from './blockchain/core/BlockchainManager';
import { CacheConfig as BlockchainCacheConfig } from './blockchain/cache/BlockchainCache';
export { BlockchainManager };
export type { BlockchainCacheConfig };
export * from './blockchain/types/BlockchainTypes';
export * from './blockchain/types/NFTTypes';
export * from './blockchain/core/WalletManager';
export * from './blockchain/core/ContractManager';
import { AvatarPath, PathPoint, PathInterpolator, PathValidator, ValidationResult as NavigationValidationResult, ValidationOptions as NavigationValidationOptions, AvatarPathComponent, PathFollowingComponent, PathFollowingState, AvatarPathSystem } from './navigation';
export { AvatarPath, PathPoint, PathInterpolator, PathValidator, AvatarPathComponent, PathFollowingComponent, PathFollowingState, AvatarPathSystem };
export type { NavigationValidationResult, NavigationValidationOptions };
export type { PathTrigger, PathMetadata, AvatarPathData, LoopMode, InterpolationType, PathEventType, AvatarPathOptions, PathFollowingOptions, NavigationSystemOptions, NavigationMeshOptions, PathFinderOptions, NavigationAgentOptions, NavigationObstacleOptions, PathEventData, NavigationPath, NavigationNode, NavigationTriangle, NavigationEdge } from './navigation';
import { AIContentGenerator } from './ai/AIContentGenerator';
import { AIRecommendationEngine } from './ai/AIRecommendationEngine';
import { AIEmotionAnalysisSystem } from './ai/AIEmotionAnalysisSystem';
import { ContentFeatureExtractor } from './ai/recommendation/ContentFeatureExtractor';
import { RealtimeRecommendationCache } from './ai/recommendation/RealtimeRecommendationCache';
import { UserBehaviorAnalyzer } from './ai/recommendation/UserBehaviorAnalyzer';
export { AIContentGenerator, AIRecommendationEngine, AIEmotionAnalysisSystem, ContentFeatureExtractor, RealtimeRecommendationCache, UserBehaviorAnalyzer };
export * from './safety';
