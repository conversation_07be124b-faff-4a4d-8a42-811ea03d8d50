/**
 * AI设计助手面板样式
 */

.ai-design-assistant-panel {
  .ant-modal-body {
    padding: 16px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .ai-suggestions {
    .suggestions-header {
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;
    }

    .ant-list {
      .ant-list-item {
        padding: 16px;
        border-bottom: 1px solid #f0f0f0;

        &:hover {
          background-color: #f9f9f9;
        }

        .ant-list-item-meta {
          .ant-list-item-meta-avatar {
            margin-right: 12px;

            .ant-avatar {
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }

          .ant-list-item-meta-title {
            margin-bottom: 8px;
            font-size: 14px;

            .ant-rate {
              margin-left: 8px;
            }
          }

          .ant-list-item-meta-description {
            font-size: 13px;
            line-height: 1.5;

            .ant-typography {
              margin-bottom: 8px;
            }

            .ant-space {
              margin-top: 8px;
            }
          }
        }

        .ant-list-item-action {
          margin-left: 16px;

          .ant-btn {
            margin-left: 8px;
          }
        }
      }
    }
  }

  .style-analysis {
    .ant-card {
      .ant-card-body {
        padding: 16px;
      }

      &.analysis-card {
        text-align: center;

        .ant-typography {
          margin-bottom: 8px;
        }
      }
    }

    .ant-statistic {
      .ant-statistic-title {
        font-size: 12px;
        color: #666666;
        margin-bottom: 4px;
      }

      .ant-statistic-content {
        .ant-statistic-content-value {
          font-size: 16px;
          font-weight: 600;
        }
      }
    }

    .color-palette {
      display: flex;
      justify-content: center;
      gap: 8px;
      margin-top: 8px;

      .color-swatch {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 1px solid #d9d9d9;
        transition: transform 0.2s ease;

        &:hover {
          transform: scale(1.2);
        }
      }
    }

    .metrics-grid {
      .ant-row {
        .ant-col {
          margin-bottom: 12px;
        }
      }
    }

    .trends-list {
      .ant-list-item {
        padding: 8px 0;

        .ant-list-item-meta {
          .ant-list-item-meta-avatar {
            margin-right: 8px;
          }

          .ant-list-item-meta-description {
            .ant-progress {
              margin-top: 4px;
            }
          }
        }
      }
    }
  }

  .suggestion-details {
    .ant-descriptions {
      .ant-descriptions-item-label {
        font-weight: 500;
        color: #333333;
      }

      .ant-descriptions-item-content {
        color: #666666;

        .ant-list {
          .ant-list-item {
            padding: 8px 0;
            border: none;

            .ant-list-item-meta {
              .ant-list-item-meta-title {
                font-size: 13px;
                margin-bottom: 4px;
              }

              .ant-list-item-meta-description {
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;

    .empty-icon {
      font-size: 48px;
      color: #d9d9d9;
      margin-bottom: 16px;
    }

    .ant-typography {
      margin-bottom: 8px;
    }
  }

  .analyzing-state {
    text-align: center;
    padding: 20px 0;

    .analyzing-icon {
      font-size: 24px;
      color: #1890ff;
      margin-bottom: 16px;
    }

    .ant-typography {
      margin-bottom: 8px;
    }
  }

  .ant-tabs {
    .ant-tabs-tab {
      .ant-badge {
        .ant-badge-count {
          font-size: 10px;
          min-width: 16px;
          height: 16px;
          line-height: 16px;
        }
      }
    }
  }
}

// 深色主题
.dark-theme {
  .ai-design-assistant-panel {
    .ant-card {
      background: #2d2d2d;
      border-color: #404040;

      .ant-card-head {
        background: #2d2d2d;
        border-bottom-color: #404040;

        .ant-card-head-title {
          color: #ffffff;
        }
      }

      .ant-card-body {
        background: #2d2d2d;
        color: #cccccc;
      }
    }

    .ai-suggestions {
      .suggestions-header {
        border-bottom-color: #404040;
      }

      .ant-list {
        .ant-list-item {
          border-bottom-color: #404040;

          &:hover {
            background-color: #404040;
          }

          .ant-list-item-meta {
            .ant-list-item-meta-title {
              color: #ffffff;
            }

            .ant-list-item-meta-description {
              color: #cccccc;
            }
          }
        }
      }
    }

    .style-analysis {
      .ant-statistic {
        .ant-statistic-title {
          color: #cccccc;
        }

        .ant-statistic-content {
          color: #ffffff;
        }
      }
    }

    .ant-descriptions {
      background: #2d2d2d;
      border-color: #404040;

      .ant-descriptions-item-label {
        background: #2d2d2d;
        color: #ffffff;
      }

      .ant-descriptions-item-content {
        background: #2d2d2d;
        color: #cccccc;
      }
    }

    .empty-state,
    .analyzing-state {
      .empty-icon,
      .analyzing-icon {
        color: #666666;
      }
    }
  }
}

// 紧凑模式
.compact-theme {
  .ai-design-assistant-panel {
    .ant-modal-body {
      padding: 12px;
    }

    .ant-card {
      .ant-card-body {
        padding: 12px;
      }
    }

    .ai-suggestions {
      .suggestions-header {
        margin-bottom: 12px;
        padding-bottom: 8px;
      }

      .ant-list {
        .ant-list-item {
          padding: 12px;

          .ant-list-item-meta {
            .ant-list-item-meta-title {
              font-size: 13px;
            }

            .ant-list-item-meta-description {
              font-size: 12px;
            }
          }
        }
      }
    }

    .style-analysis {
      .ant-statistic {
        .ant-statistic-content {
          .ant-statistic-content-value {
            font-size: 14px;
          }
        }
      }
    }

    .empty-state,
    .analyzing-state {
      padding: 30px 0;

      .empty-icon,
      .analyzing-icon {
        font-size: 36px;
        margin-bottom: 12px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .ai-design-assistant-panel {
    .ant-modal {
      width: 95% !important;
      max-width: none;
    }

    .style-analysis {
      .ant-row {
        .ant-col {
          margin-bottom: 12px;
        }
      }
    }

    .ai-suggestions {
      .suggestions-header {
        .ant-space {
          flex-direction: column;
          align-items: stretch;
          width: 100%;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .ai-design-assistant-panel {
    .style-analysis {
      .ant-row {
        .ant-col {
          flex: 0 0 100%;
          max-width: 100%;
        }
      }
    }

    .ant-tabs {
      .ant-tabs-tab {
        padding: 8px 12px;
        font-size: 12px;
      }
    }

    .ai-suggestions {
      .ant-list {
        .ant-list-item {
          .ant-list-item-action {
            margin-left: 8px;

            .ant-btn {
              margin-left: 4px;
              font-size: 12px;
              padding: 4px 8px;
            }
          }
        }
      }
    }
  }
}

// 动画效果
.ai-design-assistant-panel {
  .ant-card {
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .ai-suggestions {
    .ant-list-item {
      transition: background-color 0.2s ease;
    }
  }

  .style-analysis {
    .color-swatch {
      transition: transform 0.2s ease;
    }
  }

  .ant-btn {
    transition: all 0.2s ease;
  }

  .ant-tag {
    transition: all 0.2s ease;
  }

  .ant-progress {
    .ant-progress-bg {
      transition: background-color 0.3s ease;
    }
  }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
  .ai-design-assistant-panel {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }

    .style-analysis {
      .color-swatch {
        &:hover {
          transform: none;
        }
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .ai-design-assistant-panel {
    .ant-card {
      border: 2px solid #000;
    }

    .ant-list {
      .ant-list-item {
        border-bottom: 2px solid #000;
      }
    }

    .ant-descriptions {
      border: 2px solid #000;

      .ant-descriptions-item {
        border: 1px solid #000;
      }
    }

    .suggestions-header {
      border-bottom: 2px solid #000;
    }

    .style-analysis {
      .color-swatch {
        border: 2px solid #000;
      }
    }
  }
}

// AI助手特定样式
.ai-design-assistant-panel {
  .suggestion-priority-critical {
    border-left: 4px solid #ff4d4f;
  }

  .suggestion-priority-high {
    border-left: 4px solid #faad14;
  }

  .suggestion-priority-medium {
    border-left: 4px solid #1890ff;
  }

  .suggestion-priority-low {
    border-left: 4px solid #52c41a;
  }

  .confidence-high {
    color: #52c41a;
  }

  .confidence-medium {
    color: #faad14;
  }

  .confidence-low {
    color: #ff4d4f;
  }

  .ai-thinking {
    animation: aiThinking 2s ease-in-out infinite;
  }
}

@keyframes aiThinking {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
