/**
 * CrossPlatformAdapter.ts
 * 
 * 跨平台组件适配器，支持移动端和桌面端组件
 */

import { EventEmitter } from '../utils/EventEmitter';

/**
 * 平台类型枚举
 */
export enum PlatformType {
  WEB = 'web',
  MOBILE = 'mobile',
  DESKTOP = 'desktop',
  TABLET = 'tablet'
}

/**
 * 设备特性接口
 */
export interface DeviceCapabilities {
  /** 屏幕尺寸 */
  screenSize: {
    width: number;
    height: number;
    density: number;
  };
  /** 触摸支持 */
  touchSupport: boolean;
  /** 键盘支持 */
  keyboardSupport: boolean;
  /** 鼠标支持 */
  mouseSupport: boolean;
  /** 摄像头支持 */
  cameraSupport: boolean;
  /** 地理位置支持 */
  geolocationSupport: boolean;
  /** 推送通知支持 */
  notificationSupport: boolean;
  /** 文件系统访问 */
  fileSystemAccess: boolean;
  /** 网络状态检测 */
  networkStatusDetection: boolean;
  /** 设备方向检测 */
  orientationDetection: boolean;
}

/**
 * 平台特定样式接口
 */
export interface PlatformStyles {
  /** 基础样式 */
  base: Record<string, any>;
  /** 网页端样式 */
  web?: Record<string, any>;
  /** 移动端样式 */
  mobile?: Record<string, any>;
  /** 桌面端样式 */
  desktop?: Record<string, any>;
  /** 平板样式 */
  tablet?: Record<string, any>;
  /** 响应式断点 */
  breakpoints?: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
}

/**
 * 跨平台组件配置
 */
export interface CrossPlatformComponentConfig {
  /** 组件ID */
  id: string;
  /** 组件名称 */
  name: string;
  /** 支持的平台 */
  supportedPlatforms: PlatformType[];
  /** 平台特定实现 */
  implementations: {
    [key in PlatformType]?: {
      component: React.ComponentType<any>;
      styles?: PlatformStyles;
      props?: Record<string, any>;
    };
  };
  /** 默认属性 */
  defaultProps: Record<string, any>;
  /** 平台适配规则 */
  adaptationRules?: {
    [key in PlatformType]?: {
      propMappings?: Record<string, string>;
      styleTransforms?: Array<(styles: any) => any>;
      eventMappings?: Record<string, string>;
    };
  };
}

/**
 * 组件适配结果
 */
export interface AdaptedComponent {
  /** 适配后的组件 */
  component: React.ComponentType<any>;
  /** 适配后的属性 */
  props: Record<string, any>;
  /** 适配后的样式 */
  styles: Record<string, any>;
  /** 平台特定事件处理 */
  eventHandlers: Record<string, Function>;
}

/**
 * 跨平台适配器类
 */
export class CrossPlatformAdapter extends EventEmitter {
  private static instance: CrossPlatformAdapter;
  private currentPlatform: PlatformType;
  private deviceCapabilities: DeviceCapabilities;
  private componentRegistry: Map<string, CrossPlatformComponentConfig> = new Map();
  private adaptationCache: Map<string, AdaptedComponent> = new Map();

  private constructor() {
    super();
    this.currentPlatform = this.detectPlatform();
    this.deviceCapabilities = this.detectDeviceCapabilities();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): CrossPlatformAdapter {
    if (!CrossPlatformAdapter.instance) {
      CrossPlatformAdapter.instance = new CrossPlatformAdapter();
    }
    return CrossPlatformAdapter.instance;
  }

  /**
   * 检测当前平台
   */
  private detectPlatform(): PlatformType {
    const userAgent = navigator.userAgent.toLowerCase();
    const isElectron = window.navigator.userAgent.includes('Electron');
    
    if (isElectron) {
      return PlatformType.DESKTOP;
    }
    
    if (/android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)) {
      // 进一步区分手机和平板
      const isTablet = /ipad|android(?!.*mobile)/i.test(userAgent) || 
                      (window.screen.width >= 768 && window.screen.height >= 1024);
      return isTablet ? PlatformType.TABLET : PlatformType.MOBILE;
    }
    
    return PlatformType.WEB;
  }

  /**
   * 检测设备能力
   */
  private detectDeviceCapabilities(): DeviceCapabilities {
    return {
      screenSize: {
        width: window.screen.width,
        height: window.screen.height,
        density: window.devicePixelRatio || 1
      },
      touchSupport: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
      keyboardSupport: this.currentPlatform !== PlatformType.MOBILE,
      mouseSupport: this.currentPlatform === PlatformType.WEB || this.currentPlatform === PlatformType.DESKTOP,
      cameraSupport: 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices,
      geolocationSupport: 'geolocation' in navigator,
      notificationSupport: 'Notification' in window,
      fileSystemAccess: 'showOpenFilePicker' in window || this.currentPlatform === PlatformType.DESKTOP,
      networkStatusDetection: 'onLine' in navigator,
      orientationDetection: 'orientation' in window || ('screen' in window && 'orientation' in (window as any).screen)
    };
  }

  /**
   * 注册跨平台组件
   */
  public registerComponent(config: CrossPlatformComponentConfig): void {
    this.componentRegistry.set(config.id, config);
    this.emit('componentRegistered', config);
  }

  /**
   * 适配组件到当前平台
   */
  public adaptComponent(componentId: string, props: Record<string, any> = {}): AdaptedComponent | null {
    const cacheKey = `${componentId}_${this.currentPlatform}_${JSON.stringify(props).slice(0, 100)}`;
    
    // 检查缓存
    if (this.adaptationCache.has(cacheKey)) {
      return this.adaptationCache.get(cacheKey)!;
    }

    const config = this.componentRegistry.get(componentId);
    if (!config) {
      console.warn(`组件 ${componentId} 未注册`);
      return null;
    }

    // 检查平台支持
    if (!config.supportedPlatforms.includes(this.currentPlatform)) {
      console.warn(`组件 ${componentId} 不支持平台 ${this.currentPlatform}`);
      return null;
    }

    const implementation = config.implementations[this.currentPlatform];
    if (!implementation) {
      console.warn(`组件 ${componentId} 缺少 ${this.currentPlatform} 平台实现`);
      return null;
    }

    // 适配属性
    const adaptedProps = this.adaptProps(config, props);
    
    // 适配样式
    const adaptedStyles = this.adaptStyles(config, implementation.styles);
    
    // 适配事件处理
    const eventHandlers = this.adaptEventHandlers(config, props);

    const adaptedComponent: AdaptedComponent = {
      component: implementation.component,
      props: { ...config.defaultProps, ...implementation.props, ...adaptedProps },
      styles: adaptedStyles,
      eventHandlers
    };

    // 缓存结果
    this.adaptationCache.set(cacheKey, adaptedComponent);

    return adaptedComponent;
  }

  /**
   * 适配属性
   */
  private adaptProps(config: CrossPlatformComponentConfig, props: Record<string, any>): Record<string, any> {
    const adaptationRules = config.adaptationRules?.[this.currentPlatform];
    if (!adaptationRules?.propMappings) {
      return props;
    }

    const adaptedProps = { ...props };
    
    Object.entries(adaptationRules.propMappings).forEach(([originalProp, mappedProp]) => {
      if (originalProp in adaptedProps) {
        adaptedProps[mappedProp] = adaptedProps[originalProp];
        delete adaptedProps[originalProp];
      }
    });

    return adaptedProps;
  }

  /**
   * 适配样式
   */
  private adaptStyles(config: CrossPlatformComponentConfig, platformStyles?: PlatformStyles): Record<string, any> {
    if (!platformStyles) {
      return {};
    }

    let styles = { ...platformStyles.base };

    // 应用平台特定样式
    const platformSpecificStyles = platformStyles[this.currentPlatform];
    if (platformSpecificStyles) {
      styles = { ...styles, ...platformSpecificStyles };
    }

    // 应用响应式样式
    if (platformStyles.breakpoints) {
      const screenWidth = this.deviceCapabilities.screenSize.width;
      const breakpoints = platformStyles.breakpoints;

      if (screenWidth <= breakpoints.mobile && platformStyles.mobile) {
        styles = { ...styles, ...platformStyles.mobile };
      } else if (screenWidth <= breakpoints.tablet && platformStyles.tablet) {
        styles = { ...styles, ...platformStyles.tablet };
      } else if (platformStyles.desktop) {
        styles = { ...styles, ...platformStyles.desktop };
      }
    }

    // 应用样式转换规则
    const adaptationRules = config.adaptationRules?.[this.currentPlatform];
    if (adaptationRules?.styleTransforms) {
      adaptationRules.styleTransforms.forEach(transform => {
        styles = transform(styles);
      });
    }

    return styles;
  }

  /**
   * 适配事件处理
   */
  private adaptEventHandlers(config: CrossPlatformComponentConfig, props: Record<string, any>): Record<string, Function> {
    const adaptationRules = config.adaptationRules?.[this.currentPlatform];
    const eventHandlers: Record<string, Function> = {};

    if (!adaptationRules?.eventMappings) {
      return eventHandlers;
    }

    Object.entries(adaptationRules.eventMappings).forEach(([originalEvent, mappedEvent]) => {
      const handler = props[originalEvent];
      if (typeof handler === 'function') {
        eventHandlers[mappedEvent] = handler;
      }
    });

    return eventHandlers;
  }

  /**
   * 获取当前平台
   */
  public getCurrentPlatform(): PlatformType {
    return this.currentPlatform;
  }

  /**
   * 获取设备能力
   */
  public getDeviceCapabilities(): DeviceCapabilities {
    return this.deviceCapabilities;
  }

  /**
   * 检查平台支持
   */
  public isPlatformSupported(componentId: string, platform: PlatformType = this.currentPlatform): boolean {
    const config = this.componentRegistry.get(componentId);
    return config ? config.supportedPlatforms.includes(platform) : false;
  }

  /**
   * 获取支持的组件列表
   */
  public getSupportedComponents(platform: PlatformType = this.currentPlatform): string[] {
    return Array.from(this.componentRegistry.values())
      .filter(config => config.supportedPlatforms.includes(platform))
      .map(config => config.id);
  }

  /**
   * 清除适配缓存
   */
  public clearCache(): void {
    this.adaptationCache.clear();
  }

  /**
   * 监听平台变化
   */
  public onPlatformChange(callback: (platform: PlatformType) => void): void {
    this.on('platformChanged', callback);
  }

  /**
   * 手动设置平台（用于测试）
   */
  public setPlatform(platform: PlatformType): void {
    if (this.currentPlatform !== platform) {
      this.currentPlatform = platform;
      this.deviceCapabilities = this.detectDeviceCapabilities();
      this.clearCache();
      this.emit('platformChanged', platform);
    }
  }

  /**
   * 获取平台特定的样式工具
   */
  public getStyleUtils() {
    return {
      /**
       * 创建响应式样式
       */
      responsive: (styles: {
        mobile?: Record<string, any>;
        tablet?: Record<string, any>;
        desktop?: Record<string, any>;
        web?: Record<string, any>;
      }) => {
        return styles[this.currentPlatform] || styles.web || {};
      },

      /**
       * 根据设备能力调整样式
       */
      adaptToCapabilities: (styles: Record<string, any>) => {
        const adapted = { ...styles };

        // 触摸设备调整
        if (this.deviceCapabilities.touchSupport) {
          adapted.minHeight = Math.max(adapted.minHeight || 0, 44); // 最小触摸目标
          adapted.padding = Math.max(adapted.padding || 0, 8);
        }

        // 高密度屏幕调整
        if (this.deviceCapabilities.screenSize.density > 1) {
          adapted.borderWidth = (adapted.borderWidth || 1) / this.deviceCapabilities.screenSize.density;
        }

        return adapted;
      },

      /**
       * 获取平台特定的CSS类名
       */
      getPlatformClass: (baseClass: string) => {
        return `${baseClass} ${baseClass}--${this.currentPlatform}`;
      }
    };
  }
}

// 导出单例实例
export const crossPlatformAdapter = CrossPlatformAdapter.getInstance();
