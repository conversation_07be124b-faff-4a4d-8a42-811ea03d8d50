/**
 * 超低延迟WebRTC服务
 * 提供WebRTC连接和性能监控功能
 */
import { EventEmitter } from '../utils/EventEmitter';

// 延迟指标接口
export interface LatencyMetrics {
  // 往返时间（毫秒）
  rtt: number;
  // 抖动（毫秒）
  jitter: number;
  // 丢包率（百分比）
  packetLoss: number;
  // 带宽（Mbps）
  bandwidth: number;
  // 帧率
  fps: number;
  // 时间戳
  timestamp: number;
}

// 连接状态枚举
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  FAILED = 'failed',
  RECONNECTING = 'reconnecting'
}

// 连接质量枚举
export enum ConnectionQuality {
  POOR = 'poor',
  FAIR = 'fair',
  GOOD = 'good',
  EXCELLENT = 'excellent'
}

// WebRTC配置接口
export interface WebRTCConfig {
  // ICE服务器配置
  iceServers: RTCIceServer[];
  // 目标延迟（毫秒）
  targetLatency: number;
  // 最大重连次数
  maxReconnectAttempts: number;
  // 重连间隔（毫秒）
  reconnectInterval: number;
  // 是否启用自动优化
  autoOptimize: boolean;
}

/**
 * 超低延迟WebRTC服务类
 */
export class UltraLowLatencyWebRTC extends EventEmitter {
  private peerConnection: RTCPeerConnection | null = null;
  private dataChannel: RTCDataChannel | null = null;
  private localStream: MediaStream | null = null;
  private remoteStream: MediaStream | null = null;
  private connectionState: ConnectionState = ConnectionState.DISCONNECTED;
  private connectionQuality: ConnectionQuality = ConnectionQuality.POOR;
  private config: WebRTCConfig;
  private metricsInterval: NodeJS.Timeout | null = null;
  private reconnectAttempts = 0;

  constructor(config: Partial<WebRTCConfig> = {}) {
    super();
    
    this.config = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' }
      ],
      targetLatency: 30,
      maxReconnectAttempts: 5,
      reconnectInterval: 3000,
      autoOptimize: true,
      ...config
    };
  }

  /**
   * 初始化WebRTC连接
   */
  async initialize(): Promise<void> {
    try {
      this.peerConnection = new RTCPeerConnection({
        iceServers: this.config.iceServers
      });

      this.setupPeerConnectionEvents();
      this.createDataChannel();
      
      this.connectionState = ConnectionState.CONNECTING;
      this.emit('stateChanged', this.connectionState);
      
    } catch (error) {
      console.error('Failed to initialize WebRTC:', error);
      this.connectionState = ConnectionState.FAILED;
      this.emit('stateChanged', this.connectionState);
      throw error;
    }
  }

  /**
   * 设置对等连接事件监听器
   */
  private setupPeerConnectionEvents(): void {
    if (!this.peerConnection) return;

    this.peerConnection.oniceconnectionstatechange = () => {
      if (!this.peerConnection) return;
      
      const state = this.peerConnection.iceConnectionState;
      console.log('ICE connection state:', state);
      
      switch (state) {
        case 'connected':
        case 'completed':
          this.connectionState = ConnectionState.CONNECTED;
          this.startMetricsCollection();
          break;
        case 'disconnected':
          this.connectionState = ConnectionState.DISCONNECTED;
          this.stopMetricsCollection();
          break;
        case 'failed':
          this.connectionState = ConnectionState.FAILED;
          this.handleConnectionFailure();
          break;
      }
      
      this.emit('stateChanged', this.connectionState);
    };

    this.peerConnection.ontrack = (event) => {
      this.remoteStream = event.streams[0];
      this.emit('remoteStreamReceived', this.remoteStream);
    };
  }

  /**
   * 创建数据通道
   */
  private createDataChannel(): void {
    if (!this.peerConnection) return;

    this.dataChannel = this.peerConnection.createDataChannel('metrics', {
      ordered: true
    });

    this.dataChannel.onopen = () => {
      console.log('Data channel opened');
    };

    this.dataChannel.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.handleDataChannelMessage(data);
      } catch (error) {
        console.error('Failed to parse data channel message:', error);
      }
    };
  }

  /**
   * 处理数据通道消息
   */
  private handleDataChannelMessage(data: any): void {
    if (data.type === 'metrics') {
      this.emit('metricsReceived', data.metrics);
    }
  }

  /**
   * 开始收集性能指标
   */
  private startMetricsCollection(): void {
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
    }

    this.metricsInterval = setInterval(async () => {
      const metrics = await this.collectMetrics();
      if (metrics) {
        this.emit('metricsUpdated', metrics);
        this.updateConnectionQuality(metrics);
        
        if (this.config.autoOptimize) {
          this.optimizeConnection(metrics);
        }
      }
    }, 1000);
  }

  /**
   * 停止收集性能指标
   */
  private stopMetricsCollection(): void {
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
      this.metricsInterval = null;
    }
  }

  /**
   * 收集性能指标
   */
  private async collectMetrics(): Promise<LatencyMetrics | null> {
    if (!this.peerConnection) return null;

    try {
      const stats = await this.peerConnection.getStats();
      let rtt = 0;
      let jitter = 0;
      let packetLoss = 0;
      let bandwidth = 0;
      let fps = 0;

      stats.forEach((report) => {
        if (report.type === 'candidate-pair' && report.state === 'succeeded') {
          rtt = report.currentRoundTripTime * 1000 || 0;
        }
        
        if (report.type === 'inbound-rtp' && report.mediaType === 'video') {
          jitter = report.jitter * 1000 || 0;
          packetLoss = report.packetsLost || 0;
          fps = report.framesPerSecond || 0;
        }
        
        if (report.type === 'transport') {
          bandwidth = (report.bytesReceived || 0) / 1024 / 1024; // MB/s
        }
      });

      return {
        rtt,
        jitter,
        packetLoss,
        bandwidth,
        fps,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Failed to collect metrics:', error);
      return null;
    }
  }

  /**
   * 更新连接质量
   */
  private updateConnectionQuality(metrics: LatencyMetrics): void {
    const { rtt, packetLoss, fps } = metrics;
    
    if (rtt < 50 && packetLoss < 1 && fps > 50) {
      this.connectionQuality = ConnectionQuality.EXCELLENT;
    } else if (rtt < 100 && packetLoss < 3 && fps > 30) {
      this.connectionQuality = ConnectionQuality.GOOD;
    } else if (rtt < 200 && packetLoss < 5 && fps > 20) {
      this.connectionQuality = ConnectionQuality.FAIR;
    } else {
      this.connectionQuality = ConnectionQuality.POOR;
    }
    
    this.emit('qualityChanged', this.connectionQuality);
  }

  /**
   * 优化连接
   */
  private optimizeConnection(metrics: LatencyMetrics): void {
    // 根据指标自动调整连接参数
    if (metrics.rtt > this.config.targetLatency * 2) {
      // 延迟过高，降低质量
      this.emit('optimizationSuggestion', {
        type: 'reduce_quality',
        reason: 'High latency detected'
      });
    }
    
    if (metrics.packetLoss > 5) {
      // 丢包率过高，建议重连
      this.emit('optimizationSuggestion', {
        type: 'reconnect',
        reason: 'High packet loss detected'
      });
    }
  }

  /**
   * 处理连接失败
   */
  private handleConnectionFailure(): void {
    if (this.reconnectAttempts < this.config.maxReconnectAttempts) {
      this.reconnectAttempts++;
      this.connectionState = ConnectionState.RECONNECTING;
      this.emit('stateChanged', this.connectionState);
      
      setTimeout(() => {
        this.reconnect();
      }, this.config.reconnectInterval);
    } else {
      this.emit('connectionFailed', 'Max reconnect attempts reached');
    }
  }

  /**
   * 重新连接
   */
  private async reconnect(): Promise<void> {
    try {
      await this.disconnect();
      await this.initialize();
    } catch (error) {
      console.error('Reconnection failed:', error);
      this.handleConnectionFailure();
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    this.stopMetricsCollection();
    
    if (this.dataChannel) {
      this.dataChannel.close();
      this.dataChannel = null;
    }
    
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }
    
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }
    
    this.connectionState = ConnectionState.DISCONNECTED;
    this.reconnectAttempts = 0;
    this.emit('stateChanged', this.connectionState);
  }

  /**
   * 获取当前连接状态
   */
  getConnectionState(): ConnectionState {
    return this.connectionState;
  }

  /**
   * 获取当前连接质量
   */
  getConnectionQuality(): ConnectionQuality {
    return this.connectionQuality;
  }

  /**
   * 获取当前配置
   */
  getConfig(): WebRTCConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<WebRTCConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

export default UltraLowLatencyWebRTC;
