/**
 * 资源依赖管理器
 * 用于管理资源依赖关系，提供依赖分析和优化功能
 */
import React, { useState, useEffect, useCallback } from 'react';
import { Card, Row, Col, Table, Button, Space, Modal, Form, Select, Tag, message, Tabs, Drawer, InputNumber } from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  ReloadOutlined,
  NodeIndexOutlined
} from '@ant-design/icons';
// import { useTranslation } from 'react-i18next'; // 暂时未使用
import type { DependencyType } from '../../libs/dl-engine-types';
import { AssetType } from '../../libs/dl-engine-types';

// 本地枚举常量，对应 DependencyType
const DependencyTypeValues = {
  STRONG: 'strong' as const,
  WEAK: 'weak' as const
} as const;
import { ResourceDependencyService, ResourceInfo, DependencyInfo, OptimizationSuggestion } from '../../services/ResourceDependencyService';
import ResourceDependencyVisualizerPanel from './ResourceDependencyVisualizerPanel';
import './ResourceDependencyManager.less';

const { TabPane } = Tabs;
const { Option } = Select;

// 组件属性接口
interface ResourceDependencyManagerProps {
  className?: string;
}

/**
 * 资源依赖管理器组件
 */
const ResourceDependencyManager: React.FC<ResourceDependencyManagerProps> = ({
  className
}) => {
  // const { t } = useTranslation(); // 暂时未使用
  const [resources, setResources] = useState<ResourceInfo[]>([]);
  const [dependencies, setDependencies] = useState<Record<string, DependencyInfo[]>>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedResourceId, setSelectedResourceId] = useState<string | null>(null);
  const [selectedResource, setSelectedResource] = useState<ResourceInfo | null>(null);
  const [addDependencyModalVisible, setAddDependencyModalVisible] = useState<boolean>(false);
  const [editDependencyModalVisible, setEditDependencyModalVisible] = useState<boolean>(false);
  const [currentDependency, setCurrentDependency] = useState<DependencyInfo | null>(null);
  const [visualizerVisible, setVisualizerVisible] = useState<boolean>(false);
  const [optimizationSuggestions, setOptimizationSuggestions] = useState<OptimizationSuggestion[]>([]);
  const [activeTab, setActiveTab] = useState<string>('dependencies');
  
  // 表单实例
  const [form] = Form.useForm();
  
  // 依赖服务
  const dependencyService = ResourceDependencyService.getInstance();

  // 加载资源列表
  const loadResources = useCallback(async () => {
    setLoading(true);
    try {
      const resources = await dependencyService.getAllResources();
      setResources(resources);
    } catch (error) {
      console.error('加载资源列表失败:', error);
      message.error('加载资源列表失败');
    } finally {
      setLoading(false);
    }
  }, [dependencyService]);

  // 加载依赖关系
  const loadDependencies = useCallback(async (resourceId: string) => {
    if (!resourceId) return;
    
    setLoading(true);
    try {
      const dependencies = await dependencyService.getResourceDependencies(resourceId);
      setDependencies(prev => ({
        ...prev,
        [resourceId]: dependencies
      }));
    } catch (error) {
      console.error(`加载依赖关系失败: ${resourceId}`, error);
      message.error(`加载依赖关系失败: ${resourceId}`);
    } finally {
      setLoading(false);
    }
  }, [dependencyService]);

  // 加载资源详情
  const loadResourceDetails = useCallback(async (resourceId: string) => {
    if (!resourceId) {
      setSelectedResource(null);
      return;
    }
    
    setLoading(true);
    try {
      const resource = await dependencyService.getResourceById(resourceId);
      setSelectedResource(resource);
      
      // 加载依赖关系
      await loadDependencies(resourceId);
      
      // 加载优化建议
      const analysis = await dependencyService.analyzeDependencies(resourceId);
      const suggestions = await dependencyService.generateOptimizationSuggestions(analysis);
      setOptimizationSuggestions(suggestions);
    } catch (error) {
      console.error(`加载资源详情失败: ${resourceId}`, error);
      message.error(`加载资源详情失败: ${resourceId}`);
    } finally {
      setLoading(false);
    }
  }, [dependencyService, loadDependencies]);

  // 初始加载
  useEffect(() => {
    loadResources();
  }, [loadResources]);

  // 当选择的资源变化时，加载资源详情
  useEffect(() => {
    if (selectedResourceId) {
      loadResourceDetails(selectedResourceId);
    } else {
      setSelectedResource(null);
      setDependencies({});
      setOptimizationSuggestions([]);
    }
  }, [selectedResourceId, loadResourceDetails]);

  // 处理添加依赖
  const handleAddDependency = useCallback(async (values: any) => {
    if (!selectedResourceId) return;
    
    try {
      const success = await dependencyService.addDependency(
        selectedResourceId,
        values.dependencyId,
        values.type,
        values.priority
      );
      
      if (success) {
        message.success('添加依赖成功');
        setAddDependencyModalVisible(false);
        form.resetFields();
        
        // 重新加载依赖关系
        await loadDependencies(selectedResourceId);
      }
    } catch (error) {
      console.error('添加依赖失败:', error);
      message.error('添加依赖失败');
    }
  }, [dependencyService, selectedResourceId, form, loadDependencies]);

  // 处理编辑依赖
  const handleEditDependency = useCallback(async (values: any) => {
    if (!selectedResourceId || !currentDependency) return;
    
    try {
      // 先移除旧依赖
      await dependencyService.removeDependency(selectedResourceId, currentDependency.id);
      
      // 添加新依赖
      const success = await dependencyService.addDependency(
        selectedResourceId,
        values.dependencyId,
        values.type,
        values.priority
      );
      
      if (success) {
        message.success('更新依赖成功');
        setEditDependencyModalVisible(false);
        setCurrentDependency(null);
        form.resetFields();
        
        // 重新加载依赖关系
        await loadDependencies(selectedResourceId);
      }
    } catch (error) {
      console.error('更新依赖失败:', error);
      message.error('更新依赖失败');
    }
  }, [dependencyService, selectedResourceId, currentDependency, form, loadDependencies]);

  // 处理移除依赖
  const handleRemoveDependency = useCallback(async (dependencyId: string) => {
    if (!selectedResourceId) return;
    
    Modal.confirm({
      title: '确认移除依赖',
      content: `确定要移除依赖 ${dependencyId} 吗？`,
      onOk: async () => {
        try {
          const success = await dependencyService.removeDependency(selectedResourceId, dependencyId);
          
          if (success) {
            message.success('移除依赖成功');
            
            // 重新加载依赖关系
            await loadDependencies(selectedResourceId);
          }
        } catch (error) {
          console.error('移除依赖失败:', error);
          message.error('移除依赖失败');
        }
      }
    });
  }, [dependencyService, selectedResourceId, loadDependencies]);

  // 资源列表列定义
  const resourceColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100},
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 150},
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: AssetType) => (
        <Tag color={
          type === AssetType.TEXTURE ? 'blue' :
          type === AssetType.MODEL ? 'green' :
          type === AssetType.MATERIAL ? 'gold' :
          type === AssetType.AUDIO ? 'purple' :
          type === AssetType.SHADER ? 'magenta' :
          'default'
        }>
          {type}
        </Tag>
      )},
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
      width: 100,
      render: (size: number) => formatBytes(size)},
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: string, record: ResourceInfo) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => setSelectedResourceId(record.id)}
            title="查看依赖"
          />
          <Button
            type="text"
            icon={<NodeIndexOutlined />}
            onClick={() => {
              setSelectedResourceId(record.id);
              setVisualizerVisible(true);
            }}
            title="可视化依赖"
          />
        </Space>
      )},
  ];

  // 依赖列表列定义
  const dependencyColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100},
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: DependencyType) => (
        <Tag color={
          type === DependencyTypeValues.STRONG ? 'blue' :
          type === DependencyTypeValues.WEAK ? 'green' :
          'default'
        }>
          {type === DependencyTypeValues.STRONG ? '强依赖' :
           type === DependencyTypeValues.WEAK ? '弱依赖' :
           type}
        </Tag>
      )},
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80},
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: string, record: DependencyInfo) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => {
              setCurrentDependency(record);
              form.setFieldsValue({
                dependencyId: record.id,
                type: record.type,
                priority: record.priority || 0
              });
              setEditDependencyModalVisible(true);
            }}
            title="编辑依赖"
          />
          <Button
            type="text"
            icon={<DeleteOutlined />}
            onClick={() => handleRemoveDependency(record.id)}
            title="移除依赖"
            danger
          />
        </Space>
      )},
  ];

  // 格式化字节大小
  const formatBytes = (bytes: number, decimals: number = 2): string => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  };

  // 渲染资源详情
  const renderResourceDetails = () => {
    if (!selectedResource) {
      return (
        <div className="empty-details">
          <p>请选择一个资源查看详情</p>
        </div>
      );
    }

    return (
      <div className="resource-details">
        <Card title="资源信息" className="details-card">
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <div className="detail-item">
                <div className="detail-label">ID:</div>
                <div className="detail-value">{selectedResource.id}</div>
              </div>
            </Col>
            <Col span={12}>
              <div className="detail-item">
                <div className="detail-label">名称:</div>
                <div className="detail-value">{selectedResource.name}</div>
              </div>
            </Col>
            <Col span={12}>
              <div className="detail-item">
                <div className="detail-label">类型:</div>
                <div className="detail-value">
                  <Tag color={
                    selectedResource.type === AssetType.TEXTURE ? 'blue' :
                    selectedResource.type === AssetType.MODEL ? 'green' :
                    selectedResource.type === AssetType.MATERIAL ? 'gold' :
                    selectedResource.type === AssetType.AUDIO ? 'purple' :
                    selectedResource.type === AssetType.SHADER ? 'magenta' :
                    'default'
                  }>
                    {selectedResource.type}
                  </Tag>
                </div>
              </div>
            </Col>
            <Col span={12}>
              <div className="detail-item">
                <div className="detail-label">大小:</div>
                <div className="detail-value">{formatBytes(selectedResource.size)}</div>
              </div>
            </Col>
            <Col span={24}>
              <div className="detail-item">
                <div className="detail-label">URL:</div>
                <div className="detail-value">{selectedResource.url}</div>
              </div>
            </Col>
            <Col span={12}>
              <div className="detail-item">
                <div className="detail-label">最后修改:</div>
                <div className="detail-value">{new Date(selectedResource.lastModified).toLocaleString()}</div>
              </div>
            </Col>
          </Row>
        </Card>

        <Tabs activeKey={activeTab} onChange={setActiveTab} className="details-tabs">
          <TabPane tab="依赖关系" key="dependencies">
            <Card
              title="依赖列表"
              extra={
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    form.resetFields();
                    setAddDependencyModalVisible(true);
                  }}
                >
                  添加依赖
                </Button>
              }
              className="dependencies-card"
            >
              <Table
                dataSource={dependencies[selectedResourceId!] || []}
                columns={dependencyColumns}
                rowKey="id"
                loading={loading}
                pagination={false}
                size="small"
              />
            </Card>
          </TabPane>
          <TabPane tab="优化建议" key="optimization">
            <Card title="优化建议" className="optimization-card">
              {optimizationSuggestions.length > 0 ? (
                <ul className="suggestion-list">
                  {optimizationSuggestions.map((suggestion) => (
                    <li key={suggestion.id} className="suggestion-item">
                      <div className="suggestion-header">
                        <Tag color={
                          suggestion.severity === 'high' ? 'error' :
                          suggestion.severity === 'medium' ? 'warning' :
                          'processing'
                        }>
                          {suggestion.severity === 'high' ? '高优先级' :
                           suggestion.severity === 'medium' ? '中优先级' :
                           '低优先级'}
                        </Tag>
                        <span className="suggestion-title">{suggestion.description}</span>
                      </div>
                      <div className="suggestion-content">
                        <p><strong>影响:</strong> {suggestion.impact}</p>
                        <p><strong>解决方案:</strong> {suggestion.solution}</p>
                        <p>
                          <strong>相关资源:</strong>
                          {suggestion.resources.map(resourceId => (
                            <Tag key={resourceId} onClick={() => setSelectedResourceId(resourceId)}>
                              {resourceId}
                            </Tag>
                          ))}
                        </p>
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="empty-suggestions">
                  <p>没有优化建议</p>
                </div>
              )}
            </Card>
          </TabPane>
        </Tabs>
      </div>
    );
  };

  return (
    <div className={`resource-dependency-manager ${className || ''}`}>
      <Row gutter={16} className="manager-container">
        <Col span={8} className="resources-column">
          <Card
            title="资源列表"
            extra={
              <Button
                icon={<ReloadOutlined />}
                onClick={loadResources}
                loading={loading}
              />
            }
            className="resources-card"
          >
            <Table
              dataSource={resources}
              columns={resourceColumns}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
              size="small"
              rowClassName={(record) => record.id === selectedResourceId ? 'selected-row' : ''}
              onRow={(record) => ({
                onClick: () => setSelectedResourceId(record.id)})}
            />
          </Card>
        </Col>
        <Col span={16} className="details-column">
          {renderResourceDetails()}
        </Col>
      </Row>

      {/* 添加依赖模态框 */}
      <Modal
        title="添加依赖"
        open={addDependencyModalVisible}
        onCancel={() => setAddDependencyModalVisible(false)}
        onOk={() => form.submit()}
        okText="添加"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleAddDependency}
        >
          <Form.Item
            name="dependencyId"
            label="依赖资源"
            rules={[{ required: true, message: '请选择依赖资源' }]}
          >
            <Select
              placeholder="选择依赖资源"
              showSearch
              optionFilterProp="children"
            >
              {resources
                .filter(r => r.id !== selectedResourceId)
                .map(resource => (
                  <Option key={resource.id} value={resource.id}>
                    {resource.name} ({resource.id})
                  </Option>
                ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="type"
            label="依赖类型"
            initialValue={DependencyTypeValues.STRONG}
            rules={[{ required: true, message: '请选择依赖类型' }]}
          >
            <Select placeholder="选择依赖类型">
              <Option value={DependencyTypeValues.STRONG}>强依赖</Option>
              <Option value={DependencyTypeValues.WEAK}>弱依赖</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="priority"
            label="优先级"
            initialValue={0}
            rules={[{ required: true, message: '请输入优先级' }]}
          >
            <InputNumber min={0} max={100} style={{ width: '100%' }} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑依赖模态框 */}
      <Modal
        title="编辑依赖"
        open={editDependencyModalVisible}
        onCancel={() => {
          setEditDependencyModalVisible(false);
          setCurrentDependency(null);
        }}
        onOk={() => form.submit()}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleEditDependency}
        >
          <Form.Item
            name="dependencyId"
            label="依赖资源"
            rules={[{ required: true, message: '请选择依赖资源' }]}
          >
            <Select
              placeholder="选择依赖资源"
              showSearch
              optionFilterProp="children"
              disabled
            >
              {resources
                .filter(r => r.id !== selectedResourceId)
                .map(resource => (
                  <Option key={resource.id} value={resource.id}>
                    {resource.name} ({resource.id})
                  </Option>
                ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="type"
            label="依赖类型"
            rules={[{ required: true, message: '请选择依赖类型' }]}
          >
            <Select placeholder="选择依赖类型">
              <Option value={DependencyTypeValues.STRONG}>强依赖</Option>
              <Option value={DependencyTypeValues.WEAK}>弱依赖</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="priority"
            label="优先级"
            rules={[{ required: true, message: '请输入优先级' }]}
          >
            <InputNumber min={0} max={100} style={{ width: '100%' }} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 依赖可视化抽屉 */}
      <Drawer
        title="依赖可视化"
        placement="right"
        width="80%"
        onClose={() => setVisualizerVisible(false)}
        open={visualizerVisible}
      >
        <ResourceDependencyVisualizerPanel
          resourceId={selectedResourceId || undefined}
          showDetails={true}
          showWarnings={true}
          showOptimizationSuggestions={true}
        />
      </Drawer>
    </div>
  );
};

export default ResourceDependencyManager;
